{
  "compilerOptions": {
    "baseUrl": ".",
    "jsx": "preserve",
    "target": "esnext",
    "module": "esnext",
    "strict": true,
    "allowJs": true,
    "noEmit": true,
    "noImplicitThis": true,
    "esModuleInterop": true,
    "moduleResolution": "node"
  },
  "include": [
    "types/**/*",
    "src/**/*",
  ],
  "exclude": [
    "**/*.spec.ts",
    "**/*.spec.js",
    "node_modules"
  ]
}
