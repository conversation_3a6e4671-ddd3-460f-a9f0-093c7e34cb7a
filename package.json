{"name": "mobile-template", "version": "0.1.0", "private": true, "author": "HDEC Mobile Team", "scripts": {"serve": "vue-cli-service serve --mode development", "build": "vue-cli-service build --mode production", "build:test": "vue-cli-service build --mode test", "lint": "vue-cli-service lint", "commit": "git-cz", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "test:unit:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "1.0.2", "alloyfinger": "^0.1.16", "amfe-flexible": "^2.2.1", "axios": "^0.23.0", "bpmn-js": "7.3.0", "crypto-js": "^4.1.1", "dayjs": "^1.10.7", "deepmerge": "^4.2.2", "echarts": "^5.4.0", "echarts-gl": "^2.0.9", "el-tree-transfer": "^2.4.7", "element-resize-detector": "1.2.4", "fawkes-lib": "^0.3.0-rc1", "fawkes-mobile-lib": "^0.2.0", "ids": "1.0.0", "jsencrypt": "^3.2.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "playwright": "1.17.0", "qiankun": "^2.7.3", "qs": "^6.10.1", "sm-crypto": "^0.3.6", "sortablejs": "^1.15.6", "throttle-debounce": "^1.1.0", "vue": "^2.7.10", "vue-i18n": "^8.27.0", "vue-router": "^3.2.0", "vue2-touch-events": "3.2.2", "vuex": "^3.4.0"}, "devDependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.16.0", "@vue/cli-plugin-babel": "~4.5.18", "@vue/cli-plugin-eslint": "~4.5.18", "@vue/cli-plugin-router": "~4.5.18", "@vue/cli-plugin-vuex": "~4.5.18", "@vue/cli-service": "~4.5.18", "@vue/eslint-config-airbnb": "^5.0.2", "@vue/test-utils": "^1.3.6", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-plugin-import": "^1.13.3", "compression-webpack-plugin": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^6.7.2", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.3.0", "flush-promises": "^1.0.2", "jest": "^26.6.3", "jest-environment-jsdom": "^26.6.2", "jest-serializer-vue": "^2.0.2", "jest-transform-stub": "^2.0.0", "jest-watch-typeahead": "^0.6.5", "jsdom": "^16.7.0", "less": "^3.0.4", "less-loader": "^5.0.0", "postcss-pxtorem": "5.1.1", "prettier": "2.5.1", "pretty-quick": "^3.1.3", "svg-sprite-loader": "^6.0.11", "vue-jest": "^3.0.7", "vue-loader": "^15.10.0", "vue-template-compiler": "^2.7.10", "webpack-bundle-analyzer": "^4.5.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}