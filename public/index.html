<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- 打电话、发短信功能 -->
    <meta name="format-detection" content="telephone=yes"/>
    <!-- 禁止缩放 -->
    <!-- 从 iOS 11 开始，如果你希望状态栏覆盖Webview，必须在 viewport meta标签中 包含 viewport-fit=cover: -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover" />
    <!-- 屏蔽favicon -->
    <link rel="icon" href="data:image/ico;base64,aWNv">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <style>
      body {
        background-color:#f2f3f4;
        height: 100%;
      }
      html {
        height: 100%;
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <script>
      // 修改后的白屏检测函数
      function checkWhiteScreen() {
        const appElement = document.getElementById('app')
        const MAX_RETRY = 3 // 最大重试次数
        const RETRY_KEY = 'page_retry_counter'
        const isWhiteScreen = appElement.children.length === 0;
        console.log('检测是否是白屏：', isWhiteScreen)

        // 正常渲染时清除计数器
        if (!isWhiteScreen) {
          localStorage.removeItem(RETRY_KEY)
          window.clearInterval(window.whiteScreenInterval)
          window.whiteScreenInterval = undefined;
          window.removeEventListener('load', handleLoad)
          return
        }

        // 白屏检测逻辑
        if (isWhiteScreen) {
          const retryCount = parseInt(localStorage.getItem(RETRY_KEY) || 0)
          if (retryCount < MAX_RETRY) {
            localStorage.setItem(RETRY_KEY, retryCount + 1)
            window.location.reload()
          } else {
            // 达到最大重试次数后，清除计数器并移除事件监听器
            window.clearInterval(window.whiteScreenInterval)
            // 清除handleLoad函数
            window.removeEventListener('load', handleLoad)
            window.whiteScreenInterval = undefined;
            console.log('白屏检测失败，已达到最大重试次数')
          }
        }
      }

      // 提取 load 事件处理函数以便移除
      async function handleLoad() {
        console.log('load 事件触发，三秒后监听白屏事件')
        setTimeout(checkWhiteScreen, 3000)
        window.whiteScreenInterval = setInterval(checkWhiteScreen, 5000)
      }

      // 修改初始化逻辑
      window.addEventListener('load', handleLoad)
      function loadScript(path, callback) {
        const script = document.createElement('script');
        script.src = path;
        script.type = 'text/javascript';

        // 监听脚本加载完成事件
        script.onload = () => {
          if (typeof callback === 'function') {
            callback();
          }
        };

        // 错误处理
        script.onerror = () => {
          console.error(`Failed to load script`);
        };

        // 将脚本插入到 <head> 或 <body> 中
        document.head.appendChild(script);
      }

      const debug = localStorage.getItem('debug')
      if (debug === 'true') {
        loadScript('./static/vconsole.js', () => {
          var vConsole = new window.VConsole();
        })
      } else {
        const el = document.getElementById('__vconsole');
        if (el) {
          el.remove();
        }
      }

      document.addEventListener('keydown', function(event) {
        // ctrl + shift + 1 开启调试
        if (event.ctrlKey && event.shiftKey && (event.key === "1" || event.keyCode === 49)) {
          // 在这里添加你想要执行的代码
          // 检查当前文档中是否已经存在这个脚本
          const existingScript = Array.from(document.getElementsByTagName('script')).find(script => {
            return script.src.includes('vconsole.js')
          });

          if (existingScript) {
            return;
          }
          localStorage.setItem('debug', 'true')
          loadScript('./static/vconsole.js', () => {
            var vConsole = new window.VConsole();
          })
        } else if (event.ctrlKey && event.shiftKey && (event.key === "2" || event.keyCode === 50)) {
          // ctrl + shift + 2 关闭调试
          localStorage.setItem('debug', 'false')
          const existingScript = Array.from(document.getElementsByTagName('script')).find(script => {
            return script.src.includes('vconsole.js')
          });
          if (existingScript) {
            existingScript.remove();
          }
          const el = document.getElementById('__vconsole');
          if (el) {
            el.remove();
          }
        }
      });
      // 点击计数器
      let clickCount = 0;
      // 定义一个变量来存储上次点击的时间戳
      let lastClickTime = 0;
      // 定义一个时间间隔，单位为毫秒
      const interval = 1000;
      window.addEventListener('click', function() {
        const isMobile = /Mobi|Android|iPhone/i.test(navigator.userAgent);
        // 判断是否是移动端，仅移动端触发
        if(isMobile){
          const currentTime = Date.now();
          // 如果当前点击与上次点击的时间间隔超过指定的时间间隔，则重置计数器
          if (currentTime - lastClickTime > interval) {
            clickCount = 0;
          }
          // 每次点击时增加计数器
          clickCount++;
          lastClickTime = currentTime;
          // 当点击次数达到 20 次时，执行相应的操作
          if (clickCount === 20) {
            const existingScript = Array.from(document.getElementsByTagName('script')).find(script => {
              return script.src.includes('vconsole.js')
            });
            if (existingScript) {
              localStorage.setItem('debug', 'false')
              existingScript.remove();
              const el = document.getElementById('__vconsole');
              if (el) {
                el.remove();
              }
            } else {
              localStorage.setItem('debug', 'true')
              loadScript('./static/vconsole.js', () => {
                var vConsole = new window.VConsole();
              })
            }
            // 重置计数器
            clickCount = 0;
          }
        }

      });
    </script>
    <!-- built files will be auto injected -->
    <!-- 在html文档中引入 JSSDK -->
    <!-- JS 文件版本在升级功能时地址会变化，如有需要（比如使用新增的 API），请重新引用「网页应用开发指南」中的JSSDK链接，确保你当前使用的JSSDK版本是最新的。-->
    <script type="text/javascript"
            src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.16.js">
    </script>
    <script src="./pdfjs/build/pdf.js"></script>
    <script src="./pdfjs/build/pdf.worker.js"></script>
    <script src="./static/address-parser.js"></script>
    <script src="./static/sm4/s4.js"></script>
    <script src="./static/sm4/byte&string.js"></script>
    <script src="./static/sm4/smutils.js"></script>
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>

    <script type="text/javascript">
      window._AMapSecurityConfig = {
        securityJsCode: '51dab2e393e08afd5eef05667751c7df'
      };
    </script>
    <script type="text/javascript">
      var AMapUIProtocol = 'https:';  //注意结尾包括冒号
    </script>
    <script type="text/javascript" src="https://res2.wx.qq.com/open/js/jweixin-1.6.0.js"></script>

  </body>
</html>
