/*
 * @Author: gao_m3
 * @Date: 2022-04-24 16:03:15
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-08-15 15:47:17
 * @Descripttion:
 */
module.exports = {
  plugins: {
    // postcss-pxtorem 插件的版本需要 >= 5.0.0
    autoprefixer: {
      overrideBrowserslist: ["Android >= 4.0", "iOS >= 7"],
    },
    "postcss-pxtorem": {
      // 参考官方设置 https://youzan.github.io/vant/#/zh-CN/advanced-usage
      // 配置根字体大小,fawkes-mobile-lib组件保持原来配置
      // 注意：项目模块路径中不要出现fawkes-mobile-lib,以免配置失效
      rootValue ({ file }) {
        return file.indexOf("fawkes-mobile-lib") !== -1 ? 37.5 : 75
      },
      // rootValue: 16,
      propList: ['*'],
      unitPrecision: 5, //最多小数位数;
      // propList: ['font', 'font-size', 'line-height', 'letter-spacing'],
      selectorBlackList: [],
      replace: true,
      mediaQuery: false,
      minPixelValue: 0,
      exclude: /node_modules|exclude/i
    },
  },
}
