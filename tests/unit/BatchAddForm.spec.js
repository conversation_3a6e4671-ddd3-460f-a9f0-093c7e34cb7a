import { shallowMount, createLocalVue } from '@vue/test-utils'
import Vuex from 'vuex'

// 创建一个简单的模拟组件来测试验证逻辑
const MockBatchAddForm = {
  name: 'MockBatchAddForm',
  template: '<div></div>',
  data() {
    return {
      tableData: []
    }
  },
  computed: {
    isCompanyPortal() {
      return false
    }
  },
  methods: {
    some(array, callback) {
      return array.some(callback)
    },
    validateTable() {
      const validationErrors = [];

      // 字段名称映射，用于显示友好的错误信息
      const fieldLabels = {
        driverFullName: '姓名',
        driverPhone: '联系电话',
        driverSex: '性别',
        driverStatus: '状态',
        driverRzTime: '入职日期',
        driverJsgznxCs: '初始工作年限',
        driverResource2: '司机来源',
        driverZlFy1: '月度租赁价格',
        projectId: '项目名称'
      };

      this.tableData.forEach((data, index) => {
        const rowErrors = [];
        const rowNumber = index + 1;

        // 检查基础必填字段
        const requiredFields = ['driverFullName', 'driverPhone', 'driverSex', 'driverStatus', 'driverRzTime', 'driverJsgznxCs', 'driverResource2'];

        requiredFields.forEach(field => {
          if (!data[field]) {
            rowErrors.push(fieldLabels[field]);
          }
        });

        // 检查手机号格式
        if (data.driverPhone && !/^[1-9]\d{10}$/.test(data.driverPhone)) {
          rowErrors.push('联系电话格式不正确（应为11位手机号）');
        }

        // 检查司机来源为租赁时的额外字段
        if (data.driverResource2 === 200 && !data.driverZlFy1) {
          rowErrors.push(fieldLabels.driverZlFy1);
        }

        // 检查公司门户下的项目选择
        if (this.isCompanyPortal && !data.projectId) {
          rowErrors.push(fieldLabels.projectId);
        }

        // 如果当前行有错误，添加到总错误列表
        if (rowErrors.length > 0) {
          validationErrors.push(`第${rowNumber}行缺少：${rowErrors.join('、')}`);
        }
      });

      if (validationErrors.length > 0) {
        // 显示详细的验证错误信息
        const errorMessage = validationErrors.length > 3
          ? `${validationErrors.slice(0, 3).join('；')}；等${validationErrors.length}项错误，请检查表格信息！`
          : `${validationErrors.join('；')}，请完善表格信息！`;

        this.$message.warning(errorMessage);
        return Promise.reject(false);
      }

      // 如果表格信息完整，返回 Promise.resolve 表示验证通过
      return Promise.resolve(true);
    }
  }
}

const localVue = createLocalVue()
localVue.use(Vuex)

describe('BatchAddForm validateTable 测试', () => {
  let wrapper
  let store
  let mockStore

  beforeEach(() => {
    mockStore = {
      state: {
        portal: { id: 'test-portal', name: 'Test Portal' },
        userInfo: { id: 'test-user' }
      },
      getters: {
        authPortalList: () => [
          { id: 1, projectName: 'Test Project', portalId: 'portal-1' }
        ],
        isCompanyPortal: () => false
      }
    }

    store = new Vuex.Store(mockStore)

    wrapper = shallowMount(MockBatchAddForm, {
      localVue,
      store,
      mocks: {
        $route: { params: { projectId: 'test-project' } },
        $message: {
          warning: jest.fn()
        }
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
  })

  describe('validateTable 方法测试', () => {
    it('应该在所有必填字段都填写时验证通过', async () => {
      // 设置完整的测试数据
      wrapper.setData({
        tableData: [{
          driverFullName: '张三',
          driverPhone: '13800138000',
          driverSex: '男',
          driverStatus: '100',
          driverRzTime: '2023-01-01',
          driverJsgznxCs: 5,
          driverResource2: '100'
        }]
      })

      const result = await wrapper.vm.validateTable()
      expect(result).toBe(true)
    })

    it('应该在缺少必填字段时显示详细错误信息', async () => {
      // 设置不完整的测试数据
      wrapper.setData({
        tableData: [{
          driverFullName: '', // 缺少姓名
          driverPhone: '13800138000',
          driverSex: '', // 缺少性别
          driverStatus: '100',
          driverRzTime: '', // 缺少入职日期
          driverJsgznxCs: 5,
          driverResource2: '100'
        }]
      })

      try {
        await wrapper.vm.validateTable()
      } catch (error) {
        expect(error).toBe(false)
      }

      // 验证错误消息
      expect(wrapper.vm.$message.warning).toHaveBeenCalledWith(
        expect.stringContaining('第1行缺少：姓名、性别、入职日期')
      )
    })

    it('应该验证手机号格式', async () => {
      wrapper.setData({
        tableData: [{
          driverFullName: '张三',
          driverPhone: '123', // 错误的手机号格式
          driverSex: '男',
          driverStatus: '100',
          driverRzTime: '2023-01-01',
          driverJsgznxCs: 5,
          driverResource2: '100'
        }]
      })

      try {
        await wrapper.vm.validateTable()
      } catch (error) {
        expect(error).toBe(false)
      }

      expect(wrapper.vm.$message.warning).toHaveBeenCalledWith(
        expect.stringContaining('联系电话格式不正确')
      )
    })

    it('应该在司机来源为租赁时验证月度租赁价格', async () => {
      wrapper.setData({
        tableData: [{
          driverFullName: '张三',
          driverPhone: '13800138000',
          driverSex: '男',
          driverStatus: '100',
          driverRzTime: '2023-01-01',
          driverJsgznxCs: 5,
          driverResource2: 200, // 租赁司机
          driverZlFy1: '' // 缺少月度租赁价格
        }]
      })

      try {
        await wrapper.vm.validateTable()
      } catch (error) {
        expect(error).toBe(false)
      }

      expect(wrapper.vm.$message.warning).toHaveBeenCalledWith(
        expect.stringContaining('月度租赁价格')
      )
    })

    it('应该处理多行错误并限制显示数量', async () => {
      wrapper.setData({
        tableData: [
          { driverFullName: '', driverPhone: '', driverSex: '' }, // 第1行错误
          { driverFullName: '', driverPhone: '', driverSex: '' }, // 第2行错误
          { driverFullName: '', driverPhone: '', driverSex: '' }, // 第3行错误
          { driverFullName: '', driverPhone: '', driverSex: '' }, // 第4行错误
          { driverFullName: '', driverPhone: '', driverSex: '' }  // 第5行错误
        ]
      })

      try {
        await wrapper.vm.validateTable()
      } catch (error) {
        expect(error).toBe(false)
      }

      // 应该显示前3行错误并提示总数
      expect(wrapper.vm.$message.warning).toHaveBeenCalledWith(
        expect.stringMatching(/等5项错误/)
      )
    })
  })

  describe('组件初始化', () => {
    it('应该正确初始化组件', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.vm.tableData).toEqual([])
    })

    it('应该有正确的计算属性', () => {
      expect(wrapper.vm.isCompanyPortal).toBe(false)
    })
  })

  describe('验证方法测试', () => {
    it('应该有 some 辅助方法', () => {
      const testArray = [1, 2, 3]
      const result = wrapper.vm.some(testArray, item => item > 2)
      expect(result).toBe(true)
    })
  })
})
