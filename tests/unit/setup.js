import Vue from 'vue'
import { config } from '@vue/test-utils'

// 全局配置
Vue.config.productionTip = false

// 模拟 fawkes-lib 组件
const mockFawkesComponents = [
  'fks-table',
  'fks-table-column',
  'fks-input',
  'fks-input-number',
  'fks-select',
  'fks-option',
  'fks-date-picker',
  'fks-row',
  'fks-col',
  'fks-tooltip'
]

mockFawkesComponents.forEach(name => {
  Vue.component(name, {
    template: `<div class="${name}"><slot /></div>`,
    props: {
      value: {},
      modelValue: {},
      data: Array,
      disabled: <PERSON><PERSON><PERSON>,
      placeholder: String,
      min: Number,
      max: Number,
      span: Number,
      offset: Number
    }
  })
})

// 模拟全局方法
config.mocks = {
  $message: {
    success: jest.fn(),
    warning: jest.fn(),
    error: jest.fn(),
    info: jest.fn()
  },
  $route: {
    params: { projectId: 'test-project' },
    query: {},
    path: '/test'
  },
  $router: {
    push: jest.fn(),
    replace: jest.fn(),
    go: jest.fn()
  },
  $store: {
    state: {},
    getters: {},
    dispatch: jest.fn(),
    commit: jest.fn()
  },
  $nextTick: Vue.nextTick,
  $set: Vue.set
}

// 模拟 API 请求 - 移除这个模拟，因为路径可能不存在
// jest.mock('@/utils/request', () => ({
//   get: jest.fn(() => Promise.resolve({ data: {} })),
//   post: jest.fn(() => Promise.resolve({ data: {} })),
//   put: jest.fn(() => Promise.resolve({ data: {} })),
//   delete: jest.fn(() => Promise.resolve({ data: {} }))
// }))

// 全局测试工具
global.flushPromises = () => new Promise(resolve => setImmediate(resolve))
