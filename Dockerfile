FROM reg.hdec.com/basicimage/nginx:1.25.1
# FROM nginx:latest

ADD ./dist/ /usr/share/nginx/html

RUN rm /etc/nginx/conf.d/default.conf

ADD ./nginx.conf /etc/nginx/conf.d/nginx.template

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN echo 'Asia/Shanghai' >/etc/timezone

ENV ORIGIN $http_upgrade

ENV HOST http://127.0.0.1:18200/

ENV SUB_APP_BPM http://127.0.0.1:18200/

ENV SUB_APP_WP http://127.0.0.1:18200/

ENV FILE_PREVIEW http://127.0.0.1:8012/

ENV VUE_APP_SAAS_MODE false

# 定义构建时的参数
ARG INDEX_HTML_NAME_ARG=index.html

# 将参数赋值给环境变量
ENV INDEX_HTML_NAME=$INDEX_HTML_NAME_ARG

EXPOSE 80

ENTRYPOINT sed -i 's|VUE_APP_SAAS_MODE|'${VUE_APP_SAAS_MODE}'|g' /usr/share/nginx/html/index.html && chmod 644 /usr/share/nginx/html/index.html && cp /usr/share/nginx/html/index.html /usr/share/nginx/html/${INDEX_HTML_NAME} && envsubst '${ORIGIN},${HOST},${SUB_APP_BPM},${SUB_APP_WP},${FILE_PREVIEW},${INDEX_HTML_NAME}' < /etc/nginx/conf.d/nginx.template > /etc/nginx/conf.d/default.conf && cat /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'
