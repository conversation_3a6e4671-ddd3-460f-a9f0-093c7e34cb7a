<template>
  <div class="task-container" v-if="isMobile" :class="{'bg-white': displayList.length === 0} ">
<!--    <fm-nav-bar title="表单中心" left-text="" right-text="" :border="false" :left-arrow="isBack" />-->
    <fm-search
      v-model="searchValue"
      placeholder="请输入表单名称"
      maxlength="50"
      @clear="onClear"
      @search="onSearch"
    />
    <div class="card-container" v-if="displayList.length !== 0">
      <fm-pull-refresh
        ref="taskScroller"
        refresh-layer-color="#4b8bf4"
        v-model="refreshing"
        success-text="刷新成功"
        @refresh="onRefresh()"
      >
        <fm-list
          v-model="loading"
          :finished="finished"
          finished-text="已经到底啦~"
          :immediate-check="false"
          @load="onLoad"
        >
          <div class="v-task-item" v-for="(item, index) in displayList" :key="index" :style="currentTab != '任务待办' && borderType(item.taskState)">
            <div class="v-task-item__box">
              <div class="v-task-item__box-info" @click="handleContentClick(item)">
                <div class="box-info__top" :style="currentTab != '任务待办' ? backgroundType(item.userTask.taskState): 'background: linear-gradient(to right, rgba(60, 131, 255, 0.08), #FFF)'">
                  <!-- 标题和时间 -->
                  <div class="box-info__title">
                    <img :src="currentTab != '任务待办' ? imgType(item.userTask.taskState) : require('@/assets/img/car/icon_daiban.png')" class="address-text m-r-10">
                    <div class="box-info__title-text">
                      <div>{{ item.userTask.formName || 'mobile流程' }}</div>
                    </div>
                    <div v-if="currentTab != '任务待办'" class="box-info__task-state">
                      <div class="task-text state-text-run" v-if="item.userTask.taskState == 0">流转中</div>
                      <div class="task-text state-text-finish" v-else-if="item.userTask.taskState == 1">已完成</div>
                      <div class="task-text state-text-waste" v-else-if="item.userTask.taskState == 2">废弃</div>
                      <div class="task-text state-text-ts" v-else>暂存</div>
                    </div>
                  </div>
                  <div
                      v-if="Number.isInteger(item.applyFormApprovalReturnState)"
                      class="status-bar"
                      :style="{background: getBackground(item.applyFormApprovalReturnState)}"
                  >{{getReturnState(item.applyFormApprovalReturnState)}}</div>
                </div>
                <div class="box-info__task-info">
                  <div class="box-info__task-name">
                    <div>节点名称</div>
                    <div class="task-text sub-text text-right">
                      {{ item.userTask.taskSubject }}
                    </div>
                  </div>
                  <div class="box-info__task-starter">
                    <div>发起人</div>
                    <div class="task-text starter-text text-right">
                      {{ item.userTask.taskCreatorName }}
                    </div>
                  </div>
                  <div v-if="currentTab === '任务待办'" class="box-info__task-time">
                    <div>开始时间</div>
                    <div class="task-text time-text text-right">
                      {{ item.userTask.processCreateDate }}
                    </div>
                  </div>
                  <div v-else>
                    <div class="line_sty"></div>
                    <div class="box__task-time col-center">
                      <img src="@/assets/img/car/icon_time.png" class="icon-time m-r-10">
                      <div>
                        {{ item.userTask.processCreateDate }}
                      </div>
                    </div>
                  </div>
                  <div class="box-info__task-stop" v-if="!checkTask(item)">
                    <div class="task-text stop-text">该阶段暂不支持移动端审批</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </fm-list>
      </fm-pull-refresh>
    </div>
    <empty-data v-else></empty-data>
    <main-tabbar v-if="isMain"></main-tabbar>
    <tabbar v-else></tabbar>
  </div>
  <menus v-else-if="isPC">
    <task-agent-table/>
  </menus>
</template>
<script>
import { Image, Search, NavBar, Tab, Tabs, PullRefresh, List, Toast } from 'fawkes-mobile-lib'
import MainTabbar from '@/modules/Tabbar/index'
import tabbar from '@/modules/CarApply/components/TabBar.vue'
import { userTaskLists, relTaskLists, circulationLists } from './api'
import { eventBackButton, exitApp } from '@/utils/app'
import { mapGetters, mapMutations, mapState } from 'vuex'
import * as GetterTypes from "@store/Getter/getterTypes";
import platform from "@/mixins/platform";
import Menus from '@/components/menus/index.vue';
import EmptyData from '@/components/EmptyData/index.vue';
import TaskAgentTable from './table.vue';

export default {
  name: 'Todo',
  mixins: [platform],
  components: {
    TaskAgentTable,
    Menus,
    EmptyData,
    MainTabbar,
    tabbar,
    [Image.name]: Image,
    [Search.name]: Search,
    [NavBar.name]: NavBar,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [PullRefresh.name]: PullRefresh,
    [List.name]: List,
    [Toast.name]: Toast,
  },

  data() {
    return {
      value: '',
      // tabs: ['任务待办', '流程跟踪', '表单查询'],
      tabs: ['任务待办', '流程跟踪'],
      displayList: [],
      taskList: [],
      flowList: [],
      formList: [],
      taskPage: 1,
      flowPage: 1,
      formPage: 1,
      pageSize: 20,
      finished: false,
      refreshing: false,
      loading: false,
      loadingFinished: [false, false, false],
      searchValue: '',
      currentTab: '任务待办',
      isBack: true,
      isMain: true
    }
  },
  computed: {
    ...mapGetters([GetterTypes.IS_NOT_PC]),
    ...mapState('CarApply', ['enums']),
  },
  created() {
    this.isMain = this.$storage.get('isMainTab') === 'true';
    this.isBack = this.$storage.get('isMainTab') === 'false' && !this[GetterTypes.IS_NOT_PC];
  },
  mounted() {
    this.isMobile && Toast.loading({
      message: '加载中...',
      forbidClick: true,
    })
    this.getList()
  },
  activated() {
    // 页面激活时移除默认的返回上一级路由处理事件，设置退出应用事件
    document.removeEventListener('backbutton', this.$back, false)
    document.addEventListener('backbutton', eventBackButton, false)
    this.getList()
  },
  deactivated() {
    // 页面隐藏时移除退出应用事件，恢复默认的返回上一级路由的处理事件
    document.removeEventListener('backbutton', eventBackButton, false)
    document.removeEventListener('backbutton', exitApp, false)
    document.addEventListener('backbutton', this.$back, false)
    this.pageReset()
  },
  methods: {
    ...mapMutations('CarApply', ['SET_LAST_ROUTE']),
    getReturnState(state) {
      const item = this.enums.ApplyFormApprovalReturnStateEnums.find(item => {
        return item.key === state
      })
      return item ? item.value : '';
    },
    getBackground(state) {
      switch (true) {
        case [4,5,6,10,13].findIndex(item => item === state) > -1:
          // 红色
          return 'linear-gradient(225deg, #ecc7ca 0%, #FF3F4C 100%)'
        case [7,8,9].findIndex(item => item === state) > -1:
          // 黄色
          return 'linear-gradient(225deg, #FCE15E 0%, #FF7F3C 100%)'
        case [0,11,12].findIndex(item => item === state) > -1:
          // 蓝色
          return 'linear-gradient(225deg, #6FCFFF 0%, #3C83FF 100%)'
        default:
          // 灰色
          return 'linear-gradient(225deg, #DDDDDD 0%, #979DA0 100%)'
      }
    },
    borderType(taskState) {
      switch (taskState) {
        case '0':
          return 'border: 2px solid; border-image: linear-gradient(180deg, rgba(60, 131, 255, 0.3), rgba(255, 255, 255, 1)) 2 2; clip-path: inset(0 round 5px);'
        case '1':
          return 'border: 2px solid; border-image: linear-gradient(180deg, rgba(3, 190, 138, 0.3), rgba(255, 255, 255, 1)) 2 2; clip-path: inset(0 round 5px);'
        case '2':
          return 'border: 2px solid; border-image: linear-gradient(180deg, rgba(155, 160, 163, 0.4), rgba(255, 255, 255, 1)) 2 2; clip-path: inset(0 round 5px);'
        default:
          return 'border: 2px solid; border-image: linear-gradient(180deg, rgba(255, 164, 24, 0.3), rgba(255, 255, 255, 1)) 2 2; clip-path: inset(0 round 5px);'
      }
    },
    backgroundType(taskState) {
      switch (taskState) {
        case '0':
          return 'background: linear-gradient(to right, rgba(60, 131, 255, 0.08), #FFF);'
        case '1':
          return 'background: linear-gradient(to right, rgba(3, 190, 138, 0.08), #FFF);'
        case '2':
          return 'background: linear-gradient(to right, rgba(155, 160, 163, 0.2), #FFF);'
        default:
          return 'background: linear-gradient(to right, rgba(255, 164, 24, 0.08), #FFF);'
      }
    },
    imgType(taskState) {
      switch (taskState) {
        case '0':
          return require('@/assets/img/car/icon_daiban.png')
        case '1':
          return require('@/assets/img/car/icon_car_green.png')
        case '2':
          return require('@/assets/img/car/icon_car_gray.png')
        default:
          return require('@/assets/img/car/icon_car_orange.png')
      }
    },
    setCurPage() {
      this.SET_LAST_ROUTE(this.$route.path);
    },
    // 重置列表page,list值
    pageReset() {
      this.taskPage = 1
      this.flowPage = 1
      this.formPage = 1
      this.taskList = []
      this.flowList = []
      this.formList = []
    },
    changeType(name, title) {
      this.isMobile && Toast.loading({
        message: '加载中...',
        forbidClick: true,
      })
      this.currentTab = title
      // this.$store.commit('SET_CURRENT_TAB', this.currentTab)
      this.loading = false
      var index = this.search(this.tabs, name)
      this.finished = this.loadingFinished[index]
      this.pageReset()
      this.getList()
      if (this.currentTab == title) {
        return false
      }
    },
    search(arr, dst) {
      var i = arr.length
      while (i--) {
        if (arr[i] == dst) {
          return i
        }
      }
      return false
    },
    onRefresh() {
      this.pageReset()
      this.loading = false
      this.finished = false
      this.getList()
    },
    onLoad() {
      console.log('load')
      this.loading = true
      this.getList()
    },
    getList() {
      switch (this.currentTab) {
        case '任务待办':
          userTaskLists({
            page: this.taskPage++,
            size: this.pageSize,
            taskSubject: '',
            formName: this.searchValue,
            cloumn: 'createDate',
            order: 'desc',
            deleteFlag: 0,
          })
            .then((res) => {
              if (res.status && res.data.total == 0) {
                this.loadingFinished[0] = true
                this.finished = this.loadingFinished[0]
                this.refreshing = false
              }
              if (res.status) {
                this.refreshing = false
                if (this.taskList.length === res.data.total) {
                  this.loadingFinished[0] = true
                  this.finished = this.loadingFinished[0]
                } else {
                  this.taskList = [...this.taskList, ...res.data.list]
                }
                this.displayList = this.taskList
              }
            })
            .catch(() => {
              this.finished = true
            })
            .finally(() => {
              Toast.clear()
              this.loading = false
            })
          break
        case '流程跟踪':
          relTaskLists({
            page: this.flowPage++,
            size: this.pageSize,
            taskSubject: '',
            formName: this.searchValue,
            cloumn: '',
            order: '',
          })
            .then((res) => {
              if (res.status && res.data.total == 0) {
                this.loadingFinished[1] = true
                this.finished = this.loadingFinished[1]
                this.refreshing = false
              }
              if (res.status) {
                this.refreshing = false
                if (this.flowList.length === res.data.total) {
                  this.loadingFinished[1] = true
                  this.finished = this.loadingFinished[1]
                } else {
                  this.flowList = [...this.flowList, ...res.data.list]
                }
                this.displayList = this.flowList
              }
            })
            .catch(() => {
              this.finished = true
            })
            .finally(() => {
              Toast.clear()
              this.loading = false
            })
          break
        case '表单查询':
          circulationLists({
            asignee: '',
            creator: '',
            startDate: '',
            endDate: '',
            taskState: '',
            taskSubject: '',
            formName: this.searchValue,
            page: this.formPage++,
            size: this.pageSize,
            order: '',
            column: '',
          })
            .then((res) => {
              if (res.status && res.data.total == 0) {
                this.loadingFinished[2] = true
                this.finished = this.loadingFinished[2]
                this.refreshing = false
              }
              if (res.status) {
                this.refreshing = false
                if (this.formList.length === res.data.total) {
                  this.loadingFinished[2] = true
                  this.finished = this.loadingFinished[2]
                } else {
                  this.formList = [...this.formList, ...res.data.list]
                }
                this.displayList = this.formList
                if (!this.searchValue) {
                  this.displayList = this.displayList.filter((item) => {
                    return item.taskState != 2
                  })
                }
              }
            })
            .catch(() => {
              this.finished = true
            })
            .finally(() => {
              Toast.clear()
              this.loading = false
            })
          break
      }
    },
    onSearch(val) {
      this.searchValue = val
      this.onRefresh()
    },
    onClear() {
      this.searchValue = ''
      this.onRefresh()
    },
    // 判断是否支持审批
    checkTask(item) {
      return true
    },
    // 打开任务详情页面
    handleContentClick(result) {
      const {userTask: item} = result;
      if (this.checkTask(item)) {
        this.$store.commit('SET_CURRENT_ROW', item)
        if (this.currentTab == '任务待办') {
          this.setCurPage();
          this.$router.push({
            name: 'formExecute',
            params: {
              type: 'execute',
              formKey: item.formKey,
              taskKey: item.taskKey,
              taskId: item.taskId,
              bizId: item.formBizId,
              processInstanceId: item.processInstanceId,
              formName: item.formName
            }
          })
        } else if (this.currentTab == '流程跟踪') {
          // 流程跟踪
          this.setCurPage();
          this.$router.push({
            name: 'formView',
            params: {
              type: 'view',
              formKey: item.formKey,
              taskKey: item.taskKey,
              taskId: item.taskId,
              bizId: item.formBizId,
              processInstanceId: item.processInstanceId,
              formName: item.formName
            },
          })
        } else {
          // 表单查询
          this.setCurPage();
          this.$router.push({
            name: 'formView',
            params: {
              type: 'view',
              formKey: item.formKey,
              taskKey: item.taskKey,
              taskId: item.taskId,
              bizId: item.formBizId,
              processInstanceId: item.processInstanceId,
              taskName: item.taskName
            }
          })
        }
      } else {
        Toast('该阶段暂不支持移动端审批', 'middle')
      }
    },
  },
}
</script>
<style lang="less" scoped>
@import "../CarApply/components/index.less";
.task-container {
  height: 100%;
  overflow: hidden;
  background: #f1f2f3;
}
/deep/ .fm-pull-refresh {
  overflow: visible;
}

.card-container {
  // height: calc(100% - 420px);
  height: calc(100% - 200px);
  overflow: auto;
  padding-left: 30px;
  padding-right: 30px;
}
.v-task-item {
  background-color: #fff;
  border-radius: 16px;
  margin-top: 30px;
  &__box {
    display: flex;
    flex-direction: column;

    &-thumbnail {
      width: 180px;
      height: 180px;
      margin-right: 30px;

      image {
        width: 100%;
        height: 100%;
      }
    }

    &-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
      overflow: hidden;

      .box-info__bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }

  .v-task-item__box + .v-task-item__box {
    margin-top: 30px;
    padding-top: 30px;
  }
  //修改滑块背景颜色
  /deep/.u-swipe-content {
    background-color: #fff;
    border-radius: 16px;
  }
}

// 卡片标题栏
.box-info__top {
  padding-left: 30px;
  border-bottom: solid 1px #f2f2f3;
  padding-bottom: 32px;
  display: flex;
  align-items: start;
  justify-content: space-between;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;

  .status-bar {
    padding: 6px 20px;
    background: linear-gradient(225deg, #DDDDDD 0%, #979DA0 100%);
    border-radius: 0 14px 0 14px;
    font-size: 24px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 52px;
    letter-spacing: 1px;
  }
}

// 标题
.box-info__title {
  font-weight: bold;
  font-size: 32px;
  /*display: block;*/
  padding-top: 32px;
  display: flex;
  align-items: center;
  // 指示块
  &-bar {
    width: 6px;
    height: 30px;
    background: #4545d1;
    border-radius: 4px;
    display: inline-block;
    vertical-align: middle;
  }
  // 标题内容
  &-text {
    margin-left: 16px;
    color: #333333;
    display: inline-block;
    vertical-align: middle;
    flex: 1;
  }
}

.box-info__attr {
  color: #999;
  font-size: 20px;
  margin-top: 20px;
}

// 卡片任务内容栏
.box-info__task-info {
  padding-top: 32px;
  padding-left: 48px;
  padding-right: 48px;
  padding-bottom: 32px;
}

// 任务名称
.box-info__task-name {
  font-size: 28px;
  color: #999999;
  display: flex;
}

.box-info__task-starter {
  font-size: 28px;
  color: #999999;
  display: flex;
  padding-top: 28px;

  .starter-text {
    margin-left: 64px;
  }
}

// 任务环节
.box-info__task-node {
  font-size: 28px;
  padding-top: 10px;
  color: #999999;
  display: flex;
}

.task-text {
  color: #333333;
  font-size: 28px;
  font-weight: 400;
  margin-left: 36px;
  flex: 1;
  white-space: nowrap; //不换行
  overflow: hidden; //超出隐藏
  text-overflow: ellipsis; // 超出部分省略表示
  padding-right: 20px;
}

/*.time-text {
  margin-bottom: 30px;
}*/
.state-text {
  margin-left: 92px;
}

.stop-text {
  color: #f32111;
}

// 任务时间
.box-info__task-time {
  padding-top: 28px;
  color: #999999;
  font-size: 28px;
  display: flex;
}
.box-info__task-state {
  /*padding-top: 10px;*/
  position: absolute;
  right: 32px;
  color: #999999;
  font-size: 28px;
  display: flex;
  display: inline-block;
  vertical-align: middle;
}
.state-text-finish {
  width: 86px;
  height: 30px;
  text-align: center;
  padding: 8px 12px;
  border-radius: 26px;
  background-color: #03BE8A;
  border: 1px solid #03BE8A;
  color: #fff;
}
.state-text-run {
  width: 86px;
  height: 30px;
  text-align: center;
  padding: 8px 12px;
  border-radius: 26px;
  background-color: #3C83FF;
  border: 1px solid #3C83FF;
  color: #fff;
}
.state-text-ts {
  width: 86px;
  height: 30px;
  text-align: center;
  padding: 8px 12px;
  border-radius: 26px;
  background-color: #FFA418 ;
  border: 1px solid #FFA418 ;
  color: #fff;
}
.state-text-waste {
  width: 86px;
  height: 30px;
  text-align: center;
  padding: 8px 12px;
  border-radius: 26px;
  background-color: #9BA0A3;
  border: 1px solid #9BA0A3;
  color: #fff;
}
.box-info__task-time {
  padding-bottom: 10px;
}

.box-info__item-label {
  width: 140px;
}

.box-info-warn {
  display: inline-block;
  vertical-align: middle;
  float: right;
  color: #ff4b4b;
  font-size: 24px;
  background-color: #ffeded;
  // padding: 3px 10px;
}

.task-btn-group {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f2f2f2;
  .btn {
    border-radius: 0;
    border: none;
    height: 88px;
    width: 50%;
    white-space: nowrap;
    line-height: 88px;
    text-align: center;
    vertical-align: middle;
    padding: 0px;
    // color: #fff;
    &::after {
      display: none;
    }
  }
  .enter {
    color: #fdac42;
    background-color: #fff;
    border-right: 1px solid #f2f2f2;
    font-size: 32px;
    // background-color: #FDAC42;
  }
  .entrust {
    color: #0097d8;
    background-color: #fff;
    font-size: 32px;
    // background-color: #0097D8;
  }
  .active {
    &:hover {
      background-color: #f0f0f0;
    }
  }
}
/deep/ .fm-search {
  //padding: 0 32px;
}

/deep/ .fm-search__content {
  //height: 74px;
  border-radius: 16px !important;
}
.box__task-time {
  padding-top: 16px;
  color: #999999;
  font-size: 28px;
  display: flex;
}
.icon-time {
  width: 48px;
  height: 48px;
}
.line_sty {
  border-top: 1px solid #f2f2f2;
  margin-top: 22px;
}
</style>
