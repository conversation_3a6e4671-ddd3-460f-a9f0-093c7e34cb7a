import request from '@/utils/request'
//任务代办列表接口
export function userTaskLists(params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/userTasks',
    method: 'get',
    params,
  })
}

// 已办任务列表
export function completedTasks(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/userTasks/completed',
    method: 'post',
    data,
    params
  })
}

export function completedTaskParams() {
  return request({
    url: `/vehicle-dispatch/vd/flow/userTasks/completed/getParam`,
  })
}


export function relTaskLists(params) {
  return request({
    url: 'sys-bpm/createTasks',
    method: 'get',
    params,
  })
}

export function circulationLists(params) {
  return request({
    url: 'sys-bpm/relTasks',
    method: 'get',
    params,
  })
}

export function getFormDetail(ids) {
  return request({
    url: '/vehicle-dispatch/vd/flow/details',
    params: {ids}
  })
}

export function getUserTaskByFormId(bizIds) {
  return request({
    url: '/vehicle-dispatch/vd/flow/userTasks/byBizId',
    params: {bizIds}
  })
}
