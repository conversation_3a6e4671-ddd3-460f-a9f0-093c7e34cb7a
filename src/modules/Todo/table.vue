<template>
  <div class="user-task-container">
    <fks-query-page
      v-loading="loading"
      :tableName="'任务待办'"
      :page-sizes="[15, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :currentPage.sync="currentPage"
      :pageSize.sync="pageSize"
      :data="tableData"
      highlight-current-row
      :total="total"
      @query="getData"
    >
      <template>
        <fks-table-column type="index" align="center" label="#" width="60">
          <template slot-scope="scope">
            {{ scope.$index + (currentPage - 1) * pageSize + 1 }}
          </template>
        </fks-table-column>
        <!-- <fks-table-column prop="formName" label="表单名称" isLink align="center">
          <template slot-scope="scope">
            <span @click="viewHandler(scope.row)">{{ scope.row.formName }}</span>
          </template>
        </fks-table-column> -->
        <fks-table-column prop="userTask.taskSubject" label="任务名称" align="center" width="200px" />
        <fks-table-column prop="userTask.taskCreatorName" label="发起人" align="center" />
        <fks-table-column prop="userTask.processCreateDate" label="开始时间" align="center" width="200px">
          <template slot-scope="scope">{{ scope.row.userTask.processCreateDate ? $dayjs(scope.row.userTask.processCreateDate).format('YYYY-MM-DD HH:mm') : '' }}</template>
        </fks-table-column>
        <fks-table-column prop="userTask.taskAsigneeName" label="当前任务处理人" align="center" width="120px" />
        <fks-table-column prop="applyFormApprovalReturnState" label="状态" align="center" min-width="130" fixed="right">
          <template slot-scope="{row}">
            <fks-tag
              v-if="Number.isInteger(row.applyFormApprovalReturnState)"
              :type="getTagType(row.applyFormApprovalReturnState)"
            >
              {{getReturnState(row.applyFormApprovalReturnState)}}
            </fks-tag>
          </template>
        </fks-table-column>
        <fks-table-column label="操作" width="100" align="center" fixed="right">
          <span slot-scope="scope">
            <fks-button type="text" @click="excuteHandler(scope.row.userTask)">处理</fks-button>
          </span>
        </fks-table-column>
      </template>
    </fks-query-page>
  </div>
</template>

<script>
import { userTaskLists } from './api'
import { mapState } from 'vuex'
export default {
  name: 'TaskAgentTable',
  data() {
    return {
      loading: false,
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableData: []
    };
  },
  created() {
    this.getData();
  },
  computed: {
    ...mapState('CarApply', ['enums'])
  },
  methods: {
    getReturnState(state) {
      const item = this.enums.ApplyFormApprovalReturnStateEnums.find(item => {
        return item.key === state
      })
      return item ? item.value : '';
    },
    getTagType(state) {
      switch (true) {
        case [4,5,6,10,13].findIndex(item => item === state) > -1:
          // 红色
          return 'danger'
        case [7,8,9].findIndex(item => item === state) > -1:
          // 黄色
          return 'warning'
        case [0,11,12].findIndex(item => item === state) > -1:
          // 蓝色
          return ''
        default:
          // 灰色
          return 'info'
      }
    },
    // 查看
    // viewHandler(row) {
    //   this.$store.commit('SET_CURRENT_ROW', row)
    //   this.$router.push({ name: 'formExecute',
    //     params: {
    //       type: 'view',
    //       formKey: row.formKey,
    //       taskKey: row.taskKey,
    //       taskId: row.taskId,
    //       bizId: row.formBizId,
    //       processInstanceId: row.processInstanceId,
    //       formName: row.formName
    //     },
    //     query: {
    //       isApply: 'false',
    //       narrow: 'true'
    //     }
    //   });
    // },
    // 处理
    excuteHandler(row) {
      this.$store.commit('SET_CURRENT_ROW', row)
      this.$router.push({
        name: 'formExecute',
        params: {
          type: 'execute',
          formKey: row.formKey,
          taskKey: row.taskKey,
          taskId: row.taskId,
          bizId: row.formBizId,
          processInstanceId: row.processInstanceId,
          formName: row.formName
        },
        query: {
          isApply: 'false',
          narrow: 'true'
        }
      });
    },
    // 列表
    getData() {
      this.loading = true;
      userTaskLists({
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        columnName: 'createDate',
        sort: 'desc'
      }).then((res) => {
        if (res.status) {
          const { list, total } = res.data || [];
          this.tableData = list;
          this.total = total;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    // 切换size
    sizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.getData();
    },
    // 切换page
    currentChange(val) {
      this.getData();
    },
  }
}
</script>
<style lang="scss" scoped>
.user-task-container {
  position: relative;
  height: 100%;
}
</style>
