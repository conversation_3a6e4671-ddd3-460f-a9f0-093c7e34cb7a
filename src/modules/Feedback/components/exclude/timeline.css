.fb-processing-details {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
  max-width: 800px;
  margin: 0 auto;
}
.fb-processing-details .fb-title {
  font-size: 24px;
  font-weight: normal;
  color: #666;
  margin-bottom: 30px;
}
.fb-processing-details .fb-timeline {
  position: relative;
  padding-left: 30px;
}
.fb-processing-details .fb-timeline .fb-timeline-line {
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e6f0ff;
  z-index: 1;
}
.fb-processing-details .fb-timeline .fb-timeline-item {
  position: relative;
  margin-bottom: 10px;
}
.fb-processing-details .fb-timeline .fb-timeline-item:last-child {
  margin-bottom: 0;
}
.fb-processing-details .fb-timeline .fb-timeline-item .fb-timeline-dot {
  position: absolute;
  left: -27px;
  top: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: rgba(60, 131, 255, 0.3);
  z-index: 2;
}
.fb-processing-details .fb-timeline .fb-timeline-item .fb-timeline-dot .fb-timeline-dot-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(60, 131, 255, 1);
  z-index: 3;
}
.fb-processing-details .fb-timeline .fb-timeline-item .fb-timeline-content .fb-timeline-header {
  display: flex;
  margin-bottom: 10px;
}
.fb-processing-details .fb-timeline .fb-timeline-item .fb-timeline-content .fb-timeline-header .fb-timeline-time {
  margin-right: 16px;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #333333;
}
.fb-processing-details .fb-timeline .fb-timeline-item .fb-timeline-content .fb-timeline-header .fb-timeline-person {
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #333333;
}
.fb-processing-details .fb-timeline .fb-timeline-item .fb-timeline-content .fb-timeline-message {
  background: rgba(244, 244, 244, 0.5);
  padding: 10px;
  border-radius: 4px;
  min-height: 76px;
}
.fb-processing-details .fb-timeline .fb-timeline-item .fb-timeline-content .fb-timeline-message p {
  margin: 0;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  text-align: justify; /* 浏览器可能不支持 */
  letter-spacing: 1px;
  color: rgba(51, 51, 51, 0.6);
}
