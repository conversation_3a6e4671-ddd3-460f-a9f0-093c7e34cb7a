<template>
  <div class="fb-processing-details">
    <div class="fb-timeline">
      <div v-for="(item, index) in timelineData" :key="index" class="fb-timeline-item">
        <div class="fb-timeline-dot">
          <div class="fb-timeline-dot-icon" />
        </div>
        <div class="fb-timeline-content">
          <div class="fb-timeline-header">
            <span class="fb-timeline-time">{{ item.time }}</span>
            <span class="fb-timeline-person">{{ item.person }}</span>
          </div>
          <div class="fb-timeline-message">
            <p>{{ item.message }}</p>
          </div>
        </div>
      </div>
      <div class="fb-timeline-line"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommentTimeline',
  props: {
    comments: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    timelineData() {
      return this.comments.map(comment => ({
        time: this.$dayjs(new Date(comment.createDate)).format('YYYY-MM-DD HH:mm'),
        person: comment.commentUserFullName,
        message: comment.commentContent
      }))
    }
  }
}
</script>

<style scoped>
@import './exclude/timeline.css';
</style>
