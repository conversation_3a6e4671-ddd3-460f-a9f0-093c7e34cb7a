<template>
  <div class="flex flex-column">
    <fm-nav-bar>
      <template slot="title">
        <span class="feedback-title" style="font-size: 18px">反馈详情</span>
      </template>
    </fm-nav-bar>
    <main class="flex-grow-1 overflow-y-auto">
      <div class="bg-white" style="padding: 24px 0 16px 0">
        <div class="second-title" style="font-size: 16px;margin-bottom: 8px;padding: 0 16px">反馈信息</div>
        <fm-cell-group>
          <fm-cell class="remove-cell-title">
            <template slot="title">
              <div class="m-r-20 cell-title">所属项目</div>
            </template>
            <template>
              <div class="cell-value">{{feedBackInfo.userProjectName}}</div>
            </template>
          </fm-cell>
          <fm-cell class="flex-column">
            <template slot="title">
              <div class="cell-title">问题/建议描述</div>
            </template>
            <template>
              <div class="cell-value" style="text-align: left">{{feedBackInfo.feedbackContent}}</div>
            </template>
          </fm-cell>
          <fm-cell class="flex-column">
            <template slot="title">
              <div class="cell-title">上传图片</div>
            </template>
            <template>
              <picture-preview
                v-if="feedBackInfo.feedbackFile"
                :g9s="feedBackInfo.feedbackFile"
              />
            </template>
          </fm-cell>
          <fm-cell>
            <template slot="title">
              <div class="m-r-20 cell-title">所属时间</div>
            </template>
            <template>
              <div class="cell-value">{{feedBackInfo.feedbackTime}}</div>
            </template>
          </fm-cell>
        </fm-cell-group>
      </div>
      <div style="height: 16px;background: #F1F2F3" />
      <div class="bg-white" style="padding: 24px 0 16px 0">
        <div class="second-title" style="font-size: 16px;margin-bottom: 8px;padding: 0 16px">处理信息</div>
        <fm-cell-group>
          <fm-cell>
            <template slot="title">
              <div class="m-r-20 cell-title">问题分类</div>
            </template>
            <template>
              <div class="cell-value">{{getFeedbackText(feedBackInfo.feedbackType)}}</div>
            </template>
          </fm-cell>
          <fm-cell>
            <template slot="title">
              <div class="m-r-20 cell-title">处理状态</div>
            </template>
            <template>
              <div
                style="font-size: 13px;border-radius: 11px;padding: 4px 8px;max-width: 70px;float: right"
                :style="{
                background: getBackgroundColor(feedBackInfo.feedbackStatus),
                color: getColor(feedBackInfo.feedbackStatus)
              }"
              >
                {{getStatusText(feedBackInfo.feedbackStatus)}}
              </div>
            </template>
          </fm-cell>
          <fm-cell class="flex-column">
            <template slot="title">
              <div class="cell-title">处理详情</div>
            </template>
            <template>
              <comment-timeline style="margin-top: 10px" :comments="commentList" />
            </template>
          </fm-cell>
        </fm-cell-group>
      </div>
    </main>
  </div>
</template>

<script>
import {getFeedBackDetail} from "@components/HelpCenter/api";
import RedStar from "@components/red-star.vue";
import PicturePreview from "@components/PicturePreview/index.vue";
import FksImageViewer from "@components/PicturePreview/FksImageViewer.vue";
import { mapActions, mapState } from 'vuex'
import {hexToRgba} from "@utils/util";
import CommentTimeline from "@modules/Feedback/components/comment-timeline.vue";

export default {
  name: 'FeedbackRecordDetail',
  components: {CommentTimeline, FksImageViewer, PicturePreview, RedStar},
  data() {
    return {
      feedBackInfo: {},
      commentList: [],
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    userInfo() {
      return this.$storage.getObject('user')
    },
  },
  methods: {
    ...mapActions('HelpCenter', ['readMsg']),
    getTime(time) {
      if (time) {
        return this.$dayjs(new Date(time)).format('YYYY-MM-DD HH:mm');
      }
      return '';
    },
    getFeedbackText(val) {
      if (val) {
        return this.enums.VdFeedbackIssueTypeEnums.find(item => item.key === val).value;
      }
      return '';
    },
    getColor(status) {
      switch (status) {
        case 100:
          return '#FFA418';
        case 200:
          return '#3C83FF';
        case 300:
          return '#03BE8A';
        case 400:
          return '#9BA0A3';
      }
    },
    getBackgroundColor(status) {
      if (!status) {
        return '#F1F2F3';
      }
      const color = this.getColor(status);
      return hexToRgba(color, 0.1);
    },
    getStatusText(val) {
      if (val) {
        return this.enums.VdFeedbackStatusEnums.find(item => item.key === val).value;
      }
      return '';
    }
  },
  async created() {
    // 解析路由的参数
    const {id} = this.$route.params;
    const {data} = await getFeedBackDetail(id)
    this.feedBackInfo = data.vdFeedback;
    this.commentList = data.vdFeedbackComments
    // 如果当前用户是反馈发起人，并且该反馈需要提醒，消除提醒
    let userName = this.userInfo.userName
    if (userName == this.feedBackInfo.userName && this.feedBackInfo.needNotify) {
      await this.readMsg(id)
    }
  },
  mounted() {
    // 为了解决项目文字过长会换行的问题，将左边标题的宽度给缩小
    const el = document.querySelector('.remove-cell-title');
    if (!el) {
      return;
    }
    const targetEl = el.querySelector('.fm-cell__title');
    if (targetEl) {
      targetEl.classList.remove('fm-cell__title');
    }
  }
}
</script>

<style lang="less" scoped>
.cell-title {
  color: rgba(25, 25, 25, 0.6);
}
.second-title {
  font-weight: 500;
  line-height: normal;
  letter-spacing: normal;
  color: #191919;
}

.cell-value {
  color: #191919;
}
</style>
