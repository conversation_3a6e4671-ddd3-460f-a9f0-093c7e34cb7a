<template>
  <div class="feedback-record-container flex flex-column">
    <header>
      <fm-nav-bar>
        <template slot="title">
          <span class="feedback-title" style="font-size: 18px">反馈记录</span>
        </template>
      </fm-nav-bar>
      <fm-search
        v-model="searchValue"
        clearable
        placeholder="请输入"
        @clear="handleSearch"
        @search="handleSearch"
      />
    </header>
    <main
      v-loading="loading"
      class="flex-grow-1 flex flex-column"
      style="padding: 16px;"
    >
      <div class="switchers" style="margin-bottom: 16px">
        <div
          v-for="item in statusOptions"
          :key="item.key"
          :class="{active: currentStatus === item.key}"
          class="item"
          style="border-radius: 16px;font-size: 14px;padding: 4px 10px"
          @click="currentStatus = item.key"
        >
          {{ item.value }}
          <div class="dot-icon" v-if="hasStatusMap[item.key]"></div>
        </div>
      </div>
      <div
        ref="cardFlowRef"
        class="card-flow flex-grow-1 overflow-y-auto"
        @scroll="handleScroll"
      >
        <feedback-card
          v-for="(dataItem, index) in list"
          :key="index"
          :item="dataItem"
          @click.native="handleClick(dataItem)"
        />
        <i v-if="iconLoading" class="fks-icon-loading loading-icon"/>
        <div v-if="isLastPage && list.length"
             style="font-size: 10px;color: #999999;text-align: center">加载到底啦
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import {getFeedbackPageByUser} from "@components/HelpCenter/api";
import { mapActions, mapState } from 'vuex'
import FeedbackCard from "@modules/Feedback/components/feedbackCard.vue";

export default {
  name: 'FeedbackRecord',
  components: {FeedbackCard},
  data() {
    return {
      pageNo: 1,
      pageSize: 10,
      searchValue: '',
      loading: false,
      currentStatus: 'all',
      list: [],
      isLastPage: false,
      iconLoading: false,
      hasStatusMap: {}
    }
  },
  computed: {
    ...mapState('HelpCenter', ['notifyFeedbackList']),
    ...mapState('CarApply', ['enums']),
    statusOptions() {
      return [{value: '全部', key: 'all'}, ...this.enums.VdFeedbackStatusEnums];
    },
    filterOption() {
      if (this.currentStatus === 'all') {
        return {}
      } else {
        const enums = this.enums.VdFeedbackStatusEnums.map(item => ({
          value: item.value,
          key: item.key
        }));
        return {
          "conditions": [
            {
              "sort": 400,
              "field": "feedbackStatus",
              "fieldName": "处理状态",
              "fieldType": "Integer",
              "fieldEnum": enums,
              "operators": [
                {
                  "operator": "=",
                  "operatorName": "等于",
                  "operatorDefault": false
                }
              ],
              "value": this.currentStatus,
              "default": false,
              "operator": "="
            }
          ]
        }
      }
    }
  },
  methods: {
    handleClick(item) {
      // 跳转至详情页
      this.$router.push(`/feedback/detail/${item.id}`)
    },
    handleScroll(e) {
      const el = e.target;
      const threshold = 50;
      if (el.scrollTop + el.clientHeight >= el.scrollHeight - threshold) {
        if (this.isLastPage) return;
        if (!this.iconLoading) {
          this.loadMore = true;
          // 获取数据
          this.pageNo++;
          this.getData();
        }
        this.iconLoading = true;
      }
    },
    handleSearch() {
      this.pageNo = 1;
      this.getData();
    },
    getData() {
      if (this.pageNo === 1) {
        this.loading = true;
      } else {
        this.iconLoading = true;
      }
      getFeedbackPageByUser(
        {pageNo: this.pageNo, pageSize: this.pageSize, ...this.filterOption},
        {searchValue: this.searchValue}
      ).then(res => {
        if (res.status) {
          const {list, isLastPage} = res.data;
          // 如果是第一页的数据，直接赋值
          if (this.pageNo === 1) {
            this.list = list;
          } else {
            this.list = [...this.list, ...list];
          }
          this.isLastPage = isLastPage;
        }
      }).finally(() => {
        this.loading = false;
        this.iconLoading = false;
      })
    }
  },
  mounted() {
    this.getData();
  },
  watch: {
    currentStatus() {
      // 将 cardFlow 元素滚动回到顶部再获取数据
      const cardFlow = this.$refs.cardFlowRef;
      cardFlow.scrollTop = 0;
      this.$nextTick(() => {
        this.pageNo = 1;
        this.getData();
      });
    },
    notifyFeedbackList: {
      immediate: true,
      handler(newList) {
        const map = {};
        newList.forEach(item => {
          if (item.feedbackStatus !== undefined) {
            map[item.feedbackStatus] = true;
          }
        });
        this.hasStatusMap = map;
      }
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .fm-search__content {
  border-radius: 16px !important;
}
.loading-icon {
  font-size: 36px;
  color: #3c83ff;
  margin-left: 280px;
  margin-top: 20px;
}

.feedback-record-container {
  .feedback-title {
    font-weight: 500;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    color: #191919;
  }

  main {
    background: #F1F2F3;

    .switchers {
      display: grid;
      grid-template-columns: repeat(5, 1fr);

      .item {
        background: #F1F2F3;
        box-sizing: border-box;
        border: 1px solid rgba(204, 204, 204, 0.7);
        color: #ccc;
        margin: 0 auto;
        position: relative;

        .dot-icon {
          position: absolute;
          top: -2px;
          right: -2px;
          width: 14px;
          height: 14px;
          background-color: red;
          border-radius: 50%;
        }

        &.active {
          background: linear-gradient(0deg, rgba(60, 131, 255, 0.1), rgba(60, 131, 255, 0.1)), #FFFFFF;
          border-color: #3C83FF;
          color: #3C83FF;
        }
      }
    }
  }

}
</style>
