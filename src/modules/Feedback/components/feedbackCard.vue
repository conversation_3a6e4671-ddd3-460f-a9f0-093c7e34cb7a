<template>
  <div class="feedback-card-item-container" style="border-radius: 8px;margin-bottom: 16px">
    <header
      style="padding: 14px 16px;"
      class="flex col-center row-between"
      :style="{background: getGradientColor(item.feedbackStatus)}"
    >
      <div class="project-title flex col-center">
        <div class="title-frame" style="width: 20px;height: 20px" :style="{backgroundColor: getColor(item.feedbackStatus)}">
          <fks-icon icon-class="feedback-card-title" />
        </div>
        <div class="m-l-20 title-text" style="font-size: 16px">{{ item.userProjectName || '-' }}</div>
      </div>
      <div
        class="status"
        style="font-size: 13px;border-radius: 11px;padding: 4px 8px;"
        :style="{background: getColor(item.feedbackStatus)}"
      >
        {{getStatusText(item.feedbackStatus)}}
      </div>
    </header>
    <main style="padding: 14px 16px">
      <div class="feedback-info flex col-center row-between" style="margin-bottom: 12px">
        <div class="left flex col-center">
          <fks-icon
            icon-class="feedback-user"
            style="margin-right: 3px;width: 16px;height: 16px"
          />
          <span style="font-size: 14px">反馈人：</span>
        </div>
        <div style="font-size: 14px;color: #191919">{{item.userFullName}}</div>
      </div>
      <div class="feedback-info flex col-center row-between" style="margin-bottom: 12px">
        <div class="left flex col-center">
          <fks-icon
            icon-class="feedback-user"
            style="margin-right: 3px;width: 16px;height: 16px"
          />
          <span style="font-size: 14px">反馈时间：</span>
        </div>
        <div style="font-size: 14px;color: #191919">{{getTime(item.feedbackTime)}}</div>
      </div>
      <div class="feedback-content" style="padding: 12px;border-radius: 8px;font-size: 14px;letter-spacing: 1.27px">
        {{item.feedbackContent || '暂无反馈内容'}}
      </div>
    </main>
  </div>
</template>

<script>
import {mapState} from "vuex";
import {hexToRgba} from "@utils/util";

export default {
  name: 'FeedbackCard',
  props: {
    item: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {

    };
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    statusOptions() {
      return this.enums.VdFeedbackStatusEnums;
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', this.item);
    },
    getTime(time) {
      return this.$dayjs(time).format('YYYY-MM-DD HH:mm');
    },
    getStatusText(status) {
      const option = this.statusOptions.find(item => item.key === status);
      return option ? option.value : '';
    },
    getColor(status) {
      switch (status) {
        case 100:
          return '#FFA418';
        case 200:
          return '#3C83FF';
        case 300:
          return '#03BE8A';
        case 400:
          return '#9BA0A3';
      }
    },
    getGradientColor(status) {
      const c = this.getColor(status);
      const color1 = hexToRgba(c, 0)
      const color2 = hexToRgba(c, 0.08)
      return `linear-gradient(270deg, ${color1} 0%, ${color2} 100%)`
    }
  },
};
</script>
<style lang="less" scoped>
.feedback-card-item-container {
  background: #FFFFFF;
  &:last-child {
    margin-bottom: 0;
  }

  .project-title {
    .title-frame {
      border-radius: 50%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .title-text {
      font-weight: 500;
      line-height: normal;
      letter-spacing: normal;
      color: #000000;
    }
  }

  .status {
    color: white;
    white-space: nowrap;
  }

  main {
    .feedback-info {
      .left {
        color: rgba(25, 25, 25, 0.6);
      }
    }
    .feedback-content {
      background: #F4F4F4;
      font-weight: normal;
      line-height: normal;
      color: rgba(25, 25, 25, 0.6);
    }
  }
}
</style>
