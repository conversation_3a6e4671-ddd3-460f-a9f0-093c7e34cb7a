<template>
  <div class="feedback-container flex flex-column">
    <fm-nav-bar @click-right="onClickRight">
      <template slot="title">
        <span class="feedback-title" style="font-size: 18px">问题反馈</span>
      </template>
      <template slot="right">
        <span style="color: #3C83FF; position: relative">
          我的反馈记录
          <div v-if="feedbackNotify" style="
            position: absolute;
            top: 2px;
            right: -8px;
            width: 6px;
            height: 6px;
            background-color: red;
            border-radius: 50%;"></div>
        </span>
      </template>
    </fm-nav-bar>
    <main style="margin-top: 16px" class="bg-white flex-grow-1 flex flex-column">
      <fm-cell-group class="flex-grow-1 overflow-y-auto">
        <fm-cell class="remove-cell-title" is-link @click="showProjectSelect = true">
          <template slot="title">
            <div class="m-r-20">所属项目</div>
          </template>
          <template>
            <div v-if="!params.projectId" style="font-size: 14px" class="placeholder">请选择项目</div>
            <div v-else>{{ params.projectName }}</div>
          </template>
        </fm-cell>
        <fm-cell class="flex-column">
          <template slot="title">
            <red-star title="问题/建议描述" style="justify-content: start" />
          </template>
          <template>
            <fm-field
              v-model="params.content"
              rows="3"
              autosize
              type="textarea"
              placeholder="请输入"
            />
          </template>
        </fm-cell>
        <fm-cell class="flex-column" title="上传图片">
          <template>
            <div class="flex flex-column">
              <div class="description" style="font-size: 12px;margin: 10px 0">最多可上传4张图片，JPG/JPEG/PNG格式</div>
              <fm-uploader
                v-model="params.attachment"
                :max-count="4"
                :after-read="afterRead"
              />
            </div>
          </template>
        </fm-cell>
        <select-picker
          :show.sync="showProjectSelect"
          :is-enum="false"
          :column="mobileThirdLevelPortals"
          @choose="handleSelectChange"
          :is-show-search="true"
          @update:search="handleSearchUpdate"
        />
      </fm-cell-group>
      <div
        v-loading="loading"
        class="submit-button"
        style="height: 44px;font-size: 16px;line-height: 44px;margin: 10px auto"
        @click="handleSubmit"
      >
        提交
      </div>
    </main>
  </div>
</template>

<script>
import SelectPicker from "@modules/FormCenter/components/SelectPicker/index.vue";
import * as StateTypes from "@store/State/stateTypes";
import {PROJECT_PORTAL} from "@utils/constants";
import {mapState} from "vuex";
import RedStar from "@components/red-star.vue";
import {uuid} from "@utils/util";
import request from "@utils/request";
import {commitFeedBack} from "@components/HelpCenter/api";

export default {
  components: {RedStar, SelectPicker},
  data() {
    return {
      loading: false,
      showProjectSelect: false,
      g9s: uuid(16, 32),
      fileList: [],
      params: {
        projectId: '',
        projectName: '',
        content: '',
        attachment: []
      },
      thirdLevelPortals: [],
      mobileThirdLevelPortals: [],
      initMobileThirdLevelPortals: []
    };
  },
  computed: {
    ...mapState([StateTypes.PORTAL, StateTypes.PORTALS, StateTypes.SECOND_LEVEL_PORTAL, StateTypes.DATA_BASE_PORTAL, StateTypes.OLD_PROJECT_PORTAL]),
    // thirdLevelPortals() {
    //   return this[StateTypes.PORTALS].filter(item => (item.parentName === PROJECT_PORTAL) && (item.id !== this[StateTypes.DATA_BASE_PORTAL].companyPortalId));
    // },
    // mobileThirdLevelPortals() {
    //   return this.thirdLevelPortals.map(item => ({ code: item.id, value: item.name, key: item.id }));
    // },
    ...mapState('HelpCenter', ['itemList']), // 注意这里用 state 的变量名 itemList，而 getter 用 mapGetters
    feedbackNotify() {
      const target = this.itemList.find(item => item.tag === 'feedback');
      return target ? target.notify : false;
    },
  },
  methods: {
    handleSearchUpdate(newSearchValue) {
      if(newSearchValue) {
        this.mobileThirdLevelPortals = [...this.initMobileThirdLevelPortals.filter(item => item.value.includes(newSearchValue))];
      }else {
        this.mobileThirdLevelPortals =[...this.initMobileThirdLevelPortals]
      }
    },
    onClickRight() {
      // 跳转到我的反馈记录页面
      this.$router.push('/feedback/record');
    },
    handleSelectChange(val) {
      this.params.projectId = val.code;
      this.params.projectName = val.value;
    },
    afterRead(files) {
      // 上传状态
      files.status = "uploading";
      files.message = "上传中...";
      files.name = files.file.name;
      files.progress = 0;
      // 调用上传接口
      const index = this.fileList.length;
      this.uploadFile({
        files,
        g9s: this.g9s,
        index
      }).then(res => {
        if (!res.status) {
          // 请求未成功  状态修改为失败
          files.status = "failed";
          files.message = "上传失败";
          this.fileList.splice(index, 1);
          return false;
        } else {
          // 请求成功修改为成功状态
          files.message = "上传完成";
          files.status = "done";
          // 把接口返回的数据放到数组里面
          files.progress = 100;
          files.type = 'success';
          this.$set(this.fileList, index, {...files, ...res.data});
        }
      })
    },
    uploadFile(params) {
      const { files, g9s, index } = params
      let formData = new FormData();
      formData.append('g9s', g9s);
      formData.append('file', files.file, files.file.name);
      const _this = this;
      return request({
        url: '/sys-storage/upload',
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          'Fawkes-Auth': this.$storage.get("access_token")
        },
        onUploadProgress: event => {
          // 这里可以获取上传进度
          if (event.lengthComputable) {
            let progress = Math.ceil((event.loaded * 100) / event.total)
            if (progress <= 100) {
              files.progress = progress;
              _this.$set(_this.fileList, index, files);
            }
          }
        },
        data: formData
      });
    },
    handleSubmit() {
      // 校验填写
      if (!this.params.content) {
        this.$toast('请输入问题/建议描述');
        return;
      }
      const params = {
        userName: this.$storage.get('username'),
        userFullName: this.$storage.get('userFullname'),
        userProjectId: this.params.projectId,
        userProjectName: this.params.projectName,
        feedbackContent: this.params.content,
        feedbackFile: this.g9s,
      }
      this.loading = true;
      commitFeedBack(params).then(res => {
        if (res.status) {
          this.$toast('提交成功');
          this.$router.back();
        }
      }).finally(() => {
        this.loading = false;
      })
    }
  },
  mounted() {
    // 为了解决项目文字过长会换行的问题，将左边标题的宽度给缩小
    const el = document.querySelector('.remove-cell-title');
    const targetEl = el.querySelector('.fm-cell__title');
    if (targetEl) {
      targetEl.classList.remove('fm-cell__title');
    };
    this.thirdLevelPortals = this[StateTypes.PORTALS].filter(item => (item.parentName === PROJECT_PORTAL) && (item.id !== this[StateTypes.DATA_BASE_PORTAL].companyPortalId));
    this.mobileThirdLevelPortals = this.thirdLevelPortals.map(item => ({ code: item.id, value: item.name, key: item.id }));
    this.initMobileThirdLevelPortals = this.thirdLevelPortals.map(item => ({ code: item.id, value: item.name, key: item.id }));
  }
}
</script>

<style lang="less">
.feedback-container {
  background: #f1f2f3;

  .feedback-title {
    font-weight: 500;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    color: #191919;
  }

  main {
    .placeholder {
      font-weight: normal;
      line-height: normal;
      text-align: right;
      letter-spacing: normal;
      color: rgba(25, 25, 25, 0.3);
    }
    .description {
      font-weight: normal;
      line-height: normal;
      letter-spacing: normal;
      color: #999999;
      text-align: left;
    }
    .submit-button {
      width: 90%;
      border-radius: 4px;
      background: #3C83FF;
      color: #FFFFFF;
      font-weight: 500;
      line-height: normal;
      text-align: center;
      letter-spacing: 1.45px;
    }
  }


}

</style>
