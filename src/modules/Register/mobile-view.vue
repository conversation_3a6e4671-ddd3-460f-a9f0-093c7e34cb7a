<template>
  <div class="mobile-register-container">
    <!-- 你的内容 -->
    <div class="register-logo">
      <img :src="require('@/assets/img/homepage/new-logo.png')" width="43px" height="26px"
           style="border-radius: 6px;object-fit: contain; padding-top: 1px"/>
      <!--      <div class="divider" />-->
      <span style="font-size: 19px; padding-bottom: 1px" class="second-title">华智车管</span>
    </div>
    <div class="register-form">
      <div class="form-title" >
        用户注册
      </div>
      <div class="form-content">
        <fks-form class="flex-grow-1 " v-if="isRegis" :model="form" ref="registerForm" label-position="top">
          <fks-form-item prop="userFullName">
            <template #label>
              <regis-label label="姓名" :tip="errors.userFullName" icon="fks-icon-warning-outline" :req="true" />
            </template>
            <fks-input v-model="form.userFullName"
                       maxlength="20" placeholder="请输入姓名" @blur="validateField('userFullName')" />
          </fks-form-item>

          <fks-form-item prop="userName">
            <template #label>
              <regis-label label="用户名" :tip="errors.userName" icon="fks-icon-warning-outline" :req="true" />
            </template>
            <fks-input v-model="form.userName"
                       maxlength="20" placeholder="请输入用户名" @blur="validateField('userName')" />
          </fks-form-item>

          <fks-form-item prop="userPhone">
            <template #label>
              <regis-label label="手机号" :tip="errors.userPhone" icon="fks-icon-warning-outline" :req="true" />
            </template>
            <fks-input v-model="form.userPhone" placeholder="请输入手机号" @blur="validateField('userPhone')" />
          </fks-form-item>

          <fks-form-item prop="smsCode">
            <template #label>
              <regis-label label="验证码" :tip="errors.smsCode" icon="fks-icon-warning-outline" :req="true" />
            </template>
            <fks-input
              v-model="form.smsCode"
              placeholder="请输入验证码"
              @blur="validateField('smsCode')"
              style="width: 200px; display: inline-block; margin-right: 8px;"
            />
            <fks-button
              type="primary"
              @click="onSendCode"
              :disabled="countDown > 0 || !form.userPhone"
              :style="(countDown > 0 || !form.userPhone)
                ? {
                    backgroundColor: 'rgb(236, 242, 254)',
                    color: '#3C83FF',
                    borderColor: '#3C83FF'
                  }
                : {}"
            >
              {{ countDown > 0 ? `${countDown}s 后重试` : '获取验证码' }}
            </fks-button>
          </fks-form-item>


          <fks-form-item prop="inviteCode">
            <template #label>
              <regis-label label="邀请码" :tip="errors.inviteCode" icon="fks-icon-warning-outline" :req="true" />
            </template>
            <fks-input v-model="form.inviteCode" placeholder="请输入二级单位邀请码" @blur="getDeptByInviteCode" />
          </fks-form-item>

          <fks-form-item>
            <template #label>
              <regis-label label="二级单位" />
            </template>
            <fks-input v-model="form.userDepName" disabled placeholder="自动生成" />
          </fks-form-item>

          <fks-form-item>
            <template #label>
              <regis-label label="三级单位" />
            </template>
            <fks-select class="full-width" v-model="form.userDepName2" filterable placeholder="请输入三级单位">
              <fks-option
                v-for="item in level3Options"
                :key="item.deptName"
                :label="item.deptName"
                :value="item.deptName"
              />
            </fks-select>
          </fks-form-item>
        </fks-form>

      </div>
      <div v-if="isSuccess" class="success-container">
        <div class="img-container">
          <img class="plus-1" src="@/assets/img/register/plus-icon.svg"/>
          <img class="circle-1" src="@/assets/img/register/circle-icon.svg"/>
          <img class="plus-2" src="@/assets/img/register/plus-icon.svg"/>
          <img class="circle-2" src="@/assets/img/register/circle-icon.svg"/>
          <div class="center-img">
            <img src="@/assets/img/register/success.svg" alt="" style="z-index: 3">
            <img src="@/assets/img/register/success-back.svg" alt="" style="z-index: 2">
            <img src="@/assets/img/register/success-back-2.svg" class="success-back-2" alt="" style="z-index: 1">
          </div>
        </div>
        <div class="success-content">
          <div class="content-title">提交成功</div>
          <div class="content-text">
            您的用户信息已注册成功
            <br>
            可搜索微信服务号“华智车管”关注使用。
          </div>
        </div>
      </div>

      <div style="text-align: center; width: 100%; height: 66px">
        <fks-button v-if="isRegis" class="form-btn" type="primary" :loading="loading" @click="onSubmit">提  交</fks-button>
        <fks-button v-if="isSuccess" class="form-btn" type="primary" @click="toRegis">返  回</fks-button>
      </div>
    </div>
  </div>
</template>

<script>
import registerLabel from '@modules/Register/exclude/RegisterLabel.vue'
import {
  vdAndSysUserExistByName, vdAndSysUserExistByPhone
} from '@modules/ProjectCar/CompanyPortal/UserManagement/api'
import { getDeptByInviteCode, regisUser, sendSmsCodeWithPhone } from '@modules/Register/api'
import { Toast } from 'fawkes-mobile-lib'

export default {
  name: 'mobileRegister',
  components: {
    // 在这里注册子组件
    regisLabel: registerLabel,
  },
  data() {
    return {
      type: 'register',
      // 响应式数据
      form: {
        userFullName: '',
        userName: '',
        userPhone: '',
        inviteCode: '',
        userDepName: '自动生成',
        userDepName2: '',
        smsCode: '',
        smsCodeKey: '',
      },
      backForm: {},
      fieldLabels: {
        userFullName: '姓名',
        userName: '用户名',
        userPhone: '手机号',
        inviteCode: '邀请码',
        smsCode: "验证码"
      },
      errors: {
        userFullName: '',
        userName: '',
        userPhone: '',
        inviteCode: '',
        smsCode: ''
      },
      level3Options: [ ],
      countDown: 0,
      timer: null,
      loading: false,
    };
  },
  computed: {
    isRegis() {
      return this.type === 'register';
    },
    isSuccess() {
      return this.type === 'success';
    }
  },
  watch: {

  },
  methods: {
    async getDeptByInviteCode() {
      let code = this.form.inviteCode;

      this.form.userDepName = '';
      this.form.userDepName2 = '';
      this.level3Options = [];

      if (code) {
        const res = await getDeptByInviteCode(code)
        if (res.status) {
          if (res.data) {
            let data = res.data;
            let {level2Dept, level3Depts} = data;
            if (level2Dept) {
              this.form.userDepName = level2Dept.deptName;
              this.level3Options = level3Depts;
              if (level3Depts && level3Depts.length > 0) {
                this.form.userDepName2 = level3Depts[0].deptName;
              }
            }
          }
        }
      }
      this.validateField('inviteCode');
    },
    async validateField(field) {
      const value = this.form[field];
      const label = this.fieldLabels[field];
      let message = '';

      if (!value) {
        message = `请输入${label}`;
      }
      if (field === 'userName') {
        const reg = /^[a-zA-Z0-9_]+$/;
        if (!value) {
          message = '用户名不能为空';
        } else if (!reg.test(value)) {
          // 验证是否符合字母、数字和下划线的组合
          message = '用户名只能包含字母、数字和下划线';
        } else {
          const res = await vdAndSysUserExistByName(value)
          if (res.status) {
            if (res.data) {
              message = '该用户名已存在，请重新输入'
            }
          } else {
            message = '无法校验是否重复'
          }
        }
      }

      // 手机号验证
      if (field === 'userPhone') {
        if (!/^1\d{10}$/.test(value)) {
          message = '请输入有效的手机号';
        } else {
          const res = await vdAndSysUserExistByPhone(value)
          if (res.status) {
            if (res.data) {
              message = '该手机号已存在，请重新输入'
            }
          } else {
            message = '无法校验是否重复'
          }
        }
      }
      if (field === 'inviteCode') {
        if (value && !this.form.userDepName) {
          message = '无效的邀请码'
        }
      }
      this.$set(this.errors, field, message);
      return !message;
    },
    async onSendCode() {
      const valid = await this.validateField('userPhone');
      if (!valid) {
        Toast.warning('请先输入有效手机号！')
        return;
      }

      let res = await sendSmsCodeWithPhone(this.form.userPhone)
      if (res.status) {
        // 模拟接口调用发送验证码
        Toast.warning('验证码已发送！')
        this.form.smsCodeKey = res.data;
      } else {
        this.$message.warning(res.message);
      }

      // 启动倒计时
      this.countDown = 60;
      this.timer = setInterval(() => {
        this.countDown--;
        if (this.countDown <= 0) {
          clearInterval(this.timer);
          this.timer = null;
        }
      }, 1000);
    },
    async onSubmit() {
      this.loading = true;
      const fieldsToValidate = ['userFullName', 'userName', 'userPhone', 'inviteCode'];
      const validationResults = await Promise.all(
        fieldsToValidate.map(field => this.validateField(field))
      );

      const isValid = validationResults.every(result => result);

      if (isValid) {

        Toast.loading({
          message: '加载中...',
          forbidClick: true,
        })

        let data = this.form;
        let res = await regisUser(data);
        if (res.status) {
          Toast.success('提交成功！')
          this.form = this.backForm;
          this.errors = {}
          this.type = 'success'
        }
        this.loading = false;
      } else {
        this.loading = false;
        Toast.error('信息填写不完整或有误')
      }
    },
    toRegis() {
      this.form = this.backForm;
      this.errors = {}
      this.type = 'register'
    }
  },
  mounted() {
    this.backForm = {...this.form};
  }
};
</script>

<style scoped lang="less">
@import "./exclude/mobile.css";
</style>
