.pc-register-container {
    background-image: url("../../../assets/img/register/back.png");
    background-size: cover;
    background-repeat: no-repeat;

    height: 100vh; /* 关键点：给父容器一个固定高度 */

    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    position: relative;

    .register-logo {
        flex-shrink: 0;
        position: absolute;
        top: 20px;
        left: 40px;
        display: flex;
        align-items: center;
        gap: 14px;
    }

    .register-form {
        width: 586px;
        height: 700px;
        max-height: 95% !important;
        border-radius: 12px;

        background: linear-gradient(0deg, rgba(255, 255, 255, 0.1416) 0%, rgba(255, 255, 255, 0.5973) 100%);
        box-sizing: border-box;
        border-image: linear-gradient(216deg, rgba(255, 255, 255, 0.8734) 0%, rgba(255, 255, 255, 0.3461) 99%) 1;
        box-shadow: 8px 12px 24px 0px rgba(29, 32, 132, 0.05);
        padding: 24px 68px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

        .form-title {
            font-size: 22px;
            font-weight: 500;
            line-height: normal;
            letter-spacing: normal;
            color: #333333;
            text-align: center;
        }

        .form-content {
            flex: 1 1 auto;
            width: 100%;

            /** 关键：滚动逻辑 **/
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0; /* 防止子元素撑高容器 */
        }

        .fks-form-item {
            margin-bottom: unset;
        }
        .fks-input__inner {
            border-radius: 6px;
            background: rgba(20, 96, 255, 0.1);
            box-sizing: border-box;
            height: 38px;
            border: unset;
        }
        ::placeholder {
            font-size: 14px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #90A5CF;
        }

        .form-btn {
            width: 100%;
            border-radius: 6px;
            margin-top: 30px;
            margin-bottom: 20px;
            background: linear-gradient(121deg, #69A0FF -3%, #3C83FF 102%);
        }
    }

    .success-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-content: center;
        .img-container {
            margin: 0 auto;
            position: relative;
            height: 200px;
            width: 300px;

            .center-img {
                position: absolute;
                top: 50%;
                left: 50%;
                width: 125px;
                height: 125px;
                transform: translate(-50%, -50%);
            }

            .center-img img {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }

            /* 针对 back-2 做偏移 */
            .center-img .success-back-2 {
                transform: translate(-50%, -50%) translate(5px, 5px);
            }

            .plus-1 {
                position: absolute;
                top: 0;
                left: 0;
            }
            .plus-2 {
                position: absolute;
                right: 30px;
                bottom: 30px;
            }
            .circle-1 {
                position: absolute;
                bottom: 0;
                left: 30px;
            }
            .circle-2 {
                position: absolute;
                right: 0;
                top: 30px;
            }

        }
        .success-content {
            margin-top: 10px;
            .content-title {
                text-align: center;
                font-size: 16px;
                font-weight: 500;
                line-height: normal;
                letter-spacing: normal;
                color: #333333;
                margin-bottom: 12px;
            }
            .content-text {
                font-size: 14px;
                font-weight: normal;
                line-height: normal;
                text-align: center;
                letter-spacing: normal;
                color: rgba(51, 51, 51, 0.6);
            }
        }
    }
}
