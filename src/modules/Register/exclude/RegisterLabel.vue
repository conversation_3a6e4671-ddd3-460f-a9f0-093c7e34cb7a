<template>
  <span class="regis-label">
    <span v-if="req" class="regis-required">*</span>
    {{ label }}
    <span v-if="tip" class="regis-label-tip">
      <i :class="icon"></i>
      {{ tip }}
    </span>
  </span>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
  props: {
    label: {
      type: String,
      required: true
    },
    tip: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'fks-icon-warning-outline'
    },
    req: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style scoped>
.regis-label {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
}

.regis-label-tip {
  display: inline-flex;
  align-items: center;
  margin-left: 6px;

  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #FF4143;
}

.regis-label-tip i {
  margin-right: 4px;
  margin-left: 8px;
  padding-top: 2px;
}

.regis-required {
  color: red;
  margin-right: 4px;
}
</style>
