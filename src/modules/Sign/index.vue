<template>
  <div class="container" :class="{'desktop': isPC}">
    <div class="images">
      <img :src="require('@/assets/img/login/login-car.png')" class="car-bg"/>
      <img :src="require('@/assets/img/login/alarm-bg.png')" class="alarm-bg"/>
      <img :src="require('@/assets/img/login/location-pg.png')" class="location-bg"/>
      <img :src="require('@/assets/img/login/profile-bg.png')" class="profile-bg"/>
    </div>
    <div class="panel">
      <fm-form validate-first @failed="pass = false" @submit="onSubmit">

        <header>您好，欢迎使用</header>
        <fm-field v-model="phoneNumber" :rules="phoneRules" class="field" placeholder="请输入手机号" type="tel"/>
        <div class="captcha-field">
          <fm-field :maxlength="6" v-model="captchaCode" class="field" placeholder="请输入验证码"/>
          <fm-button
              :disabled="disableFetchCaptcha"
              class="captcha-button"
              size="large" type="primary" @click="fetchCode">
            {{ buttonText }}
          </fm-button>
        </div>
        <fm-button
            :loading="loading"
            class="submit-button"
            native-type="submit"
            size="large"
            type="primary"
            @click="signUp"
        >
          登录
        </fm-button>
      </fm-form>
    </div>
  </div>
</template>

<script>
import {ActionSheet, Button, Field, Form, Toast, Dialog} from 'fawkes-mobile-lib'
import {mapActions, mapMutations, mapState} from 'vuex';
import * as StateTypes from '@/store/State/stateTypes'
import * as ActionTypes from '@/store/Action/actionTypes'
import {authRegister, registerSms} from "@/api/carApply";
import {startCountdown} from "@utils/util";
import feishuLogin from "@/mixins/feishuLogin";
import * as mutationTypes from "@store/Mutation/mutationTypes";
import platform from "@/mixins/platform";

export default {
  name: 'Sign',
  mixins: [feishuLogin, platform],
  components: {
    [ActionSheet.name]: ActionSheet,
    [Button.name]: Button,
    [Field.name]: Field,
    [Form.name]: Form,
    [Dialog.name]: Dialog,
    Toast
  },
  data() {
    return {
      visible: false,
      phoneNumber: undefined,
      captchaCode: undefined,
      captchaFetched: false,
      captchaFetching: false,
      pass: false,
      remainSecond: 0,
      captchaKey: '',
      loading: false
    }
  },
  watch: {
    [StateTypes.FEISHU_USER_INFO]: {
      immediate: true,
      handler(newVal) {
        const feishuInfo = newVal
        if (feishuInfo && this[StateTypes.APPLY_RESOURCE] === 1) {
          // 飞书环境下，默认填写手机号
          if (feishuInfo.mobile) {
            this.phoneNumber = Number(feishuInfo.mobile.toString().replace('+86', ''))
          }
        }
      }
    }
  },
  methods: {
    ...mapMutations([mutationTypes.SET_USER_INFO]),
    ...mapActions('CarApply', [
      'getCurrentUser',
      'getEnum'
    ]),
    ...mapActions([ActionTypes.LOGIN_SUCCESS]),
    fetchCode() {
      setTimeout(() => {
        if (this.pass) {
          this.captchaFetching = true
          startCountdown(60, () => {
            this.captchaFetching = false;
          }, (second) => {
            this.remainSecond = second;
          })
          registerSms({phone: this.phoneNumber, source: this[StateTypes.APPLY_RESOURCE]}).then(res => {
            if (res.status) {
              this.captchaKey = res.data;
              this.captchaFetched = true;
            } else {
              Dialog({message: res.message})
            }
          })
        }
      })
    },
    onSubmit() {
      this.pass = true
    },
    async signUp() {
      if (this.pass) {
        // 执行注册逻辑
        if (this[StateTypes.APPLY_RESOURCE] === 1) {
          // 飞书注册流程
          const openId = this.$route.query.openId || this[StateTypes.OPEN_ID]
          const params = {
            captchaCode: this.captchaCode,
            captchaKey: this.captchaKey,
            openId,
            phone: this.phoneNumber,
            source: 1
          }
          console.info('🚀🚀', 'params -->', params, `<-- index.vue/signUp`)
          this.loading = true;
          authRegister(params).then(async (res) => {
            console.info('🚀🚀', '调用注册接口后的返回数据 -->', res, `<-- index.vue/`)
            if (res.status) {
              this.isMobile && Toast.success('登录成功');
              this.$message.success('登录成功')
              await this[ActionTypes.LOGIN_SUCCESS](res.data)
              this.loading = false;
            } else {
              this.isMobile && Toast(res.message)
              this.isPC && this.$message.error(res.message)
              this.loading = false;
            }
          }, () => {
            this.loading = false;
          })
        }

      }
    }
  },
  computed: {
    ...mapState([StateTypes.FEISHU_USER_INFO, StateTypes.APPLY_RESOURCE, StateTypes.OPEN_ID]),
    disabled() {
      return !(this.captchaCode && this.phoneNumber)
    },
    disableFetchCaptcha() {
      return this.captchaFetching
    },
    phoneRules() {
      return [{
        validator(val) {
          return /^1[3456789]\d{9}$/.test(val)
        },
        message: '请填写正确的手机号'
      }]
    },
    buttonText() {
      return this.remainSecond > 0 ? `${this.remainSecond}秒后获取` : '获取验证码'
    }
  }
}
</script>
<style lang="less" scoped>
.container {
  height: 100%;
  &.desktop {
    width: 1000px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .panel {
      margin: 64px 0;
    }
  }


  .images {
    width: 100%;
    height: 400px;
    background-image: url("~@/assets/img/login/car-bg.png");
    position: relative;

    .car-bg {
      position: absolute;
      bottom: -20px;
      right: 0;
      width: 70%;
    }

    .alarm-bg {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 50px;
      width: 6%;
    }

    .profile-bg {
      position: absolute;
      top: 50px;
      left: 23%;
      width: 11%;
    }

    .location-bg {
      position: absolute;
      bottom: 50px;
      left: 19%;
      width: 9%;
    }

  }

  .panel {
    margin: 64px 48px;

    header {
      margin-bottom: 48px;
      font-size: 40px;
      color: #191919;
      font-weight: bold;
    }

    .captcha-field {
      margin-top: 48px;
      position: relative;

      .captcha-button {
        position: absolute;
        width: 300px;
        right: 10px;
        top: 10px;
        bottom: 10px;
        height: 80% !important;
      }

    }

    .field {
      background: #F4F7FD;
      border-radius: 8px;

      &::placeholder {
        color: rgba(25, 25, 25, 0.30);
        font-size: 28px;
        letter-spacing: 2px;
      }
    }

    .submit-button {
      margin-top: 64px;
    }
  }


  .content {
    padding: 30px 40px 25px 40px;

    header {
      display: flex;
      align-items: center;
      margin-bottom: 40px;

      .title {
        margin-left: 10px;
        color: #191919;
        font-size: 32px;
        font-weight: bold;
      }
    }

    /deep/ .fm-field {
      border-radius: 8px;
      border: 1px solid rgba(25, 25, 25, 0.07);
    }

    .label {
      font-size: 28px;
      color: rgba(25, 25, 25, 0.6);
      letter-spacing: 2px;
      margin-bottom: 24px;
      display: inline-block;
    }

    .phone {
      margin-bottom: 40px;
    }

    .captcha {
      margin-bottom: 40px;
    }

    .buttons {
      display: grid;
      grid-template-columns: 6fr 4fr;
      grid-column-gap: 20px;
    }
  }
}
</style>
