.container {
  height: 100%;


  .images {
    width: 100%;
    height: 400px;
    background-image: url("~@/assets/img/login/car-bg.png");
    position: relative;

    .car-bg {
      position: absolute;
      bottom: -20px;
      right: 0;
      width: 70%;
    }

    .alarm-bg {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 50px;
      width: 6%;
    }

    .profile-bg {
      position: absolute;
      top: 50px;
      left: 23%;
      width: 11%;
    }

    .location-bg {
      position: absolute;
      bottom: 50px;
      left: 19%;
      width: 9%;
    }

  }

  .panel {
    margin: 64px 48px;

    header {
      margin-bottom: 48px;
      font-size: 40px;
      color: #191919;
      font-weight: bold;
    }

    .captcha-field {
      margin-top: 48px;
      position: relative;

      .captcha-button {
        position: absolute;
        width: 300px;
        right: 10px;
        top: 10px;
        bottom: 10px;
        height: 80% !important;
      }

    }

    .field {
      background: #F4F7FD;
      border-radius: 8px;

      &::placeholder {
        color: rgba(25, 25, 25, 0.30);
        font-size: 28px;
        letter-spacing: 2px;
      }
    }

    .submit-button {
      margin-top: 64px;
    }
  }


  .content {
    padding: 30px 40px 25px 40px;

    header {
      display: flex;
      align-items: center;
      margin-bottom: 40px;

      .title {
        margin-left: 10px;
        color: #191919;
        font-size: 32px;
        font-weight: bold;
      }
    }

    /deep/ .fm-field {
      border-radius: 8px;
      border: 1px solid rgba(25, 25, 25, 0.07);
    }

    .label {
      font-size: 28px;
      color: rgba(25, 25, 25, 0.6);
      letter-spacing: 2px;
      margin-bottom: 24px;
      display: inline-block;
    }

    .phone {
      margin-bottom: 40px;
    }

    .captcha {
      margin-bottom: 40px;
    }

    .buttons {
      display: grid;
      grid-template-columns: 6fr 4fr;
      grid-column-gap: 20px;
    }
  }
}
