<template>
  <div v-if="isMobile" class="mobileContainer" v-loading="!showForm">
    <Track
      v-if="startPoint.x && endPoint.x"
      id="detailMobile"
      :end-point="endPoint"
      :show-tags="false"
      :start-point="startPoint"
      class="track"
      show-back
      @detail="handleDetail"
      @complete="showForm = true"
    />
    <div
      id="info-panel"
      :style="infoStyle"
      v-if="showForm"
    >
      <div class="indicator">
        <div class="indicator-line"></div>
      </div>
      <div class="summary-info">
        <div class="fee-info" v-if="feeInfo">
          <span style="vertical-align: bottom">合计：<span class="fee">{{
              feeInfo[0].cost6 || 0
            }}</span>元</span>
          <div class="time">
            <img :src="require('@/assets/img/detail/shalou.png')"/>
            <div>耗时：<span class="timing">{{ timing || '暂无' }}</span></div>
          </div>
        </div>
        <div v-if="vdDriverInfo" class="driver-info">
          <div class="driver">
            <img :src="require('@/assets/img/detail/driver.png')" alt="司机头像"/>
            <div class="driver-name">
              <div class="name-and-number">
                <span class="name">{{ vdDriverInfo.driverFullName }}</span>
                <div class="number">{{ vdCarInfo.carNum }}</div>
              </div>
              <div class="tag">
                {{ carInfos }}
              </div>
            </div>
          </div>
          <a
            v-if="vdDriverInfo"
            :href="`tel:${vdDriverInfo.driverPhone}`"
            @click.stop="">
            <div class="calling">
              <img :src="require('@/assets/img/detail/dianhua.png')"/>
              <span>拨打电话</span>
            </div>
          </a>
        </div>
        <div class="fees">
          <div class="fee-item isBold">
            <div class="label">行程开始时间</div>
            <div class="value">{{ startTripTime }}</div>
          </div>
          <div class="fee-item isBold">
            <div class="label">行程结束时间</div>
            <div class="value">{{ endTripTime }}</div>
          </div>
        </div>
        <div class="fees">
          <div class="fee-item">
            <div class="label">乘车人数</div>
            <div class="value">{{ item.useCarPersonNum }}人</div>
          </div>
          <div v-if="passengerInfo && passengerInfo.length"
               class="fee-item contact-card">
            <fm-cell v-for="(person, index) in passengerInfo" :key="index"
                     class="person-view flex col-center">
              <template #title>
                <div class="font-28 color-black font-400">{{ person.ucPersonFullName }}</div>
                <div class="color-blue font-28 font-400">{{ person.ucPersonPhone }}</div>
              </template>
              <template #right-icon>
                <a :href="`tel:${person.ucPersonPhone}`" class="flex col-center d-block"
                   @click.stop="">
                  <img class="call-img" src='@/assets/img/car/call.png'>
                </a>
              </template>
            </fm-cell>
          </div>
        </div>
        <template v-if="feeInfo">
          <fm-collapse v-for="(it, i) in feeInfo"
                       :key="i + 'vdAfUcCompleteFormList'" v-model="activeNames">
            <fm-collapse-item :name="`${i + 1}`">
              <template #title>
                <div class="font-bold">行车日志</div>
              </template>
              <div class="fees">
                <div
                  v-for="(listItem, index) in list.filter(item => item.prop !== 'startTime2' && item.prop !== 'endTime2')"
                  :key="index"
                  :class="{isBold: index === 0}"
                  class="fee-item"
                >
                  <span class="label">{{ listItem.label }}</span>
                  <span class="value">{{ it[listItem['prop']] || 0 }}{{
                      listItem.unit || ''
                    }}</span>
                </div>
                <!--                <div v-if="it.remarks" class="full-width">-->
                <!--                  <div class="font-bold">备注:</div>-->
                <!--                  <div class="text-break line-height-30">{{ it.remarks }}</div>-->
                <!--                </div>-->
              </div>
            </fm-collapse-item>
          </fm-collapse>
        </template>
        <fks-divider class="noMarginBottom"/>
      </div>
    </div>
  </div>
  <div v-else-if="isPC" class="detail-container" style="height: 100%" v-loading="!showForm">
    <Track
      v-if="startPoint.x && endPoint.x"
      id="detail"
      :end-point="endPoint"
      :show-tags="false"
      :start-point="startPoint"
      height="100%"
      show-back
      @detail="handleDetail"
      @complete="showForm = true"
    />
    <div v-if="showForm" style="overflow-y: auto;padding: 20px;width: 500px">
      <common-title title="行程信息"/>
      <fks-divider/>
      <fks-form>
        <fks-form-item label="行程开始时间">
          <fks-input :value="startTripTime" disabled/>
        </fks-form-item>
        <fks-form-item label="行程结束时间">
          <fks-input :value="endTripTime" disabled/>
        </fks-form-item>
        <fks-form-item label="出发地">
          <fks-input
            :value="item.startAddress === '其他' ? item.startAddressDetail : item.startAddress"
            autosize
            disabled
            type="textarea"
          />
        </fks-form-item>
        <fks-form-item label="目的地">
          <fks-input
            :value="`${item.endAddress}`"
            autosize
            disabled
            type="textarea"
          />
        </fks-form-item>
        <fks-form-item label="耗时">
          <fks-input :value="timing" disabled/>
        </fks-form-item>
      </fks-form>
      <div v-if="passengerInfo">
        <common-title title="乘车人信息"/>
        <fks-collapse v-model="activePersons" style="margin: 20px 0 20px 0">
          <fks-collapse-item name="1">
            <template slot="title">
              <div>人数：<span class="fee">{{ item?.useCarPersonNum }}</span></div>
            </template>
            <div class="fees">
              <div
                v-for="(listItem, index) in passengerInfo"
                :key="index"
                class="fee-item"
              >
                <span>{{ listItem.ucPersonFullName }}</span>
                <span>{{ listItem.ucPersonPhone }}</span>
              </div>
            </div>
          </fks-collapse-item>
        </fks-collapse>
      </div>
      <div v-else style="margin-bottom: 30px">
        <common-title title="乘车人信息"/>
        <fks-divider/>
        <fks-form>
          <fks-form-item label="乘车人数">
            <fks-input :value="item.useCarPersonNum" disabled/>
          </fks-form-item>
        </fks-form>
      </div>
      <div v-if="vdDriverInfo">
        <common-title title="驾驶员信息"/>
        <fks-divider/>
        <fks-form>
          <fks-form-item label="姓名">
            <fks-input :value="vdDriverInfo.driverFullName" disabled/>
          </fks-form-item>
          <fks-form-item label="联系电话">
            <fks-input :value="vdDriverInfo.driverPhone" disabled/>
          </fks-form-item>
          <fks-form-item label="驾龄（年）">
            <fks-input :value="vdDriverInfo?.driverAge" disabled/>
          </fks-form-item>
        </fks-form>
      </div>
      <div v-if="vdCarInfo">
        <common-title title="车辆信息"/>
        <fks-divider/>
        <fks-form>
          <fks-form-item label="车型">
            <fks-input :value="vdCarInfo?.carType" disabled/>
          </fks-form-item>
          <fks-form-item label="车牌">
            <fks-input :value="vdCarInfo?.carNum" disabled/>
          </fks-form-item>
          <fks-form-item label="车龄（年）">
            <fks-input :value="vdCarInfo?.carAge" disabled/>
          </fks-form-item>
        </fks-form>
      </div>

      <div v-if="feeInfo">
        <template v-for="(it, i ) in feeInfo">
          <common-title
            is-slot
            style="margin: 20px 0"
            title="行车日志"
          >
            <div slot="titleOther" class="font-32">费用总计：<span class="fee">{{ it.cost6 }}</span>元
            </div>
          </common-title>
          <fks-collapse :key="it.id" v-model="activeNames">
            <fks-collapse-item :name="`${i + 1}`">
              <div class="fees">
                <div
                  v-for="(listItem, index) in list"
                  :key="index"
                  class="fee-item"
                >
                  <span>{{ listItem.label }}</span>
                  <span>{{ it[listItem['prop']] }}{{ listItem.unit || '' }}</span>
                </div>
              </div>
            </fks-collapse-item>
          </fks-collapse>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import platform from "@/mixins/platform";
import Menus from "@components/menus/index.vue";
import Track from "@components/amap/components/track.vue";
import CommonTitle from "@components/CommonTitle/index.vue";
import {getUseCarInfo} from "@modules/CarApply/components/api";
import {feeConfigs} from "@utils/constants";
import dayjs from 'dayjs'
import {Tag} from "fawkes-mobile-lib";
import {mapState} from 'vuex'
import {getCarInfo, getDriverInfo, getFeeInfo, getPassengerList} from "@/api/carApply";

export default {
  name: 'TripDetail',
  mixins: [platform],
  components: {Menus, Track, CommonTitle, [Tag.name]: Tag},
  data() {
    return {
      showForm: false,
      stars: [1, 2, 3, 4, 5],
      list: feeConfigs,
      activeNames: ['1'],
      activePersons: ['1'],
      startPoint: {
        name: '',
        x: '',
        y: ''
      },
      endPoint: {
        name: '',
        x: '',
        y: ''
      },
      item: {},
      collapse: false,
      startTime: '',
      startDistanceX: '',
      startDistanceY: '',
      endTime: '',
      endDistanceX: '',
      endDistanceY: '',
      moveTime: '',
      moveDistanceX: '',
      moveDistanceY: '',
      journeyCostTotal: 0,
      vdCarInfo: null,
      vdDriverInfo: null,
      passengerInfo: null,
      feeInfo: null
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    carInfos() {
      if (this.vdCarInfo) {
        const a = this.vdCarInfo.carType;
        const b = this.vdCarInfo.carNum;
        return [a, b].filter(Boolean).join(' | ')
      }
    },
    infoStyle() {
      return {
        height: this.collapse ? '180px' : '500px',
        overflow: this.collapse ? 'hidden' : 'auto'
      }
    },
    startTripTime() {
      return this.item?.startTime2 || ''
    },
    endTripTime() {
      return this.item?.endTime2 || ''
    },
    timing() {
      if (this.endTripTime && this.startTripTime) {
        const startTime = dayjs(this.startTripTime)
        const endTime = dayjs(this.endTripTime)
        const minute = endTime.diff(startTime, 'minute')
        const hour = endTime.diff(startTime, 'hour')
        return minute >= 60 ? hour + '小时' : minute + '分钟';
      }
      return ''
    }
  },
  watch: {
    collapse(newVal) {
      if (newVal && this.isMobile) {
        setTimeout(() => {
          const el = document.getElementById('info-panel')
          el.scrollTo({top: 0})
        })
      }
    },
    showForm(newVal) {
      if (newVal) {
        if (this.isMobile) {
          this.$nextTick(() => {
            this.enableScroll();
          })
        }
      }
    }
  },
  async mounted() {
    const {id} = this.$route.query
    const res = await getUseCarInfo(id);
    this.item = res.data;
    // 获取乘车人列表
    const passengerRes = await getPassengerList(id)
    this.passengerInfo = passengerRes.data ? passengerRes.data[id] : null;
    // 获取车辆，司机列表；在车辆调度员审批后调用
    if (res.data && res.data.carId && res.data.driverId) {
      const carRes = await getCarInfo(res.data.carId)
      const driverRes = await getDriverInfo(res.data.driverId)
      this.vdCarInfo = carRes.data[res.data.carId];
      this.vdDriverInfo = driverRes.data[res.data.driverId];
    }
    // 获取费用填报表单信息
    const feeRes = await getFeeInfo(id)
    if (feeRes.data) {
      feeRes.data[id][0].startTime2 = this.$dayjs(this.item.startTime2).format('YYYY-MM-DD HH:mm');
      feeRes.data[id][0].endTime2 = this.$dayjs(this.item.endTime2).format('YYYY-MM-DD HH:mm');
    }
    this.feeInfo = feeRes.data ? feeRes.data[id] : null;
    this.startPoint = {
      name: this.item.startAddress,
      x: Number(this.item.startAddressSmx),
      y: Number(this.item.startAddressSmy)
    }
    this.endPoint = {
      name: this.item.endAddress,
      x: Number(this.item.endAddressSmx),
      y: Number(this.item.endAddressSmy)
    }
  },
  methods: {
    enableScroll() {
      const el = document.querySelector('.indicator');
      if (el) {
        el.addEventListener('touchstart', (e) => {
          this.startTime = new Date().getTime();
          this.startDistanceX = e.touches[0].screenX;
          this.startDistanceY = e.touches[0].screenY;
        })
        el.addEventListener('touchend', (e) => {
          this.endTime = new Date().getTime();
          this.endDistanceX = e.changedTouches[0].screenX;
          this.endDistanceY = e.changedTouches[0].screenY;
          this.moveTime = this.endTime - this.startTime;
          this.moveDistanceX = this.startDistanceX - this.endDistanceX;
          this.moveDistanceY = this.startDistanceY - this.endDistanceY;
          if (Math.abs(this.moveDistanceY) > 40 && this.moveTime < 500) {
            if (this.moveDistanceY > 0 && this.collapse === true) {
              this.collapse = false
            }
            if (this.moveDistanceY < 0 && this.collapse === false) {
              this.collapse = true
            }
          }
        })
      }
    },
    handleDetail() {
      const params = this.$route.query;
      this.$router.push({
        name: 'Application',
        params: {
          ...params,
          type: 'view'
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.detail-container {
  display: flex;
  justify-content: space-between;
  height: 100%;

  .amount {
    font-size: 56px;
    font-weight: bold;
  }

  .fee {
    font-size: 28px;
    font-weight: bold;
    margin-right: 10px;
  }

}

.contact-card {
  display: flex;
  flex-direction: column;

  .person-view {
    border-radius: 4px;
  }
}

.mobileContainer {
  display: flex;
  flex-direction: column;

  #info-panel {
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;

    .indicator {
      position: sticky;
      top: 0;
      left: 0;
      right: 0;
      height: 80px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 99;
      background: white;

      .indicator-line {
        width: 96px;
        height: 12px;
        border-radius: 4px;
        background: #D8D8D8;
        color: #D8D8D8;
        margin: 6px auto;
      }
    }

    .summary-info {
      position: absolute;
      top: 80px;
      left: 20px;
      right: 20px;

      .fee-info {
        display: flex;
        justify-content: space-between;
        align-items: end;
        font-size: 32px;
        margin-bottom: 20px;

        .fee {
          font-size: 56px;
          font-weight: bold;
          vertical-align: bottom;
          line-height: 56px;
        }

        .time {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 28px;

          img {
            width: 36px;
            margin-right: 10px;
          }

          .timing {
            font-size: 40px;
            font-weight: bold;
            letter-spacing: 4px;
          }
        }
      }


    }

    .driver-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 40px 0;
      background: #FFFFFF;
      box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
      border-radius: 8px;
      padding: 24px;

      .driver {
        display: flex;

        img {
          width: 96px;
        }

        .driver-name {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-left: 5px;

          .name-and-number {
            color: #333;
            display: flex;
            align-items: end;

            .name {
              font-size: 28px;
              font-weight: bold;

              display: inline-block;
              margin-right: 18px;
            }

            .number {
              font-size: 24px;
            }
          }

          .tag {
            background: #D1E4FF;
            border-radius: 20px;
            border: 1px solid #008BFF;
            color: #008BFF;
            font-size: 24px;
            align-self: start;
            padding: 4px 20px;
          }
        }
      }

      .calling {
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          width: 24px;
        }

        span {
          display: inline-block;
          margin-top: 10px;
          color: #333;
          font-size: 24px;
        }
      }
    }
  }

  .track {
    flex: 1;
  }
}

/deep/ .fm-collapse-item__content {
  color: #333333 !important;
}

.fees {
  background: #F4F4F4;
  padding: 10px 40px;
  border-radius: 8px;
  margin-bottom: 40px;

  .fee-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;

    &.isBold {
      font-weight: bold;
    }

    .label {
      font-size: 24px;
    }

    .value {
      font-size: 28px;
    }

    .call-img {
      width: 94px;
      height: 96px;
    }
  }
}

/deep/ .fks-divider .noMarginBottom {
  margin-bottom: 0 !important;
}

/deep/ .fks-input.is-disabled .fks-input__inner {
  background: #F4F4F4;
  color: #333 !important;
}

/deep/ .fks-textarea.is-disabled .fks-textarea__inner {
  background: #F4F4F4;
  color: #333 !important;
}
</style>
