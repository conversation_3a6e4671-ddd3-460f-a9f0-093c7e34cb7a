@import "../../FormCenter/components/FlowButton/index.less";
/deep/.fm-icon-plus {
  font-size: 32px;
}
.main {
  background-color: #f2f3f4 !important;
  height: 100%;
  &.bg-white {
    background-color: #FFFFFF !important;
  }
}
.filter-img {
  width: 32px;
  height: 32px;
  margin-right: 32px;
}
.search-apply>form {
  width: 100%;
  /*width: calc(100% - 32px);*/
}
/deep/ .fm-overlay {
  top: 104px;
}
.apply-container {
  //height: calc(100% - 208px - 85px - 52px);
  height: calc(100% - 196px);
  overflow: auto;
}
.task-container /deep/ .fm-nav-bar__content {
  height: 88px;
}
.task-container /deep/ .fm-nav-bar__title {
  font-size: 36px;
  font-weight: 600;
}
.task-container /deep/ .fm-nav-bar .fm-icon {
  color: #333;
}
.task-container /deep/ .fm-nav-bar__arrow {
  font-size: 48px;
}
.fm-sidebar {
  width: 100%;
  padding-left: 30px;
  padding-right: 30px;
  border-bottom: 2px solid #ebedf0;
}
.fm-cell::after {
  border: none;
}
.fm-cell-group {
  margin-top: 24px;
}
/deep/ .fm-swipe-cell__right {
  display: flex;
  align-items: center;
}
/deep/ .fm-button--square {
  border-radius: 50%;
  width: 106px;
  height: 106px;
  padding: 0;
  margin-right: 20px;
}
.bottom {
  position: fixed;
  bottom: 24px;
  right: 44px;
  background-color: #4545d1;
  width: 102px;
  height: 102px;
  line-height: 102px;
  border-radius: 50%;
  color: #fff;
  z-index: 1000;
  text-align: center;
}

/deep/ .fm-search {
  //padding: 0 32px;
  //height: 100px;
}

/deep/ .fm-search__content {
  //height: 74px;
  border-radius: 16px !important;
}

.status {
  width: max-content;
  height: 48px;
  padding: 8px 22px 6px 16px;
  background: linear-gradient(225deg, #DDDDDD 0%, #979DA0 100%);
  border-radius: 14px 0 14px 0;
  font-size: 24px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 52px;
  letter-spacing: 1px;
}

.status.status-active {
  background: linear-gradient(225deg, #6FCFFF 0%, #3C83FF 100%);
}
.status.status-start {
  background: linear-gradient(225deg, #FCE15E 0%, #FF7F3C 100%);
}
.status.status-danger {
  background: linear-gradient(225deg, #ecc7ca 0%, #FF3F4C 100%);
}

/deep/ .fm-cell-group {
  border-radius: 16px;
  border: 2px solid #FFFFFF;
}

/deep/ .fm-cell-group.start  {
  background: linear-gradient(180deg, #FFF0E9 0%, #FFFDFD 51%, #FFFFFF 100%);
}
//background: linear-gradient(225deg, #ecc7ca 0%, #FF3F4C 100%);
/deep/ .fm-cell-group.driving  {
  background: linear-gradient(180deg, #F2F7FF 0%, #FFFFFF 100%);
}
/deep/ .fm-cell-group.danger  {
  background: linear-gradient(180deg, #f6eeee 0%, #FFFFFF 100%);
}
/deep/ .fm-cell-group.end  {
  background: linear-gradient(180deg, #ECECEF 0%, #FFFFFF 100%);
}

.address {
  font-size: 28px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #191919;
  line-height: 40px;
  letter-spacing: 1px;
}

.address-line {
  height: 68px;
  border: 2px solid;
}

.address-line.address-line-driving {
  border-image: linear-gradient(180deg, rgba(60, 131, 255, 1), rgba(60, 131, 255, 0.1)) 2 2;
}

.address-line.address-line-end {
  border-image: linear-gradient(180deg, rgba(158, 162, 165, 1), rgba(158, 162, 165, 0.1)) 2 2;
}

.address-line-left {
  width: 48px;
  height: 4px;
  background: linear-gradient(270deg, #86B2FF 0%, rgba(134,178,255,0) 100%);
}

.address-line-right {
  width: 48px;
  height: 4px;
  background: linear-gradient(270deg, rgba(134,178,255,0) 0%, #86B2FF 100%);
}

.round-trip-img {
  width: 44px;
  height: 44px;
}

.address-text {
  width: 32px;
  height: 32px;
}

.address-city {
  height: 40px;
  width: max-content;
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(25,25,25,0.6);
  line-height: 40px;
  letter-spacing: 1px;
}

.car-cell {
  padding: 32px 32px 0 32px;
}

.car-line {
  height: 2px;
  background: #3C83FF;
  opacity: 0.2;
}

.time {
  min-height: 40px;
  min-width: 160px;
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(25,25,25,0.6);
  line-height: 40px;
  letter-spacing: 1px;
}

.time.time-info {
  width: calc(100% - 183px);
  color: #191919;
  text-align: right;

  a {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
  }
}

.car-matter {
  min-height: 128px;
  height: auto;
  background: #F4F7FD;
  border-radius: 8px;
  padding: 24px;
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #191919;
  line-height: 40px;
  letter-spacing: 1px;
}

.foot-filter {
  top: 0;
  bottom: auto;
  transform: translate(0, 50vh);
  transition: all .1s linear;
}

.obt-btn {
  padding: 22px 0;
  border-radius: 8px;
  font-size: 32px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 44px;
  letter-spacing: 1px;
}

.obt-btn.other-btn {
  min-width: 80px;
  padding: 10px;
  border: 2px solid #3C83FF;
  border-radius: 32px;
  font-size: 24px;
  line-height: 24px;
}
.obt-btn.time-tag {
  width: calc(100% / 3 - 22px);
  background: #FFFFFF;
  border: 2px solid #3C83FF;
  border-radius: 50px;
  padding: 22px 0;
  font-size: 28px;
  line-height: 28px;
  color: #3C83FF;
}

.obt-btn.time-tag.time-input {
  width: calc(100% / 2 - 44px - 30px);
}

.obt-btn.time-tag.no-time {
  color: #e3e3e3;
}

.obt-btn.time-tag.primary {
  color: #FFFFFF;
}

.obt-btn.primary {
  background: #3C83FF;
}

.time-line {
  width: 50px;
  height: 2px;
  background: #3C83FF;
}

.obt-btn.primary-pain {
  border: 2px solid #3C83FF;
  color:  #3C83FF;
}

.obt-btn.danger {
  background: #FB5354;
}
.detail-text,
.evaluate-text {
  width: 120px;
  height: 40px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 40px;
}
.evaluate-text {
  font-size: 24px;
  color:#191919 ;
}
.detail-text {
  font-size: 28px;
  color: #3C83FF;
}
.icon-right {
  width: 32px;
  height: 32px;
}

.call-box {
  display: inline-block;
  background: #3C83FF;
  border-radius: 16px;
  padding: 0px 40px 4px;
}

.call-img {
  width: 32px;
  height: 32px;
  vertical-align: middle;
}

.driver-btn {
  padding: 22px 48px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 2px solid #3C83FF;
  font-size: 32px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #3C83FF;
  line-height: 44px;
  letter-spacing: 1px;
}

.driver-btn.driver-task {
  padding: 22px 24px;
}
.driver-btn.driver-navigation {
  padding: 22px 48px;
}

.opt-btn {
  width: calc(100% - 32px - 96px - 140px);
}

.obt-btn.obt-btn-task {
  padding: 22px 24px;
}

/deep/ .more-btn.fks-button.is-text,
/deep/ .evaluate-btn.fks-button.is-text {
  padding-left: 16px;
}
.more-btn:hover {
  background: linear-gradient(225deg, #ecc7ca 0%, #FF3F4C 100%);
  color: #FFFFFF !important;
}

.evaluate-btn:hover {
  background: linear-gradient(225deg, #6FCFFF 0%, #3C83FF 100%);
  color: #FFFFFF !important;
}
