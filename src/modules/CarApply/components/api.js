import request from '@/utils/request'

// 获取流程表单列表
export function getCommonFlowList (params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/page',
    method: 'get',
    params,
  })
}

export function getApprovalList (params) {
  return request({
    url: 'sys-bpm/process/history',
    method: 'get',
    params,
  })
}

// 用车完成评价
export function updateEvaluate(data) {
  return request({
    url: '/vehicle-dispatch/vd/af/uc/complete/evaluate',
    method: 'post',
    data,
  })
}

// 获取用车完成评价详情
export function getEvaluateDetail(vdApplyFormId) {
  return request({
    url: '/vehicle-dispatch/vd/af/uc/complete/evaluate/detail',
    method: 'get',
    params: { vdApplyFormId }
  })
}

// 司机出行任务
export function getDriverTask(params) {
  return request({
    url: '/vehicle-dispatch/vd/af/driver/task',
    method: 'get',
    params
  })
}

// 获取详情
export function getUseCarInfo(id){
  return request({
    url: '/vehicle-dispatch/vd/flow/detail',
    params: {
      entityName: 'vehicleDispatch',
      id: id
    }
  })
}

// 获取用车记录参数
export function getUseCarParam(params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/useCar/page/getParam',
    params
  })
}

// post形式获取用车记录
export function getUseCarListByPost(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/useCar/page',
    method: 'post',
    data,
    params
  })
}

// 导出用车记录
export function exportUseCarRecord(params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/useCar/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

export function exportFlowData(params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/export/v2',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}



// 行车日志查询参数
export function getFlowSearchParam(params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/page/getParam',
    params
  })
}

// 获取流程表单列表
export function getCommonFlowListByPost (data, params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/page',
    method: 'post',
    data,
    params
  })
}
