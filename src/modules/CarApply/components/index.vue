<template>
  <div v-if="isMobile" id="carApply" class="main" :class="{'bg-white': formList.length === 0}">
    <div class="flex row-between col-center full-width bg-white search-apply">
      <fm-search
        v-model="form.searchValue"
        clearable
        maxlength="50"
        placeholder="请输入位置名称、位置详细地址"
        @clear="onClear"
        @search="onSearch"
      >
        <template #left-icon>
          <i class="fm-icon fm-icon-search" @click="onSearch"></i>
        </template>
      </fm-search>
      <img src="@/assets/img/car/filter.png" class="filter-img" @click="showFilter = true;">
    </div>
    <fm-popup class="filter-popup" :visible.sync="showFilter" position="top" :style="{ height: 'calc(50vh + 20px)','max-height': 'calc(50vh + 20px)', top: '52px', 'overflow-y': 'auto' }" get-container="#carApply" :append-to-body="false">
      <div class="font-32 font-bold m-32">按申请时间选择</div>
      <div class="flex flex-wrap col-center row-between m-32">
        <div
          v-for="item in timeList"
          :key="item.value"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': timeApplyChoose.includes(item.value)}"
          @click="timeApplyChoose = [item.value]; form.startApplyTime = ''; form.endApplyTime = '';"
        >
          {{ item.text }}
        </div>

        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.startApplyTime, 'no-time': !form.startApplyTime }"
          @click="showStartApplyTime = true"
        >
          {{ form.startApplyTime ? form.startApplyTime : '开始时间' }}
        </div>
        <div class="time-line m-b-32"></div>
        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.endApplyTime, 'no-time': !form.endApplyTime }"
          @click="showEndApplyTime = true"
        >
          {{  form.endApplyTime ? form.endApplyTime : '结束时间' }}
        </div>
        <date-time-picker
          :show.sync="showStartApplyTime"
          :time.sync="form.startApplyTime"
          title="开始时间"
          type="date"
          @confirm="timeApplyChoose = [];"
        ></date-time-picker>
        <date-time-picker
          :show.sync="showEndApplyTime"
          :time.sync="form.endApplyTime"
          title="结束时间"
          type="date"
          @confirm="timeApplyChoose = [];"
        ></date-time-picker>
      </div>
      <div class="font-32 font-bold m-32">按出发时间选择</div>
      <div class="flex flex-wrap col-center row-between m-32">
        <div
          v-for="item in timeList"
          :key="item.value"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': timeChoose.includes(item.value)}"
          @click="timeChoose = [item.value]; form.startTime = ''; form.endTime = '';"
        >
          {{ item.text }}
        </div>

        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.startTime, 'no-time': !form.startTime }"
          @click="showStartTime = true"
        >
          {{ form.startTime ? form.startTime : '开始时间' }}
        </div>
        <div class="time-line m-b-32"></div>
        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.endTime, 'no-time': !form.endTime }"
          @click="showEndTime = true"
        >
          {{  form.endTime ? form.endTime : '结束时间' }}
        </div>
        <date-time-picker
          :show.sync="showStartTime"
          :time.sync="form.startTime"
          title="开始时间"
          type="date"
          @confirm="timeChoose = [];"
        ></date-time-picker>
        <date-time-picker
          :show.sync="showEndTime"
          :time.sync="form.endTime"
          title="结束时间"
          type="date"
          @confirm="timeChoose = [];"
        ></date-time-picker>
      </div>
      <div class="font-32 font-bold m-32">按状态选择</div>
      <div class="flex flex-wrap col-center m-32">
        <div
          v-for="item in applyFormState"
          :key="item.code"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': statusChoose.includes(item.key)}"
          @click="statusChoose = [item.key]"
        >
          {{ item.value }}
        </div>
      </div>
      <div class="font-32 font-bold m-32">按用车类型选择</div>
      <div class="flex flex-wrap col-center m-32 p-b-100">
        <div
          v-for="item in useCarType"
          :key="item.code"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': useCarTypeChoose.includes(item.key)}"
          @click="useCarTypeChoose = [item.key]"
        >
          {{ item.value }}
        </div>
      </div>
      <div class="foot foot-filter">
        <fm-button
          class="flow-btn btn-50 m-r-30"
          type="primary"
          plain
          :disabled="loading"
          :loading="loading"
          @click="clearFilter"
        >重置</fm-button>
        <fm-button
          class="flow-btn btn-50"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="onSearchFilter"
        >确定</fm-button>
      </div>
    </fm-popup>
    <empty-data v-if="formList.length === 0"></empty-data>
    <div v-else class="apply-container">
      <!-- 下拉刷新 -->
      <fm-pull-refresh
        v-model="isRefresh"
        refresh-layer-color="#4b8bf4"
        success-text="刷新成功"
        @refresh="handleRefresh"
      >
        <!-- 上拉加载 -->
        <fm-list
          v-model="loading"
          :finished="finished"
          :immediate-check="false"
          finished-text="已经到底啦~"
          @load="onLoad"
        >
          <!-- 滑动 -->
          <fm-swipe-cell v-for="(item, i) in formList" :key="i">
            <!-- 卡片 -->
            <fm-cell-group :class="item.applyFormUserState === 9 ? 'end' : item.applyFormUserState === 10 ? 'start' : item.applyFormState === 4 && item.processState === '0' ? 'danger' : ['driving', 'end', 'end', 'driving'][+item.processState]" inset
                           @click="handleView(item,  showTripDetail(item.applyFormUserState) ? 3 : 1)">
              <div :class="item.applyFormUserState === 9 ?  '' : item.applyFormUserState === 10 ? 'status-start' : item.applyFormState === 4 && item.processState === '0' ? 'status-danger' : ['status-active', '', '', 'status-active'][+item.processState]"
                   class="status flex col-center row-center">
                <template v-if="item.applyFormUserState !== null">
                  <span>{{
                      item.applyFormUserState | transferEnums('ApplyFormUserStateEnums')
                    }}</span>
                  <span class="d-inline-block m-l-10 m-r-10">|</span>
                </template>
                <span class="d-inline-block">{{
                    item.useCarType | transferEnums('UseCarTypeEnums')
                  }}</span>
              </div>
              <div class="car-cell p-r-32">
                <div class="flex col-center row-between full-width">
                  <div class="flex col-center">
                    <img class="address-text m-r-10" src="@/assets/img/car/icon_start.png">
                    <span class="address-city">
                      {{ getStartCity(item) }}
                    </span>
                  </div>
                  <div class="address text-right m-l-40">
                    {{ getStartAddress(item) }}
                    <!--                    {{ item.startAddress === '其他' || item.startAddress === '9'  ? item.startAddressDetail.includes('市') -->
                    <!--                    ? item.startAddressDetail.substring(item.endAddress.lastIndexOf('市') + 1) : item.startAddressDetail : item.startAddress }}-->
                  </div>
                </div>
                <div class="flex col-center">
                  <div :class="['address-line-driving', 'address-line-end', 'address-line-end', 'address-line-driving'][+item.processState]"
                       class="address-line m-l-16"></div>
                  <img v-if="['0', '3'].includes(item.processState)"
                       class="round-trip-img m-l-10 m-r-10"
                       src="@/assets/img/car/icon_round_trip_active.png">
                  <img v-else class="round-trip-img m-l-10 m-r-10"
                       src="@/assets/img/car/icon_round_trip.png">
                </div>
                <div class="flex col-center row-between full-width">
                  <div class="flex col-center">
                    <img class="address-text m-r-10" src="@/assets/img/car/icon_end.png">
                    <span class="address-city d-inline-block">
                      {{ getEndCity(item) }}
                    </span>
                  </div>
                  <div class="address text-right m-l-40">
                    {{ getEndAddress(item) }}
                  </div>
                </div>
              </div>
              <div class="car-line m-l-32 m-r-32 m-t-32"></div>
              <div class="flex row-between car-cell">
                <div class="flex">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_driver.png">
                  <span class="time">申请人：</span>
                </div>
                <div class="time text-break text-right time-info">{{item.applyFullName }}</div>
              </div>
              <div class="flex col-center row-between car-cell">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_corp.png">
                  <span class="time">所属部门：</span>
                </div>
                <div class="time time-info text-right">{{item.applyDepartment }}</div>
              </div>
              <div class="flex col-center row-between car-cell">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_time.png">
                  <span class="time">申请时间：</span>
                </div>
                <div class="time time-info text-right">{{
                    item.applyUserTime ? $dayjs(item.applyUserTime).format('YYYY-MM-DD HH:mm') : ''
                  }}
                </div>
              </div>
              <div class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_time.png">
                  <span class="time">出发时间：</span>
                </div>
                <div class="time time-info text-right">
                  {{ item.startTime ? $dayjs(item.startTime).format('YYYY-MM-DD HH:mm') : '' }}
                </div>
              </div>
              <div v-if="item.vdCarInfo && item.vdCarInfo.carNum" class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_car_info.png">
                  <span class="time">车辆信息：</span>
                </div>
                <div class="time time-info">
                  <span>{{ item.vdCarInfo.carType }}</span>
                  <span class="m-l-16">{{ item.vdCarInfo.carNum }}</span>
                </div>
              </div>
              <div v-if="item.vdDriverInfo && item.vdDriverInfo.driverPhone" class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_driver.png">
                  <span class="time">司机信息：</span>
                </div>
                <div class="time time-info">
                  <a @click.stop="" :href="`tel:${item.vdDriverInfo.driverPhone}`">
                    <img class="m-l-16" width="14px" height="14px" :src="require('@/assets/img/detail/dianhua.png')"/>
                    <span class="m-l-16">{{ item.vdDriverInfo.driverPhone }}</span>
                    <span>{{ item.vdDriverInfo.driverFullName }}</span>
                  </a>
                  <!--       微信拨号           -->
                  <!--  <a @click.stop="" :href="`tel:${item.vdDriverInfo.driverPhone}#mp.weixin.qq.com`">微信拨号</a>-->
                  <!--                  <a v-if="item.vdDriverInfo" @click.stop="" :href="`tel:${item.vdDriverInfo.driverPhone}`" class="call-box">-->
                  <!--                    <img src="@/assets/img/car/icon_call.png" class="call-img">-->
                  <!--                  </a>-->
                </div>
              </div>
<!--              <div-->
<!--                class="car-matter m-l-32 m-r-32 m-t-24 p-24 time opacity-6 m-b-24 text-break text-wrap">-->
<!--                {{ `${item.useCarMatter}` }}-->
<!--              </div>-->
              <div class="m-t-24"></div>
              <div class="m-l-32 m-r-32">
                <template v-for="(it, i) in item.buttonsBig">
                  <div
                    v-if="it.buttonValue !== '查看'"
                    class="obt-btn full-width primary text-center m-b-32"
                    :style="{'background-color': `rgb(${it.buttonColor})`}"
                    @click.stop="handleOpt(it, item)"
                    :key="item.id + i + it.buttonValue"
                  >
                    {{ it.buttonValue }}
                  </div>
                </template>
              </div>
              <div v-if="item.buttonsSmallMore.length > 0" class="flex row-between col-center">
                <div @click.stop="" :id="item.id">
                  <fm-popover
                    class="m-l-32 m-b-32"
                    :visible.sync="item.showPopover"
                    trigger="click"
                    placement="bottom-start"
                    :actions="item.buttonsSmallMore"
                    get-container="'#carApply'"
                    @select="it => handleOpt(it, item)"
                  >
                    <template #reference>
                      <fm-button text size="small">更多<i class="fm-icon fm-icon-arrow"></i></fm-button>
                    </template>
                  </fm-popover>
                </div>
                <div class="flex row-end m-l-32 m-r-32">
                  <template v-for="(it, i) in item.buttonsSmall">
                    <div
                      v-if="it.buttonSizeType === 'small'"
                      class="obt-btn other-btn text-center m-b-32"
                      :class="{'m-r-10': i + 1 < item.buttonsSmall.length}"
                      @click.stop="handleOpt(it, item)"
                      :key="it.buttonKey + item.id"
                      :style="{'border-color': `rgb(${it.buttonColor})`, color: `rgb(${it.buttonColor})`}"
                    >
                      {{ it.buttonValue }}
                    </div>
                  </template>
                </div>
              </div>
              <div v-else class="flex row-end m-l-32 m-r-32">
                <template v-for="(it, i) in item.buttonsSmall">
                  <div
                    v-if="it.buttonSizeType === 'small'"
                    class="obt-btn other-btn text-center m-b-32"
                    :class="{'m-r-10': i + 1 < item.buttonsSmall.length}"
                    @click.stop="handleOpt(it, item)"
                    :key="it.buttonKey + item.id"
                    :style="{'border-color': `rgb(${it.buttonColor})`, color: `rgb(${it.buttonColor})`}"
                  >
                    {{ it.buttonValue }}
                  </div>
                </template>
              </div>
<!--              <div-->
<!--                v-if="showBtn(item)"-->
<!--                class="flex row-between m-b-32 m-l-32 m-r-32">-->
<!--                :class="['', '', '', '', 'danger', '', '', '', 'primary', 'danger', 'primary', 'primary', 'primary'][item.applyFormState]"-->
<!--                <div-->
<!--                  class="obt-btn opt-btn text-center primary"-->
<!--                  @click.stop="handleView(item, 2)"-->
<!--                >-->
<!--                  处理-->
<!--                  {{-->
<!--                    ['', '', '', '', '重新提交', '', '', '', '确认行程开始', '行程结束', '提交', '费用确认'][item.applyFormState]-->
<!--                  }}-->
                  <!--                  {{  ['', '', '', '', '', '', '', '', '行程进行中', '行程进行中', '已完成', '已完成'][item.applyFormUserState] }}-->
<!--                </div>-->
<!--              </div>-->
<!--              <div-->
<!--                v-else-if="showDeal(item)"-->
<!--                class="obt-btn text-center m-b-32 m-l-32 m-r-32 primary"-->
<!--                @click.stop="handleView(item, 2)"-->
<!--              >-->
<!--                :class="['', '', '', '', 'danger', '', '', '', 'primary', 'danger', 'primary', 'primary'][item.applyFormState]"-->
<!--                处理-->
<!--                {{-->
<!--                  ['', '', '', '', '重新提交', '', '', '', '确认行程开始', '行程结束', '提交', '确认费用'][item.applyFormState]-->
<!--                }}-->
<!--              </div>-->
<!--              <div-->
<!--                v-else-if="item.applyFormUserState  === 3 && currUser.userName === vdApprovalUser.userName"-->
<!--                class="obt-btn primary text-center m-b-32 m-l-32 m-r-32"-->
<!--                @click.stop="handleView(item, 2)"-->
<!--              >-->
<!--                处理-->
<!--                提交-->
<!--              </div>-->
<!--              <div v-if="item.flagAllowEvaluate"-->
<!--                   class="obt-btn primary text-center m-b-32 m-l-32 m-r-32 m-t-24"-->
<!--                   @click.stop="openRate(item, 1)">-->
<!--                去评价-->
<!--              </div>-->
              <div v-if="item.vdAfUcCompleteEvaluate"
                   class="flex col-center row-between m-l-32 m-b-32 m-r-32">
                <div class="flex col-center">
                  <fm-rate v-model="item.vdAfUcCompleteEvaluate.evaluateScore" :iconClass="require('@/assets/img/car/icon_star_active.png')"
                           :void-icon-class="require('@/assets/img/car/icon_star.png')"
                           readonly size="28px"/>
                  <span class="evaluate-text"> {{
                      ['', '非常差', '差', '一般', '满意', '非常满意'][+item.vdAfUcCompleteEvaluate.evaluateScore]
                    }}</span>
                </div>
<!--                <div class="flex col-center" @click.stop="openRate(item, 2)">-->
<!--                  <span class="detail-text">查看详情</span>-->
<!--                  <img class="icon-right" src="@/assets/img/car/icon_right.png"/>-->
<!--                </div>-->
              </div>
            </fm-cell-group>
          </fm-swipe-cell>
        </fm-list>
      </fm-pull-refresh>
    </div>
    <rate-popup ref="ratePopup" :cur-car.sync="curCar" :is-cur="true" :rate-type="rateType"
                @refreshList="onRefresh"></rate-popup>
    <rate-drawer ref="rateDrawer" :cur-car.sync="curCar" :is-cur="true" :rate-type="rateType"
                 @refreshList="onRefresh"></rate-drawer>
    <revocation-drawer
      ref="revocationDrawer"
      :cur-car.sync="curCar"
      :is-cur="true"
      @refreshList="onRefresh"
    ></revocation-drawer>
    <!-- 增加按钮 -->
    <!--    <div class="bottom" @click="add">-->
    <!--      <i class="fm-icon fm-icon-plus"></i>-->
    <!--    </div>-->
    <tabbar></tabbar>
  </div>
  <menus v-else-if="isPC">
    <fks-query-page
      v-loading="loading"
      :page-sizes="[15, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :current-page.sync="form.pageNo"
      :page-size.sync="form.pageSize"
      :data="formList"
      highlight-current-row
      :total="form.total"
      :table-name="'记录列表'"
      class="record-table"
      @query="query"
      @clear="onClear"
      @sort-change="sortHandler"
    >
      <template slot="button">
        <div class="flex row-end">
          <fks-button
            type="primary"
            icon="fks-icon-export"
            :loading="exportLoading"
            @click="handleExport"
          >
            {{`导出列表(${form.total}条)`}}
          </fks-button>
          <compacted-search-bar
            class="m-l-20"
            id="car-apply"
            :configs="configs"
            :optionConfigs="optionConfigs"
            @query="query"
            @change="handleQueryChange"
            @clear="onClear"
          />
          <fks-button
            class="m-l-20"
            icon="fks-icon-search"
            @click="query"
          >
            搜索
          </fks-button>
        </div>
      </template>
      <template>
        <fks-table-column type="index" align="center" label="#" width="60">
            <template slot-scope="scope">
              {{ scope.$index + (form.pageNo - 1) * form.pageSize + 1 }}
            </template>
        </fks-table-column>
        <fks-table-column prop="applyFullName" label="申请人" />
        <fks-table-column
          prop="startAddress"
          min-width="240"
          label="出发地"
          align="center"
        >
          <template slot-scope="scope">
            {{ getStartCity(scope.row) }}{{ getStartAddress(scope.row) }}
          </template>
        </fks-table-column>
        <fks-table-column
          prop="endAddress"
          min-width="220"
          label="目的地"
          align="center"
        />
        <fks-table-column
          prop="startTime"
          min-width="150"
          label="出车时间"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.startTime ? $dayjs(scope.row.startTime).format('YYYY-MM-DD HH:mm') : '' }}
          </template>
        </fks-table-column>
        <fks-table-column
          prop="predictEndTime"
          min-width="150"
          label="预计返回时间"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.predictEndTime ? $dayjs(scope.row.predictEndTime).format('YYYY-MM-DD HH:mm') : '' }}
          </template>
        </fks-table-column>
        <fks-table-column
          prop="applyUserTime"
          min-width="150"
          label="申请时间"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.applyUserTime ? $dayjs(scope.row.applyUserTime).format('YYYY-MM-DD HH:mm') : '' }}
          </template>
        </fks-table-column>
        <fks-table-column label="所属部门" align="center" min-width="200">
          <template slot-scope="{row}">
            <div class="flex col-center row-center">
              <span>{{row.applyTopDepartment}}</span>
              <span style="display: inline-block;margin-left: 10px">{{row.applyDepartment}}</span>
            </div>
          </template>
        </fks-table-column>
        <fks-table-column prop="carResourceType" label="车辆来源">
          <template slot-scope="{row}">
            {{getResourceType(row.carResourceType)}}
          </template>
        </fks-table-column>
        <fks-table-column
          prop="useCarPersonConfirmEndTime"
          label="行程完成时间"
          min-width="150"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.useCarPersonConfirmEndTime ? $dayjs(scope.row.useCarPersonConfirmEndTime).format('YYYY-MM-DD HH:mm') : '' }}
          </template>
        </fks-table-column>
        <fks-table-column
          min-width="130"
          prop="taskState"
          label="状态"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <fks-tag style="width: 90px" :type="tagTypeList[+scope.row.applyFormUserState]">
              {{ scope.row.applyFormUserState | transferEnums('ApplyFormUserStateEnums') }}
            </fks-tag>
          </template>
        </fks-table-column>
        <fks-table-column label="操作" align="center" fixed="right" width="150px">
          <template slot-scope="scope">
            <div class="flex col-center row-center">
            <template v-for="(it, i) in scope.row.buttonsBig">
              <fks-button
                class="m-r-20"
                :style="{'color': it.buttonValue === '处理' ? 'rgb(253, 77, 89)' : `rgb(${it.buttonColor})`}"
                @click.stop="handleOpt(it,  scope.row)"
                :key="scope.row.id + i + it.buttonValue"
                text
              >
                {{ it.buttonValue }}
              </fks-button>
            </template>
            <fks-popover
              v-if="scope.row.buttonsSmall.length > 1"
              width="100"
              placement="bottom"
              trigger="click">
              <div class="flex column col-center row-start">
                <template v-for="it in [...scope.row.buttonsSmall, ...scope.row.buttonsSmallMore]">
<!--                  <template v-if="it.buttonKey === 'REVOCATION'">-->
<!--                    <fks-popconfirm-->
<!--                      :key="scope.row.id + it.buttonValue"-->
<!--                      confirmButtonText="确定"-->
<!--                      cancelButtonText="取消"-->
<!--                      icon="fks-icon-info"-->
<!--                      iconColor="red"-->
<!--                      @onConfirm="handleOpt(it,  scope.row)"-->
<!--                      @onCancel="scope.row.showPopover = false"-->
<!--                      title="是否要执行撤销操作？"-->
<!--                    >-->
<!--                      <fks-button slot="reference" text :style="{'color': `rgb(${it.buttonColor})`}">撤销</fks-button>-->
<!--                    </fks-popconfirm>-->
<!--                  </template>-->
                  <fks-button
                    class="full-width m-l-20"
                    :class="{'more-btn': it.buttonKey !== 'EVALUATE', 'evaluate-btn': it.buttonKey === 'EVALUATE' }"
                    :style="{'color': `rgb(${it.buttonColor})`, 'text-align': 'left'}"
                    @click.stop="handleOpt(it,  scope.row)"
                    :key="scope.row.id + it.buttonValue"
                    text
                  >
                    {{ it.buttonValue }}
                  </fks-button>
                </template>
              </div>
              <fks-button slot="reference" text style="color: rgb(60,131,255);"  @click="scope.row.showPopover = !scope.row.showPopover">更多</fks-button>
            </fks-popover>
            <template v-else>
              <template v-for="it in scope.row.buttonsSmall">
                <fks-button
                  :style="{'color': `rgb(${it.buttonColor})`}"
                  @click.stop="handleOpt(it,  scope.row)"
                  :key="scope.row.id + it.buttonValue"
                  text
                >
                  {{ it.buttonValue }}
                </fks-button>
              </template>
            </template>

<!--            <template v-if="showBtn(scope.row)">-->
<!--              <fks-button-->
<!--                v-if="scope.row.applyFormUserState === 4"-->
<!--                dangerText-->
<!--                @click.stop="handleView(scope.row, 2)"-->
<!--              >-->
<!--                重新提交-->
<!--              </fks-button>-->
<!--              <fks-button-->
<!--                v-else-if="showDeal(scope.row)"-->
<!--                dangerText-->
<!--                @click.stop="handleView(scope.row, 2)"-->
<!--              >-->
<!--                行程结束-->
<!--                处理-->
<!--              </fks-button>-->
<!--              <fks-button-->
<!--                v-else-->
<!--                dangerText-->
<!--                @click.stop="handleView(scope.row, 2)"-->
<!--              >-->
<!--                处理-->
<!--                {{-->
<!--                  ['', '', '', '', '重新提交', '', '', '', '确认行程开始', '行程结束', '提交', '确认费用'][scope.row.applyFormState]-->
<!--                }}-->
<!--              </fks-button>-->
<!--            </template>-->
<!--            <fks-button-->
<!--              v-else-if="showDeal(scope.row)"-->
<!--              dangerText-->
<!--              @click.stop="handleView(scope.row, 2)"-->
<!--            >-->
              <!--                :class="['', '', '', '', 'danger', '', '', '', 'primary', 'danger', 'primary', 'primary'][item.applyFormState]"-->
<!--              处理-->
              <!--                {{-->
              <!--                  ['', '', '', '', '重新提交', '', '', '', '确认行程开始', '行程结束', '提交', '确认费用'][item.applyFormState]-->
              <!--                }}-->
<!--            </fks-button>-->
<!--            <fks-button-->
<!--              v-else-if="scope.row.applyFormUserState  === 3 && currUser.userName === vdApprovalUser.userName"-->
<!--              dangerText-->
<!--              @click.stop="handleView(scope.row, 2)"-->
<!--            >-->
<!--              处理-->
<!--              提交-->
<!--            </fks-button>-->

<!--            <fks-button-->
<!--              v-if="scope.row.flagAllowEvaluate"-->
<!--              dangerText-->
<!--              @click.stop="openRate(scope.row, 1)"-->
<!--            >-->
<!--              评价-->
<!--            </fks-button>-->
<!--            <fks-button style="margin-left: 0;" text @click.stop="handleView(scope.row,  showTripDetail(scope.row.applyFormUserState) ? 3 : 1)">查看</fks-button>-->
            </div>
          </template>
        </fks-table-column>
      </template>
    </fks-query-page>
    <rate-popup
      ref="ratePopup"
      :cur-car.sync="curCar"
      :is-cur="true"
      :rate-type="rateType"
      @refreshList="onRefresh"
    ></rate-popup>
    <rate-drawer
      ref="rateDrawer"
      :cur-car.sync="curCar"
      :is-cur="true"
      :rate-type="rateType"
      @refreshList="onRefresh"
    ></rate-drawer>
    <revocation-drawer
      ref="revocationDrawer"
      :cur-car.sync="curCar"
      :is-cur="true"
      @refreshList="onRefresh"
    ></revocation-drawer>
  </menus>
</template>

<script>
import { Button, Cell, CellGroup, Dialog, Rate, Toast, Popup } from 'fawkes-mobile-lib'
import CarApplyTable from './Table.vue'
import {getApprovalList, getCommonFlowList} from './api';
import tabbar from '@/modules/CarApply/components/TabBar.vue'
import RatePopup from './RatePopup';
import RateDrawer from "@modules/CarApply/components/RateDrawer.vue";
import RevocationDrawer from "./RevocationDrawer.vue";
import {mapActions, mapGetters, mapState, mapMutations} from 'vuex'
import * as GetterTypes from "@store/Getter/getterTypes";
import {eventBackButton, exitApp} from '@/utils/app'
import platform from "@/mixins/platform";
import Menus from '@/components/menus/index.vue';
import EmptyData from '@/components/EmptyData/index.vue';
import DateTimePicker from '@/modules/FormCenter/components/DateTimePicker/index.vue';
import {fileReader} from "../../../utils/exportFile";
import CompactedSearchBar from "../../../components/CompactedSearchBar/index.vue";

export default {
  name: 'ReimburseTestList',
  mixins: [platform],
  components: {
    CompactedSearchBar,
    [Button.name]: Button,
    [Rate.name]: Rate,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Toast.name]: Toast,
    RatePopup,
    RateDrawer,
    tabbar,
    Menus,
    CarApplyTable,
    EmptyData,
    RevocationDrawer,
    Popup,
    DateTimePicker
  },
  data() {
    return {
      showFilter: false,
      evaluateScore: 0,
      evaluate: 1,
      activeKey: 0,
      value: '', //搜索文字
      formList: [],
      timeApplyChoose: [],
      timeChoose: [],
      statusChoose: [],
      useCarTypeChoose: [],
      timeList: [
        { value: 1, text: '近1个月', start: `${this.$dayjs(new Date().getTime() - 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`, end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: 3, text: '近3个月', start: `${this.$dayjs(new Date().getTime() - 3 * 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`, end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: 6, text: '近6个月', start: `${this.$dayjs(new Date().getTime() - 6 * 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`, end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: this.$dayjs().format('YYYY'), text: '今年', start: this.$dayjs().format('YYYY') + '-01-01 00:00:00', end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: this.$dayjs().format('YYYY') - 1, text: this.$dayjs().format('YYYY') - 1, start: `${this.$dayjs().format('YYYY') - 1}-01-01 00:00:00`, end: `${this.$dayjs().format('YYYY') - 1}-12-31 23:59:59`},
        { value: this.$dayjs().format('YYYY') - 2, text: this.$dayjs().format('YYYY') - 2, start:  `${this.$dayjs().format('YYYY') - 2}-01-01 00:00:00`, end: `${this.$dayjs().format('YYYY') - 2}-12-31 23:59:59` }
      ],
      showStartApplyTime: false,
      showEndApplyTime: false,
      showStartTime: false,
      showEndTime: false,
      form: {
        pageNo: 1,
        pageSize: 20,
        total: 0,
        sort: 'desc', // 排序规则,示例值(desc)
        searchValue: '', // 搜索条件【位置名称、位置详细地址】
        endTime: '',
        address: '',
        startApplyTime: '',
        startTime: '',
        endApplyTime: ''
      },
      isRefresh: false,
      finished: false,
      loading: false, // 下拉刷新时禁止无限加载
      bannerShow: true,
      curCar: {},
      rateType: 1,// 1、评价，2、未评价
      exportLoading: false,
      params: {}, // 用于筛选条件参数
      configs: [
        {prop: 'address', label: '出发/目的地', type: 'input'},
        {prop: 'onTime', label: '出车时间', type: 'daterange'},
        {prop: 'tripTime', label: '行程完成时间', type: 'daterange'},
        {prop: 'status', label: '状态', type: 'select'},
        {prop: 'carResourceType', label: '车辆来源', type: 'select'},
        {prop: 'useCarType', label: '用车类型', type: 'select'},
        {prop: 'applyTime', label: '申请时间', type: 'daterange'},
      ]
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser', 'scrollTop']),
    ...mapState(['savedFilters']),
    ...mapGetters([GetterTypes.IS_NOT_PC]),
    optionConfigs() {
      return {
        status: this.applyFormState.map(item => ({label: item.value, value: item.key})),
        useCarType: this.useCarType.map(item => ({label: item.value, value: item.key})),
        carResourceType: this.carResources.map(item => ({label: item.value, value: item.key}))
      }
    },
    tagTypeList() {
      return ['', 'success', 'danger', 'warning', 'danger', '', '', '', '', '', '', 'success', '', 'danger']
    },
    vdApprovalUser() {
      return this.currUser?.vdApprovalUser || {userName: ''}
    },
    applyFormState() {
      return this.enums?.ApplyFormStateEnums ?? [];
    },
    // 用车类型
    useCarType() {
      return this.enums?.UseCarTypeEnums ?? [];
    },
    // 车辆来源列表
    carResources() {
      return this.enums?.CarResourceTypeEnums ?? [];
    },
  },
  beforeRouteLeave(to, from, next) {
    // 导航离开该组件的对应路由时调用
    // 可以访问组件实例 `this`
    if(this.isMobile){
      const el = document.querySelector('.apply-container')
      if (el) {
        const shouldMemo = to.name === 'formView' || to.name === 'tripDetail'
        this.SET_SCROLL_TOP(shouldMemo ? el.scrollTop : 0)
      }
    }
    next();
  },
  updated() {
    // todo 先禁用返回上次停留位置功能，需要解决在滚动刷新时会回到顶部的问题
    // const el = document.querySelector('.apply-container');
    // el &&  el.scrollTo({top: this.scrollTop || 0});
  },
  mounted() {
    this.isMobile && Toast.loading({
      message: '加载中...',
      forbidClick: true,
    });
    if (this.$route.query.refresh === 'true') {
      setTimeout(() => {
        this.onRefresh();
      }, 3000);
    } else {
      if (this.isMobile) {
        this.onRefresh()
      } else if (this.isPC) {
        if (Object.keys(this.savedFilters).length === 0) {
          this.onRefresh()
        }
      }
    }
  },
  activated() {
    // 页面激活时移除默认的返回上一级路由处理事件，设置退出应用事件
    document.removeEventListener('backbutton', this.$back, false)
    document.addEventListener('backbutton', eventBackButton, false)
    this.onLoad()
    console.log('activated carApply')
  },
  deactivated() {
    // 页面隐藏时移除退出应用事件，恢复默认的返回上一级路由的处理事件
    document.removeEventListener('backbutton', eventBackButton, false)
    document.removeEventListener('backbutton', exitApp, false)
    document.addEventListener('backbutton', this.$back, false)
    this.onRefresh()
    console.log('deactivated carApply')
  },
  methods: {
    ...mapActions('CarApply', [
      'getCarDispatchUser',
      'getCarCompany',
      'getCarList',
      'getDriverList',
      'getRemindTitle',
      'getRejectCause',
      'getCurrentUser',
      'getCarCompanyApprover',
      'getApplyPerson',
      'getFlowRevocation',
      'exportAllRecords'
    ]),
    ...mapMutations('CarApply', ['SET_LAST_ROUTE', 'SET_SCROLL_TOP']),
    getResourceType(type) {
      const result = this.carResources.find(item => item.key === type)
      if (result) {
        return result.value
      }
    },
    handleQueryChange(params) {
      const {onTime, applyTime, tripTime, ...rest} = params;
      if (onTime) {
        rest.startTime = onTime[0] + ' 00:00:00';
        rest.endTime = onTime[1] + ' 23:59:59';
      }
      if (applyTime) {
        rest.startApplyTime = applyTime[0] + ' 00:00:00';
        rest.endApplyTime = applyTime[1] + ' 23:59:59';
      }
      if (tripTime) {
        rest.xcStartTime = tripTime[0] + ' 00:00:00';
        rest.xcEndTime = tripTime[1] + ' 23:59:59';
      }
      this.params = {
        ...rest,
        pageSize: this.form.pageSize,
        pageNo: this.form.pageNo
      };
    },
    handleExport() {
      this.exportLoading = true
      this.exportAllRecords(this.params).then(res => {
        fileReader(res)
      }).finally(() => {
        this.exportLoading = false;
      })
    },
    showTripDetail(state){
      return state === 9 || state === 1
    },
    showBtn(item) {
      let flag = false
      if (item.vdAfUseCarPersonList && item.vdAfUseCarPersonList.length > 0) {
        const list = item.vdAfUseCarPersonList.find(it => it.ucPersonUserName === this.currUser.userName);
        flag = !!list;
      } else {
        flag = this.currUser.userName === item.applyUserName;
      }
      const isCarCompanyApproverUser = item.carCompanyApproverUserName === this.currUser.userName && [7].includes(item.applyFormUserState);  // 允许租车公司车辆调度员处理【行程开始】
      return item.vdDriverInfo && item.vdDriverInfo.driverPhone && ([8, 10].includes(item.applyFormUserState) || isCarCompanyApproverUser || item.applyFormState === 4) && flag && +item.processState  === 0;
    },
    showDeal(item) {
      let flag = false
      if (item.vdAfUseCarPersonList && item.vdAfUseCarPersonList.length > 0) {
        const list = item.vdAfUseCarPersonList.find(it => it.ucPersonUserName === this.currUser.userName);
        flag = !!list;
      } else {
        flag = this.currUser.userName === item.applyUserName;
      }
      const val = (item.applyFormUserState === 8 || item.applyFormState === 4) && +item.processState  === 0 && flag;
      return val
    },
    getStartCity(item) {
      const hdyAreas = ['西溪院区', '朝晖院区', '三墩院区', '1', '2', '3'] // 数字代表对应的院区
      if (['上海虹桥机场', '上海浦东机场'].includes(item.startAddress)) {
        return '上海市';
      }
      if (hdyAreas.findIndex(area => area === item.startAddress) > -1) {
        return this.isPC ? '华东勘测设计研究院（' : '浙江省杭州市';
      }
      if (!['其他', '9'].includes(item.startAddress)) {
        return '浙江省杭州市';
      }
      // if (!item.startAddressDetail.includes('市')) {
      //   return this.isPC ? '华东勘测设计研究院（' : '浙江省杭州市';
      // }
      // if (item.startAddressDetail.includes('省')) {
      //   return `${item.startAddressDetail.substring(0, item.startAddressDetail.lastIndexOf('省') + 1)}`;
      // }
      if (item.startAddressDetail.includes('重庆') || item.startAddressDetail.includes('北京') || item.startAddressDetail.includes('天津') || item.startAddressDetail.includes('上海')) {
        return item.startAddressDetail.substring(0, item.startAddressDetail.indexOf('市') + 1);
      }
      return `${item.startAddressDetail.substring(0, item.startAddressDetail.lastIndexOf('市') + 1)}`;
    },
    getStartAddress(item) {
      const hdyAreas = ['西溪院区', '朝晖院区', '三墩院区', '1', '2', '3'] // 数字代表对应的院区
      if (hdyAreas.findIndex(area => area === item.startAddress) > -1) {
        const str = ['上海虹桥机场', '上海浦东机场'].includes(item.startAddress) ? '' : '）'
        return this.isPC ? item.startAddress + str : `华东勘测设计研究院（${item.startAddress}）`;
      }
      if (!item.startAddressDetail.includes('市')) {
        return item.startAddressDetail;
      }
      // if (item.startAddressDetail.includes('省')) {
      //   return `${item.startAddressDetail.substring(item.startAddressDetail.lastIndexOf('省') + 1)}`;
      // }
      if (item.startAddressDetail.includes('重庆') || item.startAddressDetail.includes('北京') || item.startAddressDetail.includes('天津') || item.startAddressDetail.includes('上海')) {
        if (item.startAddressDetail.indexOf('市') === item.startAddressDetail.lastIndexOf('市') ) {
          return item.startAddressDetail.substring(item.startAddressDetail.indexOf('市') + 1);
        }
        return item.startAddressDetail.substring(item.startAddressDetail.lastIndexOf('市') + 1);
      }
      return item.startAddressDetail.substring(item.startAddressDetail.lastIndexOf('市') + 1);
    },
    getEndCity(item) {
      if (item.endAddress.includes('重庆') || item.endAddress.includes('北京') || item.endAddress.includes('天津') || item.endAddress.includes('上海')) {
        return item.endAddress.substring(0, item.endAddress.indexOf('市') + 1);
      }
      if (item.endAddress.includes('市')) {
        if (item.endAddress.substring(item.endAddress.indexOf('市') - 2, item.endAddress.indexOf('市') + 1) === item.endAddress.substring(item.endAddress.lastIndexOf('市') - 2), item.endAddress.lastIndexOf('市') + 1 ) {
          return item.endAddress.substring(0, item.endAddress.indexOf('市') + 1);
        }
        return `${item.endAddress.substring(0, item.endAddress.lastIndexOf('市') + 1)}`;
      }
      return '';
    },
    getEndAddress(item) {
      if (item.endAddress.includes('重庆') || item.endAddress.includes('北京') || item.endAddress.includes('天津') || item.endAddress.includes('上海')) {
        if (item.endAddress.indexOf('市') === item.endAddress.lastIndexOf('市') ) {
          return item.endAddress.substring(item.endAddress.indexOf('市') + 1);
        }
        return item.endAddress.substring(item.endAddress.lastIndexOf('市') + 1);
      }
      if (item.endAddress.includes('市')) {
        if (item.endAddress.substring(item.endAddress.indexOf('市') - 2, item.endAddress.indexOf('市') + 1) === item.endAddress.substring(item.endAddress.lastIndexOf('市') - 2), item.endAddress.lastIndexOf('市') + 1 ) {
          return item.endAddress.substring(item.endAddress.indexOf('市') + 1);
        }
        return item.endAddress.substring(item.endAddress.lastIndexOf('市') + 1);
      }
      return '';
    },
    openRate(item, type) {
      this.curCar = item;
      this.rateType = +type
      this.$nextTick(() => {
        this.$refs.rateDrawer.open();
      })
    },
    sortHandler() {},
    onClear() {
      this.form = {
        pageNo: 1,
        pageSize: 20,
        total: 0,
        sort: 'desc', // 排序规则,示例值(desc)
        searchValue: '', // 搜索条件【位置名称、位置详细地址】
        endTime: '',
        address: '',
        startApplyTime: '',
        startTime: '',
        endApplyTime: ''
      }
      this.params = {};
      // 复用刷新接口
      this.onRefresh()
    },
    clearFilter() {
      this.timeApplyChoose = [];
      this.timeChoose = [];
      this.statusChoose = [];
      this.useCarTypeChoose = [];
      this.form.startApplyTime = '';
      this.form.endApplyTime = '';
      this.form.startTime = '';
      this.form.endTime = '';
    },
    onSearchFilter() {
      // const flag = this.timeApplyChoose.length === 0 && !this.form.startApplyTime && !this.form.endApplyTime &&
      // this.timeChoose.length === 0 && !this.form.startTime && !this.form.endTime &&
      // this.statusChoose.length === 0 && this.useCarTypeChoose.length === 0;
      // if (flag) {
      //   Toast({
      //     message: '您还没有选择筛选项！',
      //     duration: 3000
      //   });
      //   return false;
      // }
      this.onRefresh(true);
    },
    // 搜索
    onSearch(val) {
      // 复用刷新接口
      this.onRefresh()
    },
    getParams() {
      this.form.userName = this.currUser.userName || this.$storage.get('username');
      const form = JSON.parse(JSON.stringify(this.form));
      if (form.startTime) {
        form.startTime = form.startTime + ' 00:00:00'
      }
      if (form.startApplyTime) {
        form.startApplyTime = this.form.startApplyTime + ' 00:00:00'
      }
      if (form.endTime) {
        form.endTime = form.endTime + ' 23:59:59'
      }
      if (form.endApplyTime) {
        form.endApplyTime = form.endApplyTime + ' 23:59:59'
      }
      if (this.timeApplyChoose.length > 0) {
        const time = this.timeList.find(it => it.value === this.timeApplyChoose[0]);
        form.startApplyTime = time.start;
        form.endApplyTime = time.end;
      }
      if (this.timeChoose.length > 0) {
        const time = this.timeList.find(it => it.value === this.timeChoose[0]);
        form.startTime = time.start;
        form.endTime = time.end;
      }
      if (this.statusChoose.length > 0) {
        form.status = this.statusChoose[0];
      }
      if (this.useCarTypeChoose.length > 0) {
        form.useCarType = this.useCarTypeChoose[0];
      }
      return form;
    },
    handleRefresh(){
      // 下拉刷新后，滚动高度重置为0
      this.SET_SCROLL_TOP(0)
      this.onRefresh()
    },
    query(flag) {
      this.loading = true
      if (flag) {
        // 筛选时，分页器需要选择第一页
        this.form.pageNo = 1;
      }

      getCommonFlowList({
        userName: this.currUser.userName || this.$storage.get('username'),
        pageNo: this.form.pageNo,
        pageSize: this.form.pageSize,
        ...this.params
      })
        .then((res) => {
          this.formList = this.getData(res);
          this.form.total = res.data.total;
        })
        .finally(() => {
          this.loading = false
        })
    },
    onRefresh(flag = false) {
      this.finished = false
      this.loading = true
      this.isMobile && (this.form.pageNo = 1);
      getCommonFlowList(this.getParams())
        .then((res) => {
          if (!res.status) {
            Toast({
              message: res.message || '请求失败',
              duration: 3000
            });
            return  false;
          }
          // if (flag && res.data.list.length === 0) {
          //   this.isMobile ? Toast({
          //     message: '未找到匹配的纪录\n请修改筛选条件试试',
          //     duration: 3000
          //   }) : '';
          //   return false;
          // }
          if (flag) {
            this.showFilter = false;
          }
          this.formList = this.getData(res);
          this.form.total = res.data.total;
          this.isRefresh = false
          if (res.data.isLastPage) {
            this.finished = true
            this.isMobile && (this.form.pageNo = 1)
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {
          Toast.clear()
          this.loading = false
        })
    },
    getContainer(id) {
      try {
        return document.getElementById(id);
      } catch (e) {
        return '#carApply';
      }
    },
    onSelect() {

    },
    onLoad() {
      this.loading = true
      this.form.pageNo++
      getCommonFlowList(this.getParams())
        .then((res) => {
          if (!res.status) {
            this.finished = true
            return false;
          };
          const list = this.getData(res);
          this.formList = this.isPC ? list : [...this.formList, ...list]
          this.form.total = res.data.total;
          if (res.data.isLastPage) {
            this.finished = true
            this.isMobile && (this.form.pageNo = 1)
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    getData(res) {
      return res.data.list.map(item => {
        item.showPopover = false;
        item.buttonsBig = [];
        item.buttonsSmall = [];
        item.buttonsSmallMore = [];
        item.buttonsBig.push({
          buttonColor: '60,131,255',
          buttonDesc: '用户查看按钮',
          buttonKey: 'VIEW',
          buttonSizeType: 'big',
          buttonValue: '查看'
        });
        item.buttons = item.buttons.map(it => {
          it.text = it.buttonValue;
          if (it.buttonValue === '处理') {
            it.buttonColor = 'rgb(253, 77, 89)';
          }
          if (it.buttonSizeType === 'big') {
            item.buttonsBig.push(it);
          }
          if (it.buttonSizeType === 'small') {
            item.buttonsSmall.length < 3 ? item.buttonsSmall.push(it) : item.buttonsSmallMore.push(it);
          }
          return it;
        });
        // item.buttonsBig = item.buttons.filter(it => it.buttonSizeType === 'big');
        // item.buttonsSmall = item.buttons.filter(it => it.buttonSizeType === 'small');
        return item;
      });
    },
    setCurPage() {
      // this.SET_LAST_ROUTE(this.$route.path);
    },
    /*打开新增页面*/
    add() {
      this.setCurPage();
      this.$router.push({
        name: 'formAdd',
        params: {type: 'add', formKey: 'carApply', normal: false},
      })
    },
    /*点击按钮操作*/
    handleOpt(it, item) {
      switch (it.buttonKey) {
        case 'HANDLE':
          this.handleView(item, 2);
          break;
        case 'EVALUATE':
          item.showPopover = !item.showPopover;
          this.openRate(item, 1);
          break;
        case 'REVOCATION':
          this.revocation(item);
          break;
        case 'VIEW':
          this.handleView(item,  this.showTripDetail(item.applyFormUserState) ? 3 : 1);
          break;
        case 'XC_FORM_MODIFY':
        case 'XC_DRIVER_CAR_MODIFY':
        case 'XC_CAR_COMP_MODIFY':
          item.buttonKey = it.buttonKey;
          this.handleView(item, 4);
          break;
      }
    },
    // 撤销
    revocation(item) {
      // this.isMobile && Dialog.confirm({
      //   // 组件除show外的属性
      //   title: '提示',
      //   message: '是否要执行撤销操作?',
      // })
      //   .then(() => {
      //     this.flowRevocation(item)
      //   })
      //   .catch(() => {
      //   })
      // this.isPC && this.flowRevocation(item);
      this.flowRevocation(item);
    },
    flowRevocation(item) {
      this.curCar = item;
      this.$nextTick(() => {
        this.$refs.revocationDrawer.open();
      })
      // const params = {
      //   applyFormId: item.id,
      //   currentUserName: this.$storage.get('username'), // 当前登录人用户名
      //   revocationCause: '', // 撤销原因
      //   revocationFullName: this.$storage.get('username'), // 撤销人
      //   revocationTime: this.$dayjs().format('YYYY-MM-DD HH:mm:ss'), // 撤销时间
      //   revocationUserDept: this.currUser.deptName !== '无' ? this.currUser.deptName: '', // 撤销人部门
      //   revocationUserName: this.$storage.get('userFullname'), // 撤销人用户名
      // }
      // this.getFlowRevocation(params).then(res => {
      //   if (!res.status) {
      //     this.isMobile && Toast(res.message || '撤销失败');
      //     this.isPC && this.$message(res.message || '撤销失败');
      //     return false;
      //   }
      //   this.onRefresh();
      // })
    },
    /*打开查看页面*/
    handleView(row, type) {
      const formData = {
        ...row
      }
      getApprovalList({bizId: row.id}).then((res) => {
        this.$store.commit('SET_CURRENT_ROW', {
          ...row,
          formKey: 'vehicleDispatch',
          taskKey: res.data[res.data.length - 1].taskKey,
          taskName: res.data[res.data.length - 1].taskName,
          taskId: res.data[res.data.length - 1].taskId,
          bizId: res.data[res.data.length - 1].formBizId,
          processInstanceId: res.data[res.data.length - 1].processInstanceId,
        });
        if (type === 2 || type === 4) {
          this.setCurPage();
          this.$router.push({
            name: 'formExecute',
            params: {
              type: 'execute',
              formKey: 'vehicleDispatch',
              taskKey: res.data[res.data.length - 1].taskKey,
              taskId: res.data[res.data.length - 1].taskId,
              bizId: res.data[res.data.length - 1].formBizId,
              processInstanceId: res.data[res.data.length - 1].processInstanceId,
              formName: '车辆调度'
            },
            query: {
              isApply: 'false',
              narrow: 'true',
              buttonKey: type === 4 ? row.buttonKey : null
            }
          })
        }
        else if(type === 3) {
          this.$router.push({
            name: 'tripDetail',
            query: {
              id: row.id,
              response: JSON.stringify(res.data),
              rate: JSON.stringify(row.vdAfUcCompleteEvaluate)
            }
          })
        }
        else {
          this.setCurPage();
          //car_form_view
          this.$router.push({
            name: 'formView',
            params: {
              type: 'view',
              bizId: row.id,
              formKey: 'carApply',
              taskKey: res.data[res.data.length - 1].taskKey,
              // formKey: 'reimbursement',
              normal: false,
              page: 'carApply'
            },
            query: {
              isApply: 'false',
              narrow: 'true'
            }
          })
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
@import "./index";
.record-table {
  /deep/ .fks-query-body .fks-button+.fks-button {
    margin-left: 0 !important;
  }
}
</style>
