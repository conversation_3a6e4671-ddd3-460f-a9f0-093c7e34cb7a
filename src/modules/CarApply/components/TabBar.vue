<!--
 * @Author: xie_sm
 * @Date: 2022-04-15 08:54:47
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-08-16 09:33:43
 * @FilePath: \mobile-template\src\modules\Tabbar\index.vue
 * @Description:
 *
-->
<template>
  <div>
    <keep-alive>
      <router-view />
    </keep-alive>
    <fm-tabbar route v-model="active" safe-area-inset-bottom @change="changeTab">
      <fm-tabbar-item
          v-for="route in tabRoutes"
          :key="route.path"
          :to="route.path === '/DispatchCar/Apply' ? '' : route.path"
          :name="route.path"
      >
        <span class="index0">
          {{route.title}}
        </span>
        <template #icon="props">
          <img
              v-if="getPath(props.active, route)"
              class="props-img"
              :class="{biggerIcon: route.path === '/DispatchCar/Read'}"
            :src=" require(`@/assets/img/tabbar/${getPath(props.active, route)}.png`)"
          />
        </template>
      </fm-tabbar-item>
      <fm-tabbar-item to="/my" icon="user-o">
        <span>我的</span>
        <template #icon="props">
          <img
            class="props-img"
            :src="
              require(`@/assets/img/tabbar/${
                props.active ? 'my-active' : 'my'
              }.png`)
            "
          />
        </template>
      </fm-tabbar-item>
    </fm-tabbar>
  </div>
</template>
<script>
import {Image, Tabbar, TabbarItem} from 'fawkes-mobile-lib'
import {mapGetters, mapState} from 'vuex';
import * as StateTypes from '@/store/State/stateTypes'
import * as GetterTypes from '@/store/Getter/getterTypes'
import {traverse} from "@utils/util";

const iconTable = {
  '/DispatchCar/carApply': {
    activeIcon: 'car-active',
    inActiveIcon: 'car'
  },
  '/DispatchCar/Apply': {
    activeIcon: 'apply-active',
    inActiveIcon: 'apply'
  },
  '/DispatchCar/Todo': {
    activeIcon: 'dealt-active',
    inActiveIcon: 'dealt'
  },
  '/DispatchCar/TravelTask': {
    activeIcon: 'task-active',
    inActiveIcon: 'task'
  },
  '/DispatchCar/Read': {
    activeIcon: 'reader-active',
    inActiveIcon: 'reader'
  }
}
export default {
  name: 'Tabbar',
  components: {
    [Tabbar.name]: Tabbar,
    [TabbarItem.name]: TabbarItem,
    [Image.name]: Image,
  },
  data() {
    return {
      active: 0,
      iconTable
    }
  },
  computed: {
    ...mapState([StateTypes.ROLE_ID, StateTypes.ROUTES]),
    ...mapGetters([GetterTypes.IS_NOT_PC]),
    tabRoutes() {
      let routes = [];
      traverse(this[StateTypes.ROUTES], (item) => {
        if (item.path.includes('/DispatchCar/')) {
          routes.push(item)
        }
      }, 'children')
      return routes.sort((a, b) => a.sort - b.sort)
    }
  },
  methods: {
    changeTab(name) {
      this.$storage.set('isMainTab', false);
      if (name === '/DispatchCar/Apply') {
        this.$router.push({name: 'Apply', params: {redirect: 'true'}})
      }
    },
    getPath(active, route){
      if (route && this.iconTable[route.path]) {
        return active ?
            this.iconTable[route.path].activeIcon :
            this.iconTable[route.path].inActiveIcon
      }
      return ''
    }
  },
}
</script>
<style lang="less" scoped>
/deep/.fm-tabbar {
  /*height: 164px;*/
}
/deep/.fm-tabbar-item {
  font-size: 20px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #414b59;
  line-height: 28px;
  display: flex;
  justify-content: center;
  padding-top: 14px;
}
/deep/.fm-tabbar-item__icon {
  display: flex;
  align-items: flex-end;
}
/deep/.fm-tabbar-item--active {
  color: #3c83ff;
  font-weight: 500;
  // font-size: 20px;
}
.props-img {
  width: 40px;
  height: 40px;
}
.biggerIcon {
  width: initial !important;
}
</style>
