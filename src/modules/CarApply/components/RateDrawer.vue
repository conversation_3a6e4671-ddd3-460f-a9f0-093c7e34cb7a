<template>
  <fm-action-sheet v-if="isMobile" :visible.sync="show">
    <div class="drawer-content">
      <p class="bold">您对本次用车满意吗？</p>
      <div class="rate">
        <div class="stars">
          <img
            v-for="star in stars"
            :key="star"
            :src="star > form.evaluateScore ? require('@/assets/img/car/gray-star.svg') : require('@/assets/img/car/star.svg')"
            width="40px"
            height="40px"
            @click="rateType === 2 ? '' : form.evaluateScore = star"
          />
        </div>
        <span class="bold">{{ comment }}</span>
      </div>
      <p class="bold">您对本次用车的建议反馈</p>
      <textarea
          v-model="form.evaluateContent"
          id="story"
          name="story"
          rows="10"
          :readonly="rateType === 2"
          placeholder="请输入您的建议反馈"
      >
      </textarea>
      <div v-if="rateType === 1" class="buttons">
        <fm-button size="large" @click="onCancel"> 取 消 </fm-button>
        <fm-button type="primary" size="large" @click="onSubmit"> 提交评价 </fm-button>
      </div>
      <div v-if="rateType === 2" class="m-t-24">
        <fm-button size="large" @click="onCancel"> 关 闭</fm-button>
      </div>
    </div>
  </fm-action-sheet>
  <fks-dialog
    v-else-if="isPC"
    title="评价"
    :visible.sync="show"
    :before-close="onCancel"
    size="small"
    >
    <div class="drawer-content" style="padding: 0;">
      <p class="bold">您对本次用车满意吗？</p>
      <div class="rate">
        <div class="stars">
          <img
            v-for="star in stars"
            :key="star"
            :src="star > form.evaluateScore ? require('@/assets/img/car/gray-star.svg') : require('@/assets/img/car/star.svg')"
            width="40px"
            height="40px"
            @click="rateType === 2 ? '' : form.evaluateScore = star"
          />
        </div>
        <span class="bold">{{ comment }}</span>
      </div>
      <p class="bold">您对本次用车的建议反馈</p>
      <fks-input
        type="textarea"
        id="story"
        name="story"
        :rows="10"
        placeholder="请输入您的建议反馈"
        :readonly="rateType === 2"
        v-model="form.evaluateContent">
      </fks-input>
    </div>
    <span slot="footer" class="dialog-footer">
      <template v-if="rateType === 1">
        <fks-button @click="onCancel">取 消</fks-button>
        <fks-button type="primary" @click="onSubmit">提交评价</fks-button>
      </template>
       <template v-if="rateType === 2">
        <fm-button @click="onCancel"> 关 闭</fm-button>
      </template>
    </span>
  </fks-dialog>
</template>
<script>
import { ActionSheet, Button, Toast } from 'fawkes-mobile-lib'
import { getEvaluateDetail, updateEvaluate } from './api'
import { mapState } from 'vuex'
import platform from "@/mixins/platform";
const rateText = ['非常差', '差', '一般', '满意', '非常满意']
export default {
  name: 'RateDrawer',
  mixins: [platform],
  components: {
    [ActionSheet.name]: ActionSheet,
    [Button.name]: Button,
    Toast
  },
  props: {
    curCar: {
      type: Object,
      default() {
        return {}
      }
    },
    isCur: {
      type: Boolean,
      default: false
    },
    rateType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      rateText,
      show: false,
      form: {
        evaluateScore: 5,
        evaluateContent: ''
      },
      loading: false,
      stars: [1,2,3,4,5]
    }
  },
  computed: {
    ...mapState('travelTask', ['currCar']),
    comment(){
      return rateText[this.form.evaluateScore  -1];
    }
  },

  methods: {
    // 打开弹出
    open() {
      this.show = true;
      if (this.rateType !== 2 || this.loading) {
        this.form = {
          evaluateScore: 5,
          evaluateContent: ''
        }
        return false;
      }
      this.loading = true;
      getEvaluateDetail(this.curCar.id).then(res => {
        this.loading = false;
        if (res.status) {
          this.form.evaluateScore = res.data?.evaluateScore;
          this.form.evaluateContent = res.data?.evaluateContent;
        }
      }).catch(e => {
        this.loading = false;
      })
    },
    onCancel(){
      // 取消会重置状态
      this.form = {
        evaluateScore: 5,
        evaluateContent: ''
      }
      this.show = false;
    },
    onSubmit(){
      if (!this.form.evaluateScore) {
        this.isMobile && Toast('请评价！');
        this.isPC && this.$message('请评价！');
        return false;
      }
      const currCar = this.isCur ? this.curCar : this.currCar;
      const params = {
        vdApplyFormId: currCar.id,
        vdCarCompanyInfoId: currCar.vdCarCompanyInfoId,
        vdCarInfoId: currCar.vdCarInfoId,
        vdDriverInfoId: currCar.vdDriverInfoId,
        evaluateScore: this.form.evaluateScore,
        evaluateContent: this.form.evaluateContent
      }
      this.loading = true;
      updateEvaluate(params).then(res => {
        this.loading = false;
        if (res.status) {
          this.onCancel();
          this.$emit('refreshList');
        }
      }).catch(() => {
        this.loading = false;
        this.onCancel();
      });
    }
  }
}
</script>

<style lang="less" scoped>
@import "./rateDrawer.less";
</style>
