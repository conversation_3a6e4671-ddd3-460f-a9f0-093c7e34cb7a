<template>
  <fm-popup
    mode="center"
    :visible.sync="show"
    length="80%"
    show-close
    border-radius="10"
    :close-on-click-modal="false"
    :get-container="'#carApply'"
    round
    :style="{ height: '50vh', width: '80vw' }"
    @close="close">
    <item-text  v-if="rateType === 1" text="评价"/>
    <item-text  v-if="rateType === 2" text="评价详情"/>
    <fm-form ref="form" class="m-30" label-width="60" label-align="left">
      <fm-field label="评价" :required="rateType === 2" name="evaluate">
        <template #input>
          <fm-rate v-if="rateType === 1" v-model="form.evaluateScore" color="#1A90FE" void-color="#b2b2b2" gutter="20" @change="change"></fm-rate>
          <fm-rate v-if="rateType === 2" v-model="form.evaluateScore" color="#1A90FE" void-color="#b2b2b2" gutter="20" :readonly="true"></fm-rate>
        </template>
      </fm-field>

      <div class="textarea-type">
        <fm-cell-group title="评价内容" :border="false">
          <fm-field
            v-if="rateType === 1"
            name="evaluateContent"
            label=""
            v-model="form.evaluateContent"
            type="textarea"
            :border="true"
            :custom-style="{height: '160upx', width: '100%'}"
            :auto-height="false"
            maxlength="999999"
            :placeholder="`请输入评价内容`"
            :rules="[{ required: true, message: '请输入评价内容' }]"
          />
          <fm-field
            v-if="rateType === 2"
            name="evaluateContent"
            label=""
          >
            <template #input>
              <div class="text-left evaluate-content line-5 p-l-16 info-text ">{{ form.evaluateContent }}</div>
            </template>
          </fm-field>
        </fm-cell-group>
      </div>
      <div class="confrim-btn" v-if="rateType === 1">
        <fm-button type="primary" :loading="loading" block @click="submit">确定</fm-button>
      </div>
    </fm-form>

  </fm-popup>
</template>

<script>
  import { mapState, mapActions } from 'vuex';
  import { Toast, Rate, Cell, CellGroup, Button, Field, Form } from 'fawkes-mobile-lib';
  import ItemText from '@/components/ItemText/index.vue';
  import { updateEvaluate, getEvaluateDetail } from './api.js';
  export default {
    name: 'ratePopup',
    props: {
      curCar: {
        type: Object,
        default() {
          return {}
        }
      },
      isCur: {
        type: Boolean,
        default: false
      },
      rateType: {
        type: Number,
        default: 1
      }
    },
    components: {
      ItemText,
      [Form.name]: Form,
      [Field.name]: Field,
      [Button.name]: Button,
      [Rate.name]: Rate,
      [Cell.name]: Cell,
      [CellGroup.name]: CellGroup,
      Toast,
    },
    data() {
      return {
        show: false,
        loading: false,
        form: {
          evaluateScore: 0,
          evaluateContent: ''
        },
        rules: {
          evaluateScore: [
            {
              required: true,
              message: '请评价',
              type: 'number',
              min: 1,
              // 可以单个或者同时写两个触发验证方式
              trigger: ['change']
            }
          ]
        }
      }
    },
    computed: {
      ...mapState('travelTask', ['currCar']),
    },
    methods: {
      submit() {
        this.$refs.form
          .validate()
          .then(() => {
            const currCar = this.isCur ? this.curCar : this.currCar;
            const params = {
              vdApplyFormId: currCar.id,
              vdCarCompanyInfoId: currCar.vdCarCompanyInfoId,
              vdCarInfoId: currCar.vdCarInfoId,
              vdDriverInfoId: currCar.vdDriverInfoId,
              evaluateScore: this.form.evaluateScore,
              evaluateContent: this.form.evaluateContent
            }
            this.loading = true;
            updateEvaluate(params).then(res => {
              this.loading = false;
              if (res.status) {
                this.close();
                this.$emit('refreshList');
              }
            }).catch(() => {
              this.loading = false;
              this.close();
            });
          })
          .catch((e) => {
            console.log(e)
            Toast('表单校验未通过')
          })
      },
      // 打开弹出
      open() {
        this.show = true;
        if (this.rateType !== 2 || this.loading) {
          this.form = {
            evaluateScore: 0,
            evaluateContent: ''
          }
          return false;
        }
        this.loading = true;
        getEvaluateDetail(this.curCar.id).then(res => {
          this.loading = false;
          if (res.status) {
            this.form.evaluateScore = res.data?.evaluateScore;
            this.form.evaluateContent = res.data?.evaluateContent;
          }
        }).catch(e => {
          this.loading = false;
        })
      },
      // 关闭弹窗
      close() {
        this.show = false;
        this.form = {
          evaluateScore: 0,
          evaluateContent: ''
        }
      },
      // 获取评分
      change(e) {
        this.form.evaluateScore = e;
      }
    }
  }
</script>

<style lang="less" scoped>
  .fm-border-bottom:after {
    border-bottom-width: 0;
  }
  .evaluate-content {
    width: 50vw;
    line-height: 44px;
    min-height: 360px;
  }
  .textarea-type /deep/ .fm-field__value {
    border: 1px solid #bbbbbb;
    opacity: 0.5;
  }
  .textarea-type /deep/ .fm-cell-group__title {
    color: #000;
  }
  .confrim-btn {
    margin: 30px 0 25px;
    padding: 0 0.4rem;
  }

  .textarea-type /deep/.fm-field--min-height .fm-field__control {
    min-height: 360px;
  }
</style>
