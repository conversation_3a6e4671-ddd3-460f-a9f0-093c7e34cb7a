<template>
  <fm-action-sheet v-if="isMobile" :visible.sync="show">
    <div class="drawer-content">
      <p style="font-size: 14px">您对本次用车的撤销原因</p>
      <textarea
        v-model="form.revocationCause"
        id="story"
        name="story"
        rows="10"
        placeholder="请输入您的撤销原因"
      >
      </textarea>
      <div class="buttons">
        <fm-button size="large" @click="onCancel" :loading="loading"> 取 消 </fm-button>
        <fm-button type="primary" size="large" @click="onSubmit" :loading="loading">
          提交
        </fm-button>
      </div>
    </div>
  </fm-action-sheet>
  <fks-dialog
    v-else-if="isPC"
    title="撤销"
    :visible.sync="show"
    :before-close="onCancel"
    size="small"
    class="reject-dialog"
  >
    <div class="reject-drawer-content">
      <p>您对本次用车的撤销原因</p>
      <fks-input
        type="textarea"
        id="story"
        name="story"
        :autosize="{minRows: 4, maxRows: 6}"
        placeholder="请输入您的撤销原因"
        v-model="form.revocationCause"
      >
      </fks-input>
    </div>
    <span slot="footer" class="dialog-footer">
      <template>
        <fks-button :loading="loading" @click="onCancel">取 消</fks-button>
        <fks-button :loading="loading" type="primary" @click="onSubmit">提交</fks-button>
      </template>
    </span>
  </fks-dialog>
</template>
<script>
import { ActionSheet, Button, Toast } from 'fawkes-mobile-lib'
import { mapActions, mapGetters, mapState } from 'vuex'
import platform from '@/mixins/platform'
import EventBus from '@utils/eventBus'
const rateText = ['非常差', '差', '一般', '满意', '非常满意']
export default {
  name: 'RevocationDrawer',
  mixins: [platform],
  components: {
    [ActionSheet.name]: ActionSheet,
    [Button.name]: Button,
    Toast,
  },
  props: {
    curCar: {
      type: Object,
      default() {
        return {}
      },
    },
    isCur: {
      type: Boolean,
      default: false,
    },
    rateType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      rateText,
      show: false,
      form: {
        revocationCause: '',
      },
      loading: false,
    }
  },
  computed: {
    ...mapState('CarApply', ['currUser']),
  },
  methods: {
    ...mapActions('CarApply', ['getFlowRevocation']),
    // 打开弹出
    open() {
      this.show = true
    },
    onCancel() {
      // 取消会重置状态
      this.form = {
        revocationCause: '',
      }
      this.show = false
    },
    onSubmit() {
      if (!this.form.revocationCause) {
        this.isMobile && Toast('请输入您的撤销原因！')
        this.isPC && this.$message('请输入您的撤销原因！')
        return false
      }
      const user = this.$storage.getObject('user')
      const params = {
        applyFormId: this.curCar.id,
        currentUserName: this.$storage.get('username'), // 当前登录人用户名
        revocationCause: this.form.revocationCause, // 撤销原因
        revocationFullName: this.$storage.get('userFullname'), // 撤销人
        revocationTime: this.$dayjs().format('YYYY-MM-DD HH:mm:ss'), // 撤销时间
        revocationUserDept: user.userDepName, // 撤销人部门
        revocationUserName: this.$storage.get('username'), // 撤销人用户名
      }
      this.loading = true
      this.getFlowRevocation(params)
        .then((res) => {
          this.loading = false
          if (!res.status) {
            return false
          }
          this.onCancel()
          this.$message.success('撤销成功')
          EventBus.$emit('refreshDot', 'Todo')
          this.$emit('refreshList')
        })
        .catch(() => {
          this.loading = false
          this.onCancel()
        })
    },
  },
}
</script>

<style lang="less" scoped>
@import './rateDrawer.less';
@import 'exclude/rejectDialog.css';
</style>
