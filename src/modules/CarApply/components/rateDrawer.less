.drawer-content {
  padding: 10px 40px 25px 40px;

  .rate {
    display: flex;
    align-items: center;
    .stars {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    span {
      display: inline-block;
      margin-left: 20px;
    }
  }

  .bold {
    font-size: 28px;
    color: #191919;
    font-weight: bold;
  }

  textarea {
    width: 100%;
    background-color: #f4f7fd;
    border-radius: 4pt;
    border: none;
    font-size: 28px;
    resize: none;
    padding: 12pt;
    box-sizing: border-box;
    color: #191919;
    &::placeholder {
      color: #1919194D;
      letter-spacing: 1.67pt;
    }
  }

  .buttons {
    margin-top: 25px;
    display: grid;
    grid-template-columns: 3fr 7fr;
    grid-column-gap: 25px;
  }
}