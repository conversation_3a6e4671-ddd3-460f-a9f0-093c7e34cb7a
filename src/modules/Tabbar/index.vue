<!--
 * @Author: xie_sm
 * @Date: 2022-04-15 08:54:47
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-08-16 09:33:43
 * @FilePath: \mobile-template\src\modules\Tabbar\index.vue
 * @Description:
 *
-->
<template>
  <div>
    <keep-alive>
      <router-view />
    </keep-alive>
    <fm-tabbar route v-model="active" safe-area-inset-bottom @change="changeTab">
      <fm-tabbar-item to="/home">
        <span class="index0">首页</span>
        <template #icon="props">
          <img
            class="props-img"
            :src="require(`@/assets/img/tabbar/index.png`)"
          />
        </template>
      </fm-tabbar-item>
      <fm-tabbar-item to="/address" icon="friends-o">
        <span>通讯录</span>
        <template #icon="props">
          <img
            class="props-img"
            :src="
              require(`@/assets/img/tabbar/${
                props.active ? 'address-active' : 'address'
              }.png`)
            "
          />
        </template>
      </fm-tabbar-item>
      <fm-tabbar-item to="/todo" icon="clock-o">
        <span>待办</span>
        <template #icon="props">
          <img
            class="props-img"
            :src="
              require(`@/assets/img/tabbar/${
                props.active ? 'todo-active' : 'todo'
              }.png`)
            "
          />
        </template>
      </fm-tabbar-item>
      <fm-tabbar-item to="/my" icon="user-o">
        <span>我的</span>
        <template #icon="props">
          <img
            class="props-img"
            :src="
              require(`@/assets/img/tabbar/${
                props.active ? 'my-active' : 'my'
              }.png`)
            "
          />
        </template>
      </fm-tabbar-item>
    </fm-tabbar>
  </div>
</template>
<script>
import { Tabbar, TabbarItem, Image } from 'fawkes-mobile-lib'

export default {
  name: 'Tabbar',
  components: {
    [Tabbar.name]: Tabbar,
    [TabbarItem.name]: TabbarItem,
    [Image.name]: Image,
  },
  data: () => ({
    active: 0,
  }),
  watch: {},
  created() {},
  methods: {
    changeTab(index) {
      this.$storage.set('isMainTab', true);
    }
  },
}
</script>
<style lang="less" scoped>
/deep/.fm-tabbar {
  /*height: 164px;*/
}
/deep/.fm-tabbar-item {
  font-size: 20px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #414b59;
  line-height: 28px;
  display: flex;
  justify-content: flex-start;
  padding-top: 14px;
}
/deep/.fm-tabbar-item__icon {
  display: flex;
  align-items: flex-end;
}
/deep/.fm-tabbar-item--active {
  color: #3c83ff;
  font-weight: 500;
  // font-size: 20px;
  .index0 {
    color: #f8b222;
  }
}
.props-img {
  width: 40px;
  height: 40px;
}
</style>
