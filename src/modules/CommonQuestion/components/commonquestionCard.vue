<template>
  <div class="question-card-item-container" style="border-radius: 8px;margin-bottom: 16px">
    <header
      style="padding: 14px 16px;"
      class="flex col-center row-between"
    >
      <div class="project-title flex col-center">
        <div class="title-frame" style="width: 20px;height: 18px">
          <fks-icon icon-class="question-title" class="full-width full-height"/>
        </div>
        <div class="m-l-20 title-text" style="font-size: 14px">{{ item.faqDescribe || '-' }}</div>
      </div>
    </header>
    <main style="padding: 14px 16px">
      <div class="question-content" style="padding: 12px;border-radius: 4px;font-size: 14px;letter-spacing: 1.27px">
        <div class="question-content-describe">{{ item.faqExplain }}</div>
        <div>
          <picture-preview
            v-if="item.faqFile"
            :g9s="item.faqFile"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import PicturePreview from "@components/PicturePreview/index.vue";
export default {
  name: 'CommonquestionCard',
  components: {PicturePreview},
  props: {
    item: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
    };
  },
  methods: {
  },
};
</script>
<style lang="less" scoped>
.question-card-item-container {
  border-radius: 8px;
  background: linear-gradient(190deg, #F2F7FF 0%, #FFFFFF 29%);
  border: 1px solid #FFFFFF;
  &:last-child {
    margin-bottom: 0;
  }

  .project-title {
    .title-frame {
      border-radius: 50%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .title-text {
      font-weight: 500;
      line-height: normal;
      letter-spacing: normal;
      color: #191919;
    }
  }

  main {
    .question-content {
      background: #F4F7FD;
      font-weight: normal;
      line-height: normal;
      color: rgba(25, 25, 25, 0.6);
      .question-content-describe {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
