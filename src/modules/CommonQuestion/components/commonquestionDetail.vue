<template>
  <div class="question-card-item-detail-container" style="border-radius: 8px;margin-bottom: 16px">
    <fm-nav-bar>
      <template slot="title">
        <span class="common-question-title" style="font-size: 18px">常见问题</span>
      </template>
    </fm-nav-bar>
    <main class="question-card-content">
      <div class="question-info-box" style="padding: 14px 16px">
        <span style="font-size: 16px;font-weight: 500;margin-bottom: 25px;" >问题信息</span>
        <span style="font-size: 14px;color: rgba(25, 25, 25, 0.6);margin-bottom: 15px;">问题描述</span>
        <span style="font-size: 14px;letter-spacing: 1.27px;">{{ questionItem.faqDescribe }}</span>
      </div>
      <div style="height: 16px;background-color:#F1F2F3;"></div>
      <div class="handle-info-box" style="padding: 14px 16px">
        <span style="font-size: 16px;font-weight: 500;margin-bottom: 25px;" >处理信息</span>
        <span style="font-size: 14px;color: rgba(25, 25, 25, 0.6);margin-bottom: 15px;">解决方案</span>
        <div style="display: inline-block;max-width: 100%;flex: 1; box-sizing: border-box; overflow-y: auto;margin-bottom: 40px;font-size:14px" v-html="questionItem.faqExplainHtml"></div>
      </div>
    </main>
  </div>
</template>

<script>
export default {
  name: 'CommonquestionCardDetailItem',
  data() {
    return {
      questionItem: ''
    };
  },
  mounted() {
    this.questionItem = JSON.parse(localStorage.getItem('questionItem'));
  },
  methods: {
  },
};
</script>
<style lang="less" scoped>
.question-card-item-detail-container {
  border-radius: 8px;
  color: #191919;
  background-color: #FFFFFF;
  height: 100%;
  overflow: hidden;
  .question-card-content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    max-height: 100%;
  }
  .question-info-box,.handle-info-box {
    display: flex;
    flex-direction: column;
  }
  .handle-info-box {
   flex: 1;
   max-height: 100%;
   overflow-y: auto;
    /deep/ p {
      margin: 0 !important;
      max-height: 100%;
      margin-bottom: 30px;
      overflow-y: auto;
    }
    /deep/ img {
      margin-top: 30px;
      max-width: 100%;
      height: auto;
    }
  }
}
</style>
