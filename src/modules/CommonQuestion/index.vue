<template>
  <div class="common-question-container flex flex-column">
    <fm-nav-bar>
      <template slot="title">
        <span class="common-question-title" style="font-size: 18px">常见问题</span>
      </template>
    </fm-nav-bar>
    <fm-search
      v-model="searchValue"
      clearable
      placeholder="请输入"
      @clear="handleSearch"
      @search="handleSearch"
    />
    <main
      v-loading="loading"
      class="flex-grow-1 flex flex-column"
      style="padding: 16px;"
    >
      <div
        v-if="list.length > 0"
        ref="cardFlowRef"
        class="card-flow flex-grow-1 overflow-y-auto"
        @scroll="handleScroll"
      >
        <commonquestion-card
          v-for="(dataItem, index) in list"
          :key="index"
          :item="dataItem"
          @click.native="handleClick(dataItem)"
        />
        <i v-if="iconLoading" class="fks-icon-loading loading-icon"/>
        <div v-if="isLastPage && list.length"
             style="font-size: 10px;color: #999999;text-align: center">加载到底啦
        </div>
      </div>
      <fks-empty v-else description="暂无数据" />
    </main>
  </div>
</template>

<script>
import CommonquestionCard from "@modules/CommonQuestion/components/commonquestionCard.vue";
import {
  getFaqPage,
} from '@modules/ProjectCar/CompanyPortal/FaqMana/api'

export default {
  components: {CommonquestionCard},
  data() {
    return {
      loading: false,
      searchValue: '',
      isLastPage: false,
      iconLoading: false,
      list:[],
      pageNo: 1,
      pageSize: 10,
    };
  },
  methods: {
    handleClick(item) {
      localStorage.setItem('questionItem', JSON.stringify(item))
      // 跳转至详情页
      this.$router.push(`/question/details/${item.id}`)
    },
    handleSearch() {
      this.pageNo = 1;
      this.getData();
    },
    getData() {
      if (this.pageNo === 1) {
        this.loading = true;
      } else {
        this.iconLoading = true;
      }
      getFaqPage(
        {pageNo: this.pageNo, pageSize: this.pageSize},
        {searchValue: this.searchValue}
      ).then(res => {
        if (res.status) {
          const {list, isLastPage} = res.data;
          // 如果是第一页的数据，直接赋值
          if (this.pageNo === 1 || list.length === 0) {
            this.list = list;
          } else {
            this.list = [...this.list, ...list];
          }
          this.isLastPage = isLastPage;
        }
      }).finally(() => {
        this.loading = false;
        this.iconLoading = false;
      })
    },
    handleScroll(e) {
      const el = e.target;
      const threshold = 50;
      if (el.scrollTop + el.clientHeight >= el.scrollHeight - threshold) {
        if (this.isLastPage) return;
        if (!this.iconLoading) {
          this.loadMore = true;
          // 获取数据
          this.pageNo++;
          this.getData();
        }
        this.iconLoading = true;
      }
    }
  },
  mounted() {
    this.getData();
  }
}
</script>

<style lang="less">
.common-question-container {
  background: #f1f2f3;

  .common-question-title {
    font-weight: 500;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    color: #191919;
  }

  main {
    .placeholder {
      font-weight: normal;
      line-height: normal;
      text-align: right;
      letter-spacing: normal;
      color: rgba(25, 25, 25, 0.3);
    }
    .description {
      font-weight: normal;
      line-height: normal;
      letter-spacing: normal;
      color: #999999;
      text-align: left;
    }
    .submit-button {
      width: 90%;
      border-radius: 4px;
      background: #3C83FF;
      color: #FFFFFF;
      font-weight: 500;
      line-height: normal;
      text-align: center;
      letter-spacing: 1.45px;
    }
  }


}

</style>
