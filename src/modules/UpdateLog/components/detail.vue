<template>
  <div class="update-detail-container flex flex-column">


    <div class="title-back-img">
      <img src="@/assets/img/dialog/title-back.png">
    </div>
<!--    <div class="log-img">-->
<!--      <img src="@/assets/img/dialog/update-log.png">-->
<!--    </div>-->
    <!-- 自定义吸顶 header -->
    <header class="custom-header">
      <div class="custom-header-inner">
<!--        <div class="left" @click="handleBack">-->
<!--          <img src="@/assets/img/leftArrow.svg" alt="返回">-->
<!--        </div>-->
        <div class="title">
          <span class="update-log-title">
            {{ "V" + versionInfo.version + " 版本更新" }}
          </span>
        </div>
      </div>
    </header>
    <!-- 占位元素，保证内容不会被 header 遮挡 -->
    <div class="header-placeholder"></div>

    <main class="content">
      <div class="content-item" v-for="(item, index) in contentList" :key="index">
        <!--          <div class="content-title">-->
        <!--            {{ item.title }}-->
        <!--          </div>-->
        <div class="content-details">
          <!-- 内层循环遍历 itemList -->
          <div class="content-subitem" v-for="(subItem, subIndex) in item.itemList" :key="subIndex">
            <div class="subitem-name">
              {{ subItem.name }}
            </div>
            <ul class="subitem-contents">
              <!-- 遍历每个contents -->
              <li class="sub-item-content" v-for="(content, contentIndex) in subItem.contents" :key="contentIndex">
                <img src="@/assets/img/dialog/sub-item.svg" width="14px" height="14px">
                <span>
                  {{ content }}
                  </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { getVersionNoticeDetail } from '@components/AnnouncementDialog/api'

export default {
  data() {
    return {
      loading: false,
      contentList: [],
      versionInfo: {},
    };
  },
  computed: {
  },
  methods: {
    handleBack() {
      this.$router.back()
    },
  },
  async created() {
    const {id} = this.$route.params;
    let res = await getVersionNoticeDetail(id);
    if (res.data) {
      let data = res.data
      let {
        contentList
      } = data;
      this.versionInfo = data;
      if (contentList != null) {
        this.contentList = JSON.parse(contentList);
      }
    }
  },
  mounted() {
  }
}
</script>

<style lang="less">

</style>

<style scoped>

@import "../exclude/detail.css";
</style>
