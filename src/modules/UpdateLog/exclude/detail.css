.update-detail-container {

    position: relative; /* 添加定位，使内部绝对定位元素基于此容器 */
    overflow-y: auto;
    .title-back-img {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        /*height: 200px;*/
        background: linear-gradient(180deg, #D6E1FF 0%, #FFFFFF 100%);
        overflow: hidden;
        pointer-events: none;
        z-index: 1;

        img {
            width: 100%;
            height: auto; /* 保持图片原有的宽高比 */
            display: block;
        }
    }

    .log-img {
        position: absolute;
        top: 0;
        right: 20px;
        height: 100px;
        width: 160px;
        pointer-events: none;
        z-index: 2;

        img {
            width: 100%;
            height: auto; /* 保持图片原有的宽高比 */
            display: block;
        }
    }

    .content {
        flex: 1;
        padding: 30px 18px 0;
        z-index: 20;
    }

    .content-subitem {
        margin-bottom: 14px;
    }

    .subitem-name {
        font-size: 16px;
        font-weight: normal;
        line-height: normal;
        color: rgba(0, 0, 0, 0.8483);
        padding-left: 14px; /* 稍微增加 padding-left，给 before 元素留空间 */
        position: relative; /* 确保伪元素定位 */
    }

    .subitem-name::before {
        content: ''; /* 确保伪元素可见 */
        height: 15px;
        width: 4px;
        border-radius: 2px;
        opacity: 1;
        background: #5483F7;
        position: absolute; /* 使用绝对定位 */
        left: 0; /* 向左对齐 */
        top: 53%; /* 垂直居中对齐 */
        transform: translateY(-50%); /* 完全居中对齐 */
    }


    .subitem-contents {
        padding-left: 16px;
        img {
            margin-right: 8px;
        }
    }

    .sub-item-content {
        margin-top: 10px;
        /*vertical-align: middle; !* 使图片和文字垂直居中 *!*/
        line-height: 16px;
        font-size: 13px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-direction: row;

        text-align: justify; /* 浏览器可能不支持 */
        letter-spacing: normal;
        color: rgba(0, 0, 0, 0.65);
    }


    .custom-header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        /*background-color: #fff; !* 根据需要调整背景色 *!*/
        background-color: transparent;
        z-index: 10; /* 确保 header 层级较高 */
    }

    .custom-header-inner {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center; /* 标题文字居中 */
        height: 56px; /* header 高度 */
    }

    .custom-header .left {
        position: absolute;
        left: 16px;
        cursor: pointer;
        z-index: 10;
    }

    .custom-header .title {
        font-size: 18px;
        font-weight: 500;
        z-index: 10;
    }

    .header-placeholder {
        height: 56px; /* 占位元素高度与 header 高度一致 */
    }
}