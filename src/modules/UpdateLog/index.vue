<template>
  <div class="update-log-container flex flex-column">
    <header>
      <fm-nav-bar>
<!--        <template slot="left" @click="handleBack">-->
<!--          <img src="@/assets/img/leftArrow.svg">-->
<!--        </template>-->
        <template slot="title">
          <span class="update-log-title" style="font-size: 18px">更新日志</span>
        </template>
      </fm-nav-bar>
      <fm-search
        v-model="searchValue"
        clearable
        placeholder="请输入"
        @clear="handleSearch"
        @search="handleSearch"
      />
    </header>
    <main v-loading="loading" class="flex-grow-1 flex flex-column" style="padding: 16px">
      <div ref="cardFlowRef" class="card-flow flex-grow-1 overflow-y-auto fs-14" >
        <div class="log-card" v-for="(item, index) in filteredVersionList" :key="item.id" @click="toDetail(item)">
          <div class="card-left">
            <div class="log-card-version">
              {{ "V" + item.version + " 版本更新" }}
            </div>
            <div class="log-card-time" style="color: rgba(25,25,25,0.6)">
              {{ formatDate(item.publishDate) }}
            </div>
          </div>
          <div class="card-right">
            详情
            <img src="@/assets/img/dialog/to-detail.svg" width="14" height="14" style="padding: 2px 4px;
            width: 14px !important; height: 14px !important;aspect-ratio:unset !important;" />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import FeedbackCard from '@modules/Feedback/components/feedbackCard.vue'
import { versionNoticeList } from '@components/AnnouncementDialog/api'

export default {
  components: { FeedbackCard },
  data() {
    return {
      loading: false,
      searchValue: "",
      versionList: [],
    }
  },
  computed: {
    filteredVersionList() {
      if (!this.searchValue) return this.versionList;
      const search = this.searchValue.toLowerCase();
      return this.versionList.filter(item => {
        const versionMatch =
          item.version && item.version.toLowerCase().includes(search);
        const dateMatch =
          item.publishDate && this.formatDate(item.publishDate).toLowerCase().includes(search);
        return versionMatch || dateMatch;
      });
    }
  },
  methods: {
    handleBack() {
      this.$router.back()
    },
    toDetail(item) {
      this.$router.push(`/updateLog/detail/${item.id}`)
    },
    handleSearch() {
    },
    formatDate(dateStr) {
      if (!dateStr) return '';
      return dateStr
        .replace('年', '-')
        .replace('月', '-')
        .replace('日', '');
    },
    async getNotice() {
      let resList = await versionNoticeList();
      this.versionList = resList.data;
    }

  },
  mounted() {
    this.getNotice();
  },
}
</script>

<style lang="less" scoped>

/deep/ .fm-search__content {
  border-radius: 16px !important;
}
.loading-icon {
  font-size: 36px;
  color: #3c83ff;
  margin-left: 280px;
  margin-top: 20px;
}

.update-log-container {
  .update-log-title {
    font-weight: 500;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    color: #191919;
  }

  main {
    background: #F1F2F3;

    .log-card {
      background: linear-gradient(212deg, #F2F7FF 1%, #FFFFFF 13%);
      box-sizing: border-box;
      border: 1px solid #FFFFFF;

      .card-right {
        display: flex;
        align-items: flex-start;
        color: #4065FF;
      }
    }
    .log-card-time {
      font-weight: normal;
      line-height: normal;
      text-align: justify; /* 浏览器可能不支持 */
      letter-spacing: normal;
    }
  }
}
@import "./exclude/log.css";
</style>
