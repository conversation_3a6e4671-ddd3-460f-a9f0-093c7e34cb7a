.login-conatiner {
  /deep/.fm-field__error-message {
    position: absolute;
    top: 80px;
  }
  /deep/.fm-cell {
    overflow: visible;
  }
  .suffix-icon {
    margin-left: 10px;
  }
  /deep/.fm-field__prefix-icon {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  /deep/.fm-field__suffix-icon {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .fm-icon-closed-eye:before {
    width: 36px;
    height: auto;
  }
  .fm-icon-eye-o:before {
    width: 36px;
    height: auto;
  }
  .news-img {
    width: 40px;
    height: 40px;
    margin-right: 24px;
  }
  .fm-field {
    border-radius: 0;
  }
  .login-conatiner {
    height: 100%;
  }
  .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding-top: 164px;
    .logo-pic {
      width: 120px;
      height: 120px;
      background-image: url('../../assets/img/login/new-logo.png');
      background-repeat: no-repeat;
      background-size: 100%;
    }
    .logo-title {
      font-weight: bold;
      color: #191919;
      font-size: 40px;
      margin-top: 32px;
    }
  }
  .tip {
    margin-bottom: 44px;
    font-size: 36px;
    font-weight: 600;
  }
  .form-container {
    margin-top: 88px;
    padding: 0 80px;
    .sub-menu {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999;
      font-size: 28px;
      /deep/.fm-checkbox__label {
        color: #999;
      }
    }
  }
  /deep/.fm-checkbox__icon {
    background-color: #f5f5f5;
  }
  /deep/.fm-icon {
    border: none;
  }
  .field-username,
  .field-pwd {
    border-bottom: 2px #e3e3e3 solid;
    padding-left: 24px;
    // background-color: #f5f5f5;
    border: 1px solid #ccc;
    // height: 75px;
    margin-bottom: 52px;
    border-radius: 12px;
  }
  .field-active {
    border: 1px solid #4545d1;
    background-color: #fff;
    /deep/.fm-field__control {
      color: #4545d1 !important;
    }
  }

  .footer-container {
    padding-top: 260px;
    display: flex;
    justify-content: center;
    .logo-footer {
      width: 134px;
      height: 20px;
    }
  }

  .login-btn-container {
    margin: 0 auto;
    padding-top: 72px;
    height: 88px;
    .login-btn {
      width: 100%;
      height: 72px;
      font-size: 32px;
      border-radius: 12px;
    }
  }
  .fm-field--error ::placeholder {
    color: #999999;
  }
  /deep/ .fm-field--error .fm-field__control,
  .fm-field--error .fm-field__control::placeholder {
    color: #999999;
  }
  .fm-field ::placeholder {
    color: #999999;
  }
  /deep/.fm-button--primary {
    background-color: #4545d1;
    border: 0.02667rem solid #4545d1;
  }
  /deep/.fm-checkbox__icon--checked .fm-icon {
    background-color: #4545d1;
  }
}
