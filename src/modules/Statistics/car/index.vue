<template>
  <menus>
    <div v-if="isPC" class="position-relative full-height full-width stats-container">
      <div class="card-container">
        <card
          v-for="card in cardConfigs"
          :key="card.prop"
          :card="card"
          :source-data="sourceData"
        />
      </div>
      <div class="flex row-between col-center">
        <common-title style="margin: 16px 0" title="记录列表"/>
        <div class="flex">
          <fks-button
            icon="fks-icon-export"
            type="primary"
            :loading="exportLoading"
            @click="handleExport"
          >
            {{`导出列表(${total}条)`}}
          </fks-button>
          <compacted-search-bar
            class="m-l-20"
            id="stats-car"
            :configs="configs"
            :optionConfigs="optionConfigs"
            @change="handleQueryChange"
            @query="query"
            @clear="clear"
          />
          <fks-button class="m-l-20" icon="fks-icon-search" @click="query">搜索</fks-button>
        </div>
      </div>
      <fks-table
        v-loading="loading"
        :data="tableData"
        border
        class="stats-table"
        height="calc(100% - 200px)"
      >
        <template>
          <fks-table-column align="center" label="#" type="index" width="60">
            <template slot-scope="scope">
              {{ scope.$index + (pageNo - 1) * pageSize + 1 }}
            </template>
          </fks-table-column>
          <fks-table-column
            align="left"
            label="出发地"
            min-width="240"
            prop="startAddress"
          >
            <template slot-scope="scope">
              {{ getStartCity(scope.row) }}{{ getStartAddress(scope.row) }}
            </template>
          </fks-table-column>
          <fks-table-column
            align="left"
            label="目的地"
            min-width="220"
            prop="endAddress"
          />
          <fks-table-column
            align="center"
            label="出车时间"
            min-width="140"
            prop="startTime"
          >
            <template slot-scope="scope">
              {{
                scope.row.startTime ? $dayjs(scope.row.startTime).format('YYYY-MM-DD HH:mm') : ''
              }}
            </template>
          </fks-table-column>
          <fks-table-column
            align="center"
            label="预计返回时间"
            min-width="140"
            prop="predictEndTime"
          >
            <template slot-scope="scope">
              {{
                scope.row.predictEndTime ? $dayjs(scope.row.predictEndTime).format('YYYY-MM-DD HH:mm') : ''
              }}
            </template>
          </fks-table-column>
          <fks-table-column
            align="center"
            label="申请人"
            min-width="120"
            prop="applyFullName"
          />
          <fks-table-column label="申请部门" align="center" min-width="200">
            <template slot-scope="{row}">
              <div class="flex col-center row-center">
                <span>{{row.applyTopDepartment}}</span>
                <span style="display: inline-block;margin-left: 10px">{{row.applyDepartment}}</span>
              </div>
            </template>
          </fks-table-column>
          <fks-table-column
            align="center"
            label="申请时间"
            min-width="140"
            prop="applyUserTime"
          >
            <template slot-scope="scope">
              {{
                scope.row.applyUserTime ? $dayjs(scope.row.applyUserTime).format('YYYY-MM-DD HH:mm') : ''
              }}
            </template>
          </fks-table-column>
          <fks-table-column prop="carResourceType" label="车辆来源">
            <template slot-scope="{row}">
              {{getResourceType(row.carResourceType)}}
            </template>
          </fks-table-column>
          <fks-table-column
            prop="useCarPersonConfirmEndTime"
            label="行程完成时间"
            min-width="150"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.useCarPersonConfirmEndTime ? $dayjs(scope.row.useCarPersonConfirmEndTime).format('YYYY-MM-DD HH:mm') : '' }}
            </template>
          </fks-table-column>
          <fks-table-column
            align="center"
            fixed="right"
            label="状态"
            min-width="130"
            prop="applyFormUserState"
          >
            <template slot-scope="scope">
              <fks-tag :type="tagTypeList[+scope.row.applyFormUserState]" style="width: 90px">
                {{ scope.row.applyFormUserState | transferEnums('ApplyFormUserStateEnums') }}
              </fks-tag>
            </template>
          </fks-table-column>
          <fks-table-column align="center" fixed="right" label="操作">
            <template slot-scope="scope">
              <fks-button style="margin-left: 0;" text
                          @click.stop="openView(scope.row, scope.row.vdAfUcCompleteEvaluate ? 3 : 1)">
                查看
              </fks-button>
            </template>
          </fks-table-column>
        </template>
      </fks-table>
      <fks-pagination
        :current-page.sync="pageNo"
        :page-size.sync="pageSize"
        :page-sizes="[15, 20, 50, 100]"
        :total="total"
        bordered
        class="stats-pagination"
        layout="total, sizes, prev, pager, next, jumper"
      />
      <rate-drawer ref="rateDrawer" :cur-car.sync="curCar" :is-cur="true" :rate-type="rateType"
                   @refreshList="query"/>
    </div>
  </menus>
</template>
<script>
import {mapActions, mapMutations, mapState} from 'vuex'
import platform from "@/mixins/platform";
import Menus from '@/components/menus/index.vue';
import ApplyPerson from '@/modules/FormCenter/CarApply/ApplyPerson';
import RateDrawer from "@modules/CarApply/components/RateDrawer.vue";
import CommonTitle from "@components/CommonTitle/index.vue";
import CompactedSearchBar from "../../../components/CompactedSearchBar/index.vue";
import {getApprovalList} from '@/modules/CarApply/components/api.js';
import Card from '../company/card.vue'
import {fileReader} from "@/utils/exportFile.js"

export default {
  mixins: [platform],
  components: {
    Menus,
    ApplyPerson,
    RateDrawer,
    CommonTitle,
    CompactedSearchBar,
    Card
  },
  data() {
    return {
      loading: false,
      exportLoading: false,
      pageNo: 1,
      pageSize: 15,
      total: 0,
      tableData: [],
      form: {},
      curCar: {},
      params: {},
      configs: [
        {prop: 'searchValue', label: '出发/目的地', type: 'input'},
        {prop: 'onTime', label: '出车时间', type: 'daterange'},
        {prop: 'tripTime', label: '行程完成时间', type: 'daterange'},
        {prop: 'status', label: '状态', type: 'select'},
        {prop: 'carResourceType', label: '车辆来源', type: 'select'},
        {prop: 'applyFullName', label: '申请人', type: 'person-selector'},
        {prop: 'applyDept', label: '申请部门', type: 'input'},
        {prop: 'applyTime', label: '申请时间', type: 'daterange'},
      ],
      rateType: 1, // 1、评价，2、未评价
      cardConfigs: [
        {
          prop: 'useCarNum',
          name: '出车次数',
          unit: '次',
          imageName: 'car_number',
          color: 'rgba(60, 131, 255, 1)',
          lineHeight: '42px'
        },
        {
          prop: 'underwayTaskNum',
          name: '进行中任务',
          unit: '个',
          imageName: 'car_task',
          color: 'rgba(255, 156, 0, 1)',
          lineHeight: '42px'
        },
        {
          prop: 'completedTaskNum',
          name: '已完成',
          unit: '个',
          imageName: 'car_accomplish',
          color: 'rgba(64, 187, 90, 1)',
          lineHeight: '42px'
        },
      ],
      sourceData: null,
      statisticData: {
        name: ['出车次数'],
        value: ['2000'],
        unit: ['次', '个', '个'],
        picture: [
          require('@/assets/img/car/car_number.png'),
          require('@/assets/img/car/car_task.png'),
          require('@/assets/img/car/car_accomplish.png')
        ]
      }
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['savedFilters']),
    tagTypeList() {
      return ['', 'success', 'danger', 'warning', 'danger', '', '', '', '', '', '', 'success', '', 'danger']
    },
    applyFormState() {
      return this.enums?.ApplyFormStateEnums ?? [];
    },
    // 车辆来源列表
    carResources() {
      return this.enums?.CarResourceTypeEnums ?? [];
    },
    optionConfigs() {
      return {
        status: this.applyFormState.map(item => ({label: item.value, value: item.key})),
        carResourceType: this.carResources.map(item => ({label: item.value, value: item.key}))
      }
    }
  },
  created() {
    if (Object.keys(this.savedFilters).length === 0) {
      this.query();
    }
  },
  watch: {
    pageNo() {
      this.query()
    },
    pageSize() {
      if (this.pageNo === 1) {
        this.query()
      } else {
        this.pageNo = 1
      }
    }
  },
  methods: {
    ...mapActions('CarApply', ['getFlowCar', 'getUseCarStats', 'exportCarRecords']),
    ...mapMutations('CarApply', ['SET_LAST_ROUTE']),
    getResourceType(type) {
      const result = this.carResources.find(item => item.key === type)
      if (result) {
        return result.value
      }
    },
    handleExport() {
      this.exportLoading = true
      this.exportCarRecords(this.params).then(res => {
        fileReader(res)
      }).finally(() => {
        this.exportLoading = false;
      })
    },
    getQueryParams() {
      if (this.form.startApplyTime) {
        this.form.startApplyTime = this.form.startApplyTime + ' 00:00:00'
      }
      if (this.form.endApplyTime) {
        this.form.endApplyTime = this.form.endApplyTime + ' 23:59:59'
      }
      if (this.form.startTime) {
        this.form.startTime = this.form.startTime + ' 00:00:00'
      }
      if (this.form.endTime) {
        this.form.endTime = this.form.endTime + ' 23:59:59'
      }
      return {
        ...this.form,
        pageSize: this.pageSize,
        pageNo: this.pageNo
      };
    },
    handleQueryChange(params) {
      const {onTime, applyTime, tripTime, ...rest} = params;
      if (onTime) {
        rest.startTime = onTime[0] + ' 00:00:00';
        rest.endTime = onTime[1] + ' 23:59:59';
      }
      if (applyTime) {
        rest.startApplyTime = applyTime[0] + ' 00:00:00';
        rest.endApplyTime = applyTime[1] + ' 23:59:59';
      }
      if (tripTime) {
        rest.xcStartTime = tripTime[0] + ' 00:00:00';
        rest.xcEndTime = tripTime[1] + ' 23:59:59';
      }
      this.params = {
        ...rest,
        pageSize: this.pageSize,
        pageNo: this.pageNo
      };
    },
    query(flag) {
      this.loading = true;
      if (flag) {
        // 筛选时，分页器需要选择第一页
        this.pageNo = 1;
      }
      this.getFlowCar({
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        ...this.params
      }).then(res => {
        if (res.status) {
          let {list, total} = res.data || [];
          this.tableData = list;
          this.total = total;
        }
      }).finally(() => this.loading = false);
      const {searchValue, ...rest} = this.form
      const useParams = {
        address: searchValue,
        ...rest
      }
      this.getUseCarStats(useParams).then(res => {
        if (res.status) {
          this.sourceData = res.data;
        }
      })
    },
    clear() {
      this.pageNo = 1;
      this.pageSize = 15
      this.params = {};
      this.query();
    },
    openView(row, type) {
      const formData = {
        ...row
      }
      getApprovalList({bizId: row.id}).then((res) => {
        this.$store.commit('SET_CURRENT_ROW', {
          ...row,
          formKey: 'vehicleDispatch',
          taskKey: res.data[res.data.length - 1].taskKey,
          taskId: res.data[res.data.length - 1].taskId,
          bizId: res.data[res.data.length - 1].formBizId,
          processInstanceId: res.data[res.data.length - 1].processInstanceId,
        });
        if (type === 2) {
          this.setCurPage();
          this.$router.push({
            name: 'formExecute',
            params: {
              type: 'execute',
              formKey: 'vehicleDispatch',
              taskKey: res.data[res.data.length - 1].taskKey,
              taskId: res.data[res.data.length - 1].taskId,
              bizId: res.data[res.data.length - 1].formBizId,
              processInstanceId: res.data[res.data.length - 1].processInstanceId,
              formName: '车辆调度',
            },
            query: {
              isApply: 'false',
              narrow: 'true'
            }
          })
        } else if (type === 3) {
          this.$router.push({
            name: 'tripDetail',
            query: {
              id: row.id,
              response: JSON.stringify(res.data),
              rate: JSON.stringify(row.vdAfUcCompleteEvaluate)
            }
          })
        } else {
          this.setCurPage();
          //car_form_view
          this.$router.push({
            name: 'formView',
            params: {
              type: 'view',
              bizId: row.id,
              formKey: 'carApply',
              taskKey: res.data[res.data.length - 1].taskKey,
              // formKey: 'reimbursement',
              normal: false,
              page: 'carApply'
            },
            query: {
              isApply: 'false',
              narrow: 'true'
            }
          })
        }
      })
    },
    openRate(item, type) {
      this.curCar = item;
      this.rateType = +type
      this.$nextTick(() => {
        this.$refs.rateDrawer.open();
      })
    },
    setCurPage() {
      this.SET_LAST_ROUTE(this.$route.path);
    },
    getStartCity(item) {
      const hdyAreas = ['西溪院区', '朝晖院区', '三墩院区', '1', '2', '3'] // 数字代表对应的院区
      if (['上海虹桥机场', '上海浦东机场'].includes(item.startAddress)) {
        return '上海市';
      }
      if (hdyAreas.findIndex(area => area === item.startAddress) > -1) {
        return this.isPC ? '华东勘测设计研究院（' : '杭州市';
      }
      // if (!item.startAddressDetail.includes('市')) {
      //   return this.isPC ? '华东勘测设计研究院（' : '杭州市';
      // }
      if (item.startAddressDetail.includes('省')) {
        return `${item.startAddressDetail.substring(0, item.startAddressDetail.lastIndexOf('省') + 1)}`;
      }
      return `${item.startAddressDetail.substring(0, item.startAddressDetail.lastIndexOf('市') + 1)}`;
    },
    getStartAddress(item) {
      const hdyAreas = ['西溪院区', '朝晖院区', '三墩院区', '1', '2', '3'] // 数字代表对应的院区
      if (hdyAreas.findIndex(area => area === item.startAddress) > -1) {
        const str = ['上海虹桥机场', '上海浦东机场'].includes(item.startAddress) ? '' : '）'
        return this.isPC ? item.startAddress + str : item.startAddress;
      }
      if (!item.startAddressDetail.includes('市')) {
        return item.startAddressDetail;
      }
      if (item.startAddressDetail.includes('省')) {
        return `${item.startAddressDetail.substring(item.startAddressDetail.lastIndexOf('省') + 1)}`;
      }
      return item.startAddressDetail.substring(item.endAddress.lastIndexOf('市') + 1);
    },
    // 选择申请人员
    closePopupPerson(val) {
      this.form.applyFullName = val.ucPersonFullName;
    },
  }
}
</script>
<style lang="less" scoped>
/deep/ .fks-divider--horizontal {
  margin: 0 0 48px 0 !important;
}

.stats-container {
  box-sizing: border-box;
  padding: 48px 64px;

  .stats-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 32px;
  }

  .card-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 36px;
  }
}

.car-outer {
  height: 200px;
  margin: 40px;
}

.car-card {
  width: 32.4%;
  border-radius: 8px;
  flex-wrap: wrap;
  box-shadow: 0 0 7px 2px #e8e8e8;
}

.car-value {
  font-size: 60px;
  margin-right: 20px;
  font-weight: 700;
}

.full-height {
  height: 100%;
}

.car-data {
  height: 120px;
  line-height: 120px;
}

// .table-full {
//   height: calc(100% - 300px);
// }
</style>
