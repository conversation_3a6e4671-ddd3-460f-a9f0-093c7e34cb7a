<template>
  <menus>
    <div v-if="isPC" v-loading="loading" class="full-width full-height company-container">
      <fks-search-bar
          :isPreciseSearch="true"
          :showSwitch="false"
          @clear="clear"
          @search="getData"
      >
        <fks-search-item :span="24" label="出车时间">
          <fks-date-picker
              v-model="dates"
              :picker-options="pickerOptions"
              end-placeholder="结束日期"
              range-separator="至"
              start-placeholder="开始日期"
              type="daterange"
          />
          <fks-checkbox v-model="isLastMonth" class="m-l-24">本月</fks-checkbox>
        </fks-search-item>
      </fks-search-bar>
      <fks-divider/>
      <div class="card-container">
        <card
            v-for="card in cardConfigs"
            :key="card.prop"
            :card="card"
            :source-data="sourceData"
        />
      </div>
      <common-title style="margin: 24px 0" title="租车公司统计"/>
      <fks-row v-if="!isEmpty" :gutter="18" class="chart-container">
        <fks-col
            v-for="config in chartConfigs" :key="config.prop"
            :lg="12"
            :md="12"
            :sm="12"
            :xl="8"
            :xs="12"
            style="margin-bottom: 18px"
        >
          <div class="chart-item">
            <div class="title">{{ config.title }}</div>
            <div class="chart">
              <bar-charts :config="config" :xAxisData="config.xAxisData"
                          :yAxisData="config.yAxisData"/>
            </div>
          </div>
        </fks-col>
      </fks-row>
      <empty-data v-else/>
    </div>
  </menus>
</template>
<script>
import platform from "@/mixins/platform";
import Menus from '@/components/menus/index.vue';
import barCharts from './barCharts.vue';
import {mapActions} from 'vuex'
import CommonTitle from "@components/CommonTitle/index.vue";
import EmptyData from '@/components/EmptyData/index.vue';
import Card from './card.vue'

const defaultConfigs = [
  {prop: 'evaluationScore', title: '综合评分', color: 'rgba(255, 186, 79, 1)'},
  {prop: 'useCarNum', title: '出车次数', color: 'rgba(74, 151, 253, 1)'},
  {
    prop: 'useCarDriverNum',
    title: '出车司机人数',
    color: 'rgba(90, 200, 113, 1)'
  },
  {prop: 'trackTotal', title: '出车轨迹总长（km）', color: 'rgba(0, 162, 246, 1)'},
  {prop: 'approvalTime', title: '平均审批耗时', color: 'rgba(122, 139, 244, 1)'},
  {
    prop: 'approvalOverdueNum',
    title: '审批逾期次数',
    color: 'rgba(250, 121, 121, 1)'
  },
]

export default {
  mixins: [platform],
  components: {
    Menus,
    barCharts,
    CommonTitle,
    EmptyData,
    Card
  },
  data() {
    return {
      isLastMonth: false,
      isEmpty: false,
      dates: [],
      sourceData: null,
      loading: false,
      chartConfigs: defaultConfigs,
      cardConfigs: [
        {
          prop: 'favorableRate',
          name: '五星好评率',
          unit: '%',
          imageName: 'car_rate',
          color: 'rgba(255, 151, 0, 1)',
          lineHeight: '42px'
        },
        {
          prop: 'useCarDriverNum',
          name: '总司机出车人数',
          unit: '人',
          imageName: 'car_people',
          color: 'rgba(64, 187, 90, 1)',
          lineHeight: '42px'
        },
        {
          prop: 'useCarNum',
          name: '出车次数',
          unit: '次',
          imageName: 'car_number',
          color: 'rgba(37, 113, 255, 1)',
          lineHeight: '42px'
        },
        {
          prop: 'trackTotal',
          name: '总轨迹长',
          unit: 'km',
          imageName: 'car_track',
          color: 'rgba(0, 161, 239, 1)',
          lineHeight: '42px'
        },
      ],
    }
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  watch: {
    isLastMonth: function (newVal) {
      if (newVal) {
        let start = this.$dayjs().startOf('month').format('YYYY-MM-DD');
        let end = this.$dayjs().endOf('month').format('YYYY-MM-DD');
        this.dates = [start, end];
      } else {
        this.dates = []
      }
    }
  },
  created() {
    this.getData()
  },
  methods: {
    ...mapActions('CarApply', ['getRentCompanyStats']),
    clear() {
      this.isLastMonth = false;
      this.dates = [];
      this.$nextTick(() => {
        this.getData()
      })
    },
    generateChartData(list) {
      this.chartConfigs = this.chartConfigs.map(item => {
        if (list.length) {
          item.xAxisData = list.map(listItem => listItem.carCompName)
          item.yAxisData = list.map(listItem => {
            if (Number.isNaN(Number(listItem[item.prop]))) {
              return 0
            }
            return Number(listItem[item.prop])
          })
          return item;
        }
        return item
      })

    },
    getData() {
      let params = {}
      if (this.dates.length) {
        params.startTime = this.$dayjs(this.dates[0]).format('YYYY-MM-DD') + ' 00:00:00';
        params.endTime = this.$dayjs(this.dates[1]).format('YYYY-MM-DD') + ' 23:59:59';
      }
      this.loading = true;
      this.getRentCompanyStats(params)
          .then(res => {
            if (res.status) {
              this.sourceData = res.data
              if (res.data.statsCarCompList.length) {
                this.isEmpty = false
                this.$nextTick(() => {
                  this.generateChartData(res.data.statsCarCompList)

                })
              } else {
                this.isEmpty = true
              }
            }
          })
          .finally(() => this.loading = false)
    },
  }
}
</script>
<style lang="less" scoped>
/deep/ .fks-divider--horizontal {
  margin: 0 0 48px 0 !important;
}

.company-container {
  box-sizing: border-box;
  padding: 50px;
  display: flex;
  flex-direction: column;

  .card-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: 36px;
  }

  .chart-container {
    .chart-item {
      display: flex;
      flex-direction: column;
      padding: 20px;
      background: #FFFFFF;
      box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      border: 1px solid rgba(225, 225, 225, 0.25);

      .title {
        font-size: 28px;
        color: #333333;
        line-height: 38px;
      }

      .chart {
        flex-grow: 1;
      }
    }
  }
}
</style>
