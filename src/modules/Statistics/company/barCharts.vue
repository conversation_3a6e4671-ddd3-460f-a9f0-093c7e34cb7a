<template>
  <div class="bar-chart">
    <empty-data v-if="config.empty" class="remain"/>
    <div v-else :id="config.prop" class="remain"/>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import EmptyData from '@/components/EmptyData/index.vue';

let elementResizeDetectorMaker = require('element-resize-detector');

export default {
  name: 'barCharts',
  components: {EmptyData},
  props: {
    config: {
      type: Object,
      required: true
    },
    xAxisData: {
      type: Array
    },
    yAxisData: {
      type: Array
    }
  },
  data() {
    return {
      charts: null
    };
  },
  watch: {
    xAxisData() {
      this.initChart()
    },
    yAxisData() {
      this.initChart()
    }
  },
  methods: {
    initChart() {
      this.$nextTick(() => {
        // 如果有先清除
        if (this.charts) {
          this.charts.dispose()
        }
        const el = document.getElementById(this.config.prop)
        if (el) {
          this.charts = echarts.init(el);
          this.charts.setOption({
            xAxis: {
              type: 'category',
              data: this.config.xAxisData,
              axisLabel: {
                show: true,
                interval: 0
              },
            },
            yAxis: {
              type: 'value',
              minInterval: 1
            },
            grid: {
              top: '10%',
              bottom: '10%'
            },

            series: [{
              data: this.config.yAxisData,
              type: 'bar',
              // showBackground: true,
              barWidth: 20,
              itemStyle: {
                normal: {
                  color: this.config.color,
                  label: {
                    position: 'top',
                    show: true,
                    formatter(prop) {
                      return prop.data || '无'
                    }
                  }
                }
              }
              // backgroundStyle: {
              //   color: 'rgba(220, 220, 220, 0.8)'
              // }
            }]
          })
          let erd = elementResizeDetectorMaker();
          erd.listenTo(el, element => {
            this.$nextTick(() => {
              // 使echarts尺寸重置
              this.charts.resize();
            });
          });
        }

      })
    }
  }
};
</script>
<style lang="less" scoped>
.bar-chart {


  .remain {
    height: 500px;
  }
}
</style>
