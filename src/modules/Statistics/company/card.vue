<template>
  <div class="card">
    <div class="card-item">
      <img :src="require(`@/assets/img/car/${card.imageName}.png`)" width="70px"/>
      <div class="showcase">
        <div class="value">
              <span :style="{color: card.color, lineHeight: card.lineHeight}" class="number">{{
                  sourceData ? (sourceData[card.prop] || '无') : '无'
                }}</span>
          <span v-if="sourceData && sourceData[card.prop] && sourceData[card.prop] !== '无'"
                class="unit">{{
              card.unit
            }}</span>
        </div>
        <span class="name">{{ card.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatsCard',
  props: {
    card: {
      type: Object
    },
    sourceData: {
      type: Object
    }
  }
}
</script>
<style lang="less" scoped>
.card {
  background: #FFFFFF;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(225, 225, 225, 0.25);
  padding: 50px;

  .card-item {
    max-width: 400px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;

    .showcase {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .name {
        margin-top: 20px;
        font-size: 32px;
        color: #333333;
      }

      .value {
        display: flex;
        align-items: flex-end;

        .number {
          font-size: 96px;
          font-weight: bold;
        }

        .unit {
          font-size: 40px;
          color: #999999;
        }
      }
    }
  }

}
</style>
