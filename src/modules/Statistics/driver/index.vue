<template>
  <menus>
    <div v-if="isPC" class="driver-container">
      <fks-query-page
        v-loading="loading"
        :currentPage.sync="currentPage"
        :data="tableData"
        :page-sizes="[15, 25, 50, 100]"
        :pageSize.sync="pageSize"
        :total="total"
        border
        layout="total, sizes, prev, pager, next, jumper"
        tableName="司机出车统计"
        @clear="clear"
        @query="query"
      >
        <template slot="button">
          <div class="flex row-end">
            <compacted-search-bar
              class="m-l-20"
              id="stats-driver"
              :configs="configs"
              :optionConfigs="optionConfigs"
              @change="handleQueryChange"
              @query="query"
              @clear="clear"
            />
            <fks-button class="m-l-20" icon="fks-icon-search" @click="query">搜索</fks-button>
          </div>
        </template>
        <template>
          <fks-table-column align="center" label="#" type="index"/>
          <fks-table-column align="center" label="司机姓名" prop="driverFullName"/>
          <fks-table-column align="center" label="租车公司" prop="carCompName" min-width="150"/>
          <fks-table-column align="center" label="出车次数" prop="useCarNum"/>
          <fks-table-column align="center" label="轨迹总长（km）" prop="trackTotal"/>
          <fks-table-column align="left" fixed="right" label="评价综合分" prop="evaluationScore"
                            width="320px">
            <template slot-scope="scope">
              <fks-rate v-if="scope.row.evaluationScore" :value="Number(scope.row.evaluationScore)"
                        disabled score-template="{value}星" show-score/>
              <fks-rate v-else v-model="scope.row.evaluationScore" disabled
                        score-template="尚未评价" show-score/>
            </template>
          </fks-table-column>
        </template>
      </fks-query-page>
    </div>
  </menus>
</template>
<script>
import platform from "@/mixins/platform";
import Menus from '@/components/menus/index.vue';
import {mapActions} from 'vuex'
import CompactedSearchBar from "../../../components/CompactedSearchBar/index.vue";

export default {
  mixins: [platform],
  components: {
    CompactedSearchBar,
    Menus
  },
  data() {
    return {
      loading: false,
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
      configs: [
        {prop: 'driverFullName', label: '司机姓名', type: 'input'},
        {prop: 'carCompId', label: '租车公司', type: 'select'},
        {prop: 'onTime', label: '出车时间', type: 'daterange-check'},
      ],
      params: {},
      queryForm: {
        date: [],
        carCompId: '',
        driverFullName: '',
        startTime: '',
        endTime: '',
        isLastMonth: ''
      },
      carCompanyColumns: []
    };
  },
  computed: {
    optionConfigs() {
      return {
        carCompId: this.carCompanyColumns.map(item => ({label: item.ccName, value: item.id}))
      }
    }
  },
  watch: {
    'queryForm.isLastMonth': function (newVal) {
      if (newVal) {
        let start = this.$dayjs().startOf('month').format('YYYY-MM-DD');
        let end = this.$dayjs().endOf('month').format('YYYY-MM-DD');
        this.queryForm.date = [start, end];
      } else {
        this.queryForm.date = [];
      }
    }
  },
  created() {
    this.getCarCompany().then(res => {
      if (!res.status) {
        return false;
      }
      this.carCompanyColumns = res.data;
    })
    this.clear();
  },
  methods: {
    ...mapActions('CarApply', ['getStatsDriver', 'getCarCompany']),
    // 查询
    query(flag) {
      this.loading = true;
      if (flag) {
        // 筛选时，分页器需要选择第一页
        this.currentPage = 1;
      }
      this.getStatsDriver({
        pageSize: this.pageSize,
        pageNo: this.currentPage,
        ...this.params
      }).then((res) => {
        if (!res.status) {
          return false;
        }
        this.loading = false;
        this.tableData = res.data.list;
        this.total = res.data.total;
      }).catch(e => {
        this.loading = false;
      });
    },
    handleQueryChange(params) {
      const {onTime, ...rest} = params;
      if (onTime) {
        rest.startTime = onTime[0] + ' 00:00:00';
        rest.endTime = onTime[1] + ' 23:59:59';
      }
      this.params = {
        ...rest,
        pageSize: this.pageSize,
        pageNo: this.currentPage
      };
    },
    // 清空
    clear() {
      this.queryForm = {
        date: [],
        carCompId: '',
        driverFullName: '',
        startTime: '',
        endTime: '',
        isLastMonth: ''
      };
      this.currentPage = 1;
      this.params = {};
      this.query();
    }
  }
}
</script>
<style lang="scss" scoped>
.driver-container {
  position: relative;
  height: 100%;
}
</style>
