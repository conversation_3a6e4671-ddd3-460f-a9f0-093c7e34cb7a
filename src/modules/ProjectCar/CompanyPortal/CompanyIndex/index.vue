<template>
  <div class="flex" style="box-sizing: border-box; flex-direction: row; overflow-x: auto">
    <div style=" flex-shrink: 0">
      <treeSelector
        @choose-project="handleChooseProject"
        :is-collapsed.sync="isCollapsed"
        :data="deptTreeData"
      />
    </div>

    <div class="flex flex-grow-1 flex-column" style="min-width: 750px">
      <TitleKit style="min-width: 750px">
        <template #left>
          <div class="second-title">
            <!--            项目用车（固定用车）-->
          </div>
        </template>
        <template #right>
          <div class="flex col-center">
            <div style="font-size: 14px; margin-right: 12px">统计时段</div>
            <fks-date-picker
              @change="getStatsData"
              v-model="date"
              size="mini"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </fks-date-picker>
          </div>
        </template>
      </TitleKit>
      <div
        class="flex flex-column flex-grow-1 right-content"
        style="padding: 0 20px 20px 20px; box-sizing: border-box"
        :style="{ width: contentWidth, minWidth: '750px' }"
      >
        <TitleHeader ref="title" :loading="loading" />
        <MainContent ref="mainContent" :loading="loading" />
      </div>

      <AnnouncementDialog></AnnouncementDialog>
    </div>
  </div>
</template>

<script>
import TitleKit from '@components/PageTitle/TitleKit.vue'
import TitleHeader from './exclude/title-header.vue'
import MainContent from './exclude/main-content.vue'
import { companyDetailStats } from '@modules/ProjectCar/CompanyPortal/CompanyIndex/api'
import dayjs from 'dayjs'
import treeSelector from '@modules/ProjectCar/CompanyPortal/CompanyIndex/exclude/TreeSelector.vue'
import { getTreeData } from '@modules/ProjectCar/CompanyPortal/CompanyIndex/api'
import EventBus from '@utils/eventBus'
import AnnouncementDialog from '@components/AnnouncementDialog/index.vue'

export default {
  name: 'CompanyIndex',
  components: { AnnouncementDialog, TitleKit, TitleHeader, MainContent, treeSelector },
  // provide() {
  //   return {
  //     handleGroundClick: this.handleGroundClick
  //   }
  // },
  data() {
    return {
      date: [],
      mapData: {},
      statsData: {},
      loading: false,
      deptTreeData: [],
      isCollapsed: false,
      contentWidth: '100%',
      chooseProjectIds: [],
      selectedProvince: null,
    }
  },
  created() {
    // EventBus.$on('handleGroundClick', this.handleGroundClick)
    this.getStatsData()
    this.searchTreeData()
  },
  mounted() {
    this.$nextTick(() => {
      this.getContentWidth()
      setTimeout(() => {
        this.getContentWidth()
      }, 500)
    })
    window.addEventListener('resize', this.getContentWidth) // 监听窗口大小变化
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getContentWidth) // 清理监听器
    // EventBus.$off('handleGroundClick')
  },
  watch: {
    isCollapsed: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.$nextTick(() => {
          this.getContentWidth()
        })
      },
    },
  },
  methods: {
    handleGroundClick(params) {
      if (this.selectedProvince === params.name) {
        this.selectedProvince = null // 清空选中的省份
        // 显示整体数据
        this.$refs.title.loadData(this.statsData)
      } else {
        this.selectedProvince = params.name // 更新选中的省份
        // 显示选中省份的数据
        const item = this.statsData.mapData[this.selectedProvince]
        const obj = {
          projectTotal: 0,
          carTotal: 0,
          selfCarNum: 0,
          zlCarNum: 0,
          driverTotal: 0,
          selfDriverNum: 0,
          zlDriverNum: 0,
          travelTotal: 0,
          cccs: 0,
          totalFee: 0,
          wxbyFee: 0,
          clsyFee: 0,
          clzlFee: 0,
          sjzlFee: 0,
          driverScoreTotal: 0,
          selfDriverScore: 0,
          zlDriverScore: 0,
          dcMonthAvgFee: 0,
          dc100KmOilConsume: 0,
        }
        const renderItem = item ? item : obj
        this.$refs.title.loadData(renderItem)
      }
    },
    getContentWidth() {
      let pcMain = document.getElementById('pcMain')
      if (pcMain) {
        let width = pcMain.offsetWidth - (this.isCollapsed ? 30 : 260)
        this.contentWidth = width + 'px'
      } else {
        this.contentWidth = '100%'
      }
    },
    handleChooseProject(projectIds) {
      this.chooseProjectIds = projectIds
      this.getStatsData()
    },
    searchTreeData() {
      getTreeData().then((res) => {
        this.deptTreeData = [res.data]
      })
    },
    async getStatsData() {
      this.loading = true
      let data = {
        projectIds: this.chooseProjectIds.join(','),
      }

      if (this.date && this.date[0] && this.date[1]) {
        let beginDate = dayjs(this.date[0]).format('YYYY-MM-DD HH:mm:ss')
        let endDate = dayjs(this.date[1]).format('YYYY-MM-DD HH:mm:ss')

        data.beginDate = beginDate
        data.endDate = endDate
      }

      try {
        const res = await companyDetailStats(data) // 使用 await 等待接口返回
        let responseData = res.data
        this.mapData = responseData.mapData
        this.statsData = responseData
        console.log(responseData, '==========responseData=====')
        // 调用子组件的方法更新数据
        this.$refs.title.loadData(this.statsData)
        this.$refs.mainContent.loadData(this.mapData)
      } catch (error) {
        console.error('获取统计数据失败', error)
        // 这里可以添加错误处理逻辑，比如提示用户
      } finally {
        this.loading = false // 无论成功还是失败，都设置 loading 为 false
      }
    },
  },
}
</script>
