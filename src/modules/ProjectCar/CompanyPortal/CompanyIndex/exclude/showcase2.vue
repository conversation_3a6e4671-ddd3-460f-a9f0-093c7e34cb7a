<template>
  <fks-popover
    :trigger="$slots.popover ? 'hover' : 'manual'"
    :width="popWidth"
    placement="bottom"
  >
    <div
      slot="reference"
      class="showcase-container full-height right-showcase"
    >
      <div class="flex col-center">
        <img :src="require(`@/assets/img/companyIndex/${config.imgName}.png`)" alt="#" width="28px">
        <div class="title">{{ config.title }}</div>
      </div>
      <div v-if="$slots.custom" class="content">
        <slot name="custom"/>
      </div>
      <div v-else class="content flex col-baseline">
        <div class="num">{{ config.num }}</div>
        <div class="unit">{{ config.unit }}</div>
      </div>
    </div>
    <slot name="popover"/>
  </fks-popover>

</template>

<script>
export default {
  name: 'showcase2',
  props: ['config', 'popWidth']
}
</script>

<style lang="less" scoped>
.showcase-container {

  &.row {
    display: flex;
    flex-direction: row;
    align-items: center;

    .content {
      margin-left: 10px;

      .num {
        font-size: 12px;
      }
    }
  }

  &.column {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }

  .title {
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0px;
    color: #333333;
    white-space: nowrap;
    min-width: 70px;
  }

  .content {
    margin-left: 28px;

    .num {
      font-family: Source Han Sans SC;
      font-size: 20px;
      font-weight: bolder;
      line-height: normal;
      letter-spacing: 0px;
      color: #6193F1;
    }

    .unit {
      font-family: Source Han Sans;
      font-size: 12px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0px;
      color: #333333;
      margin-left: 4px;
      white-space: nowrap;
    }

  }

}
</style>
