<template>
  <div
    :style="{ padding: isCollapsed? '0px 8px' : '0'}"
    style="box-shadow: 3px 0px 8px 0px rgba(0, 0, 0, 0.03);"
    class="tree-selector"
  >
    <div v-if="!isCollapsed" key="open" class="flex flex-column">
      <div class="tree-header flex col-center row-between">
        <span class="tab-title" style="color: #333">部门组织架构</span>
      </div>
      <!-- 树结构 -->
      <div class="tree-selector__body flex-grow-1">
        <fks-tree
          :data="filteredData"
          :props="defaultProps"
          :expand-on-click-node="false"
          default-expand-all
          highlight-current
          node-key="id"
          show-checkbox
          @check="handleCheck"
        >
        <span
          slot-scope="{ node, data }"
          :title="node.label"
          class="custom-tree-node"
          v-text="node.label"
        >
        </span>
        </fks-tree>
      </div>

      <!-- 收起/展开按钮 -->
      <div class="tree-selector__toggle-btn" @click="toggleCollapse(true)">
        <img alt="" src="@/assets/img/companyIndex/right-arrow.svg"
             style="transform: rotate(180deg)"/>
      </div>
    </div>
    <div v-else key="collapse" class="collapsed-container" @click="toggleCollapse(false)">
      <div>
        <span>组织架构</span>
        <img alt="" height="16" src="@/assets/img/companyIndex/org.svg" width="16"/>
      </div>
      <img
        alt=""
        style="margin-top: 20px"
        src="@/assets/img/companyIndex/right-arrow.svg"
      />
    </div>
  </div>
</template>

<script>
import globalSearchMixin from "@/mixins/globalSearchMixin";
import {mapState} from "vuex";
import * as StateTypes from "@store/State/stateTypes";

export default {
  name: 'TreeSelector',
  mixins: [globalSearchMixin],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    modelValue: {
      type: [String, Number, Array],
      default: null,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    isCollapsed: {
      type: Boolean,
      default: false,
    }
  },

  data() {
    return {
      showSearch: false,
      // 用于控制是否收起
      keyword: '',
      selectedValue: this.modelValue, // 本地维护选中的值
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      treeHeight: 0,
    };
  },

  computed: {
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    // 根据搜索关键字，对树数据进行简单过滤
    filteredData() {
      if (!this.keyword.trim()) {
        return this.data;
      }
      return this.filterTree(this.data, this.keyword);
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.getPcMainHeight();
    })
    window.addEventListener('resize', this.getPcMainHeight); // 监听窗口大小变化
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getPcMainHeight); // 清理监听器
  },
  methods: {
    globalSearch() {
      this.keyword = this[StateTypes.GLOBAL_QUERY_PARAMS].searchValue || '';
    },
    // 递归过滤树节点
    filterTree(treeData, keyword) {
      const result = [];
      for (let node of treeData) {
        const tempNode = {...node};
        const isMatch = tempNode.label
          .toLowerCase()
          .includes(keyword.toLowerCase());

        if (tempNode.children && tempNode.children.length > 0) {
          tempNode.children = this.filterTree(tempNode.children, keyword);
        }

        if (isMatch || (tempNode.children && tempNode.children.length > 0)) {
          result.push(tempNode);
        }
      }
      return result;
    },

    // 勾选时触发
    handleCheck(checkedNodes, treeData) {
      // 获取当前树的所有叶子节点
      const allCheckNods = treeData.checkedNodes;
      let checkedLeafNode = allCheckNods.filter(node => !node.children || node.children.length <= 0);
      let projectIds = checkedLeafNode.map(node => node.id)
      this.$emit('choose-project', projectIds);
    },

    /**
     * 递归获取所有叶子节点
     * @param {Array} data - 树结构数据
     * @return {Array} - 返回所有叶子节点数组
     */
    getAllLeafNodes(data) {
      let leaves = [];
      data.forEach((item) => {
        if (!item.children || item.children.length === 0) {
          // 没有 children 或 children 为空 => 叶子节点
          leaves.push(item);
        } else {
          // 递归处理子节点
          leaves = leaves.concat(this.getAllLeafNodes(item.children));
        }
      });
      return leaves;
    },
    // 切换 isCollapsed
    toggleCollapse(value) {
      this.$emit('update:isCollapsed', value);
    },
    getPcMainHeight() {
      let height = 600;
      let pcMain = document.getElementById("pcMain");
      if (pcMain) {
        height = pcMain.offsetHeight - 66 - 15;
      }
      this.treeHeight = height;
    }
  },
};
</script>

<style lang="less" scoped>
.tree-selector {
  /* 外层容器尽量包含足够的宽度，方便切换 */
  font-size: 14px;
  height: 100%;
  box-sizing: border-box;
  border-radius: 0 6px 6px 0; /* 顺时针设置 */
  background-color: #fffffd;
  display: flex;
  flex-direction: row; /* 注意这里用 row 横向布局，左边树 + 右边按钮 */
  position: relative;
  overflow-y: auto;
  //overflow-x: hidden;
}

//通过自定义树形结构行内容，实现文本过多时数据不显示的问题，现在效果为显示省略号，且鼠标移上去会显示出全部文本内容
.custom-tree-node {
  width: 100%;
  overflow: hidden !important; // 溢出部分隐藏
  white-space: nowrap !important; //禁止自动换行
  text-overflow: ellipsis !important; // 使溢出部分以省略号显示
  display: block !important;
}

/* 树区域 */
.tree-selector__body {
  user-select: none;
  overflow-y: auto;
  max-height: calc(100vh - 150px);
  width: 260px;
  padding: 0 8px;
  border-radius: 8px;
  box-sizing: border-box;
}

/* 收起/展开 按钮 */
.tree-selector__toggle-btn {
  /* 让按钮贴在右边中间位置，也可以贴在左边、顶部、底部...随需求 */
  width: 18px;
  height: 56px;
  position: absolute; /* 关键点 */
  top: 50%; /* 定位到容器顶部的 50% */
  right: 0; /* 紧贴右侧 */
  transform: translateY(-50%); /* 向上移动自身高度的一半，实现垂直居中 */

  /* 视觉和交互细节 */
  cursor: pointer;
  background-image: url('../../../../../assets/img/companyIndex/trapezoidal.svg');
  background-repeat: no-repeat; /* 防止图片重复 */
  background-position: center; /* 确保图片居中 */
  background-size: auto; /* 图片保持比例缩放到合适大小 */

  display: flex;
  align-items: center;
  padding-bottom: 5px;
  box-sizing: border-box;
}

.collapsed-container {
  display: flex; /* 使用 Flexbox */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  flex-direction: column; /* 子元素纵向排列 */
  height: 100%; /* 必须明确父容器高度 */
  width: 100%;
  margin: 0 auto;
  cursor: pointer;
  user-select: none;

  div {
    width: 16px;

    span {
      margin-bottom: 4px;
      display: inline-block;
    }
  }
}

.tree-header {
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0px;
  color: #333333;
  margin-bottom: 10px;
  padding: 14px 0 0 18px;
}
</style>

<style>
</style>
