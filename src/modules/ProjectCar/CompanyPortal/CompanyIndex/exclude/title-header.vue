<template>
  <header class="header-container frame"
          v-loading="loading">
    <div v-if="cardsData.length > 0" class="left">
      <showcase1 :config="cardsData[0]" />
      <showcase1 :config="cardsData[1]" pop-width="130">
        <template slot="popover">
          <div class="popover-container">
            <div class="popover-title">{{cardsData[1].payload.title}}</div>
            <div
              v-for="(popItem, index) in cardsData[1].payload.items"
              :key="index"
              class="popover-item"
            >
              <div class="popover-text">{{popItem.name}}</div>
              <div>
                <span class="popover-value">{{popItem.num}}</span>
                <span class="popover-text">{{popItem.unit}}</span>
              </div>
            </div>
          </div>
        </template>
      </showcase1>
      <showcase1 :config="cardsData[2]" pop-width="160">
        <template slot="popover">
          <div class="popover-container">
            <div class="popover-title">{{cardsData[2].payload.title}}</div>
            <div
              v-for="(popItem, index) in cardsData[2].payload.items"
              :key="index"
              class="popover-item"
            >
              <div class="popover-text">{{popItem.name}}</div>
              <div>
                <span class="popover-value">{{popItem.num}}</span>
                <span class="popover-text">{{popItem.unit}}</span>
              </div>
            </div>
          </div>
        </template>
      </showcase1>
    </div>
    <div class="split"></div>
    <div v-if="cardsData1.length > 0" class="title-header-right">
      <showcase2 :config="cardsData1[0]"/>
      <showcase2 :config="cardsData1[1]"/>
      <showcase2 :config="cardsData1[2]" pop-width="250">
        <template slot="popover">
          <div class="popover-container">
            <div class="popover-title">总费用</div>
            <normal-pie
              :data="feeData"
              :legend="legendConfig"
              :title-config="titleConfig"
              unit="万元"
              id="totalFee"
              height="250px"
            />
          </div>
        </template>
      </showcase2>
      <showcase2 :config="{ title: '司机评分', imgName: 'rate' }" pop-width="300">
        <template slot="custom">
          <div class="flex col-center">
            <stars :value="rateData.star" />
            <span class="comment">{{ rateData.text }}</span>
          </div>
        </template>
        <template slot="popover">
          <div class="popover-container">
            <div class="popover-title">司机总数</div>
            <div
              v-for="(dataItem, index) in rateDetailData"
              :key="index"
              class="popover-item"
            >
              <div class="popover-text">{{dataItem.name}}</div>
              <div class="flex col-center">
                <stars :value="dataItem.star" size="20px" />
                <span style="margin-left: 4px" class="popover-value">{{dataItem.star}}</span>
                <span class="popover-text">星</span>
              </div>
            </div>
          </div>
        </template>
      </showcase2>
    </div>
  </header>
</template>
<script>
import showcase1 from './showcase1.vue';
import showcase2 from './showcase2.vue';
import Stars from "@components/stars.vue";
import NormalPie
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/normal-pie.vue";

export default {
  name: 'TitleHeader',
  components: {NormalPie, Stars, showcase1, showcase2},
  props: {
    loading: Boolean,
  },
  data() {
    return {
      cardsData: [],
      cardsData1: [],
      rateData: {
        star: 4,
        text: '4星'
      },
      rateDetailData: [
        {name: '出租方司机', star: 4},
        {name: '自有司机', star: 5}
      ],
      feeData: [],
      legendConfig: {
        bottom: 0,
        left: 'center'
      },
      titleConfig: {
        text: '6.3万元',
        textStyle: {
          color: '#333333',
          fontSize: 16,
        },
        left: 'center',
        top: '35%'
      }
    }
  },
  methods: {
    doLayout() {
      const resizeDiv = document.querySelector('.header-container');
      const rightElement = document.querySelector('.title-header-right');
      const rightShowcases = Array.from(document.querySelectorAll('.right-showcase'));
      const starImgs = Array.from(document.querySelectorAll('.title-header-right .star-img'));
      const showcaseImgs = Array.from(document.querySelectorAll('.showcase1-img'));
      const resizeObserver = new ResizeObserver(entries => {
        const entry = entries[0];
        const width = entry.contentRect.width; // 获取 div 当前的宽度
        rightElement.classList.remove('small', 'large');
        rightShowcases.forEach((item) => {
          item.classList.remove(...['row', 'column'])
        });
        showcaseImgs.forEach(item => {
          item.style.display = 'block'
        })
        if (width < 900) {
          showcaseImgs.forEach(item => {
            item.style.display = 'none'
          })
        } else {
          showcaseImgs.forEach(item => {
            item.style.display = 'block'
          })
        }
        if (width < 1000) {
          rightElement.classList.add('small');
          rightShowcases.forEach((item) => {
            item.classList.add('row')
          });
          starImgs.forEach(item => {
            item.style.width = '16px';
            item.style.height = '16px';
          })

        } else {
          rightElement.classList.add('large');
          rightShowcases.forEach((item) => {
            item.classList.add('column')
          });
          starImgs.forEach(item => {
            item.style.width = '24px';
            item.style.height = '24px';
          })
        }
      });
      resizeObserver.observe(resizeDiv);
    },
    loadData(data) {

      this.cardsData = [
        {title: '项目总数', num: data.projectTotal, unit: '个', color: '#696CFF', imgName: 'project-num'},
        {
          title: '车辆总数', num: data.carTotal, unit: '辆', color: '#FA6C0D', imgName: 'car-num', payload: {
            title: '车辆总数',
            items: [
              {name: '租赁数', num: data.zlCarNum, unit: '个'},
              {name: '自有数', num: data.selfCarNum, unit: '个'}
            ]
          }
        },
        {
          title: '司机总数',
          num: data.driverTotal,
          unit: '个',
          color: '#6193F1',
          imgName: 'driver-num',
          payload: {
            title: '司机总数',
            items: [
              {name: '出租方配备', num: data.zlDriverNum, unit: '个'},
              {name: '自有数', num: data.selfDriverNum, unit: '个'},
            ]
          }
        },
      ]

      this.cardsData1 = [
        {title: '总行驶里程', num: data.travelTotal, unit: '公里', imgName: 'mile'},
        {title: '出车次数', num: data.cccs, unit: '次', imgName: 'times'},
        {title: '总费用', num: data.totalFee, unit: '万元', imgName: 'fee'}
      ]

      this.feeData = [
        {name: '维修保养费', value: data.wxbyFee},
        {name: '车辆使用费', value: data.clsyFee},
        {name: '车辆租赁费', value: data.clzlFee},
        {name: '司机租赁费', value: data.sjzlFee}
      ]
      this.rateDetailData = [
        {name: '出租方司机', star: data.zlDriverScore},
        {name: '自有司机', star: data.selfDriverScore}
      ]
      let rate = Math.floor(data.driverScoreTotal);
      this.rateData = {
        star: rate,
        text: `${data.driverScoreTotal}星`
      }
      this.titleConfig.text = `${data.totalFee}万元`

      this.$nextTick(() => {
        this.doLayout();
      })
    },
  },
  mounted() {
      this.cardsData = [
        {title: '项目总数', num: 0, unit: '个', color: '#696CFF', imgName: 'project-num'},
        {
          title: '车辆总数', num: 0, unit: '辆', color: '#FA6C0D', imgName: 'car-num', payload: {
            title: '车辆总数',
            items: [
              {name: '租赁数', num: 0, unit: '个'},
              {name: '自有数', num: 0, unit: '个'}
            ]
          }
        },
        {
          title: '司机总数',
          num: 0,
          unit: '个',
          color: '#6193F1',
          imgName: 'driver-num',
          payload: {
            title: '司机总数',
            items: [
              {name: '出租房配备', num: 0, unit: '个'},
              {name: '自有数', num: 0, unit: '个'},
            ]
          }
        },
      ]

      this.cardsData1 = [
        {title: '总行驶里程', num: 0, unit: '公里', imgName: 'mile'},
        {title: '出车次数', num: 0, unit: '次', imgName: 'times'},
        {title: '总费用', num: 0, unit: '万元', imgName: 'fee'}
      ]

      this.feeData = [
        {name: '维修保养费', value: 0},
        {name: '车辆使用费', value: 0},
        {name: '车辆租赁费', value: 0},
        {name: '司机租赁费', value: 0}
      ]

      this.$nextTick(() => {
        this.doLayout();
      })
  }
}
</script>
<style lang="less" scoped>
.frame {
  border-radius: 6px;
  background: #ffffff;
  box-sizing: border-box;
  border: 1px solid #eeeeee;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.03);
}

.header-container {
  display: grid;
  grid-template-columns: 1fr 40px 1fr;
  padding: 12px;
  box-sizing: border-box;
  min-height: 92px;

  .left {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: clamp(12px, 2vw, 20px);
  }

  .split {
    width: 1px;
    height: 60px;
    background: #DEDEDE;
    margin: auto;
  }

  .title-header-right {
    display: grid;

    &.large {
      grid-template-columns: repeat(4, 1fr);
      gap: clamp(12px, 2vw, 70px);
    }

    &.small {
      grid-template-columns: 45% 55%;
      grid-template-rows: repeat(2, 1fr);
      grid-column-gap: 8px;
      grid-row-gap: 12px;
    }

    .comment {
      font-family: Source Han Sans SC;
      font-size: 12px;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0px;
      color: #6193F1;
      white-space: nowrap;
    }
  }

}

.popover-container {
  .popover-title {
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0;
    color: #333333;
    margin-bottom: 10px;
  }

  .popover-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;

    .popover-text {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0px;
      color: rgba(51, 51, 51, 0.6);
    }

    .popover-value {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      text-align: right;
      letter-spacing: 0px;
      color: rgba(0, 0, 0, 0.8456);
    }
  }


}
</style>
