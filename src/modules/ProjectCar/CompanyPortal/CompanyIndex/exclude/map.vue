<template>
  <basic-echarts id="map" :option="option" height="auto" immediate />
</template>

<script>
import BasicEcharts from "@components/Chart/index.vue";
import * as echarts from "echarts";
import chinaJson from "@/assets/china.json"

// 过滤掉海南群岛
chinaJson.features.forEach((feature) => {
  if (feature.properties && feature.properties.name === '海南省') {
    // 保留海南主岛，移除其他部分
    feature.geometry.coordinates = feature.geometry.coordinates.slice(0, 1);
  }
});

// 移除海南群岛边界线
chinaJson.features = chinaJson.features.filter(
  (feature) => feature.properties.adcode !== '100000_JD' // 根据 adcode 过滤
);
echarts.registerMap('china', chinaJson);
export default {
  name: 'Map',
  components: { BasicEcharts },
  data() {
    return {
      emptyTipData: [0, 0, 0, 0, 0, 0, 0],
      option: {
        visualMap: {
          type: 'piecewise',
          min: 0,
          max: 10,
          left: 26,
          showLabel: true,
          inRange: {
            color: ['#B9E4FF', '#0019FA'],
            symbolSize: [30, 100]
          },
          pieces: [
            // { value: 0, label: '缺失值', color: '#F1F1F1' },
            {gte: 0, lte: 0, label: '缺失值', color: '#F1F1F1'},
            {gt: 0, lte: 1, label: '<=1', color: '#B9E4FF'},
            {gt: 1, lte: 2, label: '1~2', color: '#5AC1FF'},
            {gt: 2, lte: 3, label: '2~3', color: '#23A9FF'},
            {gt: 3, lte: 4, label: '3~4', color: '#008CFF'},
            {gt: 4, lte: 5, label: '4~5', color: '#0071FF'},
            {gt: 5, lte: 6, label: '5~6', color: '#0053FF'},
            {gt: 6, lte: 7, label: '6~7', color: '#0035FF'},
            {gt: 7, label: '>7', color: '#0019FA'},
          ]
        },
        tooltip: {
          padding: 4,
          enable: true,
          transitionDuration: 1,
          formatter(params) {
            // params.data 即为 { name: '北京市', value: 123, tipData: [111, 222] }
            const { name, value, data } = params;
            const [projectNum, carNum, xslc, cccs, totalFee, dcMonthAvgFee, dc100KmOilConsume] = data.tipData;
            return `
              <div style="
                border-radius: 4px;
                background: #FFFFFF;
                padding: 10px;
                width: 200px;
                color: #333;
                font-size: 14px;
                font-weight: 200;
              ">
                <div style="font-weight: bold; margin-bottom: 8px;">
                  ${name}
                </div>

                <!-- “项目数” -->
                <div style="
                  display: flex;
                  justify-content: space-between;
                  margin: 4px 0;
                  width: 100%;
                  box-sizing: border-box;
                ">
                  <span style="display: inline-block;">项目数</span>
                  <span style="display: inline-block;">
                    <span style="font-weight: bold; ">${projectNum}</span> 个
                  </span>
                </div>

                <!-- “车辆数” -->
                <div style="
                  display: flex;
                  justify-content: space-between;
                  margin: 4px 0;
                  width: 100%;
                  box-sizing: border-box;
                ">
                  <span style="display: inline-block;">车辆数</span>
                  <span style="display: inline-block;">
                    <span style="font-weight: bold;">${carNum}</span> 辆
                  </span>
                </div>

                <!-- “行驶里程” -->
                <div style="
                  display: flex;
                  justify-content: space-between;
                  margin: 4px 0;
                  width: 100%;
                  box-sizing: border-box;
                ">
                  <span style="display: inline-block;">行驶里程</span>
                  <span style="display: inline-block;">
                    <span style="font-weight: bold;">${xslc}</span> km
                  </span>
                </div>

                <!-- “出车次数” -->
                <div style="
                  display: flex;
                  justify-content: space-between;
                  margin: 4px 0;
                  width: 100%;
                  box-sizing: border-box;
                ">
                  <span style="display: inline-block;">出车次数</span>
                  <span style="display: inline-block;">
                    <span style="font-weight: bold;">${cccs}</span> 次
                  </span>
                </div>

                <!-- “总费用” -->
                <div style="
                  display: flex;
                  justify-content: space-between;
                  margin: 4px 0;
                  width: 100%;
                  box-sizing: border-box;
                ">
                  <span style="display: inline-block;">总费用</span>
                  <span style="display: inline-block;">
                    <span style="font-weight: bold;">${totalFee}</span> 元
                  </span>
                </div>

                <!-- “单辆车月均费用” -->
                <div style="
                  display: flex;
                  justify-content: space-between;
                  margin: 4px 0;
                  width: 100%;
                  box-sizing: border-box;
                ">
                  <span style="display: inline-block;">单辆车月均费用</span>
                  <span style="display: inline-block;">
                    <span style="font-weight: bold;">${dcMonthAvgFee}</span> 元
                  </span>
                </div>

                <!-- “单辆车百公里油耗” -->
                <div style="
                  display: flex;
                  justify-content: space-between;
                  margin: 4px 0;
                  width: 100%;
                  box-sizing: border-box;
                ">
                  <span style="display: inline-block;">单辆车百公里油耗</span>
                  <span style="display: inline-block;">
                    <span style="font-weight: bold;">${dc100KmOilConsume}</span> 升
                  </span>
                </div>
              </div>
            `;
          }
        },
        geo: [
          {
            // 上层地图
            map: 'china',
            aspectScale: 0.9,
            zoom: 1.2,
            layoutCenter: ['50%', '50%'],
            layoutSize: '100%',
            show: true,
            roam: false,
            label: {
              show: false, // 各个省市县的名字
              color: '#fff',
            },
            itemStyle: {
              areaColor: '#B9E4FF',
              borderWidth: 1,
              borderColor: '#ffffff',
              emphasis: {
                areaColor: '#B9E4FF'
              }
            },
            z: 2
          },
          {
            // 下层“投影”地图
            type: 'map',
            map: 'china',
            zlevel: -1,
            aspectScale: 0.9,
            zoom: 1.2,
            layoutCenter: ['50%', '51%'],
            layoutSize: '100%',
            roam: false,
            silent: true,
            itemStyle: {
              areaColor: '#62A2CA',
              borderColor: '#62A2CA',
              shadowColor: '#62A2CA',
              shadowBlur: 20, // 阴影模糊程度，值越大阴影越扩散
              shadowOffsetX: 0, // 阴影在水平方向的偏移
              shadowOffsetY: 5, // 阴影在垂直方向的偏移
              borderWidth: 1
            },
            z: 1
          }
        ],
        series: [
          {
            type: 'map',
            map: 'china',
            geoIndex: 0, // 绑定到上层
            data: []
          }
        ]
      },
      originAreaData: [
        {
          name: '北京市',
          value: 0,
        },
        {
          name: '天津市',
          value: 0,
        },
        {
          name: '上海市',
          value: 0,
        },
        {
          name: '重庆市',
          value: 0,
        },
        {
          name: '河北省',
          value: 0,
        },
        {
          name: '河南省',
          value: 0,
        },
        {
          name: '云南省',
          value: 0,
        },
        {
          name: '辽宁省',
          value: 0,
        },
        {
          name: '黑龙江省',
          value: 0,
        },
        {
          name: '湖南省',
          value: 0,
        },
        {
          name: '安徽省',
          value: 0,
        },
        {
          name: '山东省',
          value: 0,
        },
        {
          name: '新疆维吾尔自治区',
          value: 0,
        },
        {
          name: '江苏省',
          value: 0,
        },
        {
          name: '浙江省',
          value: 0,
        },
        {
          name: '江西省',
          value: 0,
        },
        {
          name: '湖北省',
          value: 0,
        },
        {
          name: '广西壮族自治区',
          value: 0,
        },
        {
          name: '甘肃省',
          value: 0,
        },
        {
          name: '山西省',
          value: 0,
        },
        {
          name: '内蒙古自治区',
          value: 0,
        },
        {
          name: '陕西省',
          value: 0,
        },
        {
          name: '吉林省',
          value: 0,
        },
        {
          name: '福建省',
          value: 0,
        },
        {
          name: '贵州省',
          value: 0,
        },
        {
          name: '广东省',
          value: 0,
        },
        {
          name: '青海省',
          value: 0,
        },
        {
          name: '西藏自治区',
          value: 0,
        },
        {
          name: '四川省',
          value: 0,
        },
        {
          name: '宁夏回族自治区',
          value: 0,
        },
        {
          name: '海南省',
          value: 0,
        },
        {
          name: '台湾省',
          value: 0,
        },
        {
          name: '香港特别行政区',
          value: 0,
        },
        {
          name: '澳门特别行政区',
          value: 0,
        },
      ]
    }
  },
  created() {
    this.originAreaData.forEach((item) => {
      item.tipData = this.emptyTipData;
    })
    this.$set(this.option.series[0], 'data', this.originAreaData)
  },
  methods: {
    reloadData(mapData) {
      // 遍历初始数据列表，将查询结果替换
      let list = this.originAreaData.map(item => {
        const cityData = mapData[item.name];
        if (cityData) {
          item.value = cityData.projectNum;

          item.tipData = [
            cityData.projectNum,
            cityData.carNum,
            cityData.xslc,
            cityData.cccs,
            cityData.totalFee,
            cityData.dcMonthAvgFee,
            cityData.dc100KmOilConsume
          ];
        } else {
          item.value = 0;
          item.tipData = this.emptyTipData;
        }
        return item;
      });
      // 更新图表
      this.$set(this.option.series[0], 'data', list);


      // 2. 找出新的 max 值
      let newMax = 0;
      list.forEach(item => {
        if (item.value > newMax) {
          newMax = item.value;
        }
      });

      // 4. 更新 visualMap.max
      this.$set(this.option.visualMap, 'max', newMax);

    }
  }

}
</script>
