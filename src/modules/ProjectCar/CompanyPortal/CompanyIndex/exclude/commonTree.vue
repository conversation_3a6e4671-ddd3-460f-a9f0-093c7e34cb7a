<template>
  <div
    :style="{padding: isCollapsed? '0px 8px' : '14px 18px',height}"
    class="tree-selector"
  >
    <!-- 树结构主体 -->
    <transition name="fade">
      <div v-if="!isCollapsed" key="open" class="flex flex-column">
        <div class="tree-header flex col-center row-between">
          <span class="tab-title" style="color: #333">{{ title }}</span>
          <span class="fks-input__icon fks-icon-search" @click="hanldeClick"></span>
        </div>
        <div v-if="isClick" class="tree-search">
          <fks-input  v-model="filterText" placeholder="请输入搜索内容"></fks-input>
        </div>
        <!-- 树结构 -->
        <div class="tree-selector__body flex-grow-1">
          <fks-tree
            :data="treeData"
            :props="defaultProps"
            :expand-on-click-node="false"
            default-expand-all
            highlight-current
            node-key="id"
            :show-checkbox="multiple"
            :filter-node-method="filterNodeMethod"
            @check="handleCheck"
            @node-click="hanldeClickNode"
            ref="tree"
          >
            <template slot-scope="{ node, data }">
              <span
                :title="node.label"
                class="custom-tree-node"
              >
                 {{ LANG[node.label] ? LANG[node.label] : node.label }}
                <card-tag
                  v-if="showTag && data.code"
                  :tag="{'text': '通用','color':'#3C83FF'}"
                  style="line-height: 8px;margin-left: 8px;"
                />
              </span>
            </template>
          </fks-tree>
        </div>

        <!-- 收起/展开按钮 -->
        <div class="tree-selector__toggle-btn" @click="toggleCollapse(true)" v-show="showArrow">
          <img alt="" src="@/assets/img/companyIndex/right-arrow.svg"
               style="transform: rotate(180deg)"/>
        </div>
      </div>
      <div v-else key="collapse" class="collapsed-container" @click="toggleCollapse(false)">
        <div>
          <span>{{ btnText }}</span>
          <img alt="" height="16" src="@/assets/img/companyIndex/org.svg" width="16" v-if="btnText === '组织机构'"/>
        </div>
        <img
          alt=""
          style="margin-top: 20px"
          src="@/assets/img/companyIndex/right-arrow.svg"
        />
      </div>
    </transition>

  </div>
</template>

<script>
import globalSearchMixin from "@/mixins/globalSearchMixin";
import {mapState} from "vuex";
import * as StateTypes from "@store/State/stateTypes";
import CardTag from "@components/CardFlow/components/tag.vue";

export default {
  name: 'commonTree',
  mixins: [globalSearchMixin],
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
    showTag: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: [String, Number, Array],
      default: null,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    isCollapsed: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: '50%'
    },
    searchValue: {
      type: String,
      default: ''
    },
    currId: {
      default: ''
    },
    btnText: {
      default: '菜单管理'
    },
    showArrow: {
      type: Boolean,
      default: true
    },
    defaultProps: {
      type: Object,
      default:() => ({
        children: 'children',
        label: 'name',
      })
    },
    LANG: {
      type: Object,
      default:() => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  components: {
    CardTag
  },
  data() {
    return {
      showSearch: false,
      // 用于控制是否收起
      keyword: '',
      selectedValue: this.modelValue, // 本地维护选中的值
      treeHeight: 0,
      isClick: false,
      filterText: ''
    };
  },

  computed: {
    // filterText: {
    //   get() {
    //     return this.searchValue
    //   },
    //   set(val) {
    //     this.$emit('update:searchValue',val)
    //   }
    // }
  },
  mounted() {
    // console.log('===mounted====')
    this.$nextTick(() => {
      this.getPcMainHeight();
    })
    window.addEventListener('resize', this.getPcMainHeight); // 监听窗口大小变化
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getPcMainHeight); // 清理监听器
  },
  methods: {
    filterNodeMethod(value, data) {
      if (!value) return true;
      const labelProp = this.defaultProps.label
      const label = this.LANG[data[labelProp]] ? this.LANG[data[labelProp]] : data[labelProp]
      return label.indexOf(value) !== -1;
    },
    hanldeClick() {
      this.isClick = !this.isClick
    },
    // 勾选时触发
    handleCheck(checkedNodes, treeData) {
      // 获取当前树的所有叶子节点
      const allCheckNods = treeData.checkedNodes;
      let checkedLeafNode = allCheckNods.filter(node => !node.children || node.children.length <= 0);
      let projectIds = checkedLeafNode.map(node => node.id)
      this.$emit('choose-project', projectIds);
    },
    hanldeClickNode(node) {
      this.$emit('hanldeClickNode',node)
    },
    /**
     * 递归获取所有叶子节点
     * @param {Array} data - 树结构数据
     * @return {Array} - 返回所有叶子节点数组
     */
    getAllLeafNodes(data) {
      let leaves = [];
      data.forEach((item) => {
        if (!item.children || item.children.length === 0) {
          // 没有 children 或 children 为空 => 叶子节点
          leaves.push(item);
        } else {
          // 递归处理子节点
          leaves = leaves.concat(this.getAllLeafNodes(item.children));
        }
      });
      return leaves;
    },
    // 切换 isCollapsed
    toggleCollapse(value) {
      this.$emit('update:isCollapsed', value);
    },
    getPcMainHeight() {
      let height = 600;
      let pcMain = document.getElementById("pcMain");
      if (pcMain) {
        height = pcMain.offsetHeight - 66 - 15;
      }
      this.treeHeight = height;
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  }
};
</script>

<style lang="less" scoped>
/* 进入和离开的过渡效果 */
/* 进入和离开的过渡效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.fade-enter-to, .fade-leave {
  opacity: 1;
}


.tree-selector {
  /* 外层容器尽量包含足够的宽度，方便切换 */
  font-size: 14px;
  max-width: 297px;
  height: 100%;
  box-sizing: border-box;
  border-radius: 0 6px 6px 0; /* 顺时针设置 */
  border: 1px solid #EEEEEE;
  border-left: none; /* 移除左边框 */
  background-color: #fffffd;
  display: flex;
  flex-direction: row; /* 注意这里用 row 横向布局，左边树 + 右边按钮 */
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}

//通过自定义树形结构行内容，实现文本过多时数据不显示的问题，现在效果为显示省略号，且鼠标移上去会显示出全部文本内容
.custom-tree-node {
  width: 100%;
  overflow: hidden !important; // 溢出部分隐藏
  white-space: nowrap !important; //禁止自动换行
  text-overflow: ellipsis !important; // 使溢出部分以省略号显示
  display: block !important;
}

/* 树区域 */
.tree-selector__body {
  overflow-y: auto;
  max-height: calc(100vh - 220px);
  width: 260px;
  box-sizing: border-box;
}

/* 收起/展开 按钮 */
.tree-selector__toggle-btn {
  /* 让按钮贴在右边中间位置，也可以贴在左边、顶部、底部...随需求 */
  width: 18px;
  height: 56px;
  position: absolute; /* 关键点 */
  top: 50%; /* 定位到容器顶部的 50% */
  right: 0; /* 紧贴右侧 */
  transform: translateY(-50%); /* 向上移动自身高度的一半，实现垂直居中 */

  /* 视觉和交互细节 */
  cursor: pointer;
  background-image: url('../../../../../assets/img/companyIndex/trapezoidal.svg');
  background-repeat: no-repeat; /* 防止图片重复 */
  background-position: center; /* 确保图片居中 */
  background-size: auto; /* 图片保持比例缩放到合适大小 */

  display: flex;
  align-items: center;
  padding-bottom: 5px;
  box-sizing: border-box;
}

.collapsed-container {
  display: flex; /* 使用 Flexbox */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  flex-direction: column; /* 子元素纵向排列 */
  height: 100%; /* 必须明确父容器高度 */
  width: 100%;
  margin: 0 auto;
  cursor: pointer;
  user-select: none;

  div {
    width: 16px;

    span {
      margin-bottom: 4px;
      display: inline-block;
    }
  }
}

.tree-header {
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0px;
  color: #333333;
}

.tree-search {
  margin-bottom: 6px;
  margin-top: 4px;
}

.tree-search .fks-input--medium .fks-input__inner {
   line-height: 32px !important;
   height: 32px !important;
 }
</style>
