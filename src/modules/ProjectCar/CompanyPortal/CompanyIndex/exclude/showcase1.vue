<template>
  <fks-popover
    :width="popWidth"
    placement="bottom"
    :trigger="$slots.popover ? 'hover' : 'manual'"
  >
    <div
      slot="reference"
      :style="{
        border: `1px solid ${getBorderColor(config.color)}`,
        background: getBackgroundColor(config.color)
      }"
      class="showcase-container cursor-pointer"
    >
      <img :src="require(`@/assets/img/companyIndex/${config.imgName}.png`)" alt="#"
           class="showcase1-img"
           width="40px"/>
      <div class="detail">
        <div class="title">{{ config.title }}</div>
        <div class="content">
          <div :style="{ color: config.color }" class="num">{{ config.num }}</div>
          <div class="unit">{{ config.unit }}</div>
        </div>
      </div>
    </div>
    <slot name="popover" />
  </fks-popover>

</template>

<script>
import {hexToRgba} from "@utils/util";

export default {
  name: 'showcase1',
  props: {
    config: {
      type: Object,
      required: true
    },
    popWidth: {
      type: String,
      default: '160'
    }
  },
  methods: {
    getBorderColor(color) {
      return hexToRgba(color, 0.2)
    },
    getBackgroundColor(color) {
      const color1 = hexToRgba(color, 0);
      const color2 = hexToRgba(color, 0.1);
      return `linear-gradient(270deg, ${color1} 0%, ${color2} 100%)`
    }
  }
}
</script>

<style lang="less" scoped>
.showcase-container {
  border-radius: 6px;
  box-sizing: border-box;
  padding: 5%;
  display: flex;
  align-items: center;
  max-width: 200px;

  .showcase1-img {
    margin-right: 15px;
  }

  .detail {
    height: 50px;

    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .title {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0px;
      color: #333333;
      white-space: nowrap;
    }

    .content {
      display: flex;
      align-items: baseline;

      .num {
        font-size: 20px;
        font-weight: bold;
        line-height: normal;
        letter-spacing: 0px;
      }

      .unit {
        font-family: Source Han Sans;
        font-size: 12px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0px;
        color: #333333;
        margin-left: 4px;
      }
    }
  }
}
</style>
