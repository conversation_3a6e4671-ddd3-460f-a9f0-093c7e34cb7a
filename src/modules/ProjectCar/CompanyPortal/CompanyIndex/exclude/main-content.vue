<template>
  <main class="flex-grow-1 frame"
        v-loading="loading">
<!--    <fks-select class="select" size="mini" v-model="selection">-->
<!--      <fks-option label="按区域" value="region" />-->
<!--    </fks-select>-->
    <div class="full-height">
      <china-map ref="map" />
    </div>
  </main>
</template>

<script>
import ChinaMap from './map.vue'
export default {
  name: 'MainContent',
  components: { ChinaMap },
  props: {
    loading: Boolean,
  },
  data() {
    return {
      selection: 'region',
      mapData: {}
    }
  },
  methods: {
    loadData(data) {
      this.mapData = data
      this.$refs.map.reloadData(data);
    }
  }
}
</script>

<style lang="less" scoped>
main {
  margin-top: 20px;
  position: relative;
  box-sizing: border-box;
}

.select {
  position: absolute;
  width: 150px;
  top: 10px;
  left: 10px;
  z-index: 10;
}

.frame {
  border-radius: 6px;
  background: #ffffff;
  box-sizing: border-box;
  border: 1px solid #eeeeee;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.03);
}
</style>
