<template>
  <div>
    <TitleKit>
      <template #left>
        <div style="font-size: 16px; font-weight: 600">车辆列表</div>
      </template>
    </TitleKit>
    <TempTable
      :data-list="tableData"
      :table-config.sync="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      @add-item="handleAdd"
      @delete-item="handleDelete"
      @batch-add="handleBatchAdd"
      @export-data="handleExport"
      @searchData="searchData"
      @clickRow="handleClickRow"
      :showButtons="showButtons"
      :current-page.sync="pageNo"
    >
      <template v-slot:column-attachment1="{ scope }">
        <TextListUpload v-if="!loading" v-model="scope.row.attachment1" :disabled="true" />
      </template>

      <template v-slot:column-carStatus="{scope}">
        <card-tag v-if="scope.row.carStatus"  class="p-24 p-t-8 p-b-8" :tag="{color: getTagType(scope.row.carStatus), text: getTagText(scope.row.carStatus, 'carStatus')}" />
        <!--        <fks-tag v-if="scope.row.carStatus" :type="getTagType(scope.row.carStatus)">{{ getTagText(scope.row.carStatus, 'carStatus') }}</fks-tag>-->
      </template>
    </TempTable>

    <fks-drawer
      :before-close="beforeClose"
      :visible.sync="visible"
      :wrapperClosable="true"
      :modal="false"
      class="dialog"
      direction="rtl"
      size="880px"
      @open="onDrawerOpen"
    >
      <header class="card-header drawer-header" slot="title">
        <div class="flex col-center m-b-24">
          <div class="card-title" style="color: #333333" :key="currentData.carNum">
            {{ currentData.carNum }}
          </div>
          <div class="m-l-20">
            <card-tag v-for="(tag, index) in currentData.tags" :key="index" :tag="tag" />
            <card-tag class="m-l-10" :tag="{ color: '#3C83FF', text: currentData.projectName }" />
          </div>
        </div>
      </header>
      <detail-form
        :type.sync="type"
        :current-data="currentData"
        :show-header="false"
        @refreshItem="clear"
        @refresh="reSearchData"
        ref="detailForm"
      />
    </fks-drawer>
    <add-car-manage ref="add" @mutate="reSearchData"></add-car-manage>
    <batch-add-car-manage ref="batchAdd" @mutate="reSearchData" @openDialog="openDialog"></batch-add-car-manage>
    <UserForm ref="userForm" @refresh="reSearchData" />
    <fks-dialog
      title="关闭"
      :visible.sync="tipVisible"
      size="small"
    >
      <div style="padding: 16px 20px">
        <span>表单内容发生改变是否继续关闭?</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="handelCancle">取 消</fks-button>
        <fks-button type="primary" @click="onConfirmTip">确 定</fks-button>
      </span>
    </fks-dialog>

  </div>
</template>

<script>
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import UserForm from '@modules/ProjectCar/CompanyPortal/UserManagement/components/UserForm.vue'

import {
  batchDeleteDriver, exportDriverInfo,
  getCar,
  getDriverList
} from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import { mapGetters, mapState } from 'vuex'
import BatchAddCarManage from '@modules/ProjectCar/ProjectPortal/CarManage/components/BatchAdd.vue'
import AddCarManage from '@modules/ProjectCar/ProjectPortal/CarManage/components/Add.vue'
import {
  deleteCar,
  getCarList,
  getDriverInfoByCar,
  getCarSearchParam,
  getCarListByPost,
  batchDeleteCar, exportCarInfo
} from '@modules/ProjectCar/ProjectPortal/CarManage/api'
import DetailForm from '@modules/ProjectCar/ProjectPortal/CarManage/components/DetailForm.vue'
import TextListUpload from '@modules/FormCenter/components/FormUpload/TextListUpload.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import { fileReader } from '@utils/exportFile'
import * as StateTypes from "@store/State/stateTypes";
import globalSearchMixin from "@/mixins/globalSearchMixin";
import * as GetterTypes from '@store/Getter/getterTypes'
export default {
  name: 'CarManage',
  mixins: [globalSearchMixin],
  components: {
    TitleKit,
    CardTag,
    TextListUpload,
    DetailForm,
    AddCarManage,
    BatchAddCarManage,
    TempTable,
    UserForm,
  },
  data() {
    return {
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['add', 'batchAdd', 'delete', 'export', 'filter', 'filedConfig'],
      queryParams: {
        projectId: null,
        conditions: [],
      },
      visible: false,
      tableData: [],
      currentData: {},
      type: 'view',
      selectedFields: [
      ],
      tableConfig: [
        // {
        //   label: '用车类型',
        //   prop: 'projectUseCarType',
        //   group: '项目属性',
        //   width: '150px',
        //   enums: this.carResource,
        // },
        {
          label: '项目名称',
          prop: 'projectName',
          group: '项目信息',
          width: '150px',
        },
        {
          label: '车牌号',
          prop: 'carNum',
          group: '车辆信息',
          width: '120px',
          clickable: true,
        },
        {
          label: '品牌型号',
          prop: 'carType',
          group: '车辆信息',
          width: '150px',
        },
        {
          label: '座位数',
          prop: 'carSeatNum',
          group: '车辆信息',
          width: '100px',
        },
        {
          label: '车辆来源',
          prop: 'carResource',
          group: '车辆信息',
          width: '150px',
          enums: this.carResource,
        },
        {
          label: '出租方名称',
          prop: 'carCompName',
          group: '车辆信息',
          width: '150px',
        },
        {
          label: '司机名称',
          prop: 'driverFullName',
          group: '司机信息',
          width: '150px',
          default: "暂未绑定"
        },
        {
          label: '司机联系电话',
          prop: 'driverPhone',
          group: '司机信息',
          width: '200px',
        },
        {
          label: '车龄(年)',
          prop: 'carAge',
          group: '车辆信息',
          width: '120px',
        },
        {
          label: '累计行驶里程',
          prop: 'carXslcLj',
          group: '车辆信息',
          width: '150px',
        },
        {
          label: '车辆状态',
          prop: 'carStatus',
          group: '状态信息',
          // width: '120px',
          customer: true,
        },

        // {
        //   label: '车辆名称',
        //   prop: 'carName',
        //   group: '车辆信息',
        //   width: '150px',
        // },
        // {
        //   label: '月度租赁价格',
        //   prop: 'carZlFy1',
        //   group: '租赁信息',
        //   width: '130px',
        // },
        // {
        //   label: '购置时间',
        //   prop: 'carBuyTime',
        //   group: '车辆信息',
        //   width: '180px',
        //   formatDate: true,
        // },
        // {
        //   label: '入场日期',
        //   prop: 'carRcTime1',
        //   group: '车辆信息',
        //   width: '180px',
        //   formatDate: true,
        // },
        //
        // {
        //   label: '初始行驶里程',
        //   prop: 'carXslcCs',
        //   group: '车辆信息',
        //   width: '120px',
        // },
        //
        // {
        //   label: '租赁开始时间',
        //   prop: 'carZlTime1',
        //   group: '租赁信息',
        //   width: '180px',
        //   formatDate: true,
        // },
        // {
        //   label: '租赁结束时间',
        //   prop: 'carZlTime2',
        //   group: '租赁信息',
        //   width: '180px',
        //   formatDate: true,
        // },
        //
        // {
        //   label: '车架号',
        //   prop: 'carCjh',
        //   group: '车辆信息',
        //   width: '150px',
        // },
        // {
        //   label: '发动机号',
        //   prop: 'carFdjh',
        //   group: '车辆信息',
        //   width: '150px',
        // },
        // {
        //   label: '里程补贴标准',
        //   prop: 'carLcbtbz',
        //   group: '租赁信息',
        //   width: '130px',
        // },
        // // {
        // //   label: '附件（车辆照片、行驶证等）',
        // //   prop: 'attachment1',
        // //   group: '附件信息',
        // //   width: '250px',
        // //   customer: true,
        // // },
        // {
        //   label: '退场日期',
        //   prop: 'carTcTime1',
        //   group: '车辆信息',
        //   width: '130px',
        //   formatDate: true,
        // },
        // {
        //   label: '退场事由',
        //   prop: 'carTcSy',
        //   group: '车辆信息',
        //   width: '150px',
        // },
        // {
        //   label: '备注',
        //   prop: 'remark1',
        //   group: '备注信息',
        //   width: '150px',
        // },
      ],

      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      tipVisible: false,
      type1: ''
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    isCompanyPortal() {
      return this.portal.name === '数据舱'
    },
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    carResource() {
      return this.enums.CarResourceEnums
    },
    carStatus() {
      return this.enums.CarStatusEnums
    },
    projectCarType() {
      return this.enums.ProjectUseCarTypeEnums
    },
  },
  created() {
    this.selectedFields = this.tableConfig.map(item => item.prop)
    this.tableConfig.forEach((item) => {
      if (item.prop === 'carStatus') {
        item.enums = this.carResource
      }
      if (item.prop === 'carResource') {
        item.enums = this.carResource
      }
      if (item.prop === 'projectUseCarType') {
        item.enums = this.projectCarType
      }
    })
  },
  mounted() {
    // this.queryParams.projectId = this.portal.id
    this.queryParams.projectId = this.isCompanyPortal ? '' : this.portal.id
    this.getData(this.queryParams)
    getCarSearchParam().then((res) => {
      this.searchConfigs = res.data
    })
  },
  methods: {
    openDialog() {
      this.tipVisible = true
      this.type1 = 'batchAdd'
    },
    handelCancle() {
      this.tipVisible = false
      this.type1 = 'add'
    },
    onConfirmTip() {
      this.visible = false
      this.$refs.batchAdd.visible = false
      this.tipVisible = false
      if (this.type1 === 'batchAdd') {
        this.$refs.batchAdd.cleanData()
      }
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        sums[index] = (<p>
          <span style="color: red">合计1</span>
          <br/>
          <span>合计2</span>
        </p>)
      });

      return sums;
    },
    globalSearch() {
      this.tableData = [];
      this.reSearchData();
    },
    reSearchData(refresh) {
      if (!refresh || refresh == false) {
        this.pageNo = 1
      }
      this.getData(this.queryParams)
    },
    async handleDelete(rows) {
      if (rows && rows.length > 0) {
        let ids = rows.map((item) => {
          return {
            driverId: item.driverId,
            carId: item.id,
          }
        })
        let res = await batchDeleteCar(ids)
        if (res.status) {
          this.$message.success('删除成功')
          this.reSearchData(true)
        }
      }
    },
    handleBatchAdd() {
      this.$refs.batchAdd.open()
    },
    handleExport(list) {
      let data = {
        pageGetParam: {...this.queryParams},
        fields: list
      }
      exportCarInfo(data).then(res => {
        try {
          fileReader(res)
        } catch (error) {
          console.error("导出失败:", error);
          alert("导出失败，请稍后重试。");
        }
      })
    },
    handleAdd() {
      this.$refs.add.open({}, 'plus')
    },
    handleClickRow(row, prop) {
      this.visible = true
      this.type = 'view'
      this.currentData = { ...row }
      if (this.$refs.detailForm) this.$refs.detailForm.type = 'view'
      // this.$refs.userForm.open(row, 'edit')
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    async getData(query) {
      this.loading = true
      this.queryParams = { ...this.queryParams, ...query }
      try {
        const params = {
          ...this.queryParams,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
        }
        const res = await getCarListByPost(params, this[GetterTypes.GET_GLOBAL_STATE])
        if (!res.status) {
          return false
        }
        this.total = res.data.total
        this.finished = res.data.isLastPage
        // 获取司机信息
        const carIds = res.data.list.map((item) => item.id).join(',')
        let tableData = [...res.data.list]
        if (carIds.length > 0) {
          const { data: driverInfos } = await getDriverInfoByCar(carIds)
          const newData = res.data.list.map((item) => {
            const driverInfo = driverInfos[item.id] || {}
            return {
              ...item,
              ...{
                driver: driverInfo.id,
                driverPhone: driverInfo.driverPhone,
                driverFullName: driverInfo.driverFullName,
              },
            }
          })
          tableData = [...newData]
        }
        this.tableData = [...tableData]
        this.loading = false
      } catch (e) {
        this.loading = false
      }
    },

    getTagText(value, prop) {
      if (prop === 'carStatus') {
        return this.carStatus.find(item => item.key == value).value;
      }
      return value;
    },
    getTagType(value) {
      let status = {
        100: '#40bb5a',
        200: '#ffa01e',
        300: '#ff4d4f',
        400: '#909399',
        // 100: 'success',
        // 200: 'warning',
        // 300: 'danger',
        // 400: 'info',
      };
      return status[value];
    },
    formatDate(row, column) {
      console.log('时间', row, column)
      if (!value) return '' // 如果值为空，返回空字符串
      const date = new Date(value)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}` // 返回格式化后的日期字符串 "YYYY-MM-DD"
    },

    onDrawerOpen() {
    },
    beforeClose(done) {
      const type = this.$refs.detailForm.type
      if (type === 'view') {
        this.visible = false
        done()
      }
      if (type === 'edit') {
        // 基本信息 - 保险信息 - 年检信息 - 通行证信息 - 违章信息
        const formData = this.$refs.detailForm.$refs.CarManageBaseInfo.formData
        const backFromData = this.$refs.detailForm.$refs.CarManageBaseInfo.backFromData
        const flag1 = !this.isEqual(formData,backFromData)
        if (flag1) { // 内容发送变化
          this.tipVisible = true
        } else {
          this.visible = false
          done()
        }
      }
    },
    isEqual(data1,data2) {
      let flag = true
      for (let key in data1) {
        if (key in data2 && data2[key] === data1[key]) continue
        flag = false
      }
      return flag
    },
    clear() {
      this.visible = false
      this.currentData = {}
      this.pageNo = 1
      this.getData(this.queryParams)
    },
  },
}
</script>

<style scoped lang="less">
@import '~@/styles/headers';

.drawer-header {
  padding: 24px 0 0 24px;
}
</style>
