<template>
  <CardFlow
    v-if="menuInfo"
    :cards="cards"
    :loading="loading"
    :finished="true"
    @indexChange="handleIndexChange"
    ref="cardFlowRef"
  >
    <template v-slot:card="{card}">
      <showcase v-bind="card"/>
    </template>
    <template slot="panel">
      <div v-if="currentMenus">
        <header class="card-header">
          <div class="flex col-center m-b-24">
            <div class="card-title">{{ currentMenus.title }}</div>
          </div>
        </header>
        <common-title style="margin: 18px 0" title="菜单列表"/>
        <fks-table
          style="max-width: 900px;"
          :data="currentMenus.authList"
          :header-cell-style="{background: 'transparent', color: '#333333aa !important', fontWeight: 'unset !important'}"
          :cell-style="{color: '#333333 !important'}"
          max-height="300"
        >
          <fks-table-column align="center" label="#" type="index" width="60"/>
          <fks-table-column label="菜单名称" prop="menuName"/>
          <fks-table-column label="菜单ID" prop="menuId"/>
          <fks-table-column label="创建时间">
            <template slot-scope="{row}">
              {{ $dayjs(row.createDate).format('YYYY-MM-DD HH:mm') }}
            </template>
          </fks-table-column>
        </fks-table>
      </div>
      <fks-empty class="full-height" v-else description="暂无菜单信息"/>
    </template>
  </CardFlow>
</template>

<script>
import {
  getCompanyRoleList,
  getRoleMenuList
} from "@modules/ProjectCar/CompanyPortal/UserManagement/api";
import CardFlow from "@components/CardFlow/index.vue";
import Showcase from "@components/CardFlow/components/showcase.vue";
import CardTag from "@components/CardFlow/components/tag.vue";

export default {
  name: "AuthManagement",
  components: {CardTag, Showcase, CardFlow},
  data() {
    return {
      tableData: [],
      loading: false,
      menuInfo: null,
      visible: false,
      currentMenus: null,
      cards: null
    }
  },
  methods: {
    handleIndexChange(row) {
      this.$refs.cardFlowRef && this.$refs.cardFlowRef.disableContentLoading();
      if (this.menuInfo[row.id]) {
        this.visible = true;
        this.currentMenus = {...row, authList: this.menuInfo[row.id]}
      } else {
        this.currentMenus = null
      }
    }
  },
  created() {
    this.loading = true;
    getCompanyRoleList().then(res => {
      if (res.status) {
        this.tableData = res.data;
        const ids = res.data.map(item => item.id).join(',')
        getRoleMenuList(ids).then(res => {
          if (res.status) {
            this.menuInfo = res.data;
            this.cards = this.tableData.map(item => {
              return {
                ...item,
                title: item.roleName
              }
            })
            this.$nextTick(()=>{
              this.$refs.cardFlowRef.disableContentLoading();
            })
          }
        })
      }
    }).then(() => {
      this.loading = false
    })
  }
}
</script>

<style lang="less" scoped>
@import "~@/styles/headers";

.table {
  overflow-y: auto;
}

.dialog-title {
  font-size: 32px;
}
</style>
