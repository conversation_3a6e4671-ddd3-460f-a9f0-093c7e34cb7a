import request from '@utils/request'
import storage from "@utils/storage";

// 添加常见问题
export function addFaqInfo (data) {
  return request({
    url: '/vehicle-dispatch/vd/faq/add',
    method: 'post',
    data: data,
  })
}

// 修改常见问题
export function modifyFaqInfo (data) {
  return request({
    url: '/vehicle-dispatch/vd/faq/update',
    method: 'post',
    data: data,
  })
}

// 删除常见问题
export function delFaqInfo (ids) {
  return request({
    url: '/vehicle-dispatch/vd/faq/delete',
    method: 'post',
    params: {
      ids
    },
  })
}

// 常见问题搜索参数列表
export function getFaqPageParam(params) {
  return request({
    url: '/vehicle-dispatch/vd/faq/page/getParam',
    params
  })
}

// 常见问题分页列表
export function getFaqPage(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/faq/page',
    method: 'post',
    data,
    params
  })
}
