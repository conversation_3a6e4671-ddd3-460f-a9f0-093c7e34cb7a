

.sub-title {
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
    color: rgba(0, 0, 0, 0.8483);
    padding-left: 14px; /* 稍微增加 padding-left，给 before 元素留空间 */
    position: relative; /* 确保伪元素定位 */
}

.sub-title::before {
    content: ''; /* 确保伪元素可见 */
    height: 15px;
    width: 4px;
    border-radius: 2px;
    opacity: 1;
    background: #5483F7;
    position: absolute; /* 使用绝对定位 */
    left: 0; /* 向左对齐 */
    top: 53%; /* 垂直居中对齐 */
    transform: translateY(-50%); /* 完全居中对齐 */
}

.info-item {
    margin: 10px 0;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
}

.item-label {
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #333333;
    width: 250px;
}
.fb-item-content {
    flex: 1;
    max-width: calc(100% - 250px);
    word-break: break-word;
    font-size: 14px !important;
    display: flex;
    justify-content: flex-start;
}

.comment-list {
    /*overflow-y: auto;   !* y 轴滚动 *!*/
    overflow-x: hidden; /* 禁止 x 轴滚动 */
    /*max-height: 400px;  !* 举例：你可以根据需要设置一个最大高度 *!*/
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.comment-item {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    padding-left: 18px;
}

/* 单独做竖线 */
.comment-item::before {
    content: "";
    position: absolute;
    top: 0;
    left: 6px;
    width: 1px;
    height: 100%;
    background: rgba(60, 131, 255, 0.1);
    transform: translateY(10px); /* 单独下移 */
}

.comment-dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left: 0;
    top: 0;
    /*transform: translateY(4px); !* 单独下移 *!*/
}

/* 默认情况 */
.comment-item:last-of-type::before {
    display: none;
}

/* 如果是唯一一条（只有一条 item 时）就不隐藏 */
.comment-item:only-of-type::before {
    display: block;
}


.comment-content {
    padding: 10px 12px;
    min-width: 680px;
    min-height: 80px;
    border-radius: 4px;
    background: #F4F4F480;
    margin-top: 10px;
}
