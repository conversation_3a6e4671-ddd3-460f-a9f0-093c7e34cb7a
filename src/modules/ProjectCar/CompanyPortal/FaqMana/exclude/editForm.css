
.faq-form-dialog {
    width: 1000px;
    max-width: 80%;
    border-radius: 4px;
    .fks-dialog__body {
        padding-left: 26px;
    }
    .fks-dialog__footer {
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.0301);
        border-top: 1px solid #DFE0E2;
        padding: 12px 20px;
    }


    .sub-btn {
        padding: 5px 10px;
        color: #333 !important;
        border-color: #cccccc;
        transition: all 0.3s ease; /* 平滑过渡效果 */
    }

    .sub-btn:hover {
        color: #333 !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
    }

    .sub-btn:active {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
        transform: translateY(5px); /* 模拟点击下沉效果 */
    }

    .dialog-footer {
        text-align: right;
        background: #FFFFFF;
    }


    .readonly-text {
        padding: 4px 0;
        line-height: 1.6;
        white-space: pre-wrap;
        color: #333;
        min-height: 88px; /* 对齐输入框高度 */
    }

    /* 调整 label 垂直对齐顶部 */
    .readonly-item >>> .fks-form-item__label {
        align-self: flex-start;
        padding-top: 4px; /* 适配文字起始位置 */
    }


    .sub-title {
        font-size: 16px;
        font-weight: normal;
        line-height: normal;
        color: rgba(0, 0, 0, 0.8483);
        padding-left: 14px; /* 稍微增加 padding-left，给 before 元素留空间 */
        position: relative; /* 确保伪元素定位 */
    }

    .sub-title::before {
        content: ''; /* 确保伪元素可见 */
        height: 15px;
        width: 4px;
        border-radius: 2px;
        opacity: 1;
        background: #5483F7;
        position: absolute; /* 使用绝对定位 */
        left: 0; /* 向左对齐 */
        top: 53%; /* 垂直居中对齐 */
        transform: translateY(-50%); /* 完全居中对齐 */
    }

}


