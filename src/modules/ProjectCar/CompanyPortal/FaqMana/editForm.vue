<template>
  <fks-dialog
    title="常见问题"
    v-if="visible"
    :visible.sync="visible"
    :close-on-click-modal="false"
    custom-class="faq-form-dialog"
    :before-close="handleBeforeClose"
  >
    <div class="faq-dialog">
      <fks-form :model="form" ref="formRef" :rules="rules" label-width="120px" label-position="left">

        <div class="sub-title">问题信息</div>
        <!-- 问题反馈/描述 -->
        <fks-form-item label="问题描述" prop="faqDescribe" class="readonly-item">
          <template v-if="isView">
            <div class="readonly-text">{{ form.faqDescribe || '—' }}</div>
          </template>
          <fks-input
            v-else
            type="textarea"
            v-model="form.faqDescribe"
            :maxlength="500"
            show-word-limit
            placeholder="请输入问题描述..."
            :autosize="{ minRows: 4, maxRows: 8 }"
          />
        </fks-form-item>

        <div class="sub-title">处理信息</div>
        <!-- 解决方案 -->
        <fks-form-item label="解决方案" prop="faqExplainHtml" class="readonly-item">
          <template v-if="isView">
            <div style="display: inline-block;max-width: 100%;flex: 1; box-sizing: border-box; overflow-y: auto;margin-bottom: 40px;" v-html="form.faqExplainHtml"></div>
          </template>
          <editor-wrap v-else :content="form.faqExplainHtml" :faqFile="form.faqFile" :type="type" @content-change="handleChange"  @group-token="getGroupToken"></editor-wrap>
        </fks-form-item>
        <!-- <fks-form-item label="解决方案" prop="faqExplain" class="readonly-item">
          <template v-if="isView">
            <div class="readonly-text">{{ form.faqExplain || '—' }}</div>
          </template>
          <fks-input
            v-else
            type="textarea"
            v-model="form.faqExplain"
            :maxlength="500"
            show-word-limit
            placeholder="请输入解决方案..."
            :autosize="{ minRows: 4, maxRows: 8 }"
          />
        </fks-form-item> -->

        <!-- 上传附件 -->
        <!-- <fks-form-item label="上传附件">
          <drag-upload v-if="visible" v-model="form.faqFile" :disabled="isView" />
        </fks-form-item> -->

      </fks-form>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer" v-if="isView">
      <fks-button class="sub-btn" icon="fks-icon-close" danger-text @click="closeDialog">
        关闭
      </fks-button>
      <fks-button v-if="getAuth('edit')" class="sub-btn" icon="fks-icon-edit" text
                  @click="handleEdit">
        修改
      </fks-button>
    </div>
    <div slot="footer" class="dialog-footer" v-else>
      <fks-button class="sub-btn" icon="fks-icon-close" danger-text @click="closeDialog"
                  style="color: #FF4143 !important;">
        取消
      </fks-button>

      <fks-popconfirm title="确认提交？" @onConfirm="submitFaq">
        <fks-button
          v-if="getAuth('add')"
          slot="reference"
          :loading="loading"
          icon="fks-icon-check"
          class="sub-btn"
          text
        >
          提交
        </fks-button>
      </fks-popconfirm>
    </div>
  </fks-dialog>
</template>

<script>
import { mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import FormUpload from "@modules/FormCenter/components/FormUpload/index.vue";
import DragUpload from '@modules/FormCenter/components/FormUpload/DragUpload.vue'
import { addFaqInfo, modifyFaqInfo } from '@modules/ProjectCar/CompanyPortal/FaqMana/api'
import { getAuth } from '@utils/buttonAuth'
import EditorWrap from '@/components/EditorWrap/index.vue'

export default {
  name: "FaqDialog",
  components: {
    FormUpload, DragUpload, EditorWrap
  },
  data() {
    return {
      // 是否显示 Dialog
      visible: false,
      // 所属项目列表
      projectList: [],
      form: {
        faqDescribe: "", // 默认选中
        faqExplain: "",
        faqFile: "",
        faqExplainHtml: ""
      },
      type: 'add',
      // 验证规则
      rules: {
        faqDescribe: [
          { required: true, message: "请输入问题描述", trigger: "blur" }
        ],
        faqExplainHtml: [
          { required: true, message: "请输入解决方案", trigger: "change" }
        ]
      },
      loading: false,
    };
  },
  computed: {
    ...mapState([StateTypes.AUTH_PORTALS]),
    portal() {
      //当前门户
      return this.$store.state.portal
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter(item => item.projectStatus === 100); // 100激活，200关闭
    },
    isAdd() {
      return this.type === 'add'
    },
    isEdit() {
      return this.type === 'edit'
    },
    isView() {
      return this.type === 'view'
    }

  },
  methods: {
    getAuth,
    handleEdit() {
      this.type = 'edit'
    },
    // 打开弹窗
    openDialog(data) {
      this.visible = true;
      this.type = 'add'
      if (data) {
        this.form = data
      } else {
        this.form = {}
      }
    },
    openDialogWithEdit(data) {
      this.form = data;
      this.visible = true;
      this.type = 'view'
    },
    // 关闭弹窗
    closeDialog() {
      this.form = {};
      this.visible = false;
    },
    handleBeforeClose(done) {
      // 重置表单
      this.form = {};
      // 允许关闭弹窗
      done();
    },
    handleProjectChange() {

    },
    // 提交表单
    submitFaq() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.loading = true;
          const form = this.form;
          const action = form.id ? modifyFaqInfo(form) : addFaqInfo(form);
          action.then((res) => {
            if (res.status) {
              const successMsg = form.id ? "常见问题编辑成功！" : "常见问题添加成功！";
              this.$emit("research")
              this.$message.success(successMsg);
            }
            this.form = {};
            this.closeDialog();
            this.loading = false;
          });
        } else {
          // 验证失败时，可做相应提示或处理
          console.log("表单验证未通过");
          return false;
        }
      });
    },
    handleChange(content,text) {
      // this.form.faqExplainHtml = content;
      this.$set(this.form, 'faqExplainHtml', content);
      this.form.faqExplain = text;
    },
    getGroupToken(token) {
      this.form.faqFile = token;
    }
  }
};
</script>

<style scoped lang="less">
@import "exclude/editForm.css";
</style>
