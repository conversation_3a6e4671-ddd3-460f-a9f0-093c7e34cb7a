<template>
  <fks-dialog
    title="常见问题"
    :visible.sync="visible"
    :close-on-click-modal="false"
    custom-class="faq-form-dialog"
    :before-close="handleBeforeClose"
  >
    <div class="faq-dialog">
      <fks-form :model="form" ref="formRef" label-width="120px" label-position="left">

        <!-- 问题反馈/描述 -->
        <div class="sub-title">问题信息</div>
        <fks-form-item label="问题描述" prop="faqDescribe" class="readonly-item">
          <template v-if="isView">
            <div class="readonly-text">{{ form.faqDescribe || '—' }}</div>
          </template>
        </fks-form-item>

        <!-- 解决方案 -->
        <div class="sub-title">处理信息</div>
        <fks-form-item label="解决方案" prop="faqExplain" class="readonly-item">
          <template v-if="isView">
            <div style="display: inline-block;max-width: 100%;flex: 1; box-sizing: border-box; overflow-y: auto;margin-bottom: 40px;" v-html="form.faqExplainHtml"></div>
          </template>
        </fks-form-item>

        <!-- 上传附件 -->
<!--        <fks-form-item label="上传附件">-->
<!--          <drag-upload v-if="visible" v-model="form.faqFile" :disabled="isView" />-->
<!--        </fks-form-item>-->

      </fks-form>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer" v-if="isView">
      <fks-button class="sub-btn" icon="fks-icon-close" danger-text @click="closeDialog">
        关闭
      </fks-button>
    </div>
  </fks-dialog>
</template>

<script>
import { mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import FormUpload from "@modules/FormCenter/components/FormUpload/index.vue";
import DragUpload from '@modules/FormCenter/components/FormUpload/DragUpload.vue'
import { addFaqInfo, modifyFaqInfo } from '@modules/ProjectCar/CompanyPortal/FaqMana/api'
import { getAuth } from '@utils/buttonAuth'

export default {
  name: "FaqDetailDialog",
  components: {
    FormUpload, DragUpload
  },
  data() {
    return {
      // 是否显示 Dialog
      visible: false,
      // 所属项目列表
      projectList: [],
      form: {
        faqDescribe: "", // 默认选中
        faqExplain: "",
        faqFile: "",
      },
      type: 'add',
      loading: false,
    };
  },
  computed: {
    ...mapState([StateTypes.AUTH_PORTALS]),
    portal() {
      //当前门户
      return this.$store.state.portal
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter(item => item.projectStatus === 100); // 100激活，200关闭
    },
    isAdd() {
      return this.type === 'add'
    },
    isEdit() {
      return this.type === 'edit'
    },
    isView() {
      return this.type === 'view'
    }

  },
  methods: {
    getAuth,
    handleEdit() {
      this.type = 'edit'
    },
    // 打开弹窗
    openDialog(data) {
      this.visible = true;
      this.type = 'add'
      if (data) {
        this.form = data
      } else {
        this.form = {}
      }
    },
    openDialogWithEdit(data) {
      this.form = data;
      this.visible = true;
      this.type = 'view'
    },
    // 关闭弹窗
    closeDialog() {
      this.form = {};
      this.visible = false;
    },
    handleBeforeClose(done) {
      // 重置表单
      this.form = {};
      // 允许关闭弹窗
      done();
    },
    handleProjectChange() {

    },
    // 提交表单
  }
};
</script>

<style scoped lang="less">
@import "exclude/editForm.css";
</style>
