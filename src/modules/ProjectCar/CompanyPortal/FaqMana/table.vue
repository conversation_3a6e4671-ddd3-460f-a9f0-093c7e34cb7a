<template>
  <div>
    <TitleKit>
      <template #left>
        <div style="font-size: 16px; font-weight: 600">常见问题</div>
      </template>
    </TitleKit>
    <TempTable
      :data-list="tableData"
      :table-config.sync="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      @searchData="searchData"
      @delete-item="handleDelete"
      @add-item="handleAdd"
      :showButtons="showButtons"
      :current-page.sync="pageNo"
    >
      <template v-slot:column-createDate="{ scope }">
        {{ dayjs(scope.row.createDate).format('YYYY-MM-DD HH:mm') }}
      </template>

      <template v-slot:column-faqDescribe="{ scope }">
        <fks-tooltip :content="scope.row.faqDescribe" placement="top">
          <p class="descStyle">{{ scope.row.faqDescribe }}</p>
        </fks-tooltip>
      </template>

      <template v-slot:column-faqExplain="{ scope }">
        <fks-tooltip :content="scope.row.faqExplain" placement="top" popper-class="custom-tooltip">
          <template #content>
            <div v-if="scope.row.faqExplain" class="descStyle">
              {{ scope.row.faqExplain }}
            </div>
          </template>
          <p class="descStyle">{{ scope.row.faqExplain }}</p>
        </fks-tooltip>
      </template>

      <template v-slot:column-faqFile="{ scope }">
        <picture-preview v-if="scope.row.faqFile" :g9s="scope.row.faqFile" :file-list-style="fileListStyle"/>
      </template>


      <template v-slot:column-option="{ scope }">
        <div style="display: flex; justify-content: center; gap: 20px">
          <div class="primary role-btn" text @click="showDetail(scope.row)">查看</div>
        </div>
      </template>

    </TempTable>
    <editForm @research="reSearchData" ref="editForm" />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import dayjs from 'dayjs'
import PicturePreview from '@components/PicturePreview/index.vue'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import { getAuth } from '@utils/buttonAuth'
import * as GetterTypes from '@store/Getter/getterTypes'
import { getFaqPage, getFaqPageParam, delFaqInfo } from '@modules/ProjectCar/CompanyPortal/FaqMana/api'
import editForm from '@modules/ProjectCar/CompanyPortal/FaqMana/editForm.vue'

export default {
  name: 'FaqMana',
  components: {
    TitleKit,
    PicturePreview,
    TempTable,
    editForm,
  },
  data() {
    return {
      // 是否显示 Dialog
      visible: true,
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['filter', 'add', 'delete'],
      queryParams: {
        conditions: [],
      },
      tableData: [],
      type: 'view',
      selectedFields: [],
      tableConfig: [
        {
          label: '问题描述',
          prop: 'faqDescribe',
          group: '反馈信息',
          customer: true,
        },
        {
          label: '附件',
          prop: 'faqFile',
          group: '反馈信息',
          width: 180,
          customer: true,
        },
        {
          label: '问题解释',
          prop: 'faqExplain',
          group: '反馈信息',
          customer: true,
        },
        {
          label: '创建时间',
          prop: 'createDate',
          group: '反馈信息',
          customer: true,
        },
        {
          label: '查看',
          prop: 'option',
          group: '系统操作',
          fixed: 'right',
          customer: true,
          width: 80,
        },
      ],

      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      fileListStyle: {
        "justifyContent" : "center"
      }
    }
  },
  computed: {
    ...mapState([StateTypes.AUTH_PORTALS]),
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    portal() {
      //当前门户
      return this.$store.state.portal
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter((item) => item.projectStatus === 100) // 100激活，200关闭
    },
  },
  created() {
    this.selectedFields = this.tableConfig.map((item) => item.prop)
    this.tableConfig.forEach((item) => {})

    getFaqPageParam().then((res) => {
      this.searchConfigs = res.data
    })
    this.getData(this.queryParams)
  },
  methods: {
    dayjs,
    getAuth,
    showDetail(row) {
      this.$refs.editForm.openDialogWithEdit(row)
    },
    handleAdd() {
      this.$refs.editForm.openDialog()
    },
    async handleDelete(rows) {
      if (rows && rows.length > 0) {
        let ids = rows.map(item => item.id);
        let res = await delFaqInfo(ids.join(','))
        if (res.status) {
          this.$message.success('删除成功')
          this.reSearchData(true)
        }
      }
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    reSearchData() {
      this.getData(this.queryParams)
    },
    async getData(query) {
      this.loading = true
      this.tableData = []
      this.queryParams = { ...this.queryParams, ...query }
      try {
        const params = {
          ...this.queryParams,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
        }
        const { data } = await getFaqPage(params, this[GetterTypes.GET_GLOBAL_STATE])
        this.total = data.total
        this.finished = data.isLastPage
        let list = data.list
        this.$set(this.$data, 'tableData', list)
        this.loading = false
      } catch (e) {
        console.log('异常', e)
        this.loading = false
      }
    },
  },
}
</script>

<style>
.role-btn {
  margin: 2px 0;
  border-radius: 6px;
  display: inline-block;
  cursor: pointer;
  padding: 2px 4px !important;
  transition: all 0.3s ease !important; /* 平滑过渡效果 */
  user-select: none;
}

.role-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.role-btn:active {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
  transform: translateY(5px); /* 模拟点击下沉效果 */
}

.primary {
  color: #3c83ff !important;
}

.custom-tooltip {
  max-width: 800px;
  /* 如有需要，可加入自动换行 */
  word-wrap: break-word;

  .descStyle {
    -webkit-line-clamp: 5 !important;
  }
}

.descStyle {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
