<template>
  <div>
    <TitleKit>
      <template #left>
        <div class="flex col-center">
          <div style="font-size: 16px;color: #333333">项目汇总</div>
          <fks-divider direction="vertical" />
          <div style="font-size: 14px;color: #333333">项目用车（固定用车）</div>
        </div>
      </template>
    </TitleKit>
    <main>
      <div class="stats-title">统计表</div>
      <stats-table style="padding: 0" />
    </main>
    <main>
      <div class="stats-title">统计图</div>
      <stats-bar style="height: 375px" />
    </main>
  </div>
</template>

<script>
import TitleKit from "@components/PageTitle/TitleKit.vue";
import StatsBar from "./exclude/stats-bar.vue";
import StatsTable from "@modules/ProjectCar/CompanyPortal/StatsChart/exclude/stats-table.vue";
export default {
  name: "index",
  components: {StatsTable, TitleKit, StatsBar }
}
</script>

<style lang="less" scoped>
@import "~@/modules/ProjectCar/CompanyPortal/StatsChart/exclude/stats-chart.less";
</style>
