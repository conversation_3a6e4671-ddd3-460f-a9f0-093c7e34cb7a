<template>
  <TempTable
    :current-page.sync="pageNo"
    :data-list="tableData"
    :loading="loading"
    :search-configs="searchConfigs"
    :selected-fields.sync="selectedFields"
    :showButtons="showButtons"
    :summary-method="getSummaries"
    :table-config.sync="tableConfig"
    :total="total"
    :show-selection="false"
    style="height: 500px"
  >
  </TempTable>
</template>

<script>
import TempTable from "@components/CustomerTable/CustomerTable.vue";
import {duplicateObjectToArray, fakeAwait, getRandomNumber} from "@utils/util";

export default {
  name: 'StatsTable',
  components: {TempTable},
  data() {
    return {
      tableData: [],
      total: 0,
      tableConfig: [
        {label: '项目名称', prop: 'projectName', fixed: 'left'},
        {label: '车辆数量', prop: 'carNum'},
        {label: '车辆来源', prop: 'carResource', width: 150},
        {label: '司机数量', prop: 'driverNum'},
        {label: '司机来源', prop: 'driverResource'},
        {label: '统计起止日期', prop: 'startDate', width: 120},
        {label: '总费用（元）', prop: 'fee', width: 110},
        {label: '单辆车月均费用（元）', prop: 'a', width: 150},
        {label: '车辆租赁费用（元）', prop: 'b', width: 130},
        {label: '驾驶员租赁费用（元）', prop: 'c', width: 150},
        {label: '车辆使用费（元）', prop: 'd', width: 140},
        {label: '维修保养费（元）', prop: 'e', width: 140},
        {label: '行驶里程（公里）', prop: 'f', width: 140},
        {label: '单辆车辆月均行驶里程（公里）', prop: 'g', width: 160},
        {label: '行车日志记录里程（公里）', prop: 'h', width: 150},
        {label: '单辆车月均里程偏差（公里）', prop: 'i', width: 150},
        {label: '单辆车月均里程偏差（公里）', prop: 'j', width: 150},
        {label: '单辆车月均油耗（升）', prop: 'k', width: 150},
        {label: '单辆车百公里油耗（升/百公里）', prop: 'l', width: 150},
      ],
      searchConfigs: [],
      selectedFields: [],
      loading: false,
      pageNo: 1,
      showButtons: ['filter', 'export']
    }
  },
  methods: {
    getSummaryContent(firstLine, secondLine) {
      return (<div>
        <span>{firstLine}</span>
        <br/>
        <br/>
        <span>{secondLine}</span>
      </div>)
    },
    getSummaries(param) {
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index == 0) {
          sums[index] = this.getSummaryContent('本页汇总', '合计汇总')
        } else if (index === 3) {
          const num1 = getRandomNumber(0,100);
          const num2 = getRandomNumber(0,100);
          sums[index] = this.getSummaryContent(`租赁${num1}    自有${num1}`, `租赁${num2}    自有${num2}`);
        } else {
          sums[index] = this.getSummaryContent(getRandomNumber(0,100), getRandomNumber(0,100));
        }
      });

      return sums;
    },

    async getData() {
      this.loading = true;
      await fakeAwait();
      const item = this.tableConfig.reduce((acc, cur) => {
        const newObj =  {[cur.prop]: getRandomNumber(0, 100)}
        return Object.assign(acc, newObj);
      }, {});
      const d = duplicateObjectToArray(item, 20);
      this.tableData = d;
      this.total = d.length;
      this.loading = false;
    }
  },
  mounted() {
    this.getData();
  }
}
</script>
