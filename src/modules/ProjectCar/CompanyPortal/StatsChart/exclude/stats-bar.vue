<template>
  <div class="stats-bar">
    <header class="flex col-center">
      <span>统计维度</span>
      <fks-select v-model="selectVal" size="mini" style="margin-left: 10px;width: 140px">
        <fks-option label="总费用" :value="1" />
      </fks-select>
    </header>
    <main>
      <basic-echarts id="stats-bar" height="auto" :option="option" immediate />
    </main>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import BasicEcharts from '@/components/Chart/index.vue'
import { xAxisCommonConfig, yAxisCommonConfig } from "@components/Chart/common";
import {isChinese} from "@utils/util";
export default {
  name: 'StatsBar',
  components: { BasicEcharts },
  data() {
    const num = 20;
    const xData = [];
    const seriesData = []
    for (let i = 0; i < num; i++) {
      xData.push(`杭州市本级海塘安澜工程（上泗南北大塘）二期一标段EPC工程总承包`)
      seriesData.push(80000)
    }
    return {
      selectVal: 1,
      option: {
        xAxis: {
          type: 'category',
          data: xData,
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: '#999999'
            },
            formatter: function (value) {
              let ret = ''
              let maxLength = 8;
              const valLength = value.split('').map(() => 1).reduce((acc, cur) => acc + cur, 0);
              const rowN = Math.ceil(valLength / maxLength);
              if (rowN > 1) {
                for (let i = 0; i < rowN; i++) {
                  let temp = ""
                  let start = i * maxLength;
                  let end = start + maxLength;
                  temp = value.substring(start, end) + '\n';
                  ret += temp;
                }
                return ret;
              } else {
                return value
              }
            },
          }
        },
        yAxis: {
          ...yAxisCommonConfig,
          type: 'value',
          name: '(元)'
        },
        dataZoom: [
          {
            type: 'slider',
            start: 0,
            end: 20
          },
          {
            start: 0,
            end: 100
          }
        ],
        series: [
          {
            name: '费用',
            type: 'bar',
            data: seriesData,
            barWidth: 10, // 设置柱状条宽度为10px
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(18, 139, 228, 0.3018)' },
                { offset: 1, color: '#128BE4' }
              ]),
              shadowBlur: 26, // 阴影宽度
              shadowColor: 'rgba(0, 0, 0, 0.2)', // 阴影颜色
              shadowOffsetX: 0,
              shadowOffsetY: 4,
              borderRadius: [4, 4, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              color: '#333',
              fontSize: 12
            }
          }
        ],
        grid: {
          bottom: '45%'
        }
      }
    };
  }
};
</script>

<style scoped lang="less">
.stats-bar {
  padding: 20px;
  border-radius: 6px;
  opacity: 1;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #EEEEEE;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.03);
  margin-bottom: 18px;

  display: flex;
  flex-direction: column;

  header {
    span {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0px;
      color: #333333;
    }
  }

  main {
    flex-grow: 1;
    overflow-x: hidden;
  }
}
</style>
