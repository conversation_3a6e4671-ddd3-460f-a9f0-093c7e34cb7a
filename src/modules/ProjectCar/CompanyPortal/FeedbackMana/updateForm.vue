<template>
  <fks-dialog
    title="反馈详情"
    :visible.sync="visible"
    :close-on-click-modal="false"
    custom-class="feedback-update-dialog"
    :before-close="handleBeforeClose"
  >
    <div class="feedback-dialog">
      <div class="feed-back-info">
        <div class="sub-title">反馈信息</div>

        <div class="info-item">
          <div class="item-label">所属项目</div>
          <div class="fb-item-content">
            {{ feedBackInfo.userProjectName }}
          </div>
        </div>

        <div class="info-item">
          <div class="item-label">问题/建议描述</div>
          <div class="fb-item-content">
            {{ feedBackInfo.feedbackContent }}
          </div>
        </div>

        <div class="info-item">
          <div class="item-label">附件图片</div>
          <div class="fb-item-content">
            <picture-preview v-if="feedBackInfo.feedbackFile" :g9s="feedBackInfo.feedbackFile" />
          </div>
        </div>

        <div class="info-item">
          <div class="item-label">反馈时间</div>
          <div class="fb-item-content">
            {{ dayjs(feedBackInfo.feedbackTime).format('YYYY-MM-DD HH:mm') }}
          </div>
        </div>
      </div>
      <div class="handle-info">
        <div class="sub-title">处理信息</div>
        <div class="info-item">
          <div class="item-label">问题分类</div>
          <div class="fb-item-content">
            <fks-select v-model="feedBackInfo.feedbackType" filterable placeholder="请选择问题分类">
              <fks-option
                v-for="item in feedbackTypeEnum"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </fks-select>
          </div>
        </div>
        <div class="info-item">
          <div class="item-label">处理状态</div>
          <div class="fb-item-content">
            <fks-select v-model="feedBackInfo.feedbackStatus" filterable placeholder="请选择处理状态">
              <fks-option
                v-for="item in feedbackStatusEnum"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </fks-select>
          </div>
        </div>
        <div class="info-item">
          <div class="item-label">处理详情</div>
          <div class="fb-item-content comment-list">
            <div class="comment-item" v-for="(item, index) in commentList" :key="index">
              <div class="comment-dot">
                <img src="@/assets/img/dialog/dot.svg" width="12" height="12" />
              </div>
              <div class="content">
                <div class="time">
                  {{
                    dayjs(item.createDate).format('YYYY-MM-DD HH:mm') +
                    ' ' +
                    item.commentUserFullName
                  }}
                </div>
                <div class="comment-content" v-if="item.id">
                  {{ item.commentContent }}
                </div>

                <template v-else>
                  <fks-input
                    v-model="item.commentContent"
                    :rows="2"
                    maxlength="200"
                    show-word-limit
                    style="margin-top: 10px"
                    class="comment-input"
                    type="textarea"
                  />
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template slot="footer">
      <fks-button :loading="loading" icon="fks-icon-plus" class="sub-btn" text @click="addComment">
        添加处理进展
      </fks-button>

      <fks-button :loading="loading" icon="fks-icon-check" class="sub-btn" text @click="submit">
        提交
      </fks-button>
    </template>
  </fks-dialog>
</template>

<script>
import { mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import FormUpload from '@modules/FormCenter/components/FormUpload/index.vue'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import {
  commitFeedBack,
  getFeedBackDetail,
  getFeedBackParam,
  getFeedbackUserPage, modifyFeedBack
} from '@components/HelpCenter/api'
import dayjs from 'dayjs'
import PicturePreview from '@components/PicturePreview/index.vue'
import TreeTransfer from '@components/TreeTransfer/index.vue'

export default {
  name: 'feedbackForm',
  components: {
    TreeTransfer,
    PicturePreview,
    CardTag,
    TempTable,
    FormUpload,
  },
  data() {
    return {
      // 是否显示 Dialog
      visible: false,
      loading: false,
      feedBackInfo: {},
      commentList: [],
      type: 'view',
    }
  },
  computed: {
    ...mapState([StateTypes.AUTH_PORTALS]),
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    feedbackStatusEnum() {
      return this.enums.VdFeedbackStatusEnums
    },
    feedbackTypeEnum() {
      return this.enums.VdFeedbackIssueTypeEnums
    },
    portal() {
      //当前门户
      return this.$store.state.portal
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter((item) => item.projectStatus === 100) // 100激活，200关闭
    },
  },
  created() {
  },
  methods: {
    dayjs,
    submit() {
      let list = this.commentList.filter(item => !item.id);
      if (list) {
        list = list.filter(item => item.commentContent);
      }
      let form = {
        comments: list,
        feedbackStatus: this.feedBackInfo.feedbackStatus,
        feedbackType: this.feedBackInfo.feedbackType,
        id: this.feedBackInfo.id,
      }
      modifyFeedBack(form).then((res) => {
        if(res.status) {
          this.$message.success("处理成功！");
        }
        this.form = {};
        this.closeDialog();
        this.loading = false;
        this.$emit("handle-feedback")
      })
    },
    addComment() {
      let list = this.commentList
      let createDate = new Date()
      let commentUserName = this.userInfo.userName
      let commentUserFullName = this.userInfo.userFullName
      list.push({
        createDate: createDate,
        feedbackId: this.feedBackInfo.id,
        commentUserName,
        commentUserFullName,
        commentContent: "",
        id: null
      })
      this.$set(this.$data, 'commentList', list)
    },
    getTagText(value, prop) {
      if (prop === 'feedbackType') {
        return this.feedbackTypeEnum.find((item) => item.key == value).value
      }
      if (prop === 'feedbackStatus') {
        return this.feedbackStatusEnum.find((item) => item.key == value).value
      }
      return value
    },
    getTagType(value, prop) {
      let status = []
      if (prop === 'feedbackStatus') {
        status = {
          100: '#FFA418',
          200: '#3C83FF',
          300: '#03BE8A',
          400: '#999999',
        }
      }
      return status[value]
    },
    async getData(id) {
      this.loading = true
      try {
        const { data } = await getFeedBackDetail(id)
        this.feedBackInfo = data.vdFeedback
        this.commentList = data.vdFeedbackComments
        this.loading = false
      } catch (e) {
        console.log('异常', e)
        this.loading = false
      }
    },
    // 打开弹窗
    openDialog(data) {
      this.visible = true
      this.getData(data.id)
    },
    // 关闭弹窗
    closeDialog() {
      this.visible = false
    },
    handleBeforeClose(done) {
      // 重置表单
      // 允许关闭弹窗
      done()
    },
  },
}
</script>

<style>
</style>
<style>
@import './exclude/detail.css';
</style>
