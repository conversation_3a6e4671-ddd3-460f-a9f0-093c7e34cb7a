<template>
  <div>
    <TitleKit>
      <template #left>
        <div style="font-size: 16px; font-weight: 600">反馈管理</div>
      </template>
    </TitleKit>
    <TempTable
      :data-list="tableData"
      :table-config.sync="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      @searchData="searchData"
      :showButtons="showButtons"
      :current-page.sync="pageNo"
      :show-selection="false"
    >
      <template v-slot:column-feedbackTime="{ scope }">
        {{ dayjs(scope.row.feedbackTime).format('YYYY-MM-DD HH:mm') }}
      </template>
      <template v-slot:column-feedbackContent="{ scope }">
        <fks-tooltip :content="scope.row.feedbackContent" placement="top" popper-class="custom-tooltip">
          <p class="descStyle">{{ scope.row.feedbackContent }}</p>
        </fks-tooltip>
      </template>
      <template v-slot:column-feedbackFile="{ scope }">
        <picture-preview v-if="scope.row.feedbackFile" :g9s="scope.row.feedbackFile" :file-list-style="fileListStyle"/>
      </template>
      <template v-slot:column-feedbackStatus="{ scope }">
        <card-tag
          v-if="scope.row.feedbackType"
          class="p-24 p-t-4 p-b-4"
          :tag="{
            color: getTagType(scope.row.feedbackStatus, 'feedbackStatus'),
            text: getTagText(scope.row.feedbackStatus, 'feedbackStatus'),
          }"
        />
      </template>

      <template v-slot:column-feedbackType="{ scope }">
        {{ getTagText(scope.row.feedbackType, 'feedbackType') }}
      </template>

      <template v-slot:column-commentInfo="{ scope }">
        <fks-tooltip v-if="scope.row.commentInfo" placement="top" popper-class="custom-tooltip">
          <!-- 弹出层的内容区域 -->
          <template #content>
            <div v-if="scope.row.commentInfo" class="descStyle">
              {{
                dayjs(scope.row.commentInfo.createDate).format('YYYY-MM-DD HH:mm') +
                ' ' +
                scope.row.commentInfo.commentUserFullName
              }}
              <br />
              {{ scope.row.commentInfo.commentContent }}
            </div>
          </template>

          <div v-if="scope.row.commentInfo" class="descStyle">
            {{
              dayjs(scope.row.commentInfo.createDate).format('YYYY-MM-DD HH:mm') +
              ' ' +
              scope.row.commentInfo.commentUserFullName
            }}
            <br />
            {{ scope.row.commentInfo.commentContent }}
          </div>
        </fks-tooltip>

      </template>

      <template v-slot:column-option="{ scope }">
        <div style="display: flex; gap: 8px; justify-content: center">
          <div v-if="getAuth('edit')" class="primary role-btn" text @click="showForm(scope.row)">处理</div>
          <div class="primary role-btn" text @click="showDetail(scope.row)">查看</div>
<!--          <fks-popover-->
<!--            placement="bottom"-->
<!--            width="198"-->
<!--            trigger="click"-->
<!--            popper-class="custom-popover-class"-->
<!--          >-->
<!--            <div class="popover-menu">-->
<!--              <div-->
<!--                type="text"-->
<!--                class="popover-menu-item"-->
<!--                @click="handleOperation(scope.row, 'quoteFaq')"-->
<!--              >-->
<!--                引用常见问题进行解答-->
<!--              </div>-->
<!--              <div-->
<!--                type="text"-->
<!--                class="popover-menu-item"-->
<!--                @click="handleOperation(scope.row, 'transformToFaq')"-->
<!--              >-->
<!--                将本问题转化为常见问题-->
<!--              </div>-->
<!--            </div>-->

<!--            <div class="primary role-btn" text slot="reference">其他操作</div>-->
<!--          </fks-popover>-->
        </div>
      </template>
    </TempTable>
    <editForm ref="faqForm" />
    <updateForm @handle-feedback="reSearchData" ref="updateForm" />
    <FeedBackUserDetailDialog ref="detailDialog" />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import FormUpload from '@modules/FormCenter/components/FormUpload/index.vue'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import {
  getCommentList,
  getFeedbackPage,
  getFeedBackParam,
} from '@components/HelpCenter/api'
import dayjs from 'dayjs'
import FeedBackUserDetailDialog from '@components/HelpCenter/exclude/FeedBackUserDetailDialog.vue'
import PicturePreview from '@components/PicturePreview/index.vue'
import updateForm from '@modules/ProjectCar/CompanyPortal/FeedbackMana/updateForm.vue'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import { getAuth } from '@utils/buttonAuth'
import * as GetterTypes from '@store/Getter/getterTypes'
import editForm from '@modules/ProjectCar/CompanyPortal/FaqMana/editForm.vue'

export default {
  name: 'FeedbackTable',
  components: {
    TitleKit,
    PicturePreview,
    CardTag,
    TempTable,
    FormUpload,
    FeedBackUserDetailDialog,
    updateForm,
    editForm,
  },
  data() {
    return {
      // 是否显示 Dialog
      visible: true,
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['filter'],
      queryParams: {
        conditions: [],
      },
      tableData: [],
      type: 'view',
      selectedFields: [],
      tableConfig: [
        {
          label: '反馈时间',
          prop: 'feedbackTime',
          group: '反馈信息',
          width: 130,
          customer: true,
        },
        {
          label: '反馈人',
          prop: 'userFullName',
          group: '反馈信息',
          width: 80,
        },
        {
          label: '所属项目',
          prop: 'userProjectName',
          group: '反馈信息',
          width: 220,
        },
        {
          label: '问题/建议描述',
          prop: 'feedbackContent',
          group: '反馈信息',
          width: 250,
          customer: true,
        },
        {
          label: '图片',
          prop: 'feedbackFile',
          group: '反馈信息',
          width: 180,
          customer: true,
        },
        {
          label: '处理状态',
          prop: 'feedbackStatus',
          group: '反馈信息',
          width: 80,
          customer: true,
        },
        {
          label: '问题分类',
          prop: 'feedbackType',
          group: '反馈信息',
          width: 80,
          customer: true,
        },
        {
          label: '处理详情',
          prop: 'commentInfo',
          group: '反馈信息',
          width: 220,
          customer: true,
        },
        {
          label: '操作',
          prop: 'option',
          group: '系统操作',
          fixed: 'right',
          customer: true,
          width: 110,
        },
      ],

      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      fileListStyle: {
        "justifyContent" : "center"
      },
    }
  },
  computed: {
    ...mapState([StateTypes.AUTH_PORTALS]),
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    feedbackStatusEnum() {
      return this.enums.VdFeedbackStatusEnums
    },
    feedbackTypeEnum() {
      return this.enums.VdFeedbackIssueTypeEnums
    },
    portal() {
      //当前门户
      return this.$store.state.portal
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter((item) => item.projectStatus === 100) // 100激活，200关闭
    },
  },
  created() {
    this.selectedFields = this.tableConfig.map((item) => item.prop)
    this.tableConfig.forEach((item) => {})

    getFeedBackParam().then((res) => {
      this.searchConfigs = res.data
    })
    this.getData(this.queryParams)
  },
  methods: {
    dayjs,
    getAuth,
    showForm(row) {
      this.$refs.updateForm.openDialog(row)
    },
    showDetail(row) {
      this.$refs.detailDialog.openDialog(row)
    },
    // handleOperation(row, command) {
    //   switch (command) {
    //     case "quoteFaq":
    //       this.$message.info("引用常见问题进行解答");
    //       break;
    //     case "transformToFaq":
    //       let content = "";
    //       if (row.commentList) {
    //         content = row.commentList[row.commentList.length - 1].commentContent;
    //       }
    //       let data = {
    //         faqDescribe: row.feedbackContent,
    //         faqExplain: content,
    //         faqFile: row.feedbackFile
    //       }
    //       this.$refs.faqForm.openDialog(data);
    //       break;
    //     default:
    //       this.$message.warning("未定义的操作");
    //   }
    //   // 执行操作后关闭 Popover
    //   row.popover = false;
    // },
    getTagText(value, prop) {
      if (prop === 'feedbackType') {
        return this.feedbackTypeEnum.find((item) => item.key == value).value
      }
      if (prop === 'feedbackStatus') {
        return this.feedbackStatusEnum.find((item) => item.key == value).value
      }
      return value
    },
    getTagType(value, prop) {
      let status = []
      if (prop === 'feedbackStatus') {
        status = {
          100: '#FFA418',
          200: '#3C83FF',
          300: '#03BE8A',
          400: '#999999',
        }
      }
      return status[value]
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    reSearchData() {
      this.getData(this.queryParams)
    },
    async getData(query) {
      this.loading = true
      this.tableData = []
      this.queryParams = { ...this.queryParams, ...query }
      try {
        const params = {
          ...this.queryParams,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
        }
        const { data } = await getFeedbackPage(params, this[GetterTypes.GET_GLOBAL_STATE])
        this.total = data.total
        this.finished = data.isLastPage
        let newData = await this.getCommentInfo([...data.list])
        let list = newData
        this.$set(this.$data, 'tableData', list)
        this.loading = false
      } catch (e) {
        console.log('异常', e)
        this.loading = false
      }
    },
    async getCommentInfo(records) {
      try {
        const ids = records.map((item) => item.id).join(',')
        const res = await getCommentList(ids)
        if (res.data) {
          const data = res.data
          records.forEach((record) => {
            let comment = data[record.id];
            if (comment) {
              record.commentInfo = comment[0]
              record.commentList = comment
            }
          })
        }
        return records
      } catch (error) {
        console.error('getCommentInfo error:', error)
        return records
      }
    },
  },
}
</script>

<style>
.role-btn {
  margin: 2px 0;
  border-radius: 6px;
  display: inline-block;
  cursor: pointer;
  padding: 2px 4px !important;
  transition: all 0.3s ease !important; /* 平滑过渡效果 */
  user-select: none;
}

.role-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.role-btn:active {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
  transform: translateY(5px); /* 模拟点击下沉效果 */
}

.primary {
  color: #3c83ff !important;
}

.descStyle {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}


.custom-tooltip {
  max-width: 800px;
  /* 如有需要，可加入自动换行 */
  word-wrap: break-word;

  .descStyle {
    -webkit-line-clamp: 5 !important;
  }
}
</style>
<style scoped lang="less">
@import 'exclude/tablePopover.css';
</style>
