<template>
  <div class="main" id="travelTask" :class="{'bg-white': formList.length === 0}">
<!--    <fm-nav-bar-->
<!--      title="出行任务"-->
<!--      :left-arrow="!isNotPC"-->
<!--      @click-left="$router.go(-1)"-->
<!--      :border="false"-->
<!--    >-->
<!--    </fm-nav-bar>-->
    <div class="flex row-between col-center full-width bg-white search-apply">
      <fm-search
        v-model="form.searchValue"
        clearable
        maxlength="50"
        placeholder="请输入联系人、联系人电话、地址、时间"
        @clear="onClear"
        @search="onSearch"
      >
        <template #left-icon>
          <i class="fm-icon fm-icon-search" @click="onSearch"></i>
        </template>
      </fm-search>
      <img src="@/assets/img/car/filter.png" class="filter-img" @click="showFilter = true;">
    </div>
    <fm-popup class="filter-popup" :visible.sync="showFilter" position="top" :style="{ height: 'calc(50% + 20px)', top: '52px', 'overflow-y': 'auto' }" get-container="#carApply" :append-to-body="false">
      <div class="font-32 font-bold m-32">按申请时间选择</div>
      <div class="flex flex-wrap col-center row-between m-32">
        <div
          v-for="item in timeList"
          :key="item.value"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': timeApplyChoose.includes(item.value)}"
          @click="timeApplyChoose = [item.value]; form.startApplyTime = ''; form.endApplyTime = '';"
        >
          {{ item.text }}
        </div>

        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.startApplyTime, 'no-time': !form.startApplyTime }"
          @click="showStartApplyTime = true"
        >
          {{ form.startApplyTime ? form.startApplyTime : '开始时间' }}
        </div>
        <div class="time-line m-b-32"></div>
        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.endApplyTime, 'no-time': !form.endApplyTime }"
          @click="showEndApplyTime = true"
        >
          {{  form.endApplyTime ? form.endApplyTime : '结束时间' }}
        </div>
        <date-time-picker
          :show.sync="showStartApplyTime"
          :time.sync="form.startApplyTime"
          title="开始时间"
          type="date"
          @confirm="timeApplyChoose = [];"
        ></date-time-picker>
        <date-time-picker
          :show.sync="showEndApplyTime"
          :time.sync="form.endApplyTime"
          title="结束时间"
          type="date"
          @confirm="timeApplyChoose = [];"
        ></date-time-picker>
      </div>
      <div class="font-32 font-bold m-32">按出发时间选择</div>
      <div class="flex flex-wrap col-center row-between m-32">
        <div
          v-for="item in timeList"
          :key="item.value"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': timeChoose.includes(item.value)}"
          @click="timeChoose = [item.value]; form.startTime = ''; form.endTime = '';"
        >
          {{ item.text }}
        </div>

        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.startTime, 'no-time': !form.startTime }"
          @click="showStartTime = true"
        >
          {{ form.startTime ? form.startTime : '开始时间' }}
        </div>
        <div class="time-line m-b-32"></div>
        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.endTime, 'no-time': !form.endTime }"
          @click="showEndTime = true"
        >
          {{  form.endTime ? form.endTime : '结束时间' }}
        </div>
        <date-time-picker
          :show.sync="showStartTime"
          :time.sync="form.startTime"
          title="开始时间"
          type="date"
          @confirm="timeChoose = [];"
        ></date-time-picker>
        <date-time-picker
          :show.sync="showEndTime"
          :time.sync="form.endTime"
          title="结束时间"
          type="date"
          @confirm="timeChoose = [];"
        ></date-time-picker>
      </div>
      <div class="font-32 font-bold m-32">按状态选择</div>
      <div class="flex flex-wrap col-center m-32">
        <div
          v-for="item in applyFormState"
          :key="item.code"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': statusChoose.includes(item.key)}"
          @click="statusChoose = [item.key]"
        >
          {{ item.value }}
        </div>
      </div>
      <div class="font-32 font-bold m-32">按用车类型选择</div>
      <div class="flex flex-wrap col-center m-32 p-b-100">
        <div
          v-for="item in useCarType"
          :key="item.code"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': useCarTypeChoose.includes(item.key)}"
          @click="useCarTypeChoose = [item.key]"
        >
          {{ item.value }}
        </div>
      </div>
      <div class="foot foot-filter">
        <fm-button
          class="flow-btn btn-50 m-r-30"
          type="primary"
          plain
          :disabled="loading"
          :loading="loading"
          @click="clearFilter"
        >重置</fm-button>
        <fm-button
          class="flow-btn btn-50"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="onSearchFilter"
        >确定</fm-button>
      </div>
    </fm-popup>
    <empty-data v-if="formList.length === 0"></empty-data>
    <div class="apply-container" v-else>
      <!-- 下拉刷新 -->
      <fm-pull-refresh
        v-model="isRefresh"
        refresh-layer-color="#4b8bf4"
        success-text="刷新成功"
        @refresh="onRefresh()"
      >
        <!-- 上拉加载 -->
        <fm-list
          v-model="loading"
          :immediate-check="false"
          :finished="finished"
          finished-text="已经到底啦~"
          @load="onLoad"
        >
          <!-- 滑动 -->
          <fm-swipe-cell v-for="(item, i) in formList" :key="item.vdApplyForm.id">
            <!-- 卡片 -->
            <fm-cell-group inset @click="handleView(item.vdApplyForm, 1)"  :class="[1, 9].includes(item.vdApplyForm.applyFormDriverState) ? 'end' : item.vdApplyForm.applyFormDriverState === 7 ? 'start' :['driving', 'end', 'end', 'driving'][+item.vdApplyForm.processState]">
              <div class="status flex col-center row-center" :class="[1, 9].includes(item.vdApplyForm.applyFormDriverState) ? '' : item.vdApplyForm.applyFormDriverState === 7 ?'status-start' : ['status-active', '', '', 'status-active'][+item.vdApplyForm.processState]" >
                <template>
                  <span>{{ item.vdApplyForm.applyFormDriverState | transferEnums('ApplyFormDriverStateEnums') }}</span>
                  <span class="d-inline-block m-l-10 m-r-10">|</span>
                </template>
                <span class="d-inline-block">{{ item.vdApplyForm.useCarType | transferEnums('UseCarTypeEnums') }}</span>
              </div>
              <div class="car-cell p-r-32">
                <div  class="flex col-center row-between full-width">
                  <div class="flex col-center">
                    <img src="@/assets/img/car/icon_start.png" class="address-text m-r-10">
                    <span class="address-city">
<!--                      {{ item.vdApplyForm.startAddress === '其他' ?-->
<!--                      item.vdApplyForm.startAddressDetail.includes('市') ? `${item.vdApplyForm.startAddressDetail.substring(0, item.vdApplyForm.startAddressDetail.lastIndexOf('市') + 1)}` -->
<!--                        : '杭州市' : '杭州市' }}-->
                      {{ getStartCity(item.vdApplyForm) }}
                    </span>
                  </div>
                  <div class="address text-right m-l-40">
                    {{ getStartAddress(item.vdApplyForm) }}
<!--                    {{ item.vdApplyForm.startAddress === '其他' || item.vdApplyForm.startAddress === '9'  ?-->
<!--                    item.vdApplyForm.startAddressDetail.includes('市') ? item.vdApplyForm.startAddressDetail.substring(item.vdApplyForm.endAddress.lastIndexOf('市') + 1)-->
<!--                      : item.vdApplyForm.startAddressDetail : item.vdApplyForm.startAddress }}-->
                  </div>
                </div>
                <div class="flex col-center">
                  <div class="address-line m-l-16" :class="['address-line-driving', 'address-line-end', 'address-line-end', 'address-line-driving'][+item.vdApplyForm.processState]"></div>
                  <img v-if="['0', '3'].includes(item.vdApplyForm.processState) && ![1, 9].includes(item.vdApplyForm.applyFormDriverState)" src="@/assets/img/car/icon_round_trip_active.png" class="round-trip-img m-l-10 m-r-10">
                  <img v-else src="@/assets/img/car/icon_round_trip.png" class="round-trip-img m-l-10 m-r-10">
                </div>
                <div  class="flex col-center row-between full-width">
                  <div class="flex col-center">
                    <img src="@/assets/img/car/icon_end.png" class="address-text m-r-10">
                    <span class="address-city d-inline-block">
                      {{ getEndCity(item.vdApplyForm) }}
                    </span>
                  </div>
                  <div class="address text-right m-l-40">
                    {{ getEndAddress(item.vdApplyForm) }}
                  </div>
                </div>
              </div>
              <div class="car-line m-l-32 m-r-32 m-t-32"></div>
              <div class="flex col-center row-between car-cell">
                <div class="flex col-center">
                  <img src="@/assets/img/car/icon_time.png" class="address-text m-r-10">
                  <span class="time">申请时间：</span>
                </div>
                <div class="time time-info">{{ item.vdApplyForm.applyUserTime ? $dayjs(item.vdApplyForm.applyUserTime).format('YYYY-MM-DD HH:mm') : '' }}</div>
              </div>
              <div class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img src="@/assets/img/car/icon_time.png" class="address-text m-r-10">
                  <span class="time">出发时间：</span>
                </div>
                <div class="time time-info">{{ item.vdApplyForm.startTime ? $dayjs(item.vdApplyForm.startTime).format('YYYY-MM-DD HH:mm') : '' }}</div>
              </div>
              <div v-if="item.vdApplyForm.vdCarInfo" class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img src="@/assets/img/car/icon_car_info.png" class="address-text m-r-10">
                  <span class="time">车辆信息：</span>
                </div>
                <div class="time time-info">
                  <span>{{ item.vdApplyForm.vdCarInfo.carType }}</span>
                  <span class="m-l-16">{{ item.vdApplyForm.vdCarInfo.carNum }}</span>
                </div>
              </div>
              <div class="flex col-center row-between car-cell p-t-24" :class="{'m-b-24': !item.vdApplyForm.userCarRemark}">
                <div class="flex col-center">
                  <img src="@/assets/img/car/icon_driver.png" class="address-text m-r-10">
                  <span class="time">联系人：</span>
                </div>
                <div class="time time-info">
                  <a @click.stop="" :href="`tel:${item.vdApplyForm.applyUserPhone}`">
                    <img class="m-l-16" width="14px" height="14px" :src="require('@/assets/img/detail/dianhua.png')"/>
                    <span class="m-l-16">{{ item.vdApplyForm.applyUserPhone }}</span>
                    <span>{{ item.vdApplyForm.applyFullName }}</span>
                  </a>
<!--                  <span>{{ item.vdApplyForm.applyFullName }}</span>-->
<!--                  <span class="m-l-16">{{ item.vdApplyForm.applyUserPhone }}</span>-->
                </div>
              </div>
<!--              <div v-if="item.vdApplyForm.userCarRemark" class="car-matter m-l-32 m-r-32 m-t-24 p-24 time opacity-6 m-b-24 text-break text-wrap">-->
<!--                {{ item.vdApplyForm.userCarRemark }}-->
<!--              </div>-->
              <div class="m-t-24"></div>
              <div class="m-l-32 m-r-32">
                <template v-for="(it, i) in item.vdApplyForm.buttons">
                  <div
                    v-if="it.buttonSizeType === 'big'"
                    class="obt-btn full-width primary m-b-32 text-center"
                    :style="{'background-color': `rgb(${it.buttonColor})`}"
                    @click.stop="handleOpt(it, item.vdApplyForm)"
                    :key="item.vdApplyForm.id + i + it.buttonValue"
                  >
                    {{ it.buttonValue }}
                  </div>
                </template>
              </div>
              <div class="flex row-end m-l-32 m-r-32">
                <template v-for="(it, i) in item.buttons">
                  <div
                    v-if="it.buttonSizeType === 'small'"
                    class="obt-btn other-btn text-center m-b-32"
                    :class="{'m-r-10': i + 1 < item.vdApplyForm.buttons.length}"
                    @click.stop="handleOpt(it, item.vdApplyForm)"
                    :key="it.buttonKey + item.vdApplyForm.id"
                    :style="{'border-color': `rgb(${it.buttonColor})`, color: `rgb(${it.buttonColor})`}"
                  >
                    {{ it.buttonValue }}
                  </div>
                </template>
              </div>
              <!--  允许司机处理【行程结束】(applyFormDriverState为8，当前登录人为司机）-->
<!--              <div  v-if="item.vdApplyForm.applyFormDriverState === 12 || (item.vdApplyForm.applyFormDriverState === 8 && item.vdApplyForm.vdDriverInfo.driverUserName === currUser.userName)" class="flex row-between m-b-32 m-r-32">-->
<!--                <a-->
<!--                  @click.stop="" :href="`tel:${item.vdApplyForm.applyUserPhone}`"-->
<!--                  class="obt-btn primary-pain text-center d-block m-l-32"-->
<!--                  style="height: 22px;width: calc((100% - 32px) / 2);"-->
<!--                >联系乘客</a>-->
<!--                <div-->
<!--                  class="obt-btn primary text-center m-b-32 m-l-32"-->
<!--                  style="width: calc((100% - 32px) / 2);"-->
<!--                  @click.stop="handleView(item.vdApplyForm, 2)"-->
<!--                >-->
<!--                  处理-->
<!--                </div>-->
<!--              </div>-->
<!--              <div v-else-if="item.vdApplyForm.applyFormDriverState > 8 && +item.vdApplyForm.processState  === 0" class="flex row-between m-b-32 m-l-32 m-r-32">-->
<!--                <navigate-map class="half-width" :data.sync="item.vdApplyForm">-->
<!--                </navigate-map>-->
<!--                <a @click.stop="" :href="`tel:${item.vdApplyForm.applyUserPhone}`" class="half-width obt-btn primary text-center">联系乘客</a>-->
<!--                <a @click.stop="" :href="`tel:${item.vdApplyForm.applyUserPhone}`" class="full-width obt-btn primary-pain text-center">联系乘客</a>-->
<!--              </div>-->
              <!--  行程未开始的时候   -->
<!--              <div  v-else-if="item.vdApplyForm.applyFormDriverState === 7" class="flex row-between m-b-32 m-r-32">-->
<!--                <navigate-map class="m-l-32" :data.sync="item.vdApplyForm" style="width: calc((100% - 32px) / 3 + 16px);">-->
<!--                </navigate-map>-->
<!--                <a-->
<!--                  @click.stop="" :href="`tel:${item.vdApplyForm.applyUserPhone}`"-->
<!--                  class="obt-btn primary-pain text-center d-block"-->
<!--                  style="height: 22px;width: calc((100% - 32px) / 3);"-->
<!--                >联系乘客</a>-->
<!--                <div-->
<!--                  class="obt-btn primary text-center m-b-32 m-l-32"-->
<!--                  style="width: calc((100% - 32px) / 3);"-->
<!--                  @click.stop="handleView(item.vdApplyForm, 2)"-->
<!--                >-->
<!--                  行程开始-->
<!--                </div>-->
<!--                <a-->
<!--                  @click.stop="" :href="`tel:${item.vdApplyForm.applyUserPhone}`"-->
<!--                  class="obt-btn primary-pain text-center d-block m-l-32"-->
<!--                  style="height: 22px;width: calc((100% - 32px) / 2);"-->
<!--                >联系乘客</a>-->
<!--                <div-->
<!--                  class="obt-btn primary text-center m-b-32 m-l-32"-->
<!--                  style="width: calc((100% - 32px) / 2);"-->
<!--                  @click.stop="handleView(item.vdApplyForm, 2)"-->
<!--                >-->
<!--                  行程开始-->
<!--                  处理-->
<!--                </div>-->
<!--              </div>-->
<!--              <a-->
<!--                v-else @click.stop="" :href="`tel:${item.vdApplyForm.applyUserPhone}`"-->
<!--                class="text-center m-b-32 m-l-32 m-r-32 driver-btn d-block"-->
<!--              >联系乘客</a>-->
            </fm-cell-group>
          </fm-swipe-cell>
        </fm-list>
      </fm-pull-refresh>
    </div>
    <rate-popup ref="ratePopup" @refreshList="onRefresh" :is-cur="true" :cur-car.sync="curCar" :rate-type="rateType"></rate-popup>
    <tabbar></tabbar>
  </div>
</template>

<script>
import { Toast, Rate, Cell, CellGroup, Button, Popup } from 'fawkes-mobile-lib';
import { getDriverTask, getApprovalList } from '@/modules/CarApply/components/api.js';
import tabbar from '@/modules/CarApply/components/TabBar.vue'
import RatePopup from '@/modules/CarApply/components/RatePopup.vue';
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex'
import * as GetterTypes from "@/store/Getter/getterTypes";
import * as StateTypes from "@/store/State/stateTypes";
import NavigateMap from  '@/components/NavigateMap/index.vue';
import EmptyData from '@/components/EmptyData/index.vue';
import DateTimePicker from '@/modules/FormCenter/components/DateTimePicker/index.vue';


export default {
  name: 'ReimburseTestList',
  components: {
    [Button.name]: Button,
    [Rate.name]: Rate,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    Toast,
    NavigateMap,
    RatePopup,
    tabbar,
    EmptyData,
    Popup,
    DateTimePicker
  },
  data() {
    return {
      showFilter: false,
      evaluateScore: 0,
      evaluate: 1,
      activeKey: 0,
      value: '', //搜索文字
      formList: [],
      timeApplyChoose: [],
      timeChoose: [],
      statusChoose: [],
      useCarTypeChoose: [],
      timeList: [
        { value: 1, text: '近1个月', start: `${this.$dayjs(new Date().getTime() - 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`, end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: 3, text: '近3个月', start: `${this.$dayjs(new Date().getTime() - 3 * 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`, end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: 6, text: '近6个月', start: `${this.$dayjs(new Date().getTime() - 6 * 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`, end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: this.$dayjs().format('YYYY'), text: '今年', start: this.$dayjs().format('YYYY') + '-01-01 00:00:00', end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: this.$dayjs().format('YYYY') - 1, text: this.$dayjs().format('YYYY') - 1, start: `${this.$dayjs().format('YYYY') - 1}-01-01 00:00:00`, end: `${this.$dayjs().format('YYYY') - 1}-12-31 23:59:59`},
        { value: this.$dayjs().format('YYYY') - 2, text: this.$dayjs().format('YYYY') - 2, start:  `${this.$dayjs().format('YYYY') - 2}-01-01 00:00:00`, end: `${this.$dayjs().format('YYYY') - 2}-12-31 23:59:59` }
      ],
      showStartApplyTime: false,
      showEndApplyTime: false,
      showStartTime: false,
      showEndTime: false,
      form: {
        pageNo: 1,
        pageSize: 20,
        sort: 'desc', // 排序规则,示例值(desc)
        searchValue: '' // 搜索条件（联系人、联系人电话、地址、时间）
      },
      isRefresh: false,
      finished: false,
      loading: false, // 下拉刷新时禁止无限加载
      bannerShow: true,
      curCar: {},
      rateType: 1,// 1、评价，2、未评价
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState([StateTypes.USER_INFO]),
    ...mapGetters([GetterTypes.IS_NOT_PC]),
    applyFormState() {
      return this.enums?.ApplyFormStateEnums??[];
    },
    // 用车类型
    useCarType() {
      return this.enums?.UseCarTypeEnums ?? [];
    },
  },
  created() {
    if (this.$storage.get('commonOneFormNotice')) {
      this.bannerShow = false
    }
    if (!this.currUser.phone || !this.currUser.userName) {
      this.getCurrentUser(this.$storage.get('username')).then(res => {
      });
    }
  },
  mounted() {
    Toast.loading({
      message: '加载中...',
      forbidClick: true,
    })
    if (this.$route.query.refresh === 'true') {
      setTimeout(() => {
        this.onRefresh();
      }, 3000);
    } else {
      this.onRefresh()
    }
  },
  methods: {
    ...mapActions('CarApply', [
      'getCurrentUser'
    ]),
    ...mapMutations('CarApply', ['SET_LAST_ROUTE']),
    getStartCity(item) {
      const hdyAreas = ['西溪院区', '朝晖院区', '三墩院区', '1', '2', '3'] // 数字代表对应的院区
      if (['上海虹桥机场', '上海浦东机场'].includes(item.startAddress)) {
        return '上海市';
      }
      if (!['其他', '9'].includes(item.startAddress)) {
        return '浙江省杭州市';
      }
      if (hdyAreas.findIndex(area => area === item.startAddress) > -1) {
        return this.isPC ? '华东勘测设计研究院（' : '浙江省杭州市';
      }
      // if (!item.startAddressDetail.includes('市')) {
      //   return '浙江省杭州市';
      // }
      // if (item.startAddressDetail.includes('省')) {
      //   return `${item.startAddressDetail.substring(0, item.startAddressDetail.lastIndexOf('省') + 1)}`;
      // }
      if (item.startAddressDetail.includes('重庆') || item.startAddressDetail.includes('北京') || item.startAddressDetail.includes('天津') || item.startAddressDetail.includes('上海')) {
        return item.startAddressDetail.substring(0, item.startAddressDetail.indexOf('市') + 1);
      }
      return `${item.startAddressDetail.substring(0, item.startAddressDetail.lastIndexOf('市') + 1)}`;
    },
    getStartAddress(item) {
      const hdyAreas = ['西溪院区', '朝晖院区', '三墩院区', '1', '2', '3'] // 数字代表对应的院区
      if (hdyAreas.findIndex(area => area === item.startAddress) > -1) {
        const str = ['上海虹桥机场', '上海浦东机场'].includes(item.startAddress) ? '' : '）'
        return this.isPC ? item.startAddress + str : `华东勘测设计研究院（${item.startAddress}）`;
      }
      if (!['其他', '9'].includes(item.startAddress)) {
        return item.startAddress;
      }
      if (!item.startAddressDetail.includes('市')) {
        return item.startAddressDetail;
      }
      // if (item.startAddressDetail.includes('省')) {
      //   return `${item.startAddressDetail.substring(item.startAddressDetail.lastIndexOf('省') + 1)}`;
      // }
      if (item.startAddressDetail.includes('重庆') || item.startAddressDetail.includes('北京') || item.startAddressDetail.includes('天津') || item.startAddressDetail.includes('上海')) {
        if (item.startAddressDetail.indexOf('市') === item.startAddressDetail.lastIndexOf('市') ) {
          return item.startAddressDetail.substring(item.startAddressDetail.indexOf('市') + 1);
        }
        return item.startAddressDetail.substring(item.startAddressDetail.lastIndexOf('市') + 1);
      }
      return item.startAddressDetail.substring(item.endAddress.lastIndexOf('市') + 1);
    },
    getEndCity(item) {
      if (item.endAddress.includes('重庆') || item.endAddress.includes('北京') || item.endAddress.includes('天津') || item.endAddress.includes('上海')) {
        return item.endAddress.substring(0, item.endAddress.indexOf('市') + 1);
      }
      if (item.endAddress.includes('市')) {
        if (item.endAddress.substring(item.endAddress.indexOf('市') - 2, item.endAddress.indexOf('市') + 1) === item.endAddress.substring(item.endAddress.lastIndexOf('市') - 2), item.endAddress.lastIndexOf('市') + 1 ) {
          return item.endAddress.substring(0, item.endAddress.indexOf('市') + 1);
        }
        return `${item.endAddress.substring(0, item.endAddress.lastIndexOf('市') + 1)}`;
      }
      return '';
    },
    getEndAddress(item) {
      if (item.endAddress.includes('重庆') || item.endAddress.includes('北京') || item.endAddress.includes('天津') || item.endAddress.includes('上海')) {
        if (item.endAddress.indexOf('市') === item.endAddress.lastIndexOf('市') ) {
          return item.endAddress.substring(item.endAddress.indexOf('市') + 1);
        }
        return item.endAddress.substring(item.endAddress.lastIndexOf('市') + 1);
      }
      if (item.endAddress.includes('市')) {
        if (item.endAddress.substring(item.endAddress.indexOf('市') - 2, item.endAddress.indexOf('市') + 1) === item.endAddress.substring(item.endAddress.lastIndexOf('市') - 2), item.endAddress.lastIndexOf('市') + 1 ) {
          return item.endAddress.substring(item.endAddress.indexOf('市') + 1);
        }
        return item.endAddress.substring(item.endAddress.lastIndexOf('市') + 1);
      }
      return '';
    },
    openRate(item, type) {
      this.curCar = item;
      this.rateType = +type
      this.$nextTick(() => {
        this.$refs.ratePopup.open();
      })
    },
    clearFilter() {
      this.timeApplyChoose = [];
      this.timeChoose = [];
      this.statusChoose = [];
      this.useCarTypeChoose = [];
      this.form.startApplyTime = '';
      this.form.endApplyTime = '';
      this.form.startTime = '';
      this.form.endTime = '';
    },
    onSearchFilter() {
      // const flag = this.timeApplyChoose.length === 0 && !this.form.startApplyTime && !this.form.endApplyTime &&
      //   this.timeChoose.length === 0 && !this.form.startTime && !this.form.endTime &&
      //   this.statusChoose.length === 0 && this.useCarTypeChoose.length === 0;
      // if (flag) {
      //   Toast({
      //     message: '您还没有选择筛选项！',
      //     duration: 3000
      //   });
      //   return false;
      // }
      this.onRefresh(true);
    },
    onClear() {
      this.form.searchValue = ''
      // 复用刷新接口
      this.onRefresh()
    },
    // 搜索
    onSearch(val) {
      // 复用刷新接口
      this.onRefresh()
    },
    onRefresh(flag = false) {
      this.finished = false
      this.loading = true
      this.form.pageNo = 1
      getDriverTask(this.getParams())
        .then((res) => {
          if (!res.status) {
            Toast({
              message: res.message || '请求失败',
              duration: 3000
            });
            return  false;
          }
          // if (flag && res.data.list.length === 0) {
          //   this.isMobile ? Toast({
          //     message: '未找到匹配的纪录\n请修改筛选条件试试',
          //     duration: 3000
          //   }) : '';
          //   return false;
          // }
          if (flag) {
            this.showFilter = false;
          }
          this.formList = this.getData(res);
          this.isRefresh = false
          if (res.data.isLastPage) {
            this.finished = true
            this.form.pageNo = 1
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {
          Toast.clear()
          this.loading = false
        })
    },
    getData(res) {
      return res.data.list.map(list => {
        const item = list.vdApplyForm;
        item.showPopover = false;
        item.buttonsBig = [];
        item.buttonsSmall = [];
        item.buttonsSmallMore = [];
        item.buttonsBig.push({
          buttonColor: '60,131,255',
          buttonDesc: '用户查看按钮',
          buttonKey: 'VIEW',
          buttonSizeType: 'big',
          buttonValue: '查看'
        });
        item.buttons = item.buttons.map(it => {
          it.text = it.buttonValue;
          if (it.buttonValue === '处理') {
            it.buttonColor = 'rgb(253, 77, 89)';
          }
          if (it.buttonSizeType === 'big') {
            item.buttonsBig.push(it);
          }
          if (it.buttonSizeType === 'small') {
            item.buttonsSmall.length < 3 ? item.buttonsSmall.push(it) : item.buttonsSmallMore.push(it);
          }
          return it;
        });
        list.vdApplyForm = item;
        // item.buttonsBig = item.buttons.filter(it => it.buttonSizeType === 'big');
        // item.buttonsSmall = item.buttons.filter(it => it.buttonSizeType === 'small');
        return list;
      });
    },
    getParams() {
      const userName = this.currUser.userName || this.$storage.get('username') || this[StateTypes.USER_INFO].userName;
      const form = JSON.parse(JSON.stringify(this.form));
      if (form.startTime) {
        form.startTime = form.startTime + ' 00:00:00'
      }
      if (form.startApplyTime) {
        form.startApplyTime = this.form.startApplyTime + ' 00:00:00'
      }
      if (form.endTime) {
        form.endTime = form.endTime + ' 23:59:59'
      }
      if (form.endApplyTime) {
        form.endApplyTime = form.endApplyTime + ' 23:59:59'
      }
      if (this.timeApplyChoose.length > 0) {
        const time = this.timeList.find(it => it.value === this.timeApplyChoose[0]);
        form.startApplyTime = time.start;
        form.endApplyTime = time.end;
      }
      if (this.timeChoose.length > 0) {
        const time = this.timeList.find(it => it.value === this.timeChoose[0]);
        form.startTime = time.start;
        form.endTime = time.end;
      }
      if (this.statusChoose.length > 0) {
        form.status = this.statusChoose[0];
      }
      if (this.useCarTypeChoose.length > 0) {
        form.useCarType = this.useCarTypeChoose[0];
      }
      form.userName = userName;
      return form;
    },
    onLoad() {
      this.loading = true
      this.form.pageNo++
      getDriverTask(this.getParams())
        .then((res) => {
          if (res.status) {
            this.finished = true
            return false;
          }
          const list = this.getData(res);
          this.formList = this.isPC ? list : [...this.formList, ...list];
          if (res.data.isLastPage) {
            this.finished = true
            this.form.pageNo = 1
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    /*打开新增页面*/
    add() {
      this.$router.push({
        name: 'formAdd',
        params: { type: 'add', formKey: 'carApply', normal: false },
      })
    },
    setCurPage() {
      this.SET_LAST_ROUTE(this.$route.path);
    },
    /*点击按钮操作*/
    handleOpt(it, item) {
      switch (it.buttonKey) {
        case 'HANDLE':
          this.handleView(item, 2);
          break;
        case 'EVALUATE':
          this.openRate(item, 1);
          break;
        case 'REVOCATION':
        case 'XC_FORM_MODIFY':
        case 'XC_DRIVER_CAR_MODIFY':
          item.buttonKey = it.buttonKey;
          this.handleView(item, 4);
          break;
      }
    },
    /*打开查看页面*/
    handleView(row, type) {
      const formData = {
        ...row
      }
      getApprovalList({ bizId: row.id }).then((res) => {
        this.$store.commit('SET_CURRENT_ROW', {
          ...row,
          formKey: 'vehicleDispatch',
          taskKey: res.data[res.data.length - 1].taskKey,
          taskName: res.data[res.data.length - 1].taskName,
          taskId: res.data[res.data.length - 1].taskId,
          bizId: res.data[res.data.length - 1].formBizId,
          processInstanceId: res.data[res.data.length - 1].processInstanceId,
        });
        if (type === 2) {
          this.setCurPage();
          this.$router.push({
            name: 'formExecute',
            params: {
              type: 'execute',
              formKey: 'vehicleDispatch',
              taskKey: res.data[res.data.length - 1].taskKey,
              taskId: res.data[res.data.length - 1].taskId,
              bizId: res.data[res.data.length - 1].formBizId,
              processInstanceId: res.data[res.data.length - 1].processInstanceId,
              formName: '车辆调度'
            },
          })
        } else if (res.data?.length == 1 && res.data[0].approveState == 'stage') {
          this.$router.push({
            name: 'formExecute',
            params: {
              type: 'draft',
              bizId: formData.id,
              formKey: 'carApply',
              // formKey: 'reimbursement',
              normal: false,
            },
          })
        } else {
          this.setCurPage();
          this.$router.push({
            name: 'formView',
            params: {
              type: 'view',
              bizId: row.id,
              formKey: 'carApply',
              taskKey: res.data[res.data.length - 1].taskKey,
              // formKey: 'reimbursement',
              normal: false,
              page: 'travelTask'
            },
          })
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
@import "~@/modules/CarApply/components/index";
</style>
