<template>
  <div class="flex flex-column" style="box-sizing: border-box; flex-direction: row">
    <div style=" flex-shrink: 0">
      <treeSelector
        @choose-dept="chooseDept"
        @choose-common="chooseCommon"
        :is-collapsed.sync="isCollapsed"
        :data="deptTreeData"
      />
    </div>
    <div class="flex flex-grow-1 flex-column"
         :style="{width: contentWidth}" >
      <TitleKit>
        <template #left>
          <div class="second-title">角色管理</div>
        </template>
      </TitleKit>
      <div
        class="flex flex-column flex-grow-1 right-content"
        style="box-sizing: border-box"
      >
        <CommonRoleTable :style="{height: height}" key="commonRole" :curr-portal="currPortal" ref="table"
          :isProject="isProject"
          :isCompanyPortal="isCompanyPortal"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TitleKit from '@components/PageTitle/TitleKit.vue'
import CommonRoleTable from './component/common/CommonRoleTable.vue'
import treeSelector from './exclude/DeptSelector.vue'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import { mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import { getDeptTree } from '@modules/ProjectCar/CompanyPortal/RoleManagement/api'

export default {
  name: 'CompanyIndex',
  components: {
    CardTag,
    TempTable,
    treeSelector,
    TitleKit,
    CommonRoleTable,
  },
  data() {
    return {
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['add'],
      queryParams: {
        conditions: [],
      },
      isCollapsed: false,
      deptTreeData: [],
      contentWidth: '100%',
      height: '100%',
      currPortal: {
        isGeneral: false,
        currNode: null,
        roleType: null,
      },
      isProject: false,
      isCompanyPortal: false

    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
  },
  created() {
    this.searchTreeData();
  },
  mounted() {
    this.$nextTick(() => {
      this.getPcMainHeight();
      setTimeout(() => {
        this.getPcMainHeight();
      }, 500)
    })
    window.addEventListener('resize', this.getPcMainHeight); // 监听窗口大小变化
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getPcMainHeight); // 清理监听器
  },
  watch: {
    isCollapsed: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.$nextTick(() => {
          let pcMain = document.getElementById("pcMain");
          if (pcMain) {
            let width = pcMain.offsetWidth - (newVal ? 30 : 260);
            this.contentWidth = width + 'px';
          } else {
            this.contentWidth = '100%';
          }
        })
      }
    }
  },
  methods: {
    getPcMainHeight() {
      let height = 600;
      let pcMain = document.getElementById("pcMain");
      if (pcMain) {
        height = pcMain.offsetHeight - 66;
        this.height = height + 'px';
      } else {
        this.height = '100%';
      }
    },
    chooseDept(node) {
      this.currPortal.currNode = node;
      this.currPortal.roleType = node.deptType;
      this.currPortal.isGeneral = false;
      this.$nextTick(() => {
        this.isProject = node.deptType === 1000;
        this.isCompanyPortal = false
        this.$refs.table.reSearchData(false)
      })
    },
    chooseCommon(org) {
      this.currPortal.currNode = null;
      this.currPortal.roleType = org.key;
      this.currPortal.isGeneral = true;
      this.$nextTick(() => {
        this.isProject = false
        this.isCompanyPortal = org.code === "TYPE50000"
        this.$refs.table.reSearchData(false)
      })
    },
    searchTreeData() {
      getDeptTree().then(res => {
        this.deptTreeData = res.data;
      })
    },
  },
}
</script>
<style lang="less">
@import "~@/styles/disabled";
@import "~@/styles/tabs";
@import "~@/styles/input";
</style>
