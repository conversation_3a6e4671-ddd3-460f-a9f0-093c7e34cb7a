/*
 * @Author: <EMAIL>
 * @Date: 2019-11-07 09:36:15
 * @LastEditors: chen_yt
 * @LastEditTime: 2022-04-14 10:18:19
 * @Description: 权限模块接口
 */
import request from '@/utils/request'
import store from '@/store'

/** 获取角色配置的菜单 */
export function getMenuByRole(roleId, type) {
  return request({
    url: '/sys-system/role/menus',
    method: 'get',
    params: {
      roleId,
      type
    }
  })
}


/** 根据id获取功能权限 */
export function getButton(id) {
  return request({
    url: '/sys-system/button',
    method: 'get',
    params: {
      id
    }
  })
}

/** 获取角色配置的功能 */
export function getButtonByRole(roleId, type) {
  return request({
    url: '/sys-system/role/buttons',
    method: 'get',
    params: {
      roleId,
      type
    }
  })
}

/** 根据id获取数据权限详情 */
export function getDataScope(id) {
  return request({
    url: '/sys-system/dataScope',
    method: 'get',
    params: {
      id
    }
  })
}


/** 获取角色资源 */
export function getDataScopeByRole(roleId) {
  return request({
    url: '/sys-system/role/dataScopes',
    method: 'get',
    params: {
      roleId
    }
  })
}

/** 删除角色 */
export function deleteRole(id) {
  return request({
    url: '/sys-system/role',
    method: 'DELETE',
    params: {
      id
    }
  })
}


/** 获取角色列表 */
export function getRoles(params) {
  return request({
    url: '/sys-system/roles/page',
    method: 'get',
    params: {
      ...params
    }
  })
}

/** 新建角色 */
export function addRole(data) {
  return request({
    url: '/sys-system/role',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/** 更新角色 */
export function saveRole(data) {
  return request({
    url: '/sys-system/role',
    method: 'PUT',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/** 更新角色 v1分级授权版本*/
export function saveRoleV1(data) {
  return request({
    url: '/sys-system/v1/role',
    method: 'PUT',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/** 获取角色用户 */
export function getRoleUser(params) {
  return request({
    url: '/sys-system/role/users/page',
    method: 'get',
    params: {
      ...params,
      portalId: store.state.portal.id
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/** 获取角色用户组 */
export function getRoleUserGroup(params) {
  return request({
    url: '/sys-system/role/user_groups/page',
    method: 'get',
    params: {
      ...params,
      portalId: store.state.portal.id
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/** 新增角色关系 */
export function saveRoleUser(data) {
  return request({
    url: '/sys-system/role/users',
    method: 'POST',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/** 新增角色关系 */
export function saveRoleUserGroup(data) {
  return request({
    url: '/sys-system/role/user_groups',
    method: 'POST',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/** 删除角色关系 */
export function deleteRoleUser(data) {
  return request({
    url: '/sys-system/role/users',
    method: 'delete',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/** 删除角色关系 */
export function deleteRoleUserGroup(data) {
  return request({
    url: '/sys-system/role/user_groups',
    method: 'delete',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/** 获取菜单树 */
export function getMenusTree(params) {
  return request({
    url: '/sys-system/menus/tree',
    method: 'get',
    params: params
  })
}

/** 获取按钮列表 */
export function getButtons(params) {
  return request({
    url: '/sys-system/buttons',
    method: 'get',
    params: params
  })
}

/** 获取数据列表 */
export function getDataScopeList(params) {
  return request({
    url: '/sys-system/dataScopes',
    method: 'get',
    params: params
  })
}

/** 更新门户 */
export function getPortals(data) {
  return request({
    url: '/sys-user/portal',
    method: 'PUT',
    data: data
  })
}

/**
 * @description: 获取用户组
 */
export function getUserGroup(params) {
  return request({
    url: '/sys-user/user_groups/page',
    method: 'get',
    params: params
  })
}

//
/**
 * @description: 获取组织机构树
 * @param {*} portalId 门户id
 */
export function getOrgTree(params) {
  return request({
    url: '/sys-user/orgs/tree',
    method: 'get',
    params
  })
}
