<template>
  <div>
    <TempTable
      style="height: 100%"
      :data-list="tableData"
      :table-config.sync="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      @add-item="handleAdd"
      @searchData="searchData"
      :showButtons="showButtons"
      :current-page.sync="pageNo"
      :show-selection="false"
      :draggable="false"
    >
      <template v-slot:column-option="{ scope }">
        <div v-if="(currPortal.isGeneral || !scope.row.isGeneral) && getAuth('auth')" class="primary role-btn" text @click="showAuth(scope.row)"> 授权 </div>
        <div v-if="(currPortal.isGeneral || !scope.row.isGeneral) && getAuth('edit')"  class="primary role-btn" text @click="showEdit(scope.row)"> 编辑 </div>

        <fks-popconfirm v-if="(currPortal.isGeneral || !scope.row.isGeneral) && getAuth('delete')"  title="确认删除？" @onConfirm="handleDelete(scope.row)" class="m-l-10">
          <div
            slot="reference"
            class="role-btn danger"
          >
            删除
          </div>
        </fks-popconfirm>
        <div class="primary role-btn" text @click="showUserList(scope.row)">
          用户
        </div>
      </template>
    </TempTable>
    <addDialog
      @handleSubmit="reSearchData"
      :curr-portal="currPortal"
      ref="addForm"
      :visible.sync="visible"
      :type.sync="type"
    />
    <role-user :curr-portal="currPortal" ref="roleUser"></role-user>
    <role-auth v-if="authVisible" :visible.sync="authVisible" :data="node" :isProject="isProject" :isCompanyPortal="isCompanyPortal"/>
  </div>
</template>

<script>
import RoleUser from '../../exclude/RoleUser/index.vue'
import RoleAuth from '../../exclude/RoleAuth'
import addDialog from './AddDialog.vue'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import { mapGetters, mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import {
  batchDelProject,
} from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import {
  delFksRole,
  delRole, editFksRole, editRole,
  getDeptRoleList, PROJECT_TYPE
} from '@modules/ProjectCar/CompanyPortal/RoleManagement/api'
import { getAuth } from '@utils/buttonAuth'
import * as GetterTypes from '@store/Getter/getterTypes'

export default {
  name: 'CompanyIndex',
  components: {
    CardTag,
    TempTable,
    addDialog,
    RoleAuth,
    RoleUser
  },
  props: {
    deptKey: {
      type: Number,
      default: 1
    },
    currPortal: {
      isGeneral: {
        type: Boolean,
        default: false
      },
      currNode: {
        type: Object,
        default: {
          id: null
        }
      },
      roleType: {
        type: Number,
        default: null
      }
    },
    isProject: {
      type: Boolean,
      default: false
    },
    isCompanyPortal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      authVisible: false,
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['add'],
      queryParams: {
        conditions: [],
      },
      isCollapsed: false,
      deptTreeData: [],
      contentWidth: '100%',
      visible: false,
      tableData: [],
      currentData: {},
      type: 'view',
      selectedFields: [],
      tableConfig: [
        {
          label: '角色层级',
          prop: 'ext1',
          group: '项目信息',
          // width: 150,
        },
        {
          label: '角色名称-通用部分',
          prop: 'name',
          group: '项目信息',
          // width: 220,
        },
        {
          label: '角色编码-通用部分',
          prop: 'code',
          group: '项目信息',
          // width: 220,
        },
        {
          label: '数据范围',
          prop: 'ext2',
          group: '项目信息',
          // width: 200,
        },
        {
          label: '备注',
          prop: 'remark',
          group: '车辆信息',
          // width: 120,
        },
        {
          label: '操作',
          prop: 'option',
          group: '系统操作',
          fixed: 'right',
          width: 250,
          customer: true,
        },
      ],
      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    deptTypeList() {
      return this.enums.RoleTypeEnums;
    },
    deptRangeList() {
      return this.enums.RoleRangeEnums;
    },
  },
  created() {
    this.selectedFields = this.tableConfig.map((item) => item.prop)
    this.tableConfig.forEach((item) => {
      if (item.prop === 'ext1') {
        item.enums = this.deptTypeList
      }
      if (item.prop === 'ext2') {
        item.enums = this.deptRangeList
      }
    })
    // this.reSearchData(true);
  },
  mounted() {},

  watch: {
    isCollapsed: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.$nextTick(() => {
          let pcMain = document.getElementById("pcMain");
          if (pcMain) {
            let width = pcMain.offsetWidth - (newVal ? 30 : 260);
            this.contentWidth = width + 'px';
          } else {
            this.contentWidth = '100%';
          }
        })
      }
    },
    'currPortal.isGeneral'(newVal) {
      this.tableConfig.forEach(item => {
        if (item.prop === 'name') {
          item.label = newVal ? '角色名称-通用部分' : '角色名称';
        } else if (item.prop === 'code') {
          item.label = newVal ? '角色编码-通用部分' : '角色编码';
        }
      });
    }
  },
  methods: {
    getAuth,
    globalSearch() {
      this.tableData = [];
      this.reSearchData();
    },
    reSearchData(refresh) {
      this.visible = false // 关闭
      if (!refresh || refresh == false) {
        this.pageNo = 1
      }
      this.getData(this.queryParams)
    },
    showEdit(row) {
      this.type = 'edit'
      this.$nextTick(() => {
        this.$refs.addForm.open(row);
      })
    },
    showAuth(row) {
      this.node = Object.assign({ operate: "edit" }, row);
      this.node.portalId = this.isProject ? this.currPortal.currNode.id : ''
      // this.node.portalId = this.portalId;
      this.authVisible = true;
    },
    showUserList(row) {
      this.$refs.roleUser.open(row);
    },
    async handleDelete(row) {
      let res;
      if (this.currPortal.roleType != PROJECT_TYPE) {
        let id = row.id
        res = await delRole(id)
      } else {
        let id = row.id
        res = await delFksRole(id)
      }
      if (res.status) {
        this.$message.success('删除成功')
        this.reSearchData(true)
      }
    },
    handleAdd() {
      this.type = 'plus'
      this.$nextTick(() => {
        this.$refs.addForm.open();
      })
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    async getData(query) {
      this.loading = true
      this.queryParams = { ...this.queryParams, ...query }
      let nodeId = this.currPortal.currNode ? this.currPortal.currNode.id : null;
      let projectId = this.currPortal.currNode ? this.currPortal.currNode.projectId : null;
      let param = {
        isGeneral: this.currPortal.isGeneral,
        roleRefId: nodeId,
        roleType: this.currPortal.roleType,
        projectId
      };
      try {
        const params = {
          ...this.queryParams,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          ...param
        }
        const { data } = await getDeptRoleList(params, this[GetterTypes.GET_GLOBAL_STATE])
        this.total = data.total
        this.finished = data.isLastPage
        const newData = data.list;

        let list = [...newData]
        this.$set(this.$data, 'tableData', list)
        this.loading = false
      } catch (e) {
        console.log('异常', e)
        this.loading = false
      }
    },
  },
}
</script>
