<template>
  <fks-dialog
    :title="title"
    :visible.sync="visible"
    :before-close="beforeClose"
    width="1000px"
  >
    <div class="dialog-body">
      <fks-form ref="form" :model="form" :rules="formRules" label-position="left"
                :class="{newTheme: layoutSingleLine}">
        <fks-form-item label="角色层级" prop="roleLevel">
          <fks-input disabled v-model="form.roleLevel" placeholder="请输入角色层级"/>
        </fks-form-item>

        <fks-form-item v-if="!form.isGeneral" :label="deptLevel" prop="dept">
          <fks-input disabled v-model="form.dept" :placeholder="'请选择' + deptLevel"/>
        </fks-form-item>

        <fks-form-item label="角色名称" prop="name">
          <fks-input :disabled="isView" v-model="form.name" placeholder="请输入角色名称"/>
        </fks-form-item>

        <fks-form-item label="角色编码" prop="code">
          <fks-input :disabled="isView || form.id != null"  v-model="form.code" placeholder="请输入角色编码"/>
        </fks-form-item>

        <fks-form-item label="数据范围" prop="ext2">
          <fks-select
            v-model="form.ext2"
            filterable
            :disabled="isView || form.id != null"
            placeholder="请选择数据范围"
          >
            <fks-option
              v-for="(item, index) in dataRangeList"
              :key="index"
              :label="item.value"
              :value="item.key"
            >
            </fks-option>
          </fks-select>
        </fks-form-item>

        <fks-form-item label="备注" prop="remark">
          <fks-input
            v-model="form.remark"
            :disabled="isView"
            :maxlength="300"
            show-word-limit
            type="textarea"
          />
        </fks-form-item>
      </fks-form>
    </div>
    <template slot="footer">
      <fks-button class="danger sub-btn" text @click="visible = false" icon="fks-icon-close">取消</fks-button>

      <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                  @click="handleSubmit">
        保存
      </fks-button>
    </template>
  </fks-dialog>
</template>

<script>
import { mapState } from 'vuex'
import { addAnnualInspectionItem } from '@modules/ProjectCar/ProjectPortal/CarManage/api'
import {
  addRole,
  editFksRole,
  editRole, PROJECT_TYPE
} from '@modules/ProjectCar/CompanyPortal/RoleManagement/api'

export default {
  name: "CommonRoleAddDialog",
  props: {
    type: {
      type: String,
      default: "view"
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    },
    currPortal: {
      isGeneral: {
        type: Boolean,
        default: false
      },
      currNode: {
        type: Object,
        default: {
          id: null
        }
      },
      roleType: {
        type: Number,
        default: null
      }
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    deptTypeList() {
      return this.enums.RoleTypeEnums;
    },
    deptRangeList() {
      return this.enums.RoleRangeEnums;
    },
    isView() {
      return this.type === 'view';
    },
    title() {
      let map = {
        "edit": "编辑",
        "plus": "新增",
        "view": "查看"
      }
      let title = `${map[this.type]}角色`;
      return title;
    }
  },
  watch: {
    'currPortal.roleType': {
      handler(newVal) {
        this.dataRangeList = this.deptRangeList
          .filter(item => item.key == newVal || item.key == 2000) // 过滤掉小于 deptKey 的项
          .sort((a, b) => a.key - b.key);
      },
      deep: true,  // 如果 currPortal.roleType 是一个对象并且希望监听其内部变化，可以设置为 true
      immediate: true  // 在初始化时也触发一次
    },
    'currPortal.currNode': {
      handler(newVal) {
        if (newVal) {
          this.form.dept = newVal.name;
          this.form.roleRefId = newVal.id;
          this.form.projectId = newVal.projectId;
        } else {
          this.form.dept = null;
          this.form.roleRefId = null;
          this.form.projectId = null;
        }
      },
      deep: true,  // 如果 currNode 是一个对象并且希望监听其属性变化，可以设置为 true
      immediate: true  // 在初始化时也触发一次
    }
  },
  data() {
    return {
      loading: false,
      formRules: {
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入角色编码', trigger: 'blur' }
        ],
        ext2: [
          { required: true, message: '请选择数据范围', trigger: 'blur' }
        ]
      },
      visible: false,
      dataRangeList: [],
      deptLevel: "",
      form: {
        ext1: "",
        name: "",
        code: "",
        ext2: "",
        remark: "",
        projectId: null,
        isGeneral: false,
        roleRefId: null,
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true

          if (this.form.id) {
            // 不是通用角色，并且是项目级别
            let func = this.roleInfo.isGeneral || (this.roleInfo.ext1 != null && this.roleInfo.ext1 != PROJECT_TYPE) ? editRole(this.form) : editFksRole(this.form);
            func.then((res) => {
                if (res.status) {
                  this.$message.success('修改成功')
                  this.beforeClose()
                  // 新增成功后重新查询列表
                  this.$emit('handleSubmit', true)
                }
              })
              .finally(() => {
                this.loading = false
                this.beforeClose()
              })
          } else {
            addRole({ ...this.form})
              .then((res) => {
                if (res.status) {
                  this.$message.success('新增成功')
                  this.beforeClose()
                  // 新增成功后重新查询列表
                  this.$emit('handleSubmit', true)
                }
              })
              .finally(() => {
                this.loading = false
              })
          }
        }
      })
    },
    open(data) {
      let list = this.deptTypeList;
      if (this.type === 'plus') {
        let find = list.find(item => item.key === this.currPortal.roleType)
        if (find) {
          this.form.roleLevel = find.value
          this.form.ext1 = find.key
          this.deptLevel = `所属${find.value}`;
        }
      } else {
        if (data) {
          this.roleInfo = data;
          this.form = {...data}
          let find = list.find(item => item.key === data.ext1)
          if (find) {
            this.form.roleLevel = find.value
            this.deptLevel = `所属${find.value}`;
          }
        }
      }
      this.form.isGeneral = this.currPortal.isGeneral
      this.visible = true;
    },
    beforeClose() {
      this.form = {};
      this.$refs.form.resetFields();
      this.$refs.form.clearValidate();
      this.visible = false;
    }
  }
}
</script>
<style>
@import "../../exclude/role.css";
</style>

