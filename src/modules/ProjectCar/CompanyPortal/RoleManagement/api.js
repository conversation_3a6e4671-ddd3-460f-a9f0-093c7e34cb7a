import request from '@/utils/request'


export function getDeptTree() {
  return request({
    url: '/vehicle-dispatch/vd/system/role/deptProjectTree',
  })
}


export function getRoleUserList(data, params) {
  // 模拟接口返回数据
  return request({
    url: '/vehicle-dispatch/vd/role/general/userPage',
    method: 'post',
    data: data,
    params: params
  })
}
export function getPortalInfo(params) {
  return request({
    url: '/vehicle-dispatch/vd/role/general/idInfo',
    method: 'post',
    params
  })
}


export function getRoleList() {
  return request({
    url: '/vehicle-dispatch/vd/project/deptTree',
  })
}



export function getDeptRoleList(data) {
  // 模拟接口返回数据
  return request({
    url: '/vehicle-dispatch/vd/role/general/page',
    method: 'post',
    data: data
  })
}

export function addRole(data) {
  return request({
    url: '/vehicle-dispatch/vd/role/general/add',
    method: 'post',
    data: data
  })
}

export function editRole(data) {
  return request({
    url: '/vehicle-dispatch/vd/role/general/edit',
    method: 'post',
    data: data
  })
}

export function editFksRole(data) {
  return request({
    url: '/vehicle-dispatch/vd/role/fawkes/edit',
    method: 'post',
    data: data
  })
}


export function delRole(id) {
  return request({
    url: '/vehicle-dispatch/vd/role/general/delete',
    method: 'post',
    params: {
      id: id
    },
  })
}


export function delFksRole(id) {
  return request({
    url: '/vehicle-dispatch/vd/role/fawkes/delete',
    method: 'post',
    params: {
      id: id
    },
  })
}

export function bindUser(data) {
  return request({
    url: '/vehicle-dispatch/vd/role/general/bindUser',
    method: 'post',
    data: data
  })
}


export function bindFksUser(data) {
  return request({
    url: '/vehicle-dispatch/vd/role/fawkes/bindUser',
    method: 'post',
    data: data
  })
}



export function unBind(data) {
  return request({
    url: '/vehicle-dispatch/vd/role/general/unBindUser',
    method: 'post',
    data: data
  })
}


export function unFksBind(data) {
  return request({
    url: '/vehicle-dispatch/vd/role/fawkes/unBindUser',
    method: 'post',
    data: data
  })
}



/** 获取菜单树 */
// 需要portalId
export function getMenusTree(params) {
  return request({
    url: '/sys-system/menus/tree',
    method: 'get',
    params: params
  })
}

/** 获取按钮列表 */
export function getButtons(params) {
  return request({
    url: '/sys-system/buttons',
    method: 'get',
    params: params
  })
}

/** 获取数据列表 */
export function getDataScopeList(params) {
  return request({
    url: '/sys-system/dataScopes',
    method: 'get',
    params: params
  })
}

/** 获取角色配置的菜单 */
export function getMenuByRole(params) {
  return request({
    // url: isProject ? '/sys-system/role/menus': '/vehicle-dispatch/vd/role/general/menus',
    url: '/sys-system/role/menus',
    method: 'get',
    params
    // params: {
    //   roleId,
    //   type
    // }
  })
}


// /** 获取角色配置的菜单 */
// export function getMenuByRole2(roleId, type) {
//   return request({
//     url: '/vehicle-dispatch/vd/role/general/menus',
//     method: 'get',
//     params: {
//       roleId,
//       type
//     }
//   })
// }



/** 获取角色配置的功能 */
// export function getButtonByRole(roleId, type) {
//   return request({
//     url: '/sys-system/role/buttons',
//     method: 'get',
//     params: {
//       roleId,
//       type
//     }
//   })
// }
export function getButtonByRole(params) {
  return request({
    url: '/sys-system/role/buttons',
    method: 'get',
    params
  })
}


// /** 获取角色资源 */
// export function getDataScopeByRole(roleId) {
//   return request({
//     url: '/sys-system/role/dataScopes',
//     method: 'get',
//     params: {
//       roleId
//     }
//   })
// }

/** 获取角色资源 */
export function getDataScopeByRole(params) {
  return request({
    url: '/sys-system/role/dataScopes',
    method: 'get',
    params
  })
}

/** 更新角色 v1分级授权版本*/
export function saveRoleV1(data) {
  return request({
    url: '/sys-system/v1/role',
    method: 'PUT',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/** 更新角色 v1分级授权版本*/
export function saveRoleV1Own(data) {
  return request({
    url: '/vehicle-dispatch/vd/role/general/bindRoleMenus',
    method: 'PUT',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


export const PROJECT_TYPE = 1000;
