<template>
  <fks-dialog
    v-if="visible"
    :before-close="beforeClose"
    :visible.sync="visible"
    size="large"
    class="dialog limit-max-width"
    :title="roleInfo.name + '-' + '用户列表'"
  >
    <div class="role-user">
      <TempTable
        :back-ground-color="'#ffffff'"
        ref="tempTable"
        :style="{ height: height }"
        :data-list="tableData"
        :table-config.sync="tableConfig"
        :search-configs="searchConfigs"
        :selected-fields.sync="selectedFields"
        :total="total"
        :loading="loading"
        @add-item="handleAdd"
        @delete-item="handleDelete"
        @searchData="searchUserInfo"
        :showButtons="showButtons"
        :current-page.sync="pageNo"
        :draggable="false"
      >
      </TempTable>
      <AuthUser @handleSubmit="reSearchData" ref="authUser" />
    </div>
  </fks-dialog>
</template>

<script>
import AuthUser from './AuthUser.vue'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import { getUserSearchParam } from '@modules/ProjectCar/CompanyPortal/UserManagement/api'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import { mapGetters, mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import globalSearchMixin from '@/mixins/globalSearchMixin'
import CardTag from '@components/CardFlow/components/tag.vue'
import {
  getRoleUserList,
  PROJECT_TYPE,
  unBind,
  unFksBind,
} from '@modules/ProjectCar/CompanyPortal/RoleManagement/api'
import addDialog from '@modules/ProjectCar/CompanyPortal/RoleManagement/component/common/AddDialog.vue'
import * as GetterTypes from '@store/Getter/getterTypes'
export default {
  name: 'RoleUserTable',
  components: { addDialog, CardTag, TitleKit, TempTable, AuthUser },
  mixins: [globalSearchMixin],
  data() {
    return {
      visible: false,
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['add', 'filter', 'delete', 'sort'],
      queryParam: {
        isGeneral: true,
        roleId: null,
      },
      tableData: [],
      openDialog: false,
      closeDialog: false,
      chooseUserId: null,
      selectedFields: [],
      height: '600px',
      tableConfig: [
        { label: '姓名', prop: 'userFullName', group: '用户信息' },
        { label: '用户名', prop: 'userName', group: '用户信息' },
      ],
      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      roleInfo: null,
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    userResource() {
      return this.enums.ContactsResourceEnums
    },
    userAccountStatus() {
      return this.enums.AccountStatusEnums
    },
  },
  created() {
    this.selectedFields = this.tableConfig.map((item) => item.prop)
  },
  methods: {
    getDialogBodyHeight() {
      // 获取 .fks-dialog__body 元素
      const dialogBody = document.querySelector('.fks-dialog__body')

      // 如果元素存在，获取其高度
      if (dialogBody) {
        const height = dialogBody.offsetHeight // 获取实际高度
        // 进行一些计算
        let calculatedHeight = height - 40 // 例如减去50px，作为示例
        if (calculatedHeight < 560) {
          calculatedHeight = 560
        }
        this.height = calculatedHeight + 'px'
      } else {
        this.height = '600px'
      }
    },
    open(role) {
      this.roleInfo = role
      this.queryParam.isGeneral = role.isGeneral
      this.queryParam.roleId = role.id
      this.visible = true
      this.reSearchData(true)
      getUserSearchParam().then((res) => {
        this.searchConfigs = res.data
      })
      this.$nextTick(() => {
        setTimeout(() => {
          this.getDialogBodyHeight()
          this.$refs.tempTable.calculateTableHeight()
        }, 300)
      })
    },
    beforeClose() {
      this.visible = false
    },
    globalSearch() {
      this.tableData = []
      this.reSearchData()
    },
    reSearchData(refresh) {
      if (refresh) {
        this.pageNo = 1
      }
      this.loadMoreUserInfo(this.queryParam)
    },
    handleAdd() {
      this.$refs.authUser.open(this.roleInfo)
    },
    async handleDelete(rows) {
      this.loading = true
      let res
      let roleId = this.roleInfo.id
      if (rows.length > 0) {
        let data = {
          roleIds: [roleId],
          userIds: rows.map((item) => item.userId),
          userNames: rows.map((item) => item.userName),
        }
        // 只有项目级别、不通用的场景下需要调用fks接口
        if (this.roleInfo.isGeneral || (this.roleInfo.ext1 != null && this.roleInfo.ext1 != PROJECT_TYPE)) {
          res = await unBind(data)
        } else {
          res = await unFksBind(data)
        }
        if (res.status) {
          this.$message.success('删除成功')
          this.reSearchData(false)
        }
      }
      this.loading = false
    },
    searchUserInfo(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.loadMoreUserInfo(queryParams)
    },
    async loadMoreUserInfo(query) {
      this.loading = true
      this.queryParam = { ...query }
      let general = this.roleInfo.isGeneral || (this.roleInfo.ext1 != null && this.roleInfo.ext1 != PROJECT_TYPE);

      let param = {
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        needOrg: false,
        ...this.queryParam,
        isGeneral: general,
        roleId: this.roleInfo.id,
      }

      const startTime = performance.now() // 记录请求开始时间

      try {
        const response = await getRoleUserList(param, this[GetterTypes.GET_GLOBAL_STATE]) // 使用 await 获取响应
        let duration = 0

        if (response.status) {
          let data = response.data
          this.total = data.total
          this.finished = data.isLastPage
          this.tableData = [...data.list]
          duration = performance.now() - startTime // 计算耗时
        }
        setTimeout(
          () => {
            this.loading = false
          },
          duration < 200 ? 150 : 0
        )
      } catch (error) {
        console.error('Error loading user info:', error)
        this.loading = false
      }
    },
  },
}
</script>

<style scoped lang="scss">
.role-user {
  //padding: 20px 0;
  .table-component {
    background: #ffffff;
  }
  .table-component .fks-table__body-wrapper {
    background: #ffffff;
  }
}

.limit-max-width ::v-deep(.fks-dialog) {
  max-width: 95vw;
}

.limit-max-width ::v-deep(.fks-dialog__body) {
  padding: 24px 0 !important;
}

.limit-max-width ::v-deep(.fks-dialog__footer) {
  padding: 24px 36px 0 !important;
}
</style>
