<template>
  <fks-dialog
    v-if="visible"
    :before-close="beforeClose"
    :visible.sync="visible"
    size="large"
    class="dialog limit-max-width"
    title="选择用户"
  >
    <div class="choose-user">
      <TempTable
        ref="tempTable"
        :back-ground-color="'#ffffff'"
        :style="{height: height}"
        style="flex: 2"
        :data-list="tableData"
        :table-config.sync="tableConfig"
        :search-configs="searchConfigs"
        :selected-fields.sync="selectedFields"
        :total="total"
        :loading="loading"
        @searchData="searchUserInfo"
        :showButtons="showButtons"
        :current-page.sync="pageNo"
        :draggable="false"
        :choose-row="true"
        @selectItem="handleChooseUser"
        @select-all="handleSelectAll"
      >
        <template slot="toolBar">
          <div style="padding-bottom: 15px;">
            选择
          </div>
        </template>
      </TempTable>
      <div class="right-arrow">
        <img src="@/assets/img/detail/right-arrow.svg" width="12" height="12" />
      </div>
      <TempTable
        ref="tempTable2"
        :style="{height: height}"
        :back-ground-color="'#ffffff'"
        style="flex: 1"
        :data-list="selectedRows"
        :table-config.sync="selectedTableConfig"
        :selected-fields.sync="chooseTableFields"
        :total="total"
        :loading="loading"
        @searchData="searchUserInfo"
        :showButtons="[]"
        :draggable="false"
        :show-page="false"
        :show-selection="false"
        :choose-row="true"
        @selectItem="removeRows"
      >
        <template slot="toolBar">
          <div style="padding-bottom: 15px; padding-top: 45px; display: flex; justify-content: space-between">
            <div>
              已选<span style="color: #5483F7">{{selectedRows.length}}</span>
            </div>
            <div class="clear-btn"  @click="clearSelectedRows">
              清空
            </div>
          </div>
        </template>
      </TempTable>
    </div>

    <template slot="footer">
      <fks-button class="danger sub-btn" text @click="visible = false" icon="fks-icon-close">取消</fks-button>

      <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                  @click="bidingUser">
        确定
      </fks-button>
    </template>
  </fks-dialog>
</template>

<script>
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import UserForm from '@modules/ProjectCar/CompanyPortal/UserManagement/components/UserForm.vue'
import {
  getUserListByPost,
  getUserSearchParam,
} from '@modules/ProjectCar/CompanyPortal/UserManagement/api'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import { mapGetters, mapState } from 'vuex'
import { fileReader } from '@utils/exportFile'
import * as StateTypes from '@store/State/stateTypes'
import globalSearchMixin from '@/mixins/globalSearchMixin'
import CardTag from '@components/CardFlow/components/tag.vue'
import {
  bindFksUser,
  bindUser,
  PROJECT_TYPE,
  unBind,
  unFksBind
} from '@modules/ProjectCar/CompanyPortal/RoleManagement/api'
import * as GetterTypes from '@store/Getter/getterTypes'
export default {
  name: 'RoleUserTable',
  components: { CardTag, TitleKit, TempTable, UserForm },
  mixins: [globalSearchMixin],
  data() {
    return {
      visible: false,
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['filter', 'sort'],
      queryParam: {},
      tableData: [],
      openDialog: false,
      closeDialog: false,
      chooseUserId: null,
      selectedFields: [],
      chooseTableFields: ['userName', 'userFullName'],
      height: '600px',
      halfWidth: "300px",
      tableConfig: [
        { label: '姓名', prop: 'userFullName', group: '用户信息' },
        { label: '用户名', prop: 'userName', group: '用户信息' },
        { label: '二级单位', prop: 'userDepName', group: '部门信息' },
        { label: '三级部门', prop: 'userDepName2', group: '部门信息' },
      ],
      selectedTableConfig: [
        { label: '姓名', prop: 'userFullName', group: '用户信息' },
        { label: '用户名', prop: 'userName', group: '用户信息' },
      ],
      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      selectedRows: [],
      roleInfo: {}
    }
  },
  props: {
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    userResource() {
      return this.enums.ContactsResourceEnums
    },
    userAccountStatus() {
      return this.enums.AccountStatusEnums
    },
  },
  created() {
    this.selectedFields = this.tableConfig.map((item) => item.prop)
  },
  methods: {
    async bidingUser() {
      this.loading = true
      let list = this.selectedRows;
      let res;
      let roleId = this.roleInfo.id;
      let data = {
        roleIds: [roleId],
        userIds: list.map(item => item.userId),
        userNames: list.map(item => item.userName),
      }
      if (list.length > 0) {

        console.log("this。roleInfo", this.roleInfo)
        console.log("当前级别", this.roleInfo.isGeneral || (this.roleInfo.ext1 != null && this.roleInfo.ext1 != PROJECT_TYPE))
        // 只有项目级别、不通用的场景下需要调用fks接口
        if (this.roleInfo.isGeneral || (this.roleInfo.ext1 != null && this.roleInfo.ext1 != PROJECT_TYPE)) {
          res = await bindUser(data)
        } else {
          res = await bindFksUser(data)
        }
        this.loading = false
        if (res.status) {
          this.$message.success('保存成功')
          this.$emit('handleSubmit', true)
          this.beforeClose()
        }
      } else {
        this.loading = false
        this.beforeClose()
      }
    },
    clearSelectedRows() {
      this.$refs.tempTable.unSelect(this.selectedRows);
      this.$set(this.$data, "selectedRows", [])
    },
    removeRows(selectedRows) {
      const selectedIds = selectedRows.map(row => row.id)
      // 根据 userId 删除 selectedRows 中的行
      this.selectedRows = this.selectedRows.filter(row => !selectedIds.includes(row.id));
      this.$refs.tempTable.unSelect(selectedRows);
    },
    handleSelectAll(allRows) {
      this.selectedRows = allRows;
    },
    handleChooseUser(selectedRows, isDel) {
      // 遍历传入的 selectedRows，如果对应的 userId 不在 selectedRows 中，则添加
      if (isDel) {
        selectedRows.forEach(row => {
          const index = this.selectedRows.findIndex(selectedRow =>
            selectedRow.id === row.id
          );
          if (index !== -1) {
            this.selectedRows.splice(index, 1); // 删除已选中的行
          }
        });
      } else {
        selectedRows.forEach(row => {
          if (!this.selectedRows.some(item => item.id === row.id)) {

            this.selectedRows.push(row);
          }
        });
      }
    },
    getDialogBodyHeight() {
      // 获取 .fks-dialog__body 元素
      const dialogBody = document.querySelector('.fks-dialog__body');

      // 如果元素存在，获取其高度
      if (dialogBody) {
        const height = dialogBody.offsetHeight; // 获取实际高度
        // 进行一些计算
        let calculatedHeight = height - 40; // 例如减去50px，作为示例
        if (calculatedHeight < 560) {
          calculatedHeight = 560;
        }
        this.height = calculatedHeight + 'px';


        const width = dialogBody.offsetWidth; // 获取实际高度
        // 进行一些计算
        let calculatedWidth = width / 2 - 24; // 例如减去50px，作为示例
        if (calculatedWidth < 560) {
          calculatedWidth = 560;
        }
        this.halfWidth = calculatedWidth + 'px';
      } else {
        this.halfWidth = '600px';
      }
    },
    open(role) {
      this.visible = true
      this.roleInfo = role
      this.reSearchData(true);
      getUserSearchParam().then((res) => {
        this.searchConfigs = res.data
      })
      this.$nextTick(() => {
        setTimeout(() => {
          this.getDialogBodyHeight()
          this.$refs.tempTable.calculateTableHeight()
        }, 300)
      })
    },
    beforeClose() {
      this.clearSelectedRows()
      this.visible = false
    },
    globalSearch() {
      this.tableData = []
      this.reSearchData()
    },
    reSearchData(refresh) {
      if (refresh) {
        this.pageNo = 1
      }
      this.loadMoreUserInfo(this.queryParam)
    },
    searchUserInfo(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.loadMoreUserInfo(queryParams)
    },
    async loadMoreUserInfo(query) {
      this.loading = true
      this.queryParam = { ...query }
      let param = {
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        needOrg: false,
        ...this.queryParam,
      }

      const startTime = performance.now() // 记录请求开始时间

      try {
        const response = await getUserListByPost(param, this[GetterTypes.GET_GLOBAL_STATE]) // 使用 await 获取响应
        let duration = 0

        if (response.status) {
          let data = response.data
          this.total = data.total
          this.finished = data.isLastPage
          this.tableData = [...data.list]
          // 如果某行数据被选中，切换分页时保持选中
          this.$nextTick(() => {
            this.$refs.tempTable.reCheckRows();
          })
          duration = performance.now() - startTime // 计算耗时
        }
        setTimeout(
          () => {
            this.loading = false
          },
          duration < 200 ? 150 : 0
        )
      } catch (error) {
        console.error('Error loading user info:', error)
        this.loading = false
      }
    },
  },
}
</script>

<style scoped lang="scss">
.choose-user {
  //padding: 20px 0;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  overflow-x: hidden;
  .table-component{
    background: #FFFFFF;
  }
  .table-component .fks-table__body-wrapper{
    background: #FFFFFF;
  }
}

.right-arrow {
  height: 24px;
  width: 24px;
  flex-shrink: 0;
  box-sizing: border-box;
  margin: 0;
  background-color: #5483F7;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.clear-btn {
  cursor: pointer;
  color: #ff7172;
}

.limit-max-width ::v-deep(.fks-dialog) {
  max-width: 95vw;
}

.limit-max-width ::v-deep(.fks-dialog__body) {
  padding: 24px 0 !important;
}

.limit-max-width ::v-deep(.fks-dialog__footer) {
  padding: 24px 36px 0 !important;
}
</style>
