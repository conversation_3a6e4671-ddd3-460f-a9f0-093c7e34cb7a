<template>
  <fks-dialog
    :title="form.name"
    :visible="true"
    destroy-on-close
    width="1200px"
    class="dialog-8vh role-auth-dialog"
    top="8vh"
    @close="close"
  >
    <div class="dialog-body container" v-loading="loading">
      <!-- 左侧菜单权限 -->
      <div class="content">
        <div class="title">
          <div class="title-menu">
            <!-- 菜单 -->
            <span>{{ LANG.SELECT_MENU }}</span>
          </div>

          <div class="check-ex" style="right: 200px">
            <fks-checkbox
              v-model="menuChecked"

              :disabled="form.operate === 'view'"
              @change="selectWMenus"
            />
            <!-- 可操作 -->
            <span> {{ LANG.OPERABLE }} </span>
          </div>
          <div v-if="hierarchicalAuth" class="check-ex">
            <fks-checkbox
              v-model="authChecked"
              :disabled="form.operate === 'view'"
              @change="authMenus"
            />
            <!-- 可授权 -->
            <span>
              {{ LANG.LICENSABLE }}
            </span>
          </div>
          <!-- 下拉列表 折叠全部、展开全部 -->
          <fks-dropdown trigger="click" @command="handleCommand">
            <span style="cursor: pointer">
              <i class="fks-icon-arrow-down fks-icon--right" />
            </span>
            <fks-dropdown-menu slot="dropdown">
              <fks-dropdown-item command="collapse">
                <!-- 折叠全部 -->
                <span>{{ LANG.COLLAPSE_ALL }}</span>
              </fks-dropdown-item>
              <fks-dropdown-item command="expand">
                <!-- 展开全部 -->
                <span>{{ LANG.EXPAND_ALL }}</span>
              </fks-dropdown-item>
            </fks-dropdown-menu>
          </fks-dropdown>
        </div>
        <div ref="menu" class="tree-container">
          <fks-tree
            ref="menuTree"
            key="menuTree"
            :data="menuTree"
            :props="defaultProps"
            node-key="id"
            :check-strictly="true"
            :expand-on-click-node="false"
            highlight-current
            :default-expand-all="false"
            @node-click="nodeClick"
          >
            <div slot-scope="{ node, data }" class="tree-item">
              <span class="name">{{ LANG[data.content.title] || data.content.title }}</span>
              <div class="check-ex" style="right: 200px">
                <fks-checkbox
                  :value="menuOperateVisible(data)"
                  :disabled="disabled(data, node)"
                  :true-label="2"
                  :false-label="1"
                  @change="(val) => menuCheck(data, val, menuOperateVisible(data))"
                />
              </div>
              <div v-if="hierarchicalAuth" class="check-ex">
                <fks-checkbox
                  v-model="data.content.isSpread"
                  :disabled="form.operate === 'view' || !data.content.isAuth"
                  :true-label="2"
                  :false-label="1"
                  @change="(val) => authCheck(data, val)"
                />
              </div>
              <fks-button
                v-if="buttonInMenu[data.id] && buttonInMenu[data.id][0]"
                class="fks-icon-caret-right"
                style="right: 20px"
                type="text"
              />
            </div>
          </fks-tree>
        </div>
      </div>
      <!-- 菜单权限结束 -->
      <!-- 右侧功能权限开始 -->
      <div class="content">
        <div class="title" style="padding-right: 24px">
          <div class="title-fun">
            <span>{{ LANG.SELECT_FUN }}</span>
          </div>
          <div v-if="hierarchicalAuth" class="check-ex" style="right: 240px">
            <span> {{ LANG.OPERABLE }} </span>
          </div>
          <div v-if="hierarchicalAuth" class="check-ex" style="right: 120px">
            <span>
              {{ LANG.LICENSABLE }}
            </span>
          </div>
          <div class="title-data">
            <span>{{ LANG.DATA_PERMISSION }}</span>
          </div>
        </div>
        <div v-if="showButton" ref="buttons" class="data-box">
          <div class="tree-container">
            <fks-tree
              ref="buttonTree"
              :key="currentMenu ? currentMenu.id : 'buttonTree'"
              :data="buttonInMenu[currentMenu.id]"
              :props="defaultPropsB"
              node-key="id"
              :check-strictly="true"
              :expand-on-click-node="false"
              @check="buttonChange"
            >
              <div slot-scope="{ node, data }" class="tree-item">
                <span
                  v-if="!(scopeInButton[data.id] && scopeInButton[data.id].length > 0)"
                  class="name"
                  >{{ data.name }}</span
                >
                <!-- 资源权限 -->
                <fks-dropdown v-else :hide-on-click="false" trigger="click">
                  <div class="button-title">
                    <div>{{ data.name }}</div>
                    <fks-button type="text">
                      <i class="fks-icon-data-permission" />
                    </fks-button>
                  </div>
                  <fks-dropdown-menu slot="dropdown">
                    <fks-dropdown-item
                      v-for="scope in scopeInButton[data.id]"
                      :key="scope.id"
                      :label="scope.id"
                    >
                      <fks-checkbox
                        :value="scopeSelect.includes(scope.id)"
                        :disabled="form.operate === 'view'"
                        @change="(val) => scopeCheckChange(val, scope, data)"
                      >
                        {{ scope.scopeName }}
                      </fks-checkbox>
                      <fks-checkbox
                        v-if="hierarchicalAuth"
                        v-model="scope.isSpread"
                        :disabled="form.operate === 'view'"
                        :true-label="2"
                        :false-label="1"
                        :label="LANG.LICENSABLE"
                        @change="(val) => dataAuthChange(scope, val, data)"
                      />
                    </fks-dropdown-item>
                  </fks-dropdown-menu>
                </fks-dropdown>
                <div class="check-ex" style="right: 240px">
                  <fks-checkbox
                    :value="funOperateVisible(data)"
                    :disabled="disabledB(data, node)"
                    :true-label="2"
                    :false-label="1"
                    @change="(val) => buttonChange(data, val)"
                  />
                </div>
                <!-- 可授权，当前项已勾选且菜单可授权时 -->
                <div v-if="hierarchicalAuth" class="check-ex" style="right: 120px">
                  <fks-checkbox
                    v-model="data.isSpread"
                    :disabled="form.operate === 'view' || !data.isAuth"
                    :true-label="2"
                    :false-label="1"
                    @change="(val) => buttonAuth(data, val)"
                  />
                </div>
              </div>
            </fks-tree>
          </div>
        </div>
      </div>
    </div>
    <template slot="footer">
      <fks-button class="danger sub-btn" text @click="close" icon="fks-icon-close"
        >取 消</fks-button
      >

      <fks-button
        class="sub-btn"
        :loading="loading"
        icon="fks-icon-check"
        text
        @click="submit"
      >
        授 权
      </fks-button>
    </template>
  </fks-dialog>
</template>

<script>
import * as Auth from '../../api'
// import { ENUM, ADVANCED_CONFIG } from '@/store/State/stateTypes'
import { getDeleteData, getAddData, unique ,treeToArray} from '@/utils/util'
import * as ActionTypes from '@store/Action/actionTypes'
import LANG from '../../lang'
import { mapActions } from "vuex";
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          id: '',
          name: '',
          code: '',
          remark: '',
          authMap: {},
          type: '',
        }
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
    isProject: {
      type: Boolean,
      default: false
    },
    isCompanyPortal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 保存请求获取的{id: isSpread}
    this._menuRecord = {}
    this._buttonRecord = {}
    this._scopeRecord = {}
    return {
      defaultProps: {
        id: 'id',
        children: 'children',
        disabled: this.disabled,
      },
      defaultPropsB: {
        id: 'id',
        children: 'children',
        disabled: this.disabledB,
      },
      show: this.visible,
      form: Object.assign({}, this.data),
      currentMenu: {}, //当前选中菜单
      menuTree: [], //菜单权限树
      menuArray: [], //菜单权限列表
      menuSelect: [], //当前菜单权限
      menuRecord: [], //历史菜单权限
      menuChecked: false, //菜单权限全选
      authChecked: false, //菜单可授权全选
      authSelect: [], //菜单可授权勾选数组
      unLincenseMenus: [],
      buttonInMenu: {}, //根据menuId记录按钮
      buttonArray: [], //功能权限列表
      buttonSelect: [], //当前功能权限
      buttonRecord: [], //历史功能权限
      buttonChecked: false, //当前菜单下的功能权限全选
      scopeInButton: {}, //根据buttonId记录数据权限
      scopeArray: [], //数据权限列表
      scopeSelect: [], //当前数据权限
      scopeRecord: [], //历史数据权限
      loading: true,
      LANG: LANG,
      tempPortalId: '',
      tempRoleId: ''
    }
  },
  computed: {
    enClass() {
      // if (this.$lang == 'zh-CN') {
      //   return false
      // }
      // return true
      return false
    },
    portal() {
      //当前门户
      return this.$store.state.portal
    },
    portalType() {
      //门户类型
      return this.$store.state.enum?.portalType
    },
    hierarchicalAuth() {
      // 是否开启分级授权
      // return this.$store.state[ADVANCED_CONFIG].hierarchicalAuth
      return true
    },
    menuIds() {
      //菜单id
      return this.menuArray.map((menu) => menu.id)
    },
    showButton() {
      return (
        this.buttonInMenu[this.currentMenu.id] instanceof Array &&
        this.buttonInMenu[this.currentMenu.id].length > 0
      )
    },
    unauthMenus() {
      //不鉴权菜单id
      return this.menuArray
        .filter((menu) => !menu.content.isAuth)
        .map((menu) => {
          menu.content.isSpread = 2
          return menu.id
        })
    },
    buttonCheckedInMenu() {
      //已选功能id按菜单id划分
      let obj = {}
      for (let button of this.buttonArray) {
        if (this.buttonSelect.includes(button.id)) {
          if (obj.hasOwnProperty(button.menuId)) {
            obj[button.menuId].push(button.id)
          } else {
            this.$set(obj, button.menuId, [button.id])
          }
        }
      }
      return obj
    },
    unauthButtons() {
      //不鉴权功能id
      return this.buttonArray
        .filter((button) => !button.isAuth)
        .map((button) => {
          button.isSpread = 2
          return button.id
        })
    },
    defaultScopes() {
      //不鉴权数据id
      return this.scopeArray
        .filter((scope) => scope.isDefault)
        .map((scope) => {
          scope.isSpread = 2
          return scope.id
        })
    },
    scopeCheckedInButton() {
      //已选数据id按功能id划分
      let obj = {}
      for (let scope of this.scopeArray) {
        if (this.scopeSelect.includes(scope.id)) {
          if (obj.hasOwnProperty(scope.buttonId)) {
            obj[scope.buttonId].push(scope.id)
          } else {
            this.$set(obj, scope.buttonId, [scope.id])
          }
        }
      }
      return obj
    },
  },
  watch: {
    data() {
      this.form = Object.assign({}, this.data)
    },
    visible() {
      this.show = this.visible
    },
    /**
     * @description: 监听菜单选择，去除对应功能权限选中状态
     * @param {type}
     * @return:
     */
    menuSelect: {
      deep: true,
      handler: function (n, o) {
        //判断菜单是否全选
        this.$refs.menuTree && this.$refs.menuTree.setCheckedKeys(this.menuSelect)
        this.menuChecked = this.menuSelect.length == this.menuArray.length
        let authArray = []
        authArray = this.menuArray.filter((item) => {
          return item.content.isSpread == 2
        })
      },
    },
    authSelect: {
      deep: true,
      handler: function (n, o) {
        this.authChecked = this.authSelect.length == this.menuArray.length
      },
    },
    /**
     * @description: 监听菜单选择，去除对应功能权限选中状态
     * @param {type}
     * @return:
     */
    buttonSelect: {
      deep: true,
      handler: function (n, o) {
        //判断菜单是否全选
        this.$nextTick(() => {
          this.$refs.buttonTree && this.$refs.buttonTree.setCheckedKeys(this.buttonSelect)
        })
        if (!(this.currentMenu && this.currentMenu.id)) {
          return false
        }
        let currentL =
          (this.buttonInMenu[this.currentMenu.id] &&
            this.buttonInMenu[this.currentMenu.id].length) ||
          0
        let maxL =
          (this.buttonCheckedInMenu[this.currentMenu.id] &&
            this.buttonCheckedInMenu[this.currentMenu.id].length) ||
          0
        this.buttonChecked = currentL == maxL
      },
    },
    currentMenu: {
      deep: true,
      handler: function (n, o) {
        this.$nextTick(() => {
          this.$refs.buttonTree && this.$refs.buttonTree.setCheckedKeys(this.buttonSelect)
        })
        if (!(n && n.id)) {
          return false
        }
        let currentL = (this.buttonInMenu[n.id] && this.buttonInMenu[n.id].length) || 0
        let maxL = (this.buttonCheckedInMenu[n.id] && this.buttonCheckedInMenu[n.id].length) || 0
        this.buttonChecked = currentL == maxL
      },
    },
  },
  async created() {
     // 获取门户树
    await this[ActionTypes.GET_ENUMS]()
    this.open()
  },
  methods: {
    ...mapActions([ActionTypes.GET_ENUMS]),
    //打开弹窗
    async open() {
      this.currentMenu = {}
      // 获取portal信息
      if (!this.isProject) {
        const res = await Auth.getPortalInfo({
          roleId: this.form.id
        })
        this.tempPortalId = res.data ? res.data.portalId : ''
        this.tempRoleId = res.data ? res.data.roleId : ''
      }


      //获取所有权限信息
      await this.getInfo()

      //加载已有权限
      if (this.form.id) {
        await this.getRoleInfo()
      }
      //关闭loading
      this.$nextTick(() => {
        this.loading = false
      })
    },
    /**
     * @description: 加载完整权限
     * @return:
     */
    getInfo() {
      return new Promise((resolve) => {
        Promise.all([this.getMenuTree(), this.getAllButtons(), this.getDataScopesList()])
          .then((res) => {
            resolve(true)
          })
          .catch((e) => resolve(e))
      })
    },
    /**
     * @description: 获取菜单树
     * @param {type}
     * @return:
     */
    getMenuTree() {
      return new Promise(async (resolve, reject) => {
        let params = {
          type: this.form.type,
          // portalId: this.form.isGeneral ? '' : this.form.portalId,
          portalId: this.isProject ? this.form.portalId: this.tempPortalId,
          isAll: false,
        }
        this.menuArray = []
        this.menuTree = []
        Auth.getMenusTree(params)
          .then((res) => {
            if (res.status) {
              const filterList = this.filterItems([...res.data],this.isCompanyPortal)
              this.menuTree = filterList
              this.menuArray = treeToArray([...filterList])
            }
            resolve(true)
          })
          .catch(() => {
            reject(false)
          })
      })
    },
    filterItems(items,isCompanyPortal) {
      if (!items || !Array.isArray(items)) {
          return [];
      }
      let prop = isCompanyPortal ? 'VD_DATA_COMPARTMENT' : 'VD_PROJECT_SIGN'
      return items.filter(item => {
          const meta = item.content.meta ? JSON.parse(item.content.meta) : {};
          const config = meta.config || [];
          const flag = config.includes('VD_AUTH_SHOW') && config.includes(prop)
          if (flag && item.children) {
              item.children = this.filterItems(item.children, isCompanyPortal);
          }
          return flag;
      });
    },
    //获取全部的按钮数据
    async getAllButtons() {
      let params = {
        type: this.form.type,
        // portalId: this.form.isGeneral ? '' : this.form.portalId,
        portalId: this.isProject ? this.form.portalId: this.tempPortalId,
        isAll: false,
      }
      this.buttonInMenu = {}
      return Auth.getButtons(params)
        .then((res) => {
          if (res.status) {
            this.buttonArray = res.data
            for (let button of this.buttonArray) {
              if (this.buttonInMenu.hasOwnProperty(button.menuId)) {
                this.buttonInMenu[button.menuId].push(button)
              } else {
                this.$set(this.buttonInMenu, button.menuId, [button])
              }
            }
          }
        })
        .catch(() => {})
    },
    /**
     * @description: 获取数据权限数据
     * @param {type}
     * @return:
     */
    async getDataScopesList() {
      let params = {
        type: this.form.type,
        // portalId: this.form.isGeneral ? '' : this.form.portalId,
        portalId: this.isProject ? this.form.portalId: this.tempPortalId,
        isAll: !this.hierarchicalAuth,
      }
      return Auth.getDataScopeList(params)
        .then((res) => {
          if (res.status) {
            this.scopeArray = [...res.data]
            for (let scope of this.scopeArray) {
              if (this.scopeInButton.hasOwnProperty(scope.buttonId)) {
                this.scopeInButton[scope.buttonId].push(scope)
              } else {
                this.$set(this.scopeInButton, scope.buttonId, [scope])
              }
            }
          }
        })
        .catch(() => {})
    },
    /**
     * @description: 初始化当前角色权限
     * @return:
     */
    getRoleInfo() {
      return new Promise((resolve) => {
        Promise.all([this.getMenu(), this.getButton(), this.getDataScope()])
          .then((res) => {
            resolve(true)
          })
          .catch((e) => resolve(e))
      })
    },
    /**
     * @description: 获取角色已配置菜单
     * @return:
     */
    getMenu() {
      this.menuRecord = []
      this.menuSelect = []
      return new Promise((resolve, reject) => {
        let params = {}
        if (this.isProject) {
          params = {
            roleId: this.form.id,
            type: this.form.type
          }
        } else {
          params = {
            roleId: this.tempRoleId,
            portalId: this.tempPortalId,
            type: this.form.type
          }
        }
        Auth.getMenuByRole(params)
          .then((res) => {
            if (res.status && res.data) {
              for (let menu of res.data) {
                this.menuRecord.push(menu.id)
                this._menuRecord[menu.id] = menu.isSpread || 2
              }
            }
            this.menuSelect = unique([...this.menuRecord, ...this.unauthMenus])
            this.hierarchicalAuth &&
              this.menuArray.forEach((item) => {
                this._menuRecord[item.id] &&
                  this.$set(item.content, 'isSpread', this._menuRecord[item.id])
                if (item.content.isSpread == 2) {
                  this.authSelect.push(item.id)
                }
              })
            this.$forceUpdate()
            resolve(true)
          })
          .catch((error) => {
            reject(false)
          })
      })
    },
    /**
     * @description:获取角色已配置功能按钮
     * @return:
     */
    getButton() {
      this.buttonRecord = []
      this.buttonSelect = []
      return new Promise((resolve, reject) => {
        let params = {}
        if (this.isProject) {
          params = {
            roleId: this.form.id,
            type: this.form.type
          }
        } else {
          params = {
            roleId: this.tempRoleId,
            portalId: this.tempPortalId,
            type: this.form.type
          }
        }
        Auth.getButtonByRole(params)
          .then((res) => {
            if (res.status && res.data) {
              for (let button of res.data) {
                this.buttonRecord.push(button.id)
                this._buttonRecord[button.id] = button.isSpread || 2
              }
            }
            this.buttonSelect = unique([...this.buttonRecord, ...this.unauthButtons])
            this.hierarchicalAuth &&
              this.buttonArray.forEach((item) => {
                this._buttonRecord[item.id] &&
                  this.$set(item, 'isSpread', this._buttonRecord[item.id])
              })
            resolve(true)
          })
          .catch((error) => {
            reject(false)
          })
      })
    },
    /**
     * @description: 获取角色已配置资源包
     * @param {type}
     * @return:
     */
    getDataScope() {
      this.scopeRecord = []
      this.scopeSelect = []
      return new Promise((resolve, reject) => {
        let params = {}
        if (this.isProject) {
          params = {
            roleId: this.form.id,
          }
        } else {
          params = {
            roleId: this.tempRoleId,
            portalId: this.tempPortalId,
          }
        }
        Auth.getDataScopeByRole(params)
          .then((res) => {
            if (res.status && res.data) {
              for (let scope of res.data) {
                this.scopeRecord.push(scope.id)
                this._scopeRecord[scope.id] = scope.isSpread || 2
              }
            }
            this.scopeSelect = unique([...this.scopeRecord, ...this.defaultScopes])
            this.hierarchicalAuth &&
              this.scopeArray.forEach((item) => {
                this._scopeRecord[item.id] &&
                  this.$set(item, 'isSpread', this._scopeRecord[item.id])
              })
            resolve(true)
          })
          .catch((error) => {
            reject(false)
          })
      })
    },
    /**
     * @description: 菜单禁选处理
     * @return:
     */
    disabled(data, node) {
      if (this.form.code === 'superManager' || this.form.operate === 'view') {
        return true
      }
      // if (this.portal.type > this.portalType[0].code && this.form.isGeneral) {
      //   return true
      // }
      if (!data.content.isAuth) {
        return true
      }
      if (node.level > 1 && !this.menuSelect.includes(data.parentId)) {
        return true
      }
      return false
    },
    menuOperateVisible(data) {
      return this.menuSelect.includes(data.id)
    },
    buttonBoxChecked(data) {
      return this.buttonSelect.includes(data.id)
    },
    funOperateVisible(data) {
      return this.buttonSelect.includes(data.id)
    },
    /**
     * @description: 按钮禁选处理
     * @return:
     */
    disabledB(data, node) {
      if (this.form.roleCode === 'superManager' || this.form.operate === 'view') {
        return true
      }
      // if (this.portal.type > this.portalType[0].code && this.form.isGeneral) {
      //   return true
      // }
      if (!data.isAuth) {
        return true
      }
      if (!this.menuSelect.includes(data.menuId)) {
        return true
      }
      return false
    },
    /**
     * @description: 数据禁选处理
     * @return:
     */
    disabledS(data) {
      if (this.form.roleCode === 'superManager' || this.form.operate === 'view') {
        return true
      }
      if (this.portal.type > this.portalType[0].code && this.form.isGeneral) {
        return true
      }
      if (data.isDefault) {
        return true
      }
      if (!this.buttonSelect.includes(data.buttonId)) {
        return true
      }
      return false
    },
    /**
     * @description: 判断可授权框是否可见
     */
    spreadVisible(type, data, node) {
      switch (type) {
        case 'menu':
          return !(node.level !== 1 && node.parent.data.content.isSpread !== 2)
        case 'button':
          return node.checked && this.currentMenu.content.isSpread === 2
        case 'scope':
          return (
            this.scopeSelect.includes(data.id) &&
            data.buttonIsSpread === 2 &&
            this.currentMenu.content.isSpread === 2
          )
        default:
          return false
      }
    },
    /**
     * @description: 一键全选和一键取消全选菜单
     * @return:
     */
    selectWMenus() {
      if (this.menuChecked) {
        this.authChecked = true
        this.menuSelect = unique([...this.menuIds])
        this.authSelect = unique([...this.menuIds])
        this.selectAllAuth(this.menuTree, 2)
      } else {
        this.menuSelect = unique([...this.unauthMenus])
        this.authSelect = unique([...this.unauthMenus])
        this.buttonSelect = unique([...this.unauthButtons])
        this.scopeSelect = unique([...this.defaultScopes])
        this.selectAllAuth(this.menuTree, 1)
      }
    },
    authMenus() {
      if (this.authChecked) {
        this.menuChecked = true
        this.menuSelect = unique([...this.menuIds])
        this.authSelect = unique([...this.menuIds])
        this.selectAllAuth(this.menuTree, 2)
      } else {
        this.authSelect = unique([...this.unauthMenus])
        this.selectAllAuth(this.menuTree, 1)
      }
    },
    selectAllAuth(menuTree, type) {
      menuTree.forEach((item) => {
        if (item.content.isAuth) {
          this.$set(item.content, 'isSpread', type)
        }
        if (item.children) {
          this.selectAllAuth(item.children, type)
        }
      })
    },
    /**
     * @description:子节点处理方法
     * @param {data} 子节点数组
     * @param {Menu} 选中状态
     */
    determine(list, check) {
      list.forEach((item) => {
        if (item.content.isAuth) {
          if (check) {
            this.menuSelect.push(item.id)
          } else {
            if (this.menuSelect.includes(item.id)) {
              this.menuSelect.splice(this.menuSelect.indexOf(item.id), 1)
              this.$set(item.content, 'isSpread', 1)
            }
          }
          if (!check) {
            this.clearButtonForCheckChange(item.id)
          }
          if (item.children) {
            this.determine(item.children, check)
          }
        }
      })
    },
    authCheck(menu, val, type) {
      if (menu.content.isAuth) {
        this.$set(menu.content, 'isSpread', val)
        if (val == 2) {
          if (!this.authSelect.includes(menu.id)) {
            this.authSelect.push(menu.id)
          }
          if (!this.menuSelect.includes(menu.id)) {
            this.menuSelect.push(menu.id)
          }
        } else {
          if (this.authSelect.includes(menu.id)) {
            this.authSelect.splice(this.authSelect.indexOf(menu.id), 1)
          }
        }
        if (menu.children) {
          menu.children.forEach((item) => {
            this.authCheck(item, val)
          })
        }
      }
    },
    extraPermDetermine(menu, status) {
      this.$nextTick(() => {
        let checked = 1
        status ? (checked = 1) : (checked = 2)
        if (menu.content.isAuth) {
          this.$set(menu.content, 'isSpread', checked)
          if (!status) {
            if (!this.authSelect.includes(menu.id)) {
              this.authSelect.push(menu.id)
            }
          } else {
            if (this.authSelect.includes(menu.id)) {
              this.authSelect.splice(this.authSelect.indexOf(menu.id), 1)
            }
          }
          if (menu.children) {
            menu.children.forEach((item) => {
              this.extraPermDetermine(item, status)
            })
          }
        }
      })
    },
    menuCheck(menu, val, status) {
      this.$nextTick(() => {
        this.extraPermDetermine(menu, status)
        if (!this.menuSelect.includes(menu.id)) {
          this.menuSelect.push(menu.id)
          if (menu.children) {
            //循环处理子节点
            this.determine(menu.children, true)
          }
        } else {
          this.$set(menu.content, 'isSpread', 1)
          if (this.menuSelect.includes(menu.id)) {
            this.menuSelect.splice(this.menuSelect.indexOf(menu.id), 1)
          }
          if (menu.children) {
            //循环处理子节点
            this.determine(menu.children, false)
          }
        }
        if (this.menuSelect.includes(menu.id)) {
          this.clearButtonForCheckChange(menu.id)
        }
      })
    },

    /**
     * @description:清除菜单管理的功能权限
     * @param {menuId} 菜单id
     * @return:
     */
    clearButtonForCheckChange(menuId) {
      let list = this.buttonInMenu[menuId]
      if (list && list.length) {
        list.forEach((item) => {
          if (this.buttonSelect.includes(item.id)) {
            if (item.isAuth) {
              this.buttonSelect.splice(this.buttonSelect.indexOf(item.id), 1)
            }
          }
        })
      }
    },

    /**
     * @description:点击菜单树
     * @param {Menu}
     * @return:
     *
     */
    nodeClick(row, node) {
      this.currentMenu = row
    },

    /**
     * @description: 菜单更多操作
     * @return:
     */
    handleCommand(command) {
      switch (command) {
        case 'collapse':
          this.collapseAll()
          break
        case 'expand':
          this.expandAll()
          break
      }
    },

    /**
     * @description: 一键收起菜单
     * @return:
     */
    collapseAll() {
      this.$nextTick(() => {
        this.menuArray.forEach((el) => {
          this.$refs.menuTree.store.nodesMap[el.id].expanded = false
        })
      })
    },

    /**
     * @description: 一键展开菜单
     * @return:
     */
    expandAll() {
      this.$nextTick(() => {
        this.menuArray.forEach((el) => {
          this.$refs.menuTree.store.nodesMap[el.id].expanded = true
        })
      })
    },

    /**
     * @description: 一键全选和一键取消对应菜单下的全选功能权限
     * @return:
     */
    selectWButtons(list) {
      if (this.buttonChecked) {
        list.forEach((b) => {
          this.buttonSelect.push(b.id)
        })
        this.buttonSelect = unique(this.buttonSelect)
      } else {
        list.forEach((b) => {
          if (b.isAuth) {
            this.buttonSelect.splice(this.buttonSelect.indexOf(b.id), 1)
          }
        })
      }
    },
    buttonAuth(data, val) {
      if (!this.buttonSelect.includes(data.id)) {
        this.buttonSelect.push(data.id)
      }
      if (val == 1 && this.scopeInButton[data.id]) {
        this.$set(this.scopeInButton[data.id][0], 'isSpread', 1)
      }
    },
    /**
     * @description: 功能选中切换
     * @return:
     */
    buttonChange(data, val) {
      if (!this.buttonSelect.includes(data.id)) {
        this.$set(data, 'isSpread', 2)
        this.buttonSelect.push(data.id)
      } else {
        this.buttonSelect.splice(this.buttonSelect.indexOf(data.id), 1)
        this.clearScopeForCheckChange(data.id)
        this.$set(data, 'isSpread', 1)
        if (this.scopeInButton[data.id]) {
          this.$set(this.scopeInButton[data.id][0], 'isSpread', 1)
        }
      }
    },
    /**
     * @description: 数据权限切换
     * @return:
     */
    scopeCheckChange(val, scope, data) {
      if (val) {
        if (!this.buttonSelect.includes(data.id)) {
          this.$set(data, 'isSpread', 2)
          this.buttonSelect.push(data.id)
        }
        this.$set(scope, 'isSpread', 2)
        this.scopeSelect.push(scope.id)
        this.scopeSelect = unique(this.scopeSelect)
        this.$set(data, 'isSpread', 2)
      } else {
        this.scopeSelect.splice(this.scopeSelect.indexOf(scope.id), 1)
        this.$set(scope, 'isSpread', 1)
      }
    },
    dataAuthChange(data, val, buttonData) {
      if (val == 2) {
        this.$set(buttonData, 'isSpread', 2)
      }
      if (!this.buttonSelect.includes(data.id)) {
        this.buttonSelect.push(data.id)
      }
      if (val == 2 && !this.scopeSelect.includes(data.id)) {
        this.scopeSelect.push(data.id)
      }
    },
    /**
     * @description:清除菜单管理的功能权限
     * @param {menuId} 菜单id
     * @return:
     */
    clearScopeForCheckChange(buttonId) {
      let list = this.scopeInButton[buttonId]
      if (list && list.length) {
        list.forEach((item) => {
          if (this.scopeSelect.includes(item.id)) {
            if (!item.isDefault) {
              this.scopeSelect.splice(this.scopeSelect.indexOf(item.id), 1)
            }
          }
        })
      }
    },

    /**
     * @description: 授权提交
     * @param {String} formName
     * @return: void
     */
    submit() {
      // 筛选出不鉴权内容，并生成id对象
      let authMenuList = getAddData(this.menuSelect, this.unauthMenus)
      let menuObj = this.menuArray.reduce((obj, menu) => {
        if (!authMenuList.includes(menu.id)) return obj
        if (!this.hierarchicalAuth) {
          obj[menu.id] = 2
          return obj
        }
        obj[menu.id] = menu.content.isSpread || 1
        return obj
      }, {})
      let authButtonList = getAddData(this.buttonSelect, this.unauthButtons)
      let buttonObj = this.buttonArray.reduce((obj, item) => {
        if (!authButtonList.includes(item.id)) return obj
        if (!this.hierarchicalAuth) {
          obj[item.id] = 2
          return obj
        }
        // 判断menu是否可授权
        obj[item.id] =
          menuObj[item.menuId] === 2 || this.unauthMenus.includes(item.menuId)
            ? item.isSpread || 1
            : 1
        return obj
      }, {})
      let authScopeList = getAddData(this.scopeSelect, this.defaultScopes)
      let scopeObj = this.scopeArray.reduce((obj, item) => {
        if (!authScopeList.includes(item.id)) return obj
        if (!this.hierarchicalAuth) {
          obj[item.id] = 2
          return obj
        }
        // 判断button是否可授权
        obj[item.id] =
          buttonObj[item.buttonId] === 2 || this.unauthButtons.includes(item.buttonId)
            ? item.isSpread || 1
            : 1
        return obj
      }, {})
      /**
       * 生成Map
       * authObj: 选中的id对象
       * select： 选中的id数组
       * record： 初始选中的值id数组
       * _record: 初始选中的值id对象
       */
      const getAuthMap = (authObj, select, record, _record) => {
        const addObj = getAddData(select, record).reduce((obj, id) => {
          obj[id] = authObj[id]
          return obj
        }, {})
        const deleteObj = getDeleteData(select, record).reduce((obj, id) => {
          obj[id] = this.hierarchicalAuth ? 1 : 2
          return obj
        }, {})
        const updateObj = !this.hierarchicalAuth
          ? {}
          : select.reduce((obj, id) => {
              if (addObj[id]) return obj
              if (_record[id] === authObj[id]) return obj
              obj[id] = authObj[id]
              return obj
            }, {})
        return {
          POST: addObj,
          DELETE: deleteObj,
          PUT: updateObj,
        }
      }
      let authMap = {
        MENU: getAuthMap(menuObj, authMenuList, this.menuRecord, this._menuRecord),
        BUTTON: getAuthMap(buttonObj, authButtonList, this.buttonRecord, this._buttonRecord),
        DATASCOPE: getAuthMap(scopeObj, authScopeList, this.scopeRecord, this._scopeRecord),
      }
      this.form.authMap = authMap
      if (this.isProject) {
        Auth.saveRoleV1(this.form)
        .then((res) => {
          if (res.status) {
            this.$message({
              message: '保存成功',
              type: 'success',
              duration: 3000,
            })
            this.close()
          }
        })
        .catch(() => {})
      } else {
        Auth.saveRoleV1Own({
          jsonObject: this.form,
          roleId: this.form.id
        })
        .then((res) => {
          if (res.status) {
            this.$message({
              message: '保存成功',
              type: 'success',
              duration: 3000,
            })
            this.close()
          }
        })
        .catch(() => {})
      }

    },
    close() {
      this.show = false
      this.$emit('update:visible', false)
    },
  },
}
</script>
<style scoped lang="less">
@import '../../exclude/role.css';

.container {
  display: flex;
  justify-content: space-between;
  min-width: 380px;
  // margin-bottom: 22px;

  .check-ex {
    position: absolute;
    top: 0;
    right: 50px;
    display: inline-grid;
    grid-template-columns: 100%;
    align-items: center;
    width: 120px;
    height: 100%;
    font-size: 16px;
    color: #191919;
    justify-items: center;

    > span,
    > .fks-checkbox {
      margin-right: 0;
    }
  }

  .content {
    display: inline-block;
    width: 60%;
    min-width: 180px;
    border: 1px solid #f4f4f4;
    transition: width 0.3s;

    .tree-container {
      width: 100%;
      height: calc(80vh - 215px);
      overflow: auto;

      .tree-item {
        display: flex;
        width: 100%;
      }

      ::v-deep .fks-tree-node__content {
        position: relative;

        .fks-dropdown {
          flex: 1;
          height: 100%;

          .name {
            vertical-align: text-top;
          }

          > div {
            height: 36px;
            line-height: 36px;
            vertical-align: middle;
          }
        }

        .fks-button {
          position: absolute;
          top: 0;
          right: 50px;
        }
      }
    }

    .title {
      position: relative;
      padding-top: 13px;
      padding-bottom: 13px;
      border-bottom: 1px solid #f4f4f4;
      // font-weight: bold;
      font-size: 16px;
      text-align: center;
      color: #191919;
      background-color: #f9f9f9;

      &-menu {
        width: 100%;
        margin-left: 24px;
        text-align: left;
      }

      &-fun {
        position: absolute;
        left: 24px;
      }

      &-data {
        width: 100%;
        margin-right: 24px;
        text-align: right;
      }

      .fks-checkbox {
        position: absolute;
        left: 18px;
      }

      .fks-dropdown {
        position: absolute;
        top: 16px;
        right: 24px;
      }
    }
  }

  .enClass {
    margin-left: 24px;
  }
}

/* 顶部间距8vh时弹窗高度 */
.dialog-8vh {
  /deep/ .fks-dialog__body {
    max-height: calc(92vh - 175px) !important;
  }
}

.role-auth-dialog ::v-deep(.fks-dialog) {
  max-width: 95vw;
}

.role-auth-dialog ::v-deep(.fks-dialog__body) {
  padding: 24px 0 !important;
}

</style>
