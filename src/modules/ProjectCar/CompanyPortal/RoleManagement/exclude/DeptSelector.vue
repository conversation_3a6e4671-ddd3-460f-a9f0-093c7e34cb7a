<template>
  <div style="box-shadow: 3px 0px 8px 0px rgba(0, 0, 0, 0.03)" class="tree-selector">
    <div key="open" class="flex flex-column">
      <div class="tree-header flex col-center row-between">
        <span class="tab-title" style="color: #333">角色层级</span>
        <span class="fks-input__icon fks-icon-search" @click="showSearch = !showSearch"></span>
      </div>
      <div class="tree-search" v-if="showSearch">
        <fks-input  v-model="keyword" placeholder="请输入搜索内容"></fks-input>
      </div>
      <!-- 树结构 -->
      <div class="tree-selector__body flex-grow-1">
        <div
          v-for="(item, index) in deptTypes"
          @click="chooseCommonOrg(item)" class="common-org" :class="item.choose ? 'choose-org' : ''">
          <img alt="" height="16" src="@/assets/img/companyIndex/org.svg"/>
          <span style="min-width: 70px">{{item.value}}</span>
          <card-tag
            :tag="{'text': '通用','color':'#3C83FF'}"
            style="line-height: 8px;margin-left: 4px;"
          />
        </div>
        <fks-tree
          :data="filteredTreeData"
          :props="defaultProps"
          :expand-on-click-node="false"
          default-expand-all
          highlight-current
          node-key="id"
          :filter-node-method="filterNodeMethod"
          @node-click="handleClickNode"
          ref="tree"
        >
        </fks-tree>
      </div>
    </div>
  </div>
</template>

<script>
import globalSearchMixin from '@/mixins/globalSearchMixin'
import { mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import CardTag from '@components/CardFlow/components/tag.vue'

export default {
  name: 'DeptSelector',
  components: { CardTag },
  mixins: [globalSearchMixin],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    modelValue: {
      type: [String, Number, Array],
      default: null,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    isCollapsed: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      showSearch: false,
      // 用于控制是否收起
      keyword: '',
      selectedValue: this.modelValue, // 本地维护选中的值
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      deptTypes: [],
      dropdownVisible: false,
      treeHeight: 0,
      chooseOrg: true,
    }
  },
  watch: {
  },

  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    // 根据搜索关键字，对树数据进行简单过滤
    filteredData() {
      if (!this.keyword.trim()) {
        return this.data
      }
      return this.filterTree(this.data, this.keyword)
    },
    deptTypeList() {
      return this.enums.RoleTypeEnums;
    },
    filteredTreeData() {
      const keyword = this.keyword.toLowerCase(); // 小写化关键词，进行不区分大小写的匹配

      // 递归函数来遍历树数据
      const filterNodes = (nodes) => {
        return nodes
          .map((node) => {
            // 检查当前节点的 name 是否包含 keyword
            const matchesName = node.name.toLowerCase().includes(keyword);
            const matchesChildren = node.children && node.children.length > 0
              ? filterNodes(node.children) // 递归检查子节点
              : [];

            // 如果当前节点或其子节点匹配，则返回新节点，包含匹配的子节点
            if (matchesName || matchesChildren.length > 0) {
              return {
                ...node,
                children: matchesChildren, // 保留匹配的子节点
              };
            }
            return null; // 不匹配的节点返回 null
          })
          .filter((node) => node !== null); // 过滤掉 null 节点
      };

      // 从树数据中筛选符合条件的节点
      return filterNodes(this.data);
    },
  },
  mounted() {
    if (this.deptTypeList.length > 0) {
      this.deptTypes = this.deptTypeList.map(item => {
        return {
          ...item,
          choose: false
        }
      })
      console.log("调用一次选择通用")
      this.chooseCommonOrg(this.deptTypes[0])
    }
    this.$nextTick(() => {
      this.getPcMainHeight()
    })
    window.addEventListener('resize', this.getPcMainHeight) // 监听窗口大小变化
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getPcMainHeight) // 清理监听器
  },
  methods: {
    chooseCommonOrg(item) {
      this.deptTypes.forEach(item => {
        item.choose = false;
      })
      this.$refs.tree.setCurrentKey(null);
      item.choose = true;
      this.$emit('choose-common', item)
    },
    handleClickNode(node, treeNode) {

      this.deptTypes.forEach(item => {
        item.choose = false;
      })
      this.$emit('choose-dept', node)
      // if (!node.children || node.children.length === 0) {
      //   this.deptTypes.forEach(item => {
      //     item.choose = false;
      //   })
      //   this.$emit('choose-dept', node)
      // } else {
      //   // 非叶子节点，只展开或收缩
      //   treeNode.expanded = !treeNode.expanded;
      // }
    },
    filterNodeMethod(value, data) {
      if (!value) return true;
      const labelProp = this.defaultProps.label
      const label = this.LANG[data[labelProp]] ? this.LANG[data[labelProp]] : data[labelProp]
      return label.indexOf(value) !== -1;
    },
    globalSearch() {
      this.keyword = this[StateTypes.GLOBAL_QUERY_PARAMS].searchValue || ''
    },
    // 递归过滤树节点
    filterTree(treeData, keyword) {
      const result = []
      for (let node of treeData) {
        const tempNode = { ...node }
        const isMatch = tempNode.label.toLowerCase().includes(keyword.toLowerCase())

        if (tempNode.children && tempNode.children.length > 0) {
          tempNode.children = this.filterTree(tempNode.children, keyword)
        }

        if (isMatch || (tempNode.children && tempNode.children.length > 0)) {
          result.push(tempNode)
        }
      }
      return result
    },

    // 勾选时触发
    handleCheck(checkedNodes, treeData) {
      // 获取当前树的所有叶子节点
      const allCheckNods = treeData.checkedNodes
      let checkedLeafNode = allCheckNods.filter(
        (node) => !node.children || node.children.length <= 0
      )
      let projectIds = checkedLeafNode.map((node) => node.id)
      this.$emit('choose-project', projectIds)
    },

    /**
     * 递归获取所有叶子节点
     * @param {Array} data - 树结构数据
     * @return {Array} - 返回所有叶子节点数组
     */
    getAllLeafNodes(data) {
      let leaves = []
      data.forEach((item) => {
        if (!item.children || item.children.length === 0) {
          // 没有 children 或 children 为空 => 叶子节点
          leaves.push(item)
        } else {
          // 递归处理子节点
          leaves = leaves.concat(this.getAllLeafNodes(item.children))
        }
      })
      return leaves
    },
    getPcMainHeight() {
      let height = 600
      let pcMain = document.getElementById('pcMain')
      if (pcMain) {
        height = pcMain.offsetHeight - 66 - 15
      }
      this.treeHeight = height
    },
  },
}
</script>

<style lang="less" scoped>
.tree-selector {
  /* 外层容器尽量包含足够的宽度，方便切换 */
  font-size: 14px;
  height: 100%;
  width: 260px;
  box-sizing: border-box;
  border-radius: 0 6px 6px 0; /* 顺时针设置 */
  background-color: #fffffd;
  display: flex;
  flex-direction: row; /* 注意这里用 row 横向布局，左边树 + 右边按钮 */
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}

//通过自定义树形结构行内容，实现文本过多时数据不显示的问题，现在效果为显示省略号，且鼠标移上去会显示出全部文本内容
.custom-tree-node {
  width: 99%;
  overflow: hidden !important; // 溢出部分隐藏
  white-space: nowrap !important; //禁止自动换行
  text-overflow: ellipsis !important; // 使溢出部分以省略号显示
  display: block !important;
}

/* 树区域 */
.tree-selector__body {
  overflow-y: auto;
  max-height: calc(100vh - 220px);
  width: 260px;
  padding: 0 8px;
  box-sizing: border-box;
  user-select: none;

  .common-org {
    height: 36px;
    cursor: pointer;
    width: 100%;
    gap: 4px;
    padding: 0 6px;
    margin: 2px 0;
    display: flex;
    align-items: center;
    border-radius: 6px;
    box-sizing: border-box;
  }
  .common-org:hover {
    background: #DCE4FA;
  }
  .choose-org {
    color: #3C83FF;
    background: #DCE4FA;
  }
  .choose-org img {
    fill: currentColor;
  }
}

.tree-header {
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0px;
  color: #333333;
  margin-bottom: 10px;
  padding: 14px 18px 0;
  user-select: none;

  .tab-title {
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0px;
    color: #333333;
  }
}
.tree-search {
  padding: 0 18px;
  margin-bottom: 6px
}
</style>

<style lang="scss">

.fks-tree-node:hover {
  border-radius: 6px !important;
}
.fks-tree--highlight-curren {
  background: #DCE4FA;
  color: #3C83FF;
}
.tree-search .fks-input--medium .fks-input__inner {
   line-height: 32px !important;
   height: 32px !important;
 }
.tree-header .fks-icon--right {
  margin-left: 0;
}
.tree-header .fks-dropdown-link {
  color: #333333;
  font-weight: normal;
}
</style>
