import request from '@/utils/request'

export function drivingRepoExport(params) {
  return request({
    url: '/vehicle-dispatch/vd/dataCabin/xc/page/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 行程舱查询参数
export function getDrivingRepoSearchParam(params) {
  return request({
    url: '/vehicle-dispatch/vd/dataCabin/xc/page/getParam',
    params
  })
}

// 行程舱数据分页
export function getDrivingRepoPage (data, params) {
  return request({
    url: '/vehicle-dispatch/vd/dataCabin/xc/page',
    method: 'post',
    data,
    params
  })
}
