import request from '@/utils/request'

// 用户分页列表
export function getUserList(params) {
  return request({
    url: '/vehicle-dispatch/user/page',
    params
  })
}

export function getUserListByName(params) {
  return request({
    url: '/vehicle-dispatch/user/list',
    params: {userNames: params}
  })
}


// 获取用户账号状态
export function getUserStatus(userIds) {
  return request({
    url: '/vehicle-dispatch/user/status',
    params: {userIds}
  })
}

// 用户角色信息
export function getUserRoleInfo(userIds) {
  return request({
    url: '/vehicle-dispatch/user/role/list',
    params: {userIds}
  })
}

// 公司角色列表
export function getCompanyRoleList() {
  return request({
    url: '/vehicle-dispatch/role/config/list'
  })
}

// 项目角色列表
export function getProjectRoleList(projectId) {
  return request({
    url: '/vehicle-dispatch/role/list',
    params: {projectId}
  })
}


// 项目角色列表
export function getUserByRole(roleIds) {
  return request({
    url: '/vehicle-dispatch/role/users',
    params: {roleIds}
  })
}

// 角色菜单列表
export function getRoleMenuList(ids) {
  return request({
    url: '/vehicle-dispatch/role/menu/list',
    params: {ids}
  })
}

// 新增角色
export function addUser(data) {
  return request({
    url: '/vehicle-dispatch/user/add',
    method: 'post',
    data
  })
}

export function updateUser(data) {
  return request({
    url: '/vehicle-dispatch/user/update',
    method: 'post',
    data
  })
}

// 删除角色
export function deleteRole(data) {
  return request({
    url: '/vehicle-dispatch/user/role/del',
    method: 'post',
    data
  })
}

// 角色分配
export function assignRole(data) {
  return request({
    url: '/vehicle-dispatch/user/role/put',
    method: 'post',
    data
  })
}

// 单用户多角色分配
export function assignMultipleRoles(data) {
  return request({
    url: '/vehicle-dispatch/user/role/batchPut',
    method: 'post',
    data
  })
}

// 单用户多角色分配
export function removeMultipleRoles(data) {
  return request({
    url: '/vehicle-dispatch/user/role/delPut',
    method: 'post',
    data
  })
}


export function getUserSearchParam(params) {
  return request({
    url: '/vehicle-dispatch/vd/user/page/getParam',
    params
  })
}

export function getUserListByPost(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/user/page',
    method: 'post',
    data,
    params
  })
}

// 导出用户列表
export function exportUserInfo(params) {
  return request({
    url: '/vehicle-dispatch/vd/user/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}


export function getDeptsInFeishu(params) {
  /*
* openId: 飞书用户unionId
* pageSize: 每页条数
* pageToken: 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
* departmentName: 部门名称
* */
  return request({
    url: '/vehicle-dispatch/wrap/feishu/dept/search',
    params
  })
}

export function getSubDeptsInFeishu(params) {
  /*
  * openId: 飞书用户unionId
  * pageSize: 每页条数
  * pageToken: 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
  * departmentId: departmentId
  * */
  return request({
    url: '/vehicle-dispatch/wrap/feishu/dept/children',
    params
  })
}



export function updateAccountStatus(data) {
  return request({
    url: '/vehicle-dispatch/vd/user/updateAccountStatus',
    method: 'get',
    params: {...data }
  })
}

export function hasPhoneExist(phone) {
  return request({
    url: '/vehicle-dispatch/user/phone/exist',
    params: {phone}
  })
}
export function getCodeBizid(data) {
  return request({
    url: `/vehicle-dispatch/vd/xcck/info`,
    method: 'get',
    params: data
  })
}


export function vdAndSysUserExistByName(userName) {
  return request({
    url: '/vehicle-dispatch/vd/user/name/exist',
    params: {userName}
  })
}

export function vdAndSysUserExistByPhone(userPhone) {
  return request({
    url: '/vehicle-dispatch/vd/user/phone/exist',
    params: {userPhone}
  })
}

export function getAuthPhone(data) {
  return request({
    url: `/vehicle-dispatch/vd/xcck/verify/phone`,
    method: 'get',
    params: data
  })
}