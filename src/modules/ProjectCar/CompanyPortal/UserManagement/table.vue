<template>
  <div>
    <TitleKit>
      <template #left>
        <div style="font-size: 16px; font-weight: 600">用户列表</div>
      </template>
    </TitleKit>
    <TempTable
      :data-list="tableData"
      :table-config.sync="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      @add-item="handleAdd"
      @searchData="searchUserInfo"
      @clickRow="handleClickRow"
      @export-data="handleExport"
      :showButtons="showButtons"
      :current-page.sync="pageNo"
    >
      <template v-slot:column-accountStatus="{ scope }">
        <card-tag
          v-if="scope.row.accountStatus"
          class="p-24 p-t-8 p-b-8"
          :tag="{
            color: getTagType(scope.row.accountStatus, 'accountStatus'),
            text: getTagText(scope.row.accountStatus, 'accountStatus'),
          }"
        />
<!--        <fks-tag-->
<!--          v-if="scope.row.accountStatus"-->
<!--          :type="getTagType(scope.row.accountStatus, 'accountStatus')"-->
<!--          >{{ getTagText(scope.row.accountStatus, 'accountStatus') }}</fks-tag-->
<!--        >-->
      </template>

      <template v-slot:column-option="{ scope }">
        <fks-button
          v-if="scope.row.accountStatus !== 1 && getAuth('edit')"
          :loading="loading"
          text
          @click="openUser(scope.row)"
        >
          激活账号
        </fks-button>
        <fks-button
          v-if="scope.row.accountStatus === 1 && getAuth('edit')"
          :loading="loading"
          dangerText
          @click="closeUser(scope.row)"
          class="second-text"
        >
          停用账号
        </fks-button>
      </template>
    </TempTable>

    <fks-dialog :visible.sync="closeDialog" :before-close="onCancel" size="small">
      <div slot="title" class="header-title">
        <span style="color: #ff4143; font-size: 16px">停用账号</span>
      </div>

      <div style="padding: 8px 20px">
        <span>您确认停用该账号吗？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="onCancel">取 消</fks-button>
        <fks-button type="primary" @click="() => onConfirm('close')">确 定</fks-button>
      </span>
    </fks-dialog>
    <fks-dialog title="激活账号" :visible.sync="openDialog" :before-close="onCancel" size="small">
      <div style="padding: 16px 20px">
        <span>您确定恢复该账号吗？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="onCancel">取 消</fks-button>
        <fks-button type="primary" @click="() => onConfirm('open')">确 定</fks-button>
      </span>
    </fks-dialog>

    <UserForm ref="userForm" @refresh="reSearchData" />
  </div>
</template>

<script>
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import UserForm from '@modules/ProjectCar/CompanyPortal/UserManagement/components/UserForm.vue'
import {
  exportUserInfo,
  getUserListByPost,
  getUserRoleInfo,
  getUserSearchParam,
  getUserStatus,
  updateAccountStatus,
} from '@modules/ProjectCar/CompanyPortal/UserManagement/api'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import { mapGetters, mapState } from 'vuex'
import { fileReader } from '@utils/exportFile'
import * as StateTypes from '@store/State/stateTypes'
import globalSearchMixin from '@/mixins/globalSearchMixin'
import CardTag from '@components/CardFlow/components/tag.vue'
import { getAuth } from '@utils/buttonAuth'
import * as GetterTypes from '@store/Getter/getterTypes'
export default {
  name: 'UserManagement',
  components: { CardTag, TitleKit, TempTable, UserForm },
  mixins: [globalSearchMixin],
  data() {
    return {
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['add', 'filter'],
      queryParam: {},
      tableData: [],
      openDialog: false,
      closeDialog: false,
      chooseUserId: null,
      selectedFields: [
        'userName',
        'userFullName',
        'userPhone',
        'userDepName',
        'userDepName2',
        'userResource',
        'accountStatus',
      ],
      tableConfig: [
        { label: '用户名', prop: 'userName', group: '用户信息', clickable: true },
        { label: '姓名', prop: 'userFullName', group: '用户信息' },
        { label: '联系电话', prop: 'userPhone', group: '用户信息' },
        { label: '二级单位', prop: 'userDepName', group: '部门信息' },
        { label: '三级部门', prop: 'userDepName2', group: '部门信息' },
        { label: '用户来源', prop: 'userResource', group: '状态信息' },
        { label: '账号状态', prop: 'accountStatus', group: '状态信息', customer: true },
        {
          label: '操作',
          prop: 'option',
          group: '系统操作',
          fixed: 'right',
          width: 120,
          customer: true,
        },
      ],
      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    userResource() {
      return this.enums.ContactsResourceEnums
    },
    userAccountStatus() {
      return this.enums.AccountStatusEnums
    },
  },
  created() {
    this.tableConfig.forEach((item) => {
      if (item.prop === 'userResource') {
        item.enums = this.userResource
      }
      if (item.prop === 'accountStatus') {
        item.enums = this.userAccountStatus
      }
    })
    this.loadMoreUserInfo()
    getUserSearchParam().then((res) => {
      this.searchConfigs = res.data
    })
  },
  methods: {
    getAuth,
    globalSearch() {
      this.tableData = []
      this.reSearchData()
    },
    onConfirm(option) {
      this.openDialog = false
      this.closeDialog = false
      let data = {
        id: this.chooseUserId,
        accountStatus: '3',
      }
      if (option === 'open') {
        data.accountStatus = '1'
      }
      updateAccountStatus(data).then((res) => {
        if (res.status) {
          this.$message.success('操作成功！')
          this.reSearchData(true)
        }
      })
    },
    onCancel() {
      this.openDialog = false
      this.closeDialog = false
    },
    openUser(row) {
      let userId = row.id
      this.chooseUserId = userId
      this.openDialog = true
    },
    closeUser(row) {
      let userId = row.id
      this.chooseUserId = userId
      this.closeDialog = true
    },
    getTagText(value, prop) {
      if (prop === 'accountStatus') {
        return this.userAccountStatus.find((item) => item.key == value).value
      }
      return value
    },
    getTagType(value, prop) {
      let status = {}
      if (prop === 'accountStatus') {
        status = {
          1: '#40bb5a',
          2: '#ffa01e',
          3: '#ff4d4f',
          // 1: 'success',
          // 2: 'warning',
          // 3: 'danger',
        }
      }
      return status[value]
    },
    reSearchData(refresh) {
      if (refresh) {
        this.pageNo = 1
      }
      this.loadMoreUserInfo(this.queryParam)
    },
    handleAdd() {
      this.$refs.userForm.open({}, 'plus')
    },
    handleExport(list) {
      list = list.filter((item) => {
        let configItem = this.tableConfig.find((config) => config.prop === item.field)
        return item.fieldName !== '操作'
      })
      let data = {
        pageGetParam: { ...this.queryParams },
        fields: list,
      }
      exportUserInfo(data).then((res) => {
        try {
          fileReader(res)
        } catch (error) {
          console.error('导出失败:', error)
          alert('导出失败，请稍后重试。')
        }
      })
    },
    handleClickRow(row, prop) {
      this.$refs.userForm.open(row, 'edit')
    },
    searchUserInfo(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.loadMoreUserInfo(queryParams)
    },
    async loadMoreUserInfo(query) {
      this.loading = true
      this.queryParam = { ...query }
      let param = {
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        needOrg: false,
        ...this.queryParam,
      }

      const startTime = performance.now() // 记录请求开始时间

      try {
        const response = await getUserListByPost(param, this[GetterTypes.GET_GLOBAL_STATE]) // 使用 await 获取响应
        let duration = 0

        if (response.status) {
          const newData = await this.processTableData(response.data) // 确保此处处理数据时使用 await

          this.tableData = [...newData]
          duration = performance.now() - startTime // 计算耗时
        }
        setTimeout(
          () => {
            this.loading = false
          },
          duration < 200 ? 150 : 0
        )
      } catch (error) {
        console.error('Error loading user info:', error)
        this.loading = false
      }
    },
    async processTableData({ list, total, isLastPage }) {
      // 获取用户状态，账号类型
      const userIds = list.map((item) => item.userId).join(',')
      const { data: userStatus = {} } = await getUserStatus(userIds)
      const { data: userRoleInfo = {} } = await getUserRoleInfo(userIds)

      this.total = total
      this.finished = isLastPage

      return list.map((item) => {
        const status = userStatus[item.userId] || {}
        const roleMap = userRoleInfo.userRoleMap || {}

        return {
          ...item,
          accountStatus: status.accountStatus ?? null,
          statusInfo: status,
          roleInfo: roleMap[item.userId] ?? null,
        }
      })
    }
  },
}
</script>

<style scoped lang="scss"></style>
