<template>
  <fks-input v-if="disabled" :value="initialValue" :disabled="true" />
  <fks-select
    ref="selectRef"
    v-else
    v-model="searchValue"
    filterable
    :loading="loading"
    :disabled="disabled"
    :remote="true"
    :remote-method="remoteMethod"
    :reserve-keyword="true"
    :placeholder="placeholder"
    class="full-width"
    @change="handleChange"
    @focus="handleFocus"
  >
    <fks-option
      v-for="item in list"
      :key="item.departmentId"
      :value="item.departmentId"
      :label="item.name"
      :disabled="item.disabled"
    />
  </fks-select>
</template>

<script>
import {getDeptsInFeishu, getSubDeptsInFeishu} from '../api'
export default {
  name:'FeishuDepartmentSelector',
  props: {
    placeholder: {
      type: String,
      default: '请选择部门'
    },
    disabled: {
      props: Boolean,
      default: false
    },
    initialValue: {
      type: String
    },
    deptId: {
      type: String
    },
    level: {
      type: Number,
      default: 2
    },
    triggerOnFocus: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchValue: '',
      queryVal: '',
      loading: false,
      scrolled: false,
      finished: false,
      list: [],
    }
  },
  computed: {
    apiMethod() {
      return this.level === 2 ? getDeptsInFeishu : getSubDeptsInFeishu
    },
  },
  methods: {
    handleFocus() {
      if (this.level === 3 && this.deptId) {
        // this.$refs.selectRef.toggleMenu();
      }
    },
    handleChange(val) {
      const item = this.list.find(item => item.departmentId === val);
      this.$emit('dept-change', item);
    },
    remoteMethod(val) {
      this.getDepts(val, true)
    },
    async getDepts(val, resetPageToken = true) {
      this.queryVal = val;
      // resetPageToken 为 true 时，重置 pageToken
      resetPageToken && (this.scrolled = false);
      // 用户在滚动加载时，如果数据加载完则停止搜索
      if (!resetPageToken && this.finished) {
        // 停止搜索
        return
      } else {
        this.finished = false
      }
      const thirdPartyUser = this.$storage.getObject('thirdPartyUser');
      const openId = thirdPartyUser['tpOpenId']
      const params = {
        openId,
        pageSize: 50,
        pageToken: resetPageToken ? '' : this.pageToken,
        [this.level === 2 ? 'deptName' : 'departmentId']: this.level === 2 ? val || '' : this.deptId,
      }
      resetPageToken && (this.loading = true);
      const res = await this.apiMethod(params);
      resetPageToken && (this.loading = false);
      if (resetPageToken) {
        this.list = res.data.data.items || [{name: '暂无数据', disabled: true, departmentId: ''}];
      } else {
        this.list = this.list.concat(res.data.data.items || [])
      }
      this.pageToken = res.data.data.datapageToken;
      this.finished = !res.data.data.hasMore;
    }
  },
  watch: {
    deptId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.list = []; // 重置列表
          this.getDepts('', true)
        }
      }
    },
    initialValue: {
      immediate: true,
      handler(newVal) {
        this.searchValue = newVal
      }
    },
  }
}
</script>
