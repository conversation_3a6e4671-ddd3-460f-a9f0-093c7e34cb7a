<template>
  <fks-drawer
    :before-close="beforeClose"
    :modal="false"
    :visible.sync="visible"
    :wrapperClosable="false"
    class="dialog"
    direction="rtl"
    size="660px"
    @open="onDrawerOpen"
  >
    <template slot="title">
      <div class="drawer-title">{{ title }}</div>
    </template>
    <template v-if="visible">
<!--      <role-dialog ref="roleDialogRef" :row="row" @mutate="mutate"/>-->
      <div class="flex flex-column full-height">
        <main class="flex-grow-1 overflow-y-auto">
          <common-title class="m-b-10" title="用户角色信息"/>
          <fks-form
            ref="formRef"
            :disabled="isView"
            :flow-config="flowConfig"
            :model="row"
            class="form newTheme"
            label-position="left"
            label-width="210px"
          >
            <fks-form-item
              :rules="[{ required: true,validator: userFullNameValidator, trigger: 'blur' }]"
              label="姓名" prop="userFullName" required
            >
<!--              <fks-input v-model="row.userFullName" :placeholder="isView ? '' : '请输入姓名'"/>-->
              <new-person-selector
                :initial-value.sync="row.userFullName"
                :read-only="isView"
                @select="handleSelect"
              />
            </fks-form-item>
            <fks-form-item
              :rules="[{required: true, validator: userNameValidator, trigger: 'blur'}]"
              label="用户名" lead prop="userName" required
            >
              <fks-input
                v-model="row.userName"
                :disabled="type !== 'plus'"
                :placeholder="isView ? '' : '包含字母数字，和下划线，如:zhang_l'"
              />
            </fks-form-item>
            <fks-form-item
              :rules="[{required: true, validator: userPhoneValidator, trigger: 'blur' }]"
              label="联系电话" prop="userPhone" required
            >
              <fks-input
                v-model="row.userPhone"
                :disabled="type !== 'plus'"
                :placeholder="isView ? '' : '请输入联系电话'"
              />
            </fks-form-item>

            <fks-form-item label="二级单位" prop="userDepName">
              <feishu-department-selector
                :initialValue="row.userDepName"
                :level="secondLevel"
                :triggerOnFocus="true"
                :disabled="isView"
                placeholder="请输入二级单位名称"
                @dept-change="handleDeptChange"
                @value-change="row.userDepName = $event"
                @input="secondLevel = 2"
                @focus="secondLevel = 3"
              />
            </fks-form-item>
            <fks-form-item label="三级部门" prop="userDepName2">
              <feishu-department-selector
                :initialValue="row.userDepName2"
                :level="3"
                :disabled="isView"
                :deptId="deptId"
                :triggerOnFocus="Boolean(deptId)"
                placeholder="请输入三级部门名称"
                @value-change="row.userDepName2 = $event"
              />
            </fks-form-item>
          </fks-form>
<!--          <div v-if="type === 'edit'" style="max-width: 900px;">-->
<!--            <common-title class="m-b-24" title="角色列表"/>-->
<!--            <fks-table-->
<!--              :cell-style="{color: '#333333 !important'}"-->
<!--              :data="row.roleInfo"-->
<!--              :header-cell-style="{background: 'transparent', color: '#333333aa !important', fontWeight: 'unset !important'}"-->
<!--              max-height="220"-->
<!--            >-->
<!--              <fks-table-column align="center" label="#" type="index" width="50"/>-->
<!--              <fks-table-column align="center" label="角色" prop="name"/>-->
<!--            </fks-table>-->
<!--          </div>-->
        </main>
        <footer>
          <fks-button :loading="buttonLoading" class="sub-btn m-r-10" icon="fks-icon-check"
                      type="text" @click="handleSubmit">提交
          </fks-button>
<!--          <fks-button v-if="type !== 'plus'" :loading="buttonLoading" class="sub-btn" icon="fks-icon-setting"-->
<!--                      type="text"-->
<!--                      @click="handleRole">角色配置-->
<!--          </fks-button>-->
        </footer>
      </div>
    </template>
  </fks-drawer>
</template>

<script>
import {
  addUser,
  hasPhoneExist,
  updateUser
} from "@modules/ProjectCar/CompanyPortal/UserManagement/api";
import RoleDialog
  from "@modules/ProjectCar/CompanyPortal/UserManagement/components/role-dialog.vue";
import OverflowTooltip from "@components/OverflowTooltip/index.vue";
import validator from "@/mixins/validator";
import {mapState} from "vuex";
import * as StateTypes from "@store/State/stateTypes";
import FeishuDepartmentSelector
  from "@modules/ProjectCar/CompanyPortal/UserManagement/components/feishu-department-selector.vue";
import NewPersonSelector from "@components/PersonSelector/main.vue";

export default {
  name: "UserForm",
  mixins: [validator],
  components: {
    NewPersonSelector,
    FeishuDepartmentSelector,
    OverflowTooltip,
    RoleDialog
  },
  data() {
    return {
      secondLevel: 3,
      thirdLevel: 3,
      deptId: '',
      buttonLoading: false,
      finished: false,
      loading: false,
      total: 0,
      queryParams: {
        projectId: ''
      },
      type: 'edit',
      row: {
        userName: "",
        userFullName: "",
        userPhone: ""
      },
      visible: false,
    }
  },
  computed: {
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_TABLE_DATA, StateTypes.GLOBAL_QUERY_PARAMS]),
    title() {
      let typeMap = {
        "view": "查看",
        "edit": "编辑",
        "plus": "新增",
      }
      return `${typeMap[this.type]}用户`;
    },
    isView() {
      return this.type === 'view';
    },
    isEdit() {
      return this.type === 'edit';
    },
    flowConfig() {
      if (this.row) {
        const type = this.type === 'view' ? 'readonly' : ''
        return Object.keys(this.row).reduce((acc, cur) => {
          const obj = {[cur]: type}
          return Object.assign(acc, obj)
        }, {})
      }
      return {}
    },
  },
  methods: {
    handleDeptChange(payload) {
      // 当deptId有值，且发生改变的时候，清空三级部门
      if (this.deptId && (this.deptId !== payload)) {
        // 清空三级部门
        this.row.userDepName2 = '';
      }
      this.deptId = payload;
    },
    handleSelect(payload) {
      const {user} = payload;
      const phoneNumber = user.mobile.replace('+86', '');
      const userPhone = phoneNumber;
      const userFullName = user.name;
      const userName = user.nickname || '';
      let userDepName = '';
      let userDepName2 = '';
      if (user.departmentPath.length > 0) {
        const departmentStr = user.departmentPath[0].departmentPath.departmentPathName.name;
        const parts = departmentStr.split('-');
        const [firstDep, secondDep, thirdDep] = parts;
        userDepName = secondDep || '';
        userDepName2 = thirdDep || '';
      }
      this.row = {
        userPhone,
        userFullName,
        userName,
        userDepName,
        userDepName2,
      }
    },
    // 用户名的验证规则：不能为空，且需要符合特定格式
    userNameValidator(rule, value, callback) {
      const reg = /^[a-zA-Z0-9_]+$/;
      if (!value) {
        callback(new Error('用户名不能为空'));
      } else if (!reg.test(value)) {
        // 验证是否符合字母、数字和下划线的组合
        callback(new Error('用户名只能包含字母、数字和下划线'));
      } else {
        callback(); // 校验通过
      }
    },

    // 姓名的验证规则：不能为空
    userFullNameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error('姓名不能为空'));
      } else {
        callback(); // 校验通过
      }
    },
    // 电话号码的验证规则：验证格式
    async userPhoneValidator(rule, value, callback) {
      // 仅
      if (this.type === 'plus') {
        if (/^[1][23456789][0-9]{9}$/.test(value)) {
          // 验证手机号是否已添加
          const res = await hasPhoneExist(value)
          if (res.status) {
            if (res.data) {
              callback(new Error('该手机号已存在，请重新输入'))
            } else {
              callback()
            }
          } else {
            callback(new Error('无法校验是否重复'));
          }
        } else {
          if (!value) {
            callback(new Error('请输入手机号'))
          } else {
            callback(new Error('请输入正确的手机格式'))
          }
        }
      }
    },


    validator(rule, value, callback) {
      const reg = /^[a-zA-Z][a-zA-Z_0-9]*$/
      if (value) {
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error('格式错误：只能包含字母，字母和下划线'))
        }
      } else {
        callback(new Error('请输入用户名'))
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          const fn = this.type === 'plus' ? addUser : updateUser;
          fn(this.row).then(async (res) => {
            if (res.status) {
              this.$message.success(this.type === 'plus' ? '新增成功' : '编辑成功')
              this.$refs.formRef.clearValidate();
              this.clear();
              this.$emit("refresh", true);
              // if (this.type === 'plus') {
              //   this.clear();
              //   this.$emit("refresh", true);
              // } else {
              //   this.$emit("refresh", false);
              // }
            } else {
              // this.$message.error(res.message)
            }
          }).finally(() => {
            this.buttonLoading = false;
          })
        }
      })
    },
    mutate(list) {
      this.$set(this.row, 'roleInfo', list);
      this.$emit("refresh", false);
    },
    onDrawerOpen() {
      this.$refs.formRef && this.$refs.formRef.clearValidate();
    },
    beforeClose(done) {
      this.$refs.formRef.clearValidate();
      this.row = {};
      this.deptId = '';
      this.visible = false;
      done()
    },
    clear() {
      this.$refs.formRef.clearValidate();
      this.row = {};
      this.visible = false;
    },
    handleRole() {
      this.$refs.roleDialogRef.visible = true;
    },
    open(data, type) {
      this.visible = true;
      this.type = type;
      if (this.isEdit || this.isView) {
        this.$nextTick(() => {
          // this.$set(this.$data, "row", data)

          this.row = JSON.parse(JSON.stringify(data));
        })
      }
      this.$nextTick(() => {
        const portal = this[StateTypes.PORTAL];
      })
    },
  },
  async created() {
  },
}
</script>

<style lang="less" scoped>
@import "~@/styles/headers";
@import "~@/modules/FormCenter/CarApply/index.less";
@import "~@/styles/disabled";
@import "~@/styles/input";

footer {
  padding-top: 8px;
  margin-top: 12px;
  border-top: 1px solid #F1F1F0;
}

.form {
  max-width: 1450px;
  margin: 0;
}

.sub-btn {
  padding: 5px 10px;
  color: #333;
  transition: all 0.3s ease; /* 平滑过渡效果 */
}

</style>
