<template>
  <fks-autocomplete
    v-model="searchValue"
    :loading="loading"
    :disabled="disabled"
    :fetch-suggestions="querySearch"
    :trigger-on-focus="triggerOnFocus"
    :placeholder="placeholder"
    class="full-width"
    @select="handleChange"
    @blur="handleBlur"
    @focus="handleFocus"
    @input="handleInput"
  >
    <template slot-scope="{item}">
      <div>{{item.name}}</div>
    </template>
  </fks-autocomplete>
</template>

<script>
import {getDeptsInFeishu, getSubDeptsInFeishu} from '../api'
import {isDefined} from "@utils/types";
export default {
  name:'FeishuDepartmentSelector',
  props: {
    placeholder: {
      type: String,
      default: '请输入部门名称'
    },
    disabled: {
      props: Boolean,
      default: false
    },
    initialValue: {
      type: String
    },
    deptId: {
      type: String
    },
    level: {
      type: Number,
      default: 2
    },
    triggerOnFocus: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchValue: '',
      queryVal: '',
      loading: false,
      scrolled: false,
      finished: false,
      list: [],
      mode: ''
    }
  },
  computed: {
    apiMethod() {
      return this.isNew ? getDeptsInFeishu : getSubDeptsInFeishu
    },
    isNew() {
      if (this.mode === 'input') {
        return true;
      } else if (this.mode === 'focus') {
        return false
      }
    }
  },
  methods: {
    handleBlur() {},
    handleFocus() {
      this.$emit('focus')
      this.mode = 'focus'
    },
    handleInput() {
      this.$emit('input')
      this.mode = 'input'
    },
    handleChange(val) {
      this.searchValue = val.name;
      this.$emit('value-change', val.name)
      if (val.departmentId) {
        this.$emit('dept-change', val.departmentId)
      }
    },
    async querySearch(val, cb) {
      await this.getDepts(val || this.searchValue);
      cb(this.list);
    },
    async getDepts(val, resetPageToken = true) {
      this.queryVal = val;
      // resetPageToken 为 true 时，重置 pageToken
      resetPageToken && (this.scrolled = false);
      // 用户在滚动加载时，如果数据加载完则停止搜索
      if (!resetPageToken && this.finished) {
        // 停止搜索
        return
      } else {
        this.finished = false
      }
      const thirdPartyUser = this.$storage.getObject('thirdPartyUser');
      const openId = thirdPartyUser['tpOpenId']
      const params = {
        openId,
        pageSize: 50,
        pageToken: resetPageToken ? '' : this.pageToken,
        [this.isNew ? 'deptName' : 'departmentId']: this.isNew ? val || '' : this.deptId,
      }
      resetPageToken && (this.loading = true);
      const res = await this.apiMethod(params);
      resetPageToken && (this.loading = false);

      // 获取新数据
      const newItems = res.data.data.items || [];
      if (resetPageToken) {
        this.list = newItems;
      } else {
        this.list = this.list.concat(newItems);
      }
      this.pageToken = res.data.data.datapageToken;
      this.finished = !res.data.data.hasMore;
      this.$emit('update-dept', newItems);
    }
  },
  watch: {
    initialValue: {
      immediate: true,
      handler(newVal) {
        this.searchValue = newVal
        if (newVal) {
          // 需要根据输入值查询部门id，再传给上级组件
          const thirdPartyUser = this.$storage.getObject('thirdPartyUser');
          const openId = thirdPartyUser['tpOpenId']
          const params = {
            openId,
            pageSize: 1,
            pageToken: '',
            deptName: newVal
          }
          getDeptsInFeishu(params).then(res => {
            if (res.status) {
              const item = res.data?.data?.items[0];
              if (item) {
                this.handleChange(item);
              }
            }
          })
        }
      }
    }
  }
}
</script>
