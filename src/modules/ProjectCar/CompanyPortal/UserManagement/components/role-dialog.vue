<template>
  <fks-dialog
    v-if="visible"
    :before-close="beforeClose"
    :visible.sync="visible"
    size="large"
    class="dialog"
    show-fullscreen
  >
    <template slot="title">
      <div class="dialog-title flex col-center">
        <i class="fks-icon-setting m-r-10"/>
        <span>角色配置</span>
      </div>
    </template>
    <tree-transfer
      ref="treeTransferRef"
      :rightListFromParent="row.roleInfo"
      :source-data="roleList"
    />
    <template slot="footer">
      <fks-button
        :loading="loading"
        icon="fks-icon-check" type="primary"
        @click="submit"
      >
        提交
      </fks-button>
    </template>
  </fks-dialog>
</template>

<script>
import TreeTransfer from "@components/TreeTransfer/index.vue";
import {
  assignMultipleRoles,
  getProjectRoleList, removeMultipleRoles
} from "@modules/ProjectCar/CompanyPortal/UserManagement/api";
import {mapState} from "vuex";
import * as StateTypes from "@store/State/stateTypes";

export default {
  name: "role-dialog",
  components: {TreeTransfer},
  props: ['row'],
  data() {
    return {
      visible: false,
      roleList: [],
      loading: false
    }
  },
  computed: {
    ...mapState([StateTypes.PORTALS]),
    projects() {
      return this[StateTypes.PORTALS]
        .filter(i => i.type === 3)
        .map(item => ({
          name: item.name,
          id: item.id
        }))
    },
  },
  methods: {
    beforeClose(done) {
      this.visible = false;
      done();
    },
    async submit() {
      // 绑定角色
      this.loading = true;
      const userId = this.row.userId;
      const {status, message} = await assignMultipleRoles({
        roleIdList: this.$refs.treeTransferRef.rightList.map(item => item.id),
        userId: userId
      })

      if (this.$refs.treeTransferRef.deleteList.length) {
        const res = await removeMultipleRoles({
          userId,
          roleIdList: this.$refs.treeTransferRef.deleteList.map(item => item.id)
        })
        if (res.status && status) {
          this.$message.success('配置成功')
          this.visible = false;
          this.$emit('mutate', this.$refs.treeTransferRef.rightList);
          this.loading = false;
        }
      } else {
        if (status) {
          this.$message.success('配置成功')
          this.visible = false;
          this.$emit('mutate', this.$refs.treeTransferRef.rightList);
        } else {
          this.$message.error(message)
        }
        this.loading = false;
      }
    }
  },
  async created() {
    // 获取全部项目
    let projects = []
    Promise.all(this.projects.map(project => {
      return getProjectRoleList(project.id)
    })).then(res => {
      res.forEach((item, index) => {
        if (item.data) {
          const {id: parentId, name: parentName} = this.projects[index]
          projects.push({id: parentId, name: parentName, pid: 0, type: 'node'});
          projects.push(...item.data.map(el => ({...el, pid: parentId, type: 'leaf'})))
        }
      })
      this.roleList = projects
    })
  }
}
</script>

<style lang="less" scoped>
.dialog {
  font-size: 32px;
}
</style>
