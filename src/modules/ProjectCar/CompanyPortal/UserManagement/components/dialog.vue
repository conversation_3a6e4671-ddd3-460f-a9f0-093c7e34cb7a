<template>
  <fks-drawer
    :before-close="beforeClose"
    :visible.sync="visible"
    :modal="false"
    direction="rtl"
    size="700px"
    class="dialog"
  >
    <template slot="title">
      <div class="dialog-title flex col-center">
        <i :class="'fks-icon-' + type" class="m-r-10"/>
        <span>{{ title }}用户角色信息</span>
      </div>
    </template>
    <div class="drawer-container">
      <div class="form-body">
        <fks-form class="form-body" ref="formRef" :flow-config="flowConfig" :model="formData" :disabled="isView">
          <fks-form-item
            :rules="[{required: true, validator, trigger: 'blur'}]"
            :span="12" label="用户名" lead prop="userName" required>
            <fks-input
              v-model="formData.userName"
              :disabled="type !== 'plus'"
              :placeholder="isView ? '' : '包含字母数字，和下划线，如:zhang_l'"
            />
          </fks-form-item>
          <fks-form-item
            :rules="[{required: true, message: '请输入姓名', trigger: 'blur'}]"
            :span="12" label="姓名" prop="userFullName" required>
            <fks-input v-model="formData.userFullName" :placeholder="isView ? '' : '请输入姓名'"/>
          </fks-form-item>
          <fks-form-item
            :rules="[{validator: phoneValidator, message: '请输入正确格式的电话'}]"
            :span="12" label="联系电话" prop="userPhone"
            required>
            <fks-input
              v-model="formData.userPhone"
              :disabled="type !== 'plus'" :placeholder="isView ? '' : '请输入联系电话'"/>
          </fks-form-item>
          <fks-form-item :span="12" label="二级单位" prop="userDepName">
            <fks-input v-model="formData.userDepName" :placeholder="isView ? '' : '请输入二级单位'"/>
          </fks-form-item>
          <fks-form-item :span="12" label="三级部门" prop="userDepName2">
            <fks-input v-model="formData.userDepName2" :placeholder="isView ? '' : '请输入三级部门'"/>
          </fks-form-item>
        </fks-form>
        <div v-if="type === 'view'">
          <common-title class="m-b-24" title="角色列表"/>
          <fks-table
            :data="formData.roleInfo"
            border
            max-height="220"
          >
            <fks-table-column align="center" label="#" type="index" width="50"/>
            <fks-table-column align="center" label="角色" prop="name"/>
          </fks-table>
        </div>
      </div>
      <div class="footer">
        <fks-divider />
        <fks-button
          :disabled="!showSubmitButton" :loading="loading" icon="fks-icon-check" type="primary"
          @click="submit">
          提交
        </fks-button>
      </div>
    </div>
  </fks-drawer>
</template>

<script>
import validator from "@/mixins/validator";
import {addUser, updateUser} from "@modules/ProjectCar/CompanyPortal/UserManagement/api";
import TreeTransfer from "@components/TreeTransfer/index.vue";

export default {
  name: "UserDialog",
  components: {TreeTransfer},
  mixins: [validator],
  data() {
    return {
      type: 'view',
      loading: false,
      visible: false,
      formData: {
        userName: '',
        userFullName: '',
        userPhone: '',
        userDepName: '', // 所属二级单位
        userDepName2: '' // 所属三级部门
      },
      roleDialogVisible: false
    }
  },
  computed: {
    flowConfig() {
      const type = this.type === 'view' ? 'readonly' : ''
      return Object.keys(this.formData).reduce((acc, cur) => {
        const obj = {[cur]: type}
        return Object.assign(acc, obj)
      }, {})
    },
    isAdd() {
      return this.type === 'plus'
    },
    isView() {
      return this.type === 'view';
    },
    isEdit() {
      return this.type === 'edit';
    },
    showSubmitButton() {
      return !this.isView
    },
    title() {
      const typeObj = {
        plus: '新增',
        edit: '编辑',
        view: '查看'
      }
      return typeObj[this.type]
    }
  },
  methods: {
    validator(rule, value, callback) {
      const reg = /^[a-zA-Z][a-zA-Z_0-9]*$/
      if (value) {
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error('格式错误：只能包含字母，字母和下划线'))
        }
      } else {
        callback(new Error('请输入用户名'))
      }
    },
    beforeClose(done) {
      this.visible = false;
      this.formData = {}
      done();
    },
    async submit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.loading = true;
          const fn = this.type === 'plus' ? addUser : updateUser;
          fn(this.formData).then(async (res) => {
            if (res.status) {
              this.$message.success(`${this.title}成功`)
              this.visible = false;
              this.$refs.formRef.resetFields();
              this.$refs.formRef.clearValidate();
              this.$emit('mutate');
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false;
          })
        }
      })
    },
    open(data, type) {
      this.visible = true;
      this.type = type;
      if (this.isEdit || this.isView) {
        this.$nextTick(() => {
          this.formData = Object.assign({}, data);
        })
      }
    },
  },

}
</script>

<style lang="less" scoped>
@import "~@/styles/disabled";
@import "~@/styles/drawer";
.dialog {
  font-size: 32px;
}
</style>

