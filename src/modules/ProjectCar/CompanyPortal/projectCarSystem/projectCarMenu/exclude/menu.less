.sub-btn {
  padding: 5px 10px !important;
  color: #333 !important;
  transition: all 0.3s ease !important; /* 平滑过渡效果 */
}

.sub-btn:hover {
  color: #333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.sub-btn:active {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
  transform: translateY(5px); /* 模拟点击下沉效果 */
}

.menu-btn {
  padding: 2px 4px !important;
  transition: all 0.3s ease !important; /* 平滑过渡效果 */
}

.menu-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.menu-btn:active {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
  transform: translateY(5px); /* 模拟点击下沉效果 */
}

.danger {
  color: #FF4143 !important;
}

.primary {
  color: #3C83FF !important;
}

.dialog-body {
  box-sizing: border-box;
  padding: 20px;
}

.dialog-body .fks-input--medium .fks-input__inner {
  line-height: 32px !important;
  height: 32px !important;
}