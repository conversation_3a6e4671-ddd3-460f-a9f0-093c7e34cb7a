<template>
  <div class="flex flex-grow-1 full-width" style="box-sizing: border-box; flex-direction: row">
    <div style="padding-bottom: 20px">
      <commonTree
        @hanldeClickNode="hanldeClickNode"
        :is-collapsed.sync="isCollapsed"
        :treeData="portalList"
        :multiple="false"
        title="门户"
        height="100%"
        :searchValue.sync="searchValue"
        :showTag="true"
        btnText="菜单管理"
        ref="commonTree"
      />
    </div>
    <div
      class="flex flex-column flex-grow-1 right-content"
      style="box-sizing: border-box"
      :style="{ width: '100%' }"
    >
      <TitleKit>
        <template #left>
          <div class="second-title">菜单管理</div>
        </template>
      </TitleKit>
      <TempTable
        :data-list="tableData"
        :table-config.sync="tableConfig"
        :search-configs="searchConfigs"
        :selected-fields.sync="selectedFields"
        :total="total"
        :loading="loading"
        @searchData="searchData"
        :showButtons="showButtons"
        :current-page.sync="pageNo"
        :isTree="true"
        :LANG="LANG"
        ref="tempTable"
        class="tempTable"
      >
        <template v-slot:column-enable="{ scope }">
          <fks-switch v-model="scope.row.enable" disabled></fks-switch>
        </template>
        <template v-slot:column-auth="{ scope }">
          <fks-switch v-model="scope.row.auth" disabled></fks-switch>
        </template>
        <template v-slot:column-isGeneral="{ scope }">
          {{ scope.row.isGeneral ? LANG.YES : LANG.NO }}
        </template>
        <template v-slot:column-option="{ scope }">
          <fks-button type="text" style="padding: 5px !important" disabled class="menu-btn">编辑</fks-button>
          <fks-button type="text" style="padding: 5px !important" disabled class="menu-btn">添加子级</fks-button>
          <fks-button type="text" dangerText style="padding: 5px !important" disabled
          class="menu-btn"
            >删除</fks-button
          >
        </template>
      </TempTable>
    </div>
  </div>
</template>

<script>
import TitleKit from '@components/PageTitle/TitleKit.vue'
import commonTree from '@modules/ProjectCar/CompanyPortal/CompanyIndex/exclude/commonTree.vue'
import { getTableData, getPortals } from './api'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import LANG from './lang'
import { mapActions } from 'vuex'
import * as ActionTypes from '@store/Action/actionTypes'

export default {
  name: 'projectCarMenu-pc-view',
  components: {
    TitleKit,
    commonTree,
    TempTable,
  },
  data() {
    return {
      isCollapsed: false,
      treeData: [],
      tableData: [],
      tableConfig: [
        {
          label: LANG.NAME,
          prop: 'title',
          group: '项目信息',
          // width: '120px'
        },
        {
          label: LANG.ROUTEPATH,
          prop: 'path',
          group: '项目信息',
          // width: '150px',
        },
        {
          label: LANG.ACTIVATE,
          prop: 'enable',
          group: '项目信息',
          // width: '100px',
          customer: true,
        },
        {
          label: LANG.IS_AUTH,
          prop: 'auth',
          group: '项目信息',
          // width: '150px'
          customer: true,
        },
        {
          label: LANG.SORT,
          prop: 'sort',
          group: '项目信息',
          // width: '150px',
        },
        {
          label: LANG.IS_GENERAL,
          prop: 'isGeneral',
          group: '项目信息',
          customer: true,
          // width: '150px',
        },
        {
          label: LANG.OPERATION,
          prop: 'option',
          group: '系统操作',
          fixed: 'right',
          width: 200,
          customer: true,
        },
      ],
      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      selectedFields: [],
      total: 0,
      loading: false,
      showButtons: [],
      pageNo: 1,
      pageSize: 10,
      clickType: 2,
      portalId: '',
      LANG: LANG,
      searchValue: '',
      currId: '',
      // portals: []
    }
  },
  created() {
    this.selectedFields = this.tableConfig.map((item) => item.prop)
  },
  async mounted() {
    await this[ActionTypes.GET_ENUMS]()
    if (this.portalList[0]) {
      this.$refs.commonTree.$refs.tree.setCurrentKey(this.portalList[0].id)
    }
    // 设置当前选中的节点
    this.getData()
  },

  methods: {
    ...mapActions([ActionTypes.GET_ENUMS]),
    hanldeClickNode(node) {
      if (node.code) {
        this.clickType = node.code
        this.portalId = ''
      } else {
        this.clickType = node.type
        this.portalId = node.id
      }
      this.getData()
    },
    filterItems(items) {
      if (!items || !Array.isArray(items)) {
          return [];
      }
      return items.filter(item => {
          const meta = item.meta ? JSON.parse(item.meta) : {};
          const config = meta.config || [];
          const shouldInclude = config.includes('VD_AUTH_SHOW');
          if (shouldInclude && item.children) {
              item.children = this.filterItems(item.children);
          }
          return shouldInclude;
      });
    },
    getData() {
      this.loading = true
      const params = {
        type: this.clickType,
        portalId: this.portalId,
      }
      getTableData(params)
        .then((res) => {
          const tempData = res.data.map((item) => this.mergeContent(item))
          // 过滤掉不需要显示的节点
          this.tableData = this.filterItems(tempData)
        })
        .finally(() => {
          this.loading = false
        })
    },
    mergeContent(node) {
      // 将 content 的属性合并到节点本身
      Object.assign(node, node.content)
      // 删除 content 属性
      delete node.content
      // 递归处理 children
      if (node.children && node.children.length > 0) {
        node.children = node.children.map((child) => this.mergeContent(child))
      }
      return node
    },
    getPortalTree() {
      getPortals().then((res) => {
        if (res.data instanceof Array && res.data.length > 0) {
          this.portals = res.data.sort((f, b) => {
            return f.type - b.type
          })
        }
      })
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    clear() {
      this.visible = false
      this.currentData = {}
      this.pageNo = 1
      this.getData(this.queryParams)
    },
    filterPortal(list, val) {
      if (!val) return list
      return list.filter((item) => {
        if (item.children) {
          let arr = this.filterPortal(item.children, val)
          item.children = arr
          if (arr instanceof Array && arr.length > 0) {
            return true
          }
        }
        if (item.name && item.name.indexOf(val) != -1) {
          return true
        }
        return false
      })
    },
  },
  watch: {
    isCollapsed: {
      // handler(newVal) {
      //   let width = newVal ? 30 : 260;
      //   width += 40;
      //   this.contentWidth = width + 'px';
      // }
    },
  },
  computed: {
    portals() {
      return this.$store.state.portals
    },
    portalTypes() {
      return this.$store.state.enum?.portalType
    },
    portalList() {
      if (!this.portalTypes) return
      let portalList = []
      this.portalTypes.forEach((e) => {
        let arr = this.portals.filter((p) => {
          return p.type == e.code
        })
        if (arr && arr.length) {
          let portal = Object.assign({ children: arr }, e)
          portalList.push(portal)
        }
      })
      portalList.forEach((item, ind) => {
        this.$set(item, 'name', item['zh-CN'])
        this.$set(item, 'id', ind)
      })
      this.clickType = portalList[0] && +portalList[0].code
      return this.filterPortal(portalList, this.searchValue) || []
    },
  },
}
</script>

<style lang="less" scoped>
.tempTable {
  /deep/ .cell {
    display: flex !important;
  }
}
</style>
<style>
  /* @import "./exclude/menu.less"; */
</style>
