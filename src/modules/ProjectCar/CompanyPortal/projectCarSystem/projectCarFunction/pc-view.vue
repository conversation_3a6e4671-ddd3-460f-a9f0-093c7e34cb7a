<template>
  <div class="flex flex-grow-1 full-width" style="box-sizing: border-box; flex-direction: row">
    <div style="padding-bottom: 20px">
      <commonTree
        @hanldeClickNode="hanldeClickOne"
        :treeData="portalList"
        :multiple="false"
        title="门户"
        height="50%"
        :showTag="true"
        btnText="菜单管理"
        :showArrow="false"
        ref="commonTree1"
        :defaultProps="{
          children: 'children',
          label: 'name',
        }"
        class="tree1"
      />
      <div class="divder">
      </div>
      <commonTree
        @hanldeClickNode="hanldeClickTwo"
        :selectParentNode="false"
        :treeData="filterTreeData2"
        :multiple="false"
        ref="commonTree2"
        title="菜单"
        height="50%"
        :showTag="false"
        :showArrow="false"
        :defaultProps="{
          children: 'children',
          label: 'title',
        }"
        :LANG="LANG"
        class="tree2"
      />
    </div>
    <div
      class="flex flex-column flex-grow-1 right-content"
      style=" box-sizing: border-box"
      :style="{ width: '100%' }"
    >
      <TitleKit>
        <template #left>
          <div class="second-title">项目管理</div>
        </template>
      </TitleKit>
      <TempTable
        :data-list="tableData"
        :table-config.sync="tableConfig"
        :search-configs="searchConfigs"
        :selected-fields.sync="selectedFields"
        :total="total"
        :loading="tableLoading"
        @searchData="searchData"
        :showButtons="showButtons"
        :current-page.sync="authParams.pageNo"
        :renderFn="renderFn"
        @hanldeSort="hanldeSort"
        ref="TempTable"
        class="temptable"
      >
        <template v-slot:column-isAuth="{ scope }">
          <fks-switch v-model="scope.row.isAuth" disabled></fks-switch>
        </template>
        <template v-slot:column-isGeneral="{ scope }">
            <span v-if="scope.row.isGeneral">
              {{ LANG.COMMON }}
            </span>
            <span v-if="!scope.row.isGeneral">
              {{ LANG.PRIVATE }}
            </span>
        </template>
        <template v-slot:column-option="{ scope }">
          <fks-button type="text" style="padding: 5px !important" disabled>编辑</fks-button>
          <fks-button type="text" style="padding: 5px !important" v-if="scope.row.url" disabled>数据权限</fks-button>
          <fks-button type="text" dangerText style="padding: 5px !important" disabled>删除</fks-button>
        </template>
      </TempTable>
    </div>
  </div>
</template>

<script>
import TitleKit from '@components/PageTitle/TitleKit.vue'
import commonTree from '@modules/ProjectCar/CompanyPortal/CompanyIndex/exclude/commonTree.vue'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import { mapActions } from 'vuex'
import * as ActionTypes from '@store/Action/actionTypes'
import { getTableData , getButtonPage} from './api'
import LANG from './lang'

export default {
  name: 'projectCarFunction-pc-view',
  components: {
    TitleKit,
    commonTree,
    TempTable,
  },
  data() {
    return {
      isCollapsed: false,
      tableLoading: false,
      deptTreeData: [],
      tableData: [],
      tableConfig: [
        {
          label: LANG.NAME,
          prop: 'name',
          group: '项目信息',
          isSort: true
          // width: '120px'
        },
        {
          label: LANG.CODE,
          prop: 'code',
          group: '项目信息',
          // width: '150px',
        },
        {
          label: LANG.INTERFACE,
          prop: 'url',
          group: '项目信息',
          // width: '100px',
        },
        {
          label: LANG.IS_AUTH,
          prop: 'isAuth',
          group: '项目信息',
          // width: '150px'
          customer: true,
          isSort: true
        },
        {
          label: '通用',
          prop: 'isGeneral',
          group: '项目信息',
          // width: '150px',
          customer: true,
          isRenderHeader: true
        },
        {
          label: '操作',
          prop: 'option',
          group: '系统操作',
          fixed: 'right',
          width: 200,
          customer: true,
        },
      ],
      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      queryParams: {
        projectId: null,
        conditions: [],
      },
      selectedFields: [],
      total: 0,
      loading: false,
      showButtons: [],
      pageNo: 1,
      pageSize: 10,
      clickType: 2,
      portalId: '',
      treeData2: [],
      searchValue1: '',
      searchValue2: '',
      LANG: LANG,
      authParams: {
        menuId: '',
        sort: '',
        columnName: ''
      },
      tree2CurrId: '',
      currentType: 0,
      sort: '',
      columnName: '',
      isGeneral: ''
    }
  },
  created() {
    this.selectedFields = this.tableConfig.map((item) => item.prop)
  },
  async mounted() {
    // 获取门户树
    await this[ActionTypes.GET_ENUMS]()
    // 设置门户树第一个节点选中
    if (this.portalList[0]) {
      this.$refs.commonTree1.$refs.tree.setCurrentKey(this.portalList[0].id)
    }
    // 获取第一个节点对应的菜单树
    await this.getMenuData()
    // 菜单树第一个节点选中
    if (this.filterTreeData2[0]) {
      this.tree2CurrId = this.filterTreeData2[0].id
      this.$refs.commonTree2.$refs.tree.setCurrentKey(this.tree2CurrId)
    }
    // 获取权限列表
    this.getAuthList()
  },
  methods: {
    ...mapActions([ActionTypes.GET_ENUMS]),
    filterItems(items) {
      if (!items || !Array.isArray(items)) {
          return [];
      }
      return items.filter(item => {
          const meta = item.meta ? JSON.parse(item.meta) : {};
          const config = meta.config || [];
          const shouldInclude = config.includes('VD_AUTH_SHOW');
          if (shouldInclude && item.children) {
              item.children = this.filterItems(item.children);
          }
          return shouldInclude;
      });
    },
    renderFn(h, { column, $index }){
      // 给表格添加包裹的div
      return h('div', {}, [
        // 添加el-select 标签
        h(
          'fks-select',
          {
            on: {
              input: (value) => {
                this.currentType = value
              }
            },
            props: {
              value: this.currentType, // 给el-select 设置值（也是相当于v-model）
              size: 'small',
              class: 'selectRole',
              style: {
                height: '25px',
                width: '110px',
                paddingLeft: '0px',
                paddingRight: '0px'
              }
            }
          },
          [
            //给当前 el-select 设置 el-option
            this.funcType.map((item, index) => {
              return h('fks-option', {
                props: {
                  value: index,
                  label: item.name
                }
              })
            })
          ]
        )
      ])
    },
    mergeContent(node) {
      // 将 content 的属性合并到节点本身
      Object.assign(node, node.content)
      // 删除 content 属性
      delete node.content
      // 递归处理 children
      if (node.children && node.children.length > 0) {
        node.children = node.children.map((child) => this.mergeContent(child))
      }
      return node
    },
    async getMenuData() {
      const params = {
        type: this.clickType,
        portalId: this.portalId,
      }
      await getTableData(params).then((res) => {
        this.treeData2 = res.data.map((item) => this.mergeContent(item))
      })
      .finally(() => {
      })

    },
    getAuthList() {
      this.tableLoading = true
      const params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ...this.authParams,
        type: this.clickType,
        sort: this.sort,
        columnName: this.columnName,
        isGeneral: this.isGeneral,
        portalId: this.portalId
      }
      getButtonPage(params).then(res => {
        if (res.status) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
      }).finally(() => this.tableLoading = false)
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getAuthList()
    },
    clearSort() {
      this.$refs.TempTable.$refs.table.clearSort()
      this.sort = ''
      this.columnName = ''
    },
    hanldeSort(data) {
      if (!data.order) {
        this.sort = ''
        this.columnName = ''
      } else {
        this.sort = data.order == 'ascending' ? 'asc' : 'desc'
        this.columnName = data.prop == 'name' ? 'name' : 'is_auth '
      }
      this.getAuthList()
    },
    async hanldeClickOne(node) {
      if (node.code) {
        this.clickType = node.code
        this.portalId = ''
      } else {
        this.clickType = node.type
        this.portalId = node.id
      }
      await this.getMenuData()
      // 菜单树第一个节点选中
      if (this.filterTreeData2[0]) {
        this.tree2CurrId = this.filterTreeData2[0].id
        this.$refs.commonTree2.$refs.tree.setCurrentKey(this.tree2CurrId)
      }
      // 切换到第一页
      this.pageNo = 1
      // 获取权限列表
      this.getAuthList()

    },
    hanldeClickTwo(node) {
      if (node.children) {
        this.$refs.commonTree2.$refs.tree.setCurrentKey(this.tree2CurrId);
        return;
      }
      this.tree2CurrId = node.id;
      this.authParams.menuId = node.id;
      this.pageNo = 1
      this.getAuthList()
    }
  },
  watch: {
    currentType: {
      handler: function (val) {
        this.clearSort()
        this.isGeneral = this.funcType[val].isGeneral
        this.getAuthList()
      }
    }
  },
  computed: {
    portals() {
      return this.$store.state.portals
    },
    portalTypes() {
      return this.$store.state.enum?.portalType
    },
    portalList() {
      if (!this.portalTypes) return
      let portalList = []
      this.portalTypes.forEach((e) => {
        let arr = this.portals.filter((p) => {
          return p.type == e.code
        })
        if (arr && arr.length) {
          let portal = Object.assign({ children: arr }, e)
          portalList.push(portal)
        }
      })
      portalList.forEach((item, ind) => {
        this.$set(item, 'name', item['zh-CN'])
        this.$set(item, 'id', ind)
      })
      this.clickType = portalList[0] && +portalList[0].code
      // 设置authparams的menuId
      this.authParams.menuId = portalList[0] && +portalList[0].id
      return portalList
    },
    filterTreeData2() {
      if (!this.treeData2) return
      // 过滤出VD_AUTH_SHOW的节点
      const treeData = JSON.parse(JSON.stringify(this.treeData2))
      const filterData = this.filterItems(treeData)
      return filterData
    },
    funcType() {
      let data = []
      if (this.portalId) {
        data = [
          { name: '全部', isGeneral: '' },
          { name: '通用', isGeneral: true },
          { name: '私有', isGeneral: false }
        ]
      } else {
        data = [{ name: '通用', isGeneral: true }]
      }
      return data
    }
  },
}
</script>

<style lang="less" scoped>
.left-container {
  position: relative;
  height: 100%;
  background-color: #fff;
  .fks-select {
    margin-bottom: 16px;
  }
  .resize-container {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 5px;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 24px 24px 24px 0;
    overflow-x: hidden;
    overflow-y: auto;
    ::-webkit-scrollbar {
      width: 0;
    }
    .fks-tree {
      overflow: auto;
    }
    .left-top {
      position: relative;
      .search-input {
        position: absolute;
        top: 0;
        left: 24px;
        width: calc(100% - 24px);
      }
      &-portal {
        position: absolute;
        top: 36px;
        left: 24px;
        width: calc(100% - 24px);
        height: 36px;
        font-weight: 600;
        font-size: 14px;
        line-height: 36px;
        color: rgb(153, 153, 153);
        cursor: auto;
      }
      .left-top-content {
        position: absolute;
        bottom: 5px;
        left: 24px;
        width: calc(100% - 24px);
        height: calc(100% - 72px);
        overflow: hidden;
        &-title {
          font-weight: 600;
          font-size: 14px;
          color: #999;
        }
      }
    }
    .left-bottom {
      position: relative;
      box-sizing: border-box;
      width: calc(100% - 24px);
      margin-bottom: 36px;
      margin-left: 24px;
      overflow: hidden;
      .show-search {
        margin-top: 12px !important;
      }
      &-menu {
        position: absolute;
        top: 0;
        height: 36px;
        font-weight: 600;
        font-size: 14px;
        line-height: 36px;
        color: rgb(153, 153, 153);
      }
      &-tree {
        position: relative;
        height: 100%;
        margin-top: 36px;
        overflow: auto;
      }
      &-title {
        margin-top: 12px;
        font-weight: 600;
        font-size: 14px;
        color: #999;
      }
      .fks-tree-node__label {
        color: #191919;
      }
    }
  }
  .resize-bar-horizontal {
    width: 300px;
    min-height: 30%; /* 最小宽度 300px */
    max-height: 80%; /* 最大宽度 600px */
    overflow: scroll;
    resize: vertical;
    opacity: 0;
  }
  .resize-bar-vertical {
    z-index: -100 !important;
    width: 300px;
    min-width: 300px; /* 最小宽度 300px */
    max-width: 600px; /* 最大宽度 600px */
    width: 300px;
    height: inherit;
    overflow: scroll;
    resize: horizontal;
    opacity: 0;
  }
  .resize-line-horizontal {
    width: 100%;
    border-bottom: 1px solid #eee;
    pointer-events: none;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      display: block;
      width: 24px;
      height: 1px;
      border-bottom: 1px solid#eee;
      cursor: ew-resize;
    }
    &::after {
      content: '';
      position: absolute;
      right: 0;
      display: block;
      width: 32px;
      height: 1px;
      border-bottom: 1px solid#eee;
    }
  }
  .resize-line-vertical {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    height: 100vh;
    border-left: 1px solid #eee;
    pointer-events: none;
  }
  .resize-bar-vertical:hover ~ .resize-line-vertical,
  .resize-bar-vertical:active ~ .resize-line-vertical {
    border-left: 1px solid #eee;
  }
  .resize-bar-horizontal::-webkit-scrollbar {
    width: 100vh;
  }
  .resize-bar-vertical::-webkit-scrollbar {
    height: 100vh;
  }
}
.temptable {
  /deep/ .fks-table__header .fks-input--small .fks-input__inner {
    text-align: center;
    border: none;
    background: none;
  }
}

.tree1 {
  border-bottom: none;
  border-radius: 0 6px 0 0;
}
.tree2 {
  border-top: none;
  border-radius: 0 0 6px 0;
}
.divder {
  height: 2px;
  background-color: #ccc;
  margin: 0 30px;
}
</style>
