<template>
    <div class="flex flex-grow-1 full-width" style="box-sizing: border-box; flex-direction: row">
      <div
        class="flex flex-column flex-grow-1 right-content"
        style="padding: 0 20px 20px 20px; box-sizing: border-box"
        :style="{ width: '100%' }"
      >
        <TitleKit>
          <template #left>
            <div class="second-title">用户列表</div>
          </template>
        </TitleKit>
        <TempTable
          :data-list="tableData"
          :table-config.sync="tableConfig"
          :search-configs="searchConfigs"
          :selected-fields.sync="selectedFields"
          :total="total"
          :loading="loading"
          @add-item="handleAdd"
          @delete-item="handleDelete"
          @batch-add="handleBatchAdd"
          @export-data="handleExport"
          @searchData="searchData"
          @clickRow="handleClickRow"
          @showActive="handleshowActive"
          :showButtons="showButtons"
          :current-page.sync="pageNo"
        >
          <template v-slot:column-flag="{ scope }">
            <fks-switch v-model="scope.row.flag"></fks-switch>
          </template>
          <template v-slot:column-flag1="{ scope }">
            <fks-switch v-model="scope.row.flag1"></fks-switch>
          </template>
          <template v-slot:column-option="{ scope }">
            <fks-button type="text" style="padding: 0 !important">编辑</fks-button>
            <fks-button type="text" style="padding: 0 !important">添加子级</fks-button>
            <fks-button type="text" dangerText style="padding: 0 !important">删除</fks-button>
          </template>
        </TempTable>
      </div>
    </div>
</template>

<script>
import TitleKit from '@components/PageTitle/TitleKit.vue'
import commonTree from '@modules/ProjectCar/CompanyPortal/CompanyIndex/exclude/commonTree.vue'
import { getTreeData } from '@modules/ProjectCar/CompanyPortal/CompanyIndex/api'
import {
  deleteCar,
  getCarList,
  getDriverInfoByCar,
  getCarSearchParam,
  getCarListByPost,
  batchDeleteCar,
  exportCarInfo,
} from '@modules/ProjectCar/ProjectPortal/CarManage/api'
import * as StateTypes from '@store/State/stateTypes'
import TempTable from '@components/CustomerTable/CustomerTable.vue'

export default {
  name: 'projectCarUser-pc-view',
  components: {
    TitleKit,
    commonTree,
    TempTable,
  },
  data() {
    return {
      isCollapsed: false,
      deptTreeData: [],
      tableData: [
        {
          userName: 'zhang_s1',
          userFullName: '姓名',
          userPhone: '151-3333-2222',
          secondLevel: '水利水电院',
          thirdLevel: 'xx项目',
          source: '飞书用户',
          status: '激活'
        },
        {
          userName: 'zhang_s1',
          userFullName: '姓名',
          userPhone: '151-3333-2222',
          secondLevel: '水利水电院',
          thirdLevel: 'xx项目',
          source: '飞书用户',
          status: '激活'
        },
        {
          userName: 'zhang_s1',
          userFullName: '姓名',
          userPhone: '151-3333-2222',
          secondLevel: '水利水电院',
          thirdLevel: 'xx项目',
          source: '飞书用户',
          status: '激活'
        },
        {
          userName: 'zhang_s1',
          userFullName: '姓名',
          userPhone: '151-3333-2222',
          secondLevel: '水利水电院',
          thirdLevel: 'xx项目',
          source: '飞书用户',
          status: '激活'
        },


      ],
      tableConfig: [
        {
          label: '用户名',
          prop: 'userName',
          group: '项目信息',
          // width: '120px'
        },
        {
          label: '姓名',
          prop: 'userFullName',
          group: '项目信息',
          // width: '150px',
        },
        {
          label: '联系电话',
          prop: 'userPhone',
          group: '项目信息',
          // width: '100px',
        },
        {
          label: '二级单位',
          prop: 'secondLevel',
          group: '项目信息',
          // width: '100px',
        },
        {
          label: '三级单位',
          prop: 'thirdLevel',
          group: '项目信息',
          // width: '100px',
        },
        {
          label: '用户来源',
          prop: 'source',
          group: '项目信息',
          // width: '100px',
        },
        {
          label: '账号状态',
          prop: 'status',
          group: '项目信息',
          // width: '100px',
          customer: true
        },
        {
          label: '操作',
          prop: 'option',
          group: '系统操作',
          fixed: 'right',
          width: 200,
          customer: true,
        },
      ],
      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      queryParams: {
        projectId: null,
        conditions: [],
      },
      selectedFields: [],
      total: 0,
      loading: false,
      showButtons: ['filter','export','add','sort'],
      pageNo: 1,
      pageSize: 10,
    }
  },
  created() {
    this.selectedFields = this.tableConfig.map((item) => item.prop)
    this.tableConfig.forEach((item) => {
      if (item.prop === 'carStatus') {
        item.enums = this.carResource
      }
      if (item.prop === 'carResource') {
        item.enums = this.carResource
      }
      if (item.prop === 'projectUseCarType') {
        item.enums = this.projectCarType
      }
    })
  },
  mounted() {
    this.searchTreeData()
    this.queryParams.projectId = this.portal.id
    this.getData(this.queryParams)
    getCarSearchParam().then((res) => {
      this.searchConfigs = res.data
    })
  },

  methods: {
    handleshowActive() {},
    async getData(query) {
      // this.loading = true
      // this.queryParams = { ...this.queryParams, ...query }
      // try {
      //   const params = {
      //     ...this.queryParams,
      //     pageNo: this.pageNo,
      //     pageSize: this.pageSize,
      //   }
      //   const res = await getCarListByPost(params, this[StateTypes.GLOBAL_QUERY_PARAMS])
      //   if (!res.status) {
      //     return false
      //   }
      //   this.total = res.data.total
      //   this.finished = res.data.isLastPage
      //   // 获取司机信息
      //   const carIds = res.data.list.map((item) => item.id).join(',')
      //   let tableData = [...res.data.list]
      //   if (carIds.length > 0) {
      //     const { data: driverInfos } = await getDriverInfoByCar(carIds)
      //     const newData = res.data.list.map((item) => {
      //       const driverInfo = driverInfos[item.id] || {}
      //       return {
      //         ...item,
      //         ...{
      //           driver: driverInfo.id,
      //           driverPhone: driverInfo.driverPhone,
      //           driverFullName: driverInfo.driverFullName,
      //         },
      //       }
      //     })
      //     tableData = [...newData]
      //   }
      //   this.tableData = [...tableData]
      //   this.loading = false
      // } catch (e) {
      //   this.loading = false
      // }
    },
    handleAdd() {},
    handleDelete() {},
    handleBatchAdd() {},
    handleExport() {},
    handleClickRow() {},
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    handleChooseProject() {},
    searchTreeData() {
      getTreeData().then((res) => {
        this.deptTreeData = [res.data]
      })
    },
    clear() {
      this.visible = false
      this.currentData = {}
      this.pageNo = 1
      this.getData(this.queryParams)
    },
  },
  watch: {
    isCollapsed: {
      // handler(newVal) {
      //   let width = newVal ? 30 : 260;
      //   width += 40;
      //   this.contentWidth = width + 'px';
      // }
    },
  },
}
</script>

<style lang="scss" scoped></style>
