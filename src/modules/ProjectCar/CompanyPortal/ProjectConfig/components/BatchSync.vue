<template>
  <fks-drawer
    :before-close="beforeClose"
    :modal="false"
    :visible.sync="visible"
    :wrapperClosable="false"
    class="dialog"
    direction="rtl"
    size="55vw"
  >

    <header class="card-header" style="display: flex; justify-content: space-between;">
      <div class="flex col-center m-b-24">
        <div class="card-title">{{title}}</div>
        <div class="m-l-20">
          <card-tag class="m-l-10" :tag="{ color: '#3C83FF', text: projectInfo.projectName }" />
        </div>
      </div>
      <fks-radio-group style="margin-right: 100px" v-model="activeName">
        <fks-radio label="car">同步车辆</fks-radio>
        <fks-radio label="driver">同步司机</fks-radio>
      </fks-radio-group>
    </header>

    <div class="flex-grow-1 ninety-height" v-if="activeName === 'car'">
      <SyncCar style="height: 100%" :origin-list="carList" :project-info="projectInfo" ref="syncCar"></SyncCar>
    </div>
    <div class="flex-grow-1 ninety-height" v-if="activeName === 'driver'">
      <SyncDriver style="height: 100%" :origin-list="driverList" :project-info="projectInfo" ref="syncDriver"></SyncDriver>
    </div>

    <div>
<!--      <fks-divider style="margin: 18px 0"/>-->
      <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text @click="submit">
        提交
      </fks-button>
    </div>
  </fks-drawer>
</template>
<script>
import FormUpload from '@modules/FormCenter/components/FormUpload/index.vue'
import emmiter from 'fawkes-lib/lib/mixins/emitter'
import { mapState } from 'vuex'
import * as types from '@store/Getter/getterTypes.js'
import validator from '@/mixins/validator.js'
import BatchAddForm from './BatchAddForm.vue'
import {
  batchAddProject,
  getVdUserInfo,
  syncProject
} from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import CardTag from '@components/CardFlow/components/tag.vue'
import SyncCar from './SyncCar.vue'
import SyncDriver from './SyncDriver.vue'
import driver from '@modules/Statistics/driver/index.vue'
import { deleteDraft, DRIVER } from '@utils/draftUtil'

export default {
  mixins: [emmiter, validator],
  components: {
    CardTag,
    FormUpload,
    BatchAddForm,
    SyncDriver,
    SyncCar
  },
  data() {
    return {
      vdUserInfo: {},
      loading: false,
      visible: false,
      activeName: "car"
    }
  },
  props: {
    type: {
      type: String,
      default: 'plus',
    },
    tab: {
      type: String,
      default: 'car',
    },
    projectInfo: {
      type: Object,
      default: null,
    },
    carList: {
      type: Array,
      default: null,
    },
    driverList: {
      type: Array,
      default: null,
    }
  },
  computed: {
    driver() {
      return driver
    },
    ...mapState(['portal']),
    theme() {
      return this.$store.getters[types.THEME]
    },
    title() {
      const typeObj = {
        car: '车辆',
        driver: '司机',
      }
      return '批量同步' + typeObj[this.activeName]
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    username() {
      return this.$storage.get('username')
    },
    userFullName() {
      return this.$storage.get('userFullname')
    },
  },
  mounted() {
    getVdUserInfo(this.userInfo.id).then((res) => {
      if (res.status) {
        this.vdUserInfo = res.data || {}
      }
    })
  },
  methods: {
    beforeClose(done) {
      if (this.$refs.syncDriver) {
        this.$refs.syncDriver.beforeClose(done)
      }
      if (this.$refs.syncCar) {
        this.$refs.syncCar.beforeClose(done)
      }
      this.visible = false;
      done()
    },
    async submit() {
      this.loading = true
      const ref = this.activeName === 'car' ? this.$refs.syncCar : this.$refs.syncDriver;
      const list = ref.mergedList;
      const syncExData = ref.checked;


      console.log("项目信息", this.projectInfo)
      let param = {
        syncSourceProjectId: this.portal.id,
        syncTargetProjectId: this.projectInfo.portalId,
        isSyncRelationInfo: syncExData,
        carDriverParamList: []
      }

      if (this.activeName === 'driver') {
        let idList = list.map((item) => {
          let carId = syncExData ? item.carId : item.oldCarId
          return {
            driverId: item.id,
            carId: carId
          };
        })
        param.carDriverParamList = idList;
      } else if (this.activeName === 'car') {
        let idList = list.map((item) => {
          let driverId = syncExData ? item.driverId : item.oldDriverId
          return {
            carId: item.id,
            driverId: driverId
          };
        })
        param.carDriverParamList = idList;
      }
      console.log("param", param)
      const res = await syncProject(param)

      if (!res.status) {
        this.loading = false;
        this.$message.error(res.message || `同步失败！`);
        return false;
      }
      if (res.status) {
        this.$message.success('同步成功');
        // 编辑成功后，通知CardFlow组件更新数据, 如果涉及到删除数据则传true，编辑数据则传false
        this.$emit('refresh', false)
        this.loading = false
        this.visible = false;
        this.$nextTick(() => {
          ref.beforeClose(() => {
          });
        });
      }
    },
    open() {
      this.visible = true
      // 调用接口查询是否有草稿数据
      this.$nextTick(() => {})
    },
    handleClick() {

    }
  },
}
</script>

<style lang="less" scoped>
/deep/ .fks-date-editor.fks-input {
  width: 100%;
}

.dialog {
  font-size: 32px;
}

.table-container {
  display: flex;
  flex-wrap: nowrap !important;
  flex-direction: column;
  justify-content: center;
  align-content: space-between;
  height: 100%;
}

.sub-btn {
  padding: 5px 10px;
  color: #333;
  transition: all 0.3s ease; /* 平滑过渡效果 */
}

.sub-btn:hover {
  color: #333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.sub-btn:active {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
  transform: translateY(5px); /* 模拟点击下沉效果 */
}
/deep/ .fks-divider{
  margin: 10px 0;
}

@import "~@/styles/headers";
</style>
