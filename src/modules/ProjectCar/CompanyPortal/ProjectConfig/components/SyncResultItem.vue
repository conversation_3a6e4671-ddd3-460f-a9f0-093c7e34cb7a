<template>
  <div class="">
    <div v-for="(item, index) in mergedList" :key="index" class="table-item" style="height: 40px">
      <span style="width: 20px">{{ index + 1 }}</span>
      <div  style="margin-left: 10px; " v-for="(field, fieldIndex) in filedList" :key="fieldIndex">
        <template v-if="!item[field.key]">
          <span v-if="fieldIndex === 0 || (fieldIndex > 0 && item[filedList[fieldIndex - 1].key])">/</span>
        </template>
        <template v-else-if="item[field.key]">
          <span
            v-if="field.compareKey != null && item[field.compareKey] && item[field.key] !== item[field.compareKey]"
            :key="field.key + '_diff'"
            style="display: inline-block; border-radius: 11px; color: #FF4143; background: rgba(255, 65, 67, 0.1); padding: 6px 10px; font-size: 11px"
          >
            {{ item[field.compareKey] }}
          </span>
          <span
            v-if="item[field.key]"
            :key="field.key + '_same'"
            style="display: inline-block; border-radius: 11px; color: #3C83FF; background: rgba(60, 131, 255, 0.2); padding: 5px 10px; font-size: 11px"
          >
            {{ item[field.key] }}
          </span>
        </template>
      </div>
    </div>
  </div>
</template>


<script>
export default {
  name: 'syncResult',
  props: {
    // 定义接收的属性 (props)
    mergedList: {
      type: Array,
      default: () => []
    },
    filedList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
  }
};
</script>

<style scoped>
.table-item {
  width: 100%;
  font-size: 28px;
  display: flex;
  justify-content: flex-start; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  padding: 0px 30px;
  border: 1px solid rgba(0, 0, 0, 0.15);
}

.table-item span {
  margin-left: 20px;
}

.table-item div {
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: flex-start; /* 使内部的 span 均匀分布 */
}
</style>
