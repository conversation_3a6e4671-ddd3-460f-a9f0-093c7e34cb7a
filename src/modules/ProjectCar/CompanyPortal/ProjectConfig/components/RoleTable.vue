<template>
  <div>
    <common-title title="角色信息" class="m-b-36"/>
    <fks-table
      ref="table"
      :data="roleList"
      :header-cell-style="{background: 'transparent', color: '#333333aa !important', fontWeight: 'unset !important'}"
      :cell-style="{color: '#333333 !important'}"
      style="overflow-y: auto; min-height: 250px"
      class="full-height full-width"
      :sticky="true">
      <!-- 序号 -->
      <fks-table-column type="index" label="#" width="50" />
      <fks-table-column label="角色名称"  width="130" >
        <template slot-scope="scope">
          <div
            style="
                display: inline-block;
                border-radius: 11px;
                color: #3c83ff;
                background: rgba(60, 131, 255, 0.2);
                padding: 4px 8px;
                font-size: 11px;
              "
          >
            {{ filterName(scope.row.name, projectInfo.projectName) }}
          </div>
        </template>
      </fks-table-column>
      <fks-table-column label="角色标识" width="130" >
        <template slot-scope="scope">
          <span>
            {{ filterName(scope.row.code, projectInfo.projectSign) }}
          </span>
        </template>
      </fks-table-column>
      <fks-table-column label="用户" width="350" >
        <template slot-scope="scope">
          <UserSelect
            ref="userSelect"
            :options="userList"
            :value="scope.row.userIds"
            :finished="pageFinish"
            :loading="userSearchLoading"
            @input="($event) => handleUserChange(scope.row, $event)"
            @searchMore="loadMoreUserInfo"
          />
<!--          <fks-user-selector-pro-->
<!--            url-->
<!--            width="100%"-->
<!--            multiple-->
<!--            multiPortal-->
<!--            :username.sync="scope.row.maintainer"-->
<!--            :userfullname.sync="scope.row.maintainerName"-->
<!--            @change="handleUserChange(scope.row)"-->
<!--          >-->
<!--          </fks-user-selector-pro>-->
        </template>
      </fks-table-column>
    </fks-table>
  </div>
</template>

<script>
import validator from '@/mixins/validator.js';
import {
  delRoleUser,
  getUserList,
  getUserListByIds,
  projectGenerateSign,
  setRoleUser,
  getUserListByUserIds
} from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import {mapState} from "vuex";
import {
  getProjectRoleList,
  getUserByRole
} from '@modules/ProjectCar/CompanyPortal/UserManagement/api'
import UserSelect from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/UserSelect.vue'

export default {
  name: "AddForm",
  mixins: [validator],
  components: {UserSelect},
  props: {
    type: {
      type: String,
      default: 'view'
    },
    // 是否关联车辆
    lineCar: {
      type: Boolean,
      default: false
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    },
    projectInfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      loadingCarNum: false,
      roleList: [],
      userList: [],
      userMap: {},
      sysUserList: [],
      userPageNo: 1,
      userSearchLoading: false,
      pageFinish: false,
    }
  },
  watch: {
    // 监听 tableData 的变化
    tableData: {
      handler(newVal) {
        if (!this.isDraftLoading) {
          if (newVal && newVal.length > 0) {
            const hasData = newVal.some(item => {
              const formData = item;
              return formData.projectName || formData.projectSign;
            });
            if (hasData) {
              this.saveDrafts();
            }
          }
        }
      },
      deep: true, // 深度监听，以监听到对象内部的变化
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    span() {
      return this.layoutSingleLine ? 24 : 12;
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    isView() {
      return this.type === 'view';
    },
    flowConfig() {
      return {}
    },
    rules() {
      return {
      }
    },
  },
  methods: {

    filterName(name, projectInfo) {
      if (!name || !projectInfo) return name;
      return name.replace(`${projectInfo}_`, '');
    },
    updateUserIds(row, userMap) {
      const prevIds = row.prevUser;
      const currentIds = row.userIds;

      const addedIds = currentIds.filter(id => !prevIds.includes(id));
      const removedIds = prevIds.filter(id => !currentIds.includes(id));
      // 调接口新增绑定关系和删除绑定关系
      if (addedIds.length > 0) {
        setRoleUser({
          roleId: row.id,
          userIdList: addedIds
        }).then(res => {
          if (res.status) {
            this.$message.success("新增成功");
          }
        })
      }
      if (removedIds.length > 0) {
        delRoleUser({
          roleId: row.id,
          userIdList: removedIds
        }).then(res => {
          if (res.status) {
            this.$message.success("删除成功");
          }
        })
      }
      row.prevUser = row.userIds;
    },
    handleUserChange(row, event) {
      row.userIds = [...event];
      this.updateUserIds(row, this.userMap);
    },
    reload() {
      this.userList = [];
      this.roleList = [];
      this.loadMoreUserInfo(0, null); // 加载更多用户信息
      this.loadData();
    },
    loadMoreUserInfo(page, query) {
      let param = {
        pageSize: 30,
        needOrg: false,
        searchValue: query
      }
      // 有分页就往后推进，没有代表只根据关键字搜索
      if (page > 0) {
        if (this.pageFinish) return;

        this.userPageNo += page;
        param.pageNo = this.userPageNo;
      } else {
        if (query && query.length < 1) return;
        param.pageNo = 1;
      }
      this.userSearchLoading = true;
      getUserList(param).then(response => {
        if (page > 0 && response.data.isLastPage) {
          this.pageFinish = true;
        }
        const newUsers = response.data.list;
        this.setUserInfo(newUsers);
        this.userSearchLoading = false;
      })
    },
    async loadData() {
      let projectId = this.projectInfo.portalId;
      try {
        // 记录获取角色列表的开始时间
        const res = await getProjectRoleList(projectId);

        let roleList = res.data;
        let ids = res.data.map(item => item.id).join(',');

        // 记录获取用户列表的开始时间
        const users = await getUserByRole(ids);

        let userIds = new Set(); // 使用 Set 自动去重
        let data = users.data;

        // 遍历 roleList，获取相关用户ID
        roleList.forEach(item => {
          let userList = Object.values(data[item.id]);
          userList.forEach(user => userIds.add(user.id));
        });

        // 如果有用户ID，调用 getUserInfosById 获取用户信息
        if (userIds.size > 0) {
          // 记录获取用户信息的开始时间
          const response = await getUserListByIds({ ids: [...userIds].join(",") });
          this.setUserInfo(response.data); // 更新用户信息
        }

        // 遍历 roleList，为每个角色添加用户信息
        roleList.forEach(item => {
          let userList = Object.values(data[item.id]);
          item.userList = userList;
          item.prevUser = userList.map(user => user.id);
          item.userIds = userList.map(user => user.id);
        });
        this.$set(this.$data, "roleList", roleList);

        // 更新数据
        this.$nextTick(() => {
          this.$forceUpdate();
        });

      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },

    async setUserInfo(newUsers) {
      // 将新数据按 id 去重并更新 userMap
      const userNameList = newUsers.map(user => user.userName)
      const { data, status } = await getUserListByUserIds({ userNames: [... new Set(userNameList)].join(",") });
      if (status) {
        data.forEach(user => {
          user.id = user.userId;
          user.userFullname = user.userFullName;
          this.$set(this.userMap, user.id, user);
        });
      }
      const updatedUserList = Object.values(this.userMap);
      this.$set(this.$data, "userList", updatedUserList);
    }
  }
}
</script>

<style scoped lang="less">
@import "~@/styles/tag";
</style>
