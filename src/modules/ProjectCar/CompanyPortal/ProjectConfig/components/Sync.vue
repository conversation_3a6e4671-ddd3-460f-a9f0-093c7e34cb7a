<template>
  <div class="full-width">
    <header class="flex row-between col-center full-width">
      <common-title :title="titleStr" />
      <div class="flex col-center">
          <fks-dropdown trigger="click">
            <fks-button text icon="fks-icon-search">搜索</fks-button>
            <fks-dropdown-menu slot="dropdown">
              <div style="padding: 0px 5px;box-sizing: border-box">
                <fks-input
                  v-model="carSearchKey"
                  placeholder="请输入车辆或司机名称"
                  clearable
                  class="custom-input-placeholder"
                />
              </div>
            </fks-dropdown-menu>
          </fks-dropdown>
        <div>
          <fks-button v-if="type !== 'view'" text icon="fks-icon-circle-plus-outline" @click="handleSync">新增</fks-button>
          <BatchSync @refresh="handleRefresh" :car-list="carList" :driver-list="driverList" :project-info="projectInfo" ref="batchSync"></BatchSync>
        </div>
      </div>
    </header>

    <fks-tabs v-model="activeName" @tab-click="handleClick">
      <fks-tab-pane label="车辆信息" name="car" />
      <fks-tab-pane label="司机信息" name="driver" />
    </fks-tabs>
    <SyncTable :table-config="tableConfig" :table-list="tableList"></SyncTable>
  </div>
</template>

<script>
import validator from '@/mixins/validator.js';
import {mapState} from "vuex";
import { getCar, getDriverList } from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import { getCarList, getDriverInfoByCar } from '@modules/ProjectCar/ProjectPortal/CarManage/api'
import car from '@modules/Statistics/car/index.vue'
import SyncTable from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/SyncTable.vue'
import BatchSync from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/BatchSync.vue'

export default {
  name: "AddForm",
  mixins: [validator],
  components: {SyncTable, BatchSync},
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    // 是否关联车辆
    lineCar: {
      type: Boolean,
      default: false
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    },
    projectInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      loadingCarNum: false,
      carSearchKey: "",
      activeName: "car",
      driverList: [],
      carList: [],
      tableConfig: [
        {key: "车辆", value: "carNum"},
        {key: "司机", value: "driverFullName"}
      ]
    }
  },
  watch: {
    // 监听 tableData 的变化
    tableData: {
      handler(newVal) {
        if (!this.isDraftLoading) {
          if (newVal && newVal.length > 0) {
            const hasData = newVal.some(item => {
              const formData = item;
              return formData.projectName || formData.projectSign;
            });
            if (hasData) {
              this.saveDrafts();
            }
          }
        }
      },
      deep: true, // 深度监听，以监听到对象内部的变化
    },
    activeName: {
      handler(newVal) {
        if (newVal === 'car') {
          this.$set(this.$data, "tableConfig", [
            {key: "车辆", value: "carNum"},
            {key: "司机", value: "driverFullName"},])
        } else {
          this.$set(this.$data, "tableConfig", [
            {key: "司机", value: "driverFullName"},
            {key: "车辆", value: "carNum"},])
        }
      },
      deep: true, // 深度监听，以监听到对象内部的变化
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    titleStr() {
      let carNum = this.carList.length
      let driverNum = this.driverList.length
      return `车辆${carNum}辆 司机${driverNum}人`
    },
    span() {
      return this.layoutSingleLine ? 24 : 12;
    },
    tableList() {
      let filedMap = {
        "car": "carName", "driver": "driverFullName"
      }
      let list = this.activeName === 'car' ? this.carList : this.driverList
      let key = filedMap[this.activeName];
      list = list.filter(item => {
        return item[key] != null && item[key].length > 0
      })
      if (this.carSearchKey) {
        list = list.filter(item => {
          return (item['carNum'] != null && item['carNum'].includes(this.carSearchKey)) || (item['driverFullName'] != null && item['driverFullName'].includes(this.carSearchKey))
        })
      }
      return list;
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    reload() {
      this.carList = [];
      this.driverList = [];
      this.activeName = "car"
      this.carSearchKey = null;
      this.loadData();
    },
    async loadData() {
      // 查询车辆和用户
      let projectId = this.projectInfo.portalId;
      const params = {
        projectId,
        pageNo: 1,
        pageSize: 2000
      };
      this.loadCarData(params);
      this.loadDriverData(params);
    },
    async loadCarData(param) {
      const res = await getDriverList(param);
      this.driverList = res.data.list;

      let driverIds = this.driverList.map(item => item.id).join(',');
      getCar({driverIds}).then(res => {
        let carData = res.data;
        if (carData) {
          this.driverList.forEach(item => {
            if (carData[item.id]) {
              this.$set(item, 'carNum', carData[item.id].carNum);
              this.$set(item, 'carId', carData[item.id].carId);
            }
          })
        }
      })
    },
    async loadDriverData(param) {
      const res = await getCarList(param);
      this.carList = res.data.list;

      let carIds = this.carList.map(item => item.id).join(',');
      getDriverInfoByCar(carIds).then(res => {
        let carData = res.data;
        if (carData) {
          this.carList.forEach(item => {
            if (carData[item.id]) {
              this.$set(item, 'driverFullName', carData[item.id].driverFullName);
              this.$set(item, 'driverId', carData[item.id].id);
            }
          })
        }
      })
    },
    handleRefresh() {
      this.$emit('refresh', false)
    },
    handleClick(currTab) {

    },
    handleSearch() {

    },
    handleSync() {
      this.$refs.batchSync.open()
    },
  }
}
</script>

<style scoped lang="less"></style>
