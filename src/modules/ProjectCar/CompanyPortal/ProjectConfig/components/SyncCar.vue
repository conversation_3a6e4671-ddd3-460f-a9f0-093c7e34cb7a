<template>
  <div class="table-container">
      <div  class="full-width">
        <fks-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 4 }"
          placeholder="粘贴或输入多个车牌号，使用逗号或空格隔开，可从excel表格中复制粘贴。"
          @blur="searchData"
          v-model="searchKey"
        >
        </fks-input>
        <header class="flex row-between">
          <common-title title="查询结果" />
          <div class="buttons">
            <fks-popconfirm title="确认删除？" @onConfirm="handleDelete" style="margin-left: 15px">
              <fks-button slot="reference" dangerText icon="fks-icon-delete">删除</fks-button>
            </fks-popconfirm>
            <fks-button
              style="margin-left: 15px"
              text
              icon="fks-icon-circle-plus-outline"
              @click="handleAdd"
            >新增行</fks-button
            >
          </div>
        </header>
        <p
          class="fks-icon-warning"
          style="
            font-size: 12px;
            font-weight: normal;
            line-height: 20px;
            letter-spacing: 0.34px;
            color: #ff4143;
          "
        >
          选中数据将会覆盖项目中已存在的数据：名称，车名，关联关系等。
        </p>
      </div>
      <div class="full-width" style="overflow-y: auto; height: 70%">
        <fks-table
          ref="table"
          :data="dataList"
          :header-cell-style="{
            background: 'transparent',
            color: '#333333aa !important',
            fontWeight: 'unset !important',
          }"
          :cell-style="{ color: '#333333 !important' }"
          style="height: 100%; overflow-y: auto;"
          :sticky="true"
          @selection-change="handleSelectionChange"
        >
          <!-- 序号 -->
          <fks-table-column type="index" label="#" width="50" />

          <fks-table-column label="车牌号" width="200">
            <template slot-scope="scope">
              <fks-select
                v-model="scope.row.carNumber"
                placeholder="请输入车牌号"
                class="full-width"
                clearable
                filterable
                remote
                reserve-keyword
                @change="(val) => changeCarNum(val, scope.row)"
              >
                <fks-option
                  v-for="item in carNumList"
                  :key="item.carNum"
                  :label="item.carNum"
                  :value="item.carNum"
                >
                </fks-option>
              </fks-select>
            </template>
          </fks-table-column>

          <fks-table-column :label="item.key" v-for="(item, index) in tableConfig" :key="index">
            <template slot-scope="scope">
              <div>
                <div
                  v-if="scope.row[item.value]"
                  style="
                    display: inline-block;
                    border-radius: 11px;
                    color: #3c83ff;
                    background: rgba(60, 131, 255, 0.2);
                    padding: 4px 8px;
                    font-size: 11px;
                  "
                >
                  {{ scope.row[item.value] }}
                </div>
                <span v-else> / </span>
              </div>
            </template>
          </fks-table-column>

          <fks-table-column type="selection" width="50" />
        </fks-table>
      </div>

<!--      <div class="flex-grow-1" >-->
<!--        <common-title title="同步" />-->

<!--        <div class="table-header" style="height: 40px">-->
<!--          <div style="margin-right: 40px">-->
<!--            <span>同步前</span>-->
<!--            <span class="m-l-20">-->
<!--            <card-tag class="m-l-10" :tag="{ color: '#3C83FF', text: projectInfo.projectName }" />-->
<!--          </span>-->
<!--          </div>-->
<!--          <div>-->
<!--            <span>同步后</span>-->
<!--            <fks-checkbox style="margin-left: 15px"  v-model="checked">同步关联的司机</fks-checkbox>-->
<!--          </div>-->
<!--        </div>-->
<!--        <div class="table-box" style="height: 300px; overflow-y: auto">-->
<!--          <div class="before-table" style="margin-right: 40px">-->
<!--            <SyncResultItem :merged-list="mergedList" :filed-list="beforeConfig"></SyncResultItem>-->
<!--          </div>-->
<!--          <div class="after-table">-->
<!--            <SyncResultItem :merged-list="mergedList" :filed-list="afterConfig"></SyncResultItem>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
    </div>
</template>
<script>
import FormUpload from '@modules/FormCenter/components/FormUpload/index.vue'
import emmiter from 'fawkes-lib/lib/mixins/emitter'
import { mapState } from 'vuex'
import * as types from '@store/Getter/getterTypes.js'
import validator from '@/mixins/validator.js'
import BatchAddForm from './BatchAddForm.vue'
import { getVdUserInfo } from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import {
  getCarByNum,
  getCarList,
  getDriverInfoByCar,
} from '@modules/ProjectCar/ProjectPortal/CarManage/api'
import CardTag from '@components/CardFlow/components/tag.vue'
import SyncResultItem from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/SyncResultItem.vue'

export default {
  mixins: [emmiter, validator],
  components: {
    SyncResultItem,
    CardTag,
    FormUpload,
    BatchAddForm,
  },
  data() {
    return {
      formData: {},
      vdUserInfo: {},
      loading: false,
      checked: true,
      dataList: [],
      mergedList: [],
      searchKey: '',
      activeTab: 'car',
      tableConfig: [
        { key: '车辆', value: 'carNum' },
        { key: '司机', value: 'driverFullName' },
      ],
      before: [
        { key: 'oldNum', compareKey: null },
        { key: 'oldDriver', compareKey: null },
      ],
      after: [
        { key: 'carNum', compareKey: null },
        { key: 'driverFullName', compareKey: "oldDriver" },
      ],
      carNumList: [],
      selectedRows: [],
    }
  },
  props: {
    type: {
      type: String,
      default: 'plus',
    },
    tab: {
      type: String,
      default: 'car',
    },
    projectInfo: {
      type: Object,
      default: null,
    },
    originList: {
      type: Array,
      default: null,
    },
  },
  watch: {
    selectedRows: {
      handler(newSelectedRows) {
        if (newSelectedRows.length < 1) {
          this.$set(this.$data, "mergedList", []);
          return;
        }

        console.log("数据变动，监听", newSelectedRows)
        let list = newSelectedRows.map(selectedItem => {
          let result = JSON.parse(JSON.stringify(selectedItem))
          const originItem = this.originList?.find(item => item.carNum === result.carNum) ?? null;

          if (originItem) {
            result.oldData = originItem;
            result.oldNum = originItem.carNum;
            result.oldName = originItem.carName;
            result.oldDriver = originItem.driverFullName;
            result.oldDriverId = originItem.driverId
            if (result.driverId == null) {
              result.driverId = originItem.driverId;
            } else {
              result.oldData = null;
              result.oldNum = null;
              result.oldName = null;
              result.oldDriver = null;
              result.oldDriverId = null;
            }
          }
          return result;
        });
        this.$set(this.$data, "mergedList", list);
      },
      deep: true  // 深度监听
    }
  },
  computed: {
    ...mapState(['portal']),
    theme() {
      return this.$store.getters[types.THEME]
    },
    title() {
      const typeObj = {
        car: '车辆',
        driver: '司机',
      }
      return typeObj[this.activeTab] + '添加'
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    username() {
      return this.$storage.get('username')
    },
    userFullName() {
      return this.$storage.get('userFullname')
    },
    beforeConfig() {
      let list = JSON.parse(JSON.stringify(this.before));
      if (!this.checked) {
        list = list.splice(0, 1);
      }
      return list;
    },
    afterConfig() {
      let list = JSON.parse(JSON.stringify(this.after));
      if (!this.checked) {
        list = list.splice(0, 1);
      }
      return list;
    }
  },
  mounted() {
    this.remoteMethod();
    getVdUserInfo(this.userInfo.id).then((res) => {
      if (res.status) {
        this.vdUserInfo = res.data || {}
      }
    })
  },
  methods: {
    remoteMethod(carNum) {
      let projectId = this.portal.id
      this.loadingCarNum = true
      getCarList({ projectName: '公司门户', projectId })
        .then((res) => {
          this.carNumList = res.data.list || []
        })
        .catch(() => {})
    },

    changeCarNum(val, row) {
      const carInfo = this.carNumList.find((item) => item.carNum === val)
      Object.keys(carInfo).forEach((key) => {
        this.$set(row, key, carInfo[key])
      })
      getDriverInfoByCar(carInfo.id).then((res) => {
        let list = res.data
        if (list && list[carInfo.id]) {
          this.$set(row, 'driverFullName', list[carInfo.id].driverFullName)
          this.$set(row, 'driverId', list[carInfo.id].id)
        } else {
          this.$set(row, 'driverFullName', null)
          this.$set(row, 'driverId', null)
        }
      })
    },
    searchData() {
      let list = this.searchKey.split(/[,，\s\n]+/).filter((item) => item.trim() !== '')
      if (list.length < 1) return
      let param = {
        projectId: this.portal.id,
        nums: list.map((item) => item),
      }
      getCarByNum(param).then((res) => {
        this.dataList = Object.keys(res.data).map((key) => {
          let result = res.data[key]
          result.carNumber = result.carNum
          return result
        })

        this.$nextTick(() => {
          this.dataList.forEach((row) => {
            this.$refs.table.toggleRowSelection(row, true); // 勾选每一行
          });
        });
        let carIds = this.dataList.map((item) => item.id).join(',')
        getDriverInfoByCar(carIds).then((res) => {
          let carData = res.data
          if (carData) {
            this.dataList.forEach((item) => {
              if (carData[item.id]) {
                this.$set(item, 'driverFullName', carData[item.id].driverFullName)
                this.$set(item, 'driverId', carData[item.id].id)
              } else {
                this.$set(item, 'driverFullName', null)
                this.$set(item, 'driverId', null)
              }
            })
          }
        })
      })
    },
    handleSelectionChange(selection) {
      this.$set(this.$data, "selectedRows", selection);
    },
    beforeClose(done) {
      this.selectedRows = [];
      this.dataList = []
      this.loading = false
      this.visible = false
      this.mergedList = [];
      this.searchKey = "";
      done()
    },
    handleDelete() {
      this.dataList = this.dataList.filter((row) => !this.selectedRows.includes(row))
      this.selectedRows = [] // 清空选中的行
    },
    handleAdd() {
      let list = this.dataList
      let newItem = {};
      list.push(newItem);
      this.$set(this.$data, "dataList", list);

      // 选中新添加的行
      this.$nextTick(() => {
        this.$refs.table.toggleRowSelection(newItem, true);
      });
    },
    open() {
      this.visible = true
      // 调用接口查询是否有草稿数据
      this.$nextTick(() => {})
    },
    clear() {
      this.dataList = [];
      this.visible = false
      this.mergedList = [];
      this.searchKey = "";
    }
  },
}
</script>

<style lang="less" scoped>
/deep/ .fks-date-editor.fks-input {
  width: 100%;
}

.dialog {
  font-size: 32px;
}


.table-header {
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: space-around;
  padding: 4px;
  div {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}

.table-box {
  display: flex;
  justify-content: space-around;
  align-content: center;
  width: 100%;
  .before-table, .after-table {
    width: 45%;
    height: 100%;
  }
}
@import '~@/styles/headers';
</style>
