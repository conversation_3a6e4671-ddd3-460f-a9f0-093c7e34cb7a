<template>
  <fks-drawer
    :before-close="beforeClose"
    :modal="false"
    :visible.sync="visible"
    :wrapperClosable="false"
    class="dialog"
    direction="rtl"
    size="45vw"
  >
    <header class="card-header">
      <div class="flex col-center m-b-24">
        <div class="card-title">{{ currentMenus.title }}</div>
      </div>
    </header>
    <common-title style="margin: 18px 0" title="菜单列表"/>
    <fks-table
      style="max-width: 900px;"
      :data="currentMenus.authList"
      :header-cell-style="{background: 'transparent', color: '#333333aa !important', fontWeight: 'unset !important'}"
      :cell-style="{color: '#333333 !important'}"
      max-height="300"
    >
      <fks-table-column align="center" label="#" type="index" width="60"/>
      <fks-table-column label="菜单名称" prop="menuName"/>
      <fks-table-column label="菜单ID" prop="menuId"/>
      <fks-table-column label="创建时间">
        <template slot-scope="{row}">
          {{ $dayjs(row.createDate).format('YYYY-MM-DD HH:mm') }}
        </template>
      </fks-table-column>
    </fks-table>
  </fks-drawer>
</template>
<script>
import FormUpload from '@/modules/FormCenter/components/FormUpload';
import emmiter from 'fawkes-lib/lib/mixins/emitter';
import {mapState} from 'vuex';
import * as types from '@/store/Getter/getterTypes.js';
import validator from '@/mixins/validator.js';
import BatchAddForm from './BatchAddForm.vue';
import dayjs from "dayjs";
import {
  addDriver,
  batchAddDriver,
  bindDiver,
  updateDriver
} from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import { getVdUserInfo } from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import { deleteDraft, DRIVER } from '@utils/draftUtil'
import { getRoleMenuList } from '@modules/ProjectCar/CompanyPortal/UserManagement/api'

export default {
  mixins: [emmiter, validator],
  components: {
    FormUpload,
    BatchAddForm
  },
  data() {
    return {
      tableData: [],
      loading: false,
      menuInfo: null,
      visible: false,
      currentMenus: null,
      cards: null,
    }
  },
  props: {
    roleList: {
      type: Array,
      default: null
    }
  },
  computed: {
    ...mapState(['portal']),
    theme() {
      return this.$store.getters[types.THEME];
      // return '#2F54EB'
    },
  },
  methods: {
    open() {
      this.visible = true;
      // 调用接口查询是否有草稿数据
      this.$nextTick(() => {

        const ids = this.roleList.map(item => item.id).join(',')
        getRoleMenuList(ids).then(res => {
          if (res.status) {
            this.menuInfo = res.data;
            this.cards = this.tableData.map(item => {
              return {
                ...item,
                title: item.roleName
              }
            })
          }
        })
      })
    },
  }
}
</script>

<style lang='less' scoped>
@import "~@/styles/disabled";
@import "~@/styles/drawer";

/deep/ .fks-date-editor.fks-input {
  width: 100%;
}

.dialog {
  font-size: 32px;
}

.table-container {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: center;
  align-content: space-between;
  height: 100%;
}

.sub-btn {
  padding: 5px 10px;
  color: #333;
  transition: all 0.3s ease; /* 平滑过渡效果 */
}

.sub-btn:hover {
  color: #333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.sub-btn:active {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
  transform: translateY(5px); /* 模拟点击下沉效果 */
}


</style>
