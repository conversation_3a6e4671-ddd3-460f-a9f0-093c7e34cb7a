<template>
  <fks-table
    ref="table"
    :data="tableData"
    :header-cell-style="{
      background: 'transparent',
      color: '#333333aa !important',
      fontWeight: 'unset !important',
    }"
    :cell-style="{ color: '#333333 !important' }"
    style="overflow-y: auto;"
    height="500"
    class="full-height full-width"
    @selection-change="handleSelectionChange"
    @paste.native="handlePaste($event)"
    :sticky="true"
  >
    <!-- 选择框 -->
    <fks-table-column type="selection" width="50" />
    <!-- 序号 -->
    <fks-table-column type="index" label="序号" width="50" />
    <fks-table-column width="140" prop="projectSign">
      <template slot="header">
        <red-star title="项目编码" />
      </template>
      <template slot-scope="scope">
        <fks-input
          disabled
          v-model="scope.row.projectSign"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          placeholder="系统自动生成"
        />
      </template>
    </fks-table-column>
    <fks-table-column  width="220" prop="projectName">
      <template slot="header">
        <red-star title="项目名称" />
      </template>
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.projectName"
          :disabled="isView"
          placeholder="请输入项目名称"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>
    <fks-table-column width="180" prop="projectNameAbbreviation">
      <template slot="header">
        <red-star title="项目简称" />
      </template>
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.projectNameAbbreviation"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          :disabled="isView"
          placeholder="请输入项目名称"
        />
      </template>
    </fks-table-column>

    <fks-table-column label="所在地区" width="180" prop="projectProvince">
      <template slot-scope="scope">
        <fks-select
          v-model="scope.row.projectProvince"
          filterable
          placeholder="请选择所在地区"
          class="full-width"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        >
          <fks-option
            v-for="item in provinces"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </fks-select>
      </template>
    </fks-table-column>
    <fks-table-column width="220" prop="projectManageDeptName">
      <template slot="header">
        <red-star title="项目所属二级单位" />
      </template>
      <template slot-scope="scope">
        <feishu-department-select
          :initial-value="scope.row.projectManageDeptName"
          :trigger-on-focus="true"
          :level="level"
          @dept-change="handleDeptChange($event, scope)"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          @value-change="scope.row.projectManageDeptName = $event"
          @input="level = 2"
          @focus="level = 3"
          @update-dept="handleUpdateDepts"
        />
      </template>
    </fks-table-column>
    <fks-table-column width="220" prop="projectManageDept2Name">
      <template slot="header">
        <red-star title="项目所属三级部门" />
      </template>
      <template slot-scope="scope">
        <feishu-department-select
          :initial-value="scope.row.projectManageDept2Name"
          :trigger-on-focus="true"
          :level="3"
          :deptId="scope.row.projectManageDeptId"
          :triggerOnFocus="Boolean(scope.row.projectManageDeptId)"
          @dept-change="scope.row.projectManageDept2Id = $event"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          @value-change="scope.row.projectManageDept2Name = $event"
          @update-dept="handleUpdateDepts"
        />
      </template>
    </fks-table-column>

    <fks-table-column width="250" prop="projectUseCarNature">
      <template slot="header">
        <red-star title="项目用车性质" />
      </template>
      <template slot-scope="scope">
        <fks-select
          v-model="scope.row.projectUseCarNature"
          filterable
          placeholder="请选择项目用车性质"
          class="full-width"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          multiple
          collapse-tags
        >
          <fks-option v-for="option in enums.UseCarNatureEnums" :key="option.key"  :label="option.value" :value="option.key" />
        </fks-select>
      </template>
    </fks-table-column>
  </fks-table>
</template>

<script>
import validator from '@/mixins/validator.js';
import {mapState} from "vuex";
import { saveDraftDebounced, getDraft, DRIVER, deleteDraft, CAR, PROJECT } from '@utils/draftUtil'
import FeishuDepartmentSelect
  from "@modules/ProjectCar/CompanyPortal/UserManagement/components/feishu-department-selector.vue";
import RedStar from "@components/red-star.vue";
import multiSelection from "@/mixins/multiSelection";

export default {
  name: "AddForm",
  mixins: [validator, multiSelection],
  components: {RedStar, FeishuDepartmentSelect},
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    // 是否关联车辆
    lineCar: {
      type: Boolean,
      default: false
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      level :3,
      carInfo: {}, // 选择的车辆信息
      loadingCarNum: false,
      carNumList: [], // 车辆列表
      tableData: [],
      saveDraft: false,
      draftList: [],

      formData: {
        projectName: '',
        projectSign: null,
        projectNameAbbreviation: ''
      },
      backFromData: {},
      selectTableColumn: "",
      selectTableIndex: 0,
      deptList: [],
    }
  },
  watch: {
    // 监听 tableData 的变化
    tableData: {
      handler(newVal) {
        if (this.saveDraft) {
          if (newVal && newVal.length > 0) {
            const hasData = newVal.some(item => {
              const formData = item;
              return formData.projectName || formData.projectSign || formData.projectNameAbbreviation ;
            });
            if (hasData) {
              this.saveDrafts();
            }
          }
        }

      },
      deep: true, // 深度监听，以监听到对象内部的变化
    },
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['provinces']),
    span() {
      return this.layoutSingleLine ? 24 : 12;
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    isEdit() {
      return this.type === 'edit';
    },
    isView() {
      return this.type === 'view';
    },
    isAdd() {
      return this.type === 'plus';
    },
    provincesEnums() {
      return this.provinces.map(item => {
        return {
          value: item.name,
          key: item.name
        }
      })
    },
    projectCarType() {
      return this.enums.ProjectUseCarTypeEnums
    },
    flowConfig() {
      if (this.type === 'view') {
        return Object.keys(this.formData).reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      if (this.isEdit) {
        return ['carNum', 'carType', 'carSeatNum'].reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      return {}
    },
    rules() {
      return {
      }
    },
  },
  mounted() {
    this.addRows();
  },
  methods: {
    handleUpdateDepts(newDepts) {
      // 假设父组件中已有的部门数组为 this.depts
      newDepts.forEach(item => {
        if (!this.deptList.find(dept => dept.departmentId === item.departmentId)) {
          this.deptList.push(item);
        }
      });
    },
    handleDeptChange(payload, scope) {
      if (scope.row.projectManageDeptId && (scope.row.projectManageDeptId !== payload)) {
        // 清空三级部门
        scope.row.projectManageDept2Id = '';
        scope.row.projectManageDept2Name = '';
      }
      scope.row.projectManageDeptId = payload;
      this.$forceUpdate();
    },
    handleDept2Change(payload, scope) {
      scope.row.projectManageDept2Id = payload.departmentId;
      scope.row.projectManageDept2Name = payload.name;
    },
    loadData() {
      getDraft(this.userInfo.id, PROJECT).then(res => {
        this.draftList = JSON.parse(res.data);
        if (this.draftList && this.draftList.length > 0) {
          this.openHTML();
        }
      })
    },
    loadDraftData() {
      this.saveDraft = false;
      // 模拟从草稿加载数据
      this.tableData = [...this.draftList];
      this.$nextTick(() => {
        this.$message.success('草稿已恢复')
      });
    },
    // 保存草稿
    saveDrafts() {
      let context = JSON.stringify(this.tableData);
      saveDraftDebounced(this.userInfo.id, PROJECT, context)
        .then(() => {
          // this.$message.success('草稿保存成功');
        })
        .catch((error) => {
          console.error('保存草稿失败:', error);
        });
    },
    validateTable() {
      const isTableComplete = this.some(this.tableData, (data) => {
        return !data.projectName || !data.projectNameAbbreviation || !data.projectProvince ||
          !data.projectManageDeptName || !data.projectManageDept2Name || !data.projectUseCarNature || data.projectUseCarNature.length < 1;
      });

      if (isTableComplete) {
        this.$message.warning('请完善表格信息！');
        return Promise.reject(false); // 返回一个被拒绝的 Promise，以中断提交流程
      }
      // 如果表格信息完整，返回 Promise.resolve 表示验证通过
      return Promise.resolve(true);
    },
    addRows() {
      this.$nextTick(() => {
        this.saveDraft = false;
        const tableElement = this.$refs.table.$el;  // 获取表格 DOM 元素
        tableElement.scrollTop = tableElement.scrollHeight;  // 滚动到最底部
        let emptyData = JSON.parse(JSON.stringify(this.formData));
        let list = this.tableData
        list.push(emptyData);
        this.$set(this.$data, "tableData", list);
      })
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    // 删除选中的行
    delSelectedRows() {
      this.tableData = this.tableData.filter(row => !this.selectedRows.includes(row));
      this.$refs.table.clearSelection();  // 清除表格选中的状态
    },
    cleanTableData() {
      this.saveDraft = false;
      this.tableData = []
    },
    some(collection, predicate) {
      // 如果 predicate 是函数，直接使用它，否则使用默认检查值是否存在的函数
      const check = typeof predicate === 'function' ? predicate : (val) => !!val;
      // 遍历集合，遇到符合条件的元素返回 true
      for (let item of collection) {
        if (check(item)) {
          return true;
        }
      }
      return false;
    },

    removeDraft() {
      deleteDraft(this.userInfo.id, PROJECT)
      this.$nextTick(() => {
        this.$message.success('草稿删除成功')
      });
    },
    openHTML() {
      // 保存当前 Vue 实例的引用
      const self = this;

      // 定义点击恢复的事件处理函数
      const restoreDraft = () => {
        self.loadDraftData(); // 调用你在 methods 里定义的 loadDraftData 函数
      };

      // 定义点击恢复的事件处理函数
      const removeDraft = () => {
        self.removeDraft(); // 调用你在 methods 里定义的 loadDraftData 函数
      };
      // 使用 Vue 的 VNode 创建可点击的 HTML 元素
      this.$message({
        dangerouslyUseHTMLString: true,
        type: "warning",
        message: `
          <span>存在草稿，是否恢复？</span>
          <a href="javascript:void(0);" style="color: #027AFF;" id="restoreDraft">恢复</a>
          <a href="javascript:void(0);" style="color: #FF4D4F;" id="removeDraft">删除</a>
        `,
        onClose: () => {
          const restoreLink = document.getElementById('restoreDraft');
          if (restoreLink) {
            restoreLink.removeEventListener('click', restoreDraft);
          }
          const removeLink = document.getElementById('removeDraft');
          if (removeLink) {
            removeLink.removeEventListener('click', removeDraft);
          }
        }
      });

      // 手动绑定事件到生成的链接
      this.$nextTick(() => {
        const restoreLink = document.getElementById('restoreDraft');
        if (restoreLink) {
          restoreLink.addEventListener('click', restoreDraft);
        }
        // 销毁弹窗时的回调
        const removeLink = document.getElementById('removeDraft');
        if (removeLink) {
          removeLink.addEventListener('click', removeDraft);
        }
      });
    },

    handlePaste(event) {
      event.preventDefault();
      // 获取剪贴板中的文本内容
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedData = clipboardData.getData('Text');  // 获取纯文本格式的数据

      let rows = pastedData
        .trim() // 去除首尾换行，避免空行
        .split('\n') // 按行拆分
        .map(row => row.split('\t').map(cell => cell === '' ? null : cell))
        .filter(row => row.some(cell => cell !== ''));  // 过滤掉完全为空的行

      let len = rows[0].length;

      const field = this.selectTableColumn;
      let columns = this.$refs.table.columns.map(column => column.property);
      let startIndex = columns.indexOf(field)
      const props = columns.slice(startIndex, startIndex + len);

      if (rows.length > 0) {
        const startRow = this.selectTableIndex;

        // 确保 tableData 的长度足够，至少有 startRow + rows.length 行
        while (this.tableData.length < startRow + rows.length) {
          this.tableData.push({});  // 添加空对象作为新行
        }

        rows.forEach((cell, rowIndex) => {
          const targetRow = startRow + rowIndex;

          // 遍历 props，从 props[0] 开始逐一处理
          props.forEach((prop, columnIndex) => {
            let value = cell ? cell[columnIndex] : '';
            if (value) {
              value = value.replace(/[\s\r]/g, '');
            }

            if (['projectManageDeptName', 'projectManageDept2Name'].includes(prop)) {
              // 根据字段名构造对应的动态 ref 名称
              let find = this.deptList.find(item => item.name == value);
              if (find) {
                if (prop === 'projectManageDeptName') {
                  this.$set(this.tableData[targetRow], prop, value);
                  this.$set(this.tableData[targetRow], 'projectManageDeptId', find.departmentId);
                } else {
                  this.$set(this.tableData[targetRow], prop, value);
                  this.$set(this.tableData[targetRow], 'projectManageDept2Id', find.departmentId);
                }
              }
              return; // 当前回调结束，相当于 continue
            }

            if (['projectProvince'].includes(prop)) {
              const mapObject = {
                projectProvince: this.provincesEnums,
              };

              const map = mapObject[prop]; // 通过字段名获取对应的选项数组

              if (map && Array.isArray(map)) {
                const foundEntry = map.find(entry => entry.value === value);
                value = foundEntry ? foundEntry.key : '';
              }
            }
            if (['projectUseCarNature'].includes(prop)) {
              const map = this.enums.UseCarNatureEnums;
              if (map && Array.isArray(map)) {
                // 1. 先用 `,` 或空格拆分成数组
                const valuesArray = value.split(/[\s,]+/).filter(Boolean);

                // 2. 遍历查找匹配的 key
                const convertedList = valuesArray.map(v => {
                  const foundEntry = map.find(entry => entry.value === v);
                  return foundEntry ? foundEntry.key : null; // 不匹配的值返回 null
                }).filter(v => v !== null); // 过滤掉 null 值

                value = convertedList; // 返回最终的 list
              }
            }

            // 将值写入对应的列
            this.$set(this.tableData[targetRow], prop, value);
          });
        });
      }
    },

    selectColumn(row, column, index) {
      // 设置当前选择的行
      this.saveDraft = true;
      const propName = column.property; // 获取当前列的字段名（prop）
      this.selectTableColumn = propName;
      this.selectTableIndex = index;
    },
  }
}
</script>

<style scoped lang="less">
@import '~@/styles/disabled';
@import '~@/styles/scrollbar';
</style>
