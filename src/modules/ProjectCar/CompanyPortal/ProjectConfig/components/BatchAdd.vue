<template>
  <fks-drawer
    :before-close="beforeClose"
    :modal="false"
    :visible.sync="visible"
    :wrapperClosable="true"
    class="dialog"
    direction="rtl"
    size="80vw"
  >
    <template slot="title">
      <div class="dialog-title flex col-center">
        <i :class="'fks-icon-' + type" :style="{ color: theme }" class="m-r-10"/>
        <span>{{ title }}项目</span>
      </div>
    </template>
    <div class="table-container">

      <header class="flex row-between">
        <common-title title="项目信息" />
        <div class="buttons">
          <fks-checkbox v-model="checked">为自己分配管理员角色</fks-checkbox>
          <fks-popconfirm
            title="确认删除？"
            @onConfirm="handleDelete"
            :disabled="disabledConfirm"
            style="margin-left: 15px"
          >
            <fks-button
              slot="reference"
              dangerText
              icon="fks-icon-delete"
              @click="handlePreCheck"
            >
              删除
            </fks-button>
          </fks-popconfirm>
          <fks-button style="margin-left: 15px;" text icon="fks-icon-circle-plus-outline" @click="handleAdd">新增行</fks-button>
        </div>
      </header>
      <batch-add-form
        ref="addFormRef"
        :line-car="checked"
        style="flex-grow: 20; height: 500px"
        type="plus"
        layout-single-line
        @disabledPopConfirm="disabledConfirm = $event"
      />
      <common-title title="填写信息"/>
      <fks-form
        ref="formRef"
        :model="formData"
        class="form flex-grow-1 newTheme"
        label-position="left"
        label-width="210px"
      >
        <fks-form-item label="填写人">
          <fks-input :value="userFullName" disabled readonly/>
        </fks-form-item>
        <fks-form-item label="填写时间">
          <fks-input :value="createTime" disabled readonly/>
        </fks-form-item>
        <fks-form-item label="二级单位">
          <fks-input :value="vdUserInfo.userDepName" disabled readonly/>
        </fks-form-item>
        <fks-form-item label="三级部门">
          <fks-input :value="vdUserInfo.userDepName2" disabled readonly/>
        </fks-form-item>
      </fks-form>
      <div>
        <fks-divider/>
        <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                    @click="submit">
          提交
        </fks-button>
      </div>
    </div>
  </fks-drawer>
</template>
<script>
import FormUpload from '@modules/FormCenter/components/FormUpload/index.vue';
import emmiter from 'fawkes-lib/lib/mixins/emitter';
import {mapState} from 'vuex';
import * as types from '@store/Getter/getterTypes.js';
import validator from '@/mixins/validator.js';
import BatchAddForm from './BatchAddForm.vue';
import {
  authUserToProject,
  batchAddProject,
  getVdUserInfo
} from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import { deleteDraft, DRIVER } from '@utils/draftUtil'
import dayjs from 'dayjs'
import * as actionTypes from '@store/Action/actionTypes'
import * as mutationTypes from '@store/Mutation/mutationTypes'

export default {
  mixins: [emmiter, validator],
  components: {
    FormUpload,
    BatchAddForm
  },
  props: {
    type: {
      type: String,
      default: 'plus'
    }
  },
  data() {
    return {
      formData: {},
      vdUserInfo: {},
      createTime: null,
      loading: false,
      visible: false,
      driverInfo: {},
      checked: true,
      disabledConfirm: true
    }
  },
  computed: {
    ...mapState(['portal']),
    theme() {
      return this.$store.getters[types.THEME];
      // return '#2F54EB'
    },
    title() {
      const typeObj = {
        plus: '批量新增'
      }
      return typeObj[this.type]
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    username() {
      return this.$storage.get('username')
    },
    userFullName() {
      return this.$storage.get('userFullname')
    },
  },
  mounted() {
    this.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    getVdUserInfo(this.userInfo.id).then(res => {
      if (res.status) {
        this.vdUserInfo = res.data || {};
      }
    })
  },
  methods: {
    beforeClose(done) {
      // tableData
      const tableData = this.$refs.addFormRef.tableData;
      const flag = tableData.some(item => Object.values(item).some(item1 => item1))
      if (flag) { // 发生变化
        this.$emit('openDialog')
      } else {
        this.visible = false;
        // 保存草稿
        this.$refs.addFormRef.cleanTableData();
        this.loading = false;
        done()
      }
    },
    cleanData() {
      this.$refs.addFormRef.cleanTableData();
      this.loading = false;
    },
    handlePreCheck(e) {
      // 检查是否有选中项
      if (this.disabledConfirm) {
        this.$message.warning('请选择要删除的行');
        e.preventDefault();
        e.stopPropagation();
      }
    },
    handleDelete() {
      this.$refs.addFormRef.delSelectedRows();
    },
    handleAdd() {
      this.$refs.addFormRef.addRows();
    },
    async submit() {
      try {
        await this.$refs.addFormRef.validateTable();
      } catch (e) {
        return false;
      }
      this.loading = true;
      const tableData = this.$refs.addFormRef.tableData;
      // 提交数据
      const payload = JSON.parse(JSON.stringify(tableData));
      let dataList = payload.map(item => {
        delete item.projectSign
        item.projectUseCarNature = item.projectUseCarNature.join(",")
        return {
          vdProject: item
        }
      })
      const res = await batchAddProject(dataList);
      if (!res.status) {
        this.loading = false;
        // this.$message.error(res.message || `新增失败！`);
        return false;
      }
      if (this.checked) {
        let projectSigns = res.data.map(item => item.projectSign);
        let userIds = [this.userInfo.userId]
        authUserToProject({projectSigns, userIds})
      }


      this.loading = false;
      this.$refs.addFormRef.cleanTableData();
      this.broadcast('FormUpload', 'formSubmit', true);
      let portalList = await  this.$store.dispatch(actionTypes.GET_PORTALS)
      this.$store.commit(mutationTypes.SET_PORTALS, portalList)
      this.$message.success(`批量新增成功`);
      this.$emit('updateTable');
      this.$emit('refresh')
      deleteDraft(this.userInfo.id, DRIVER)
      this.$nextTick(() => {
        this.beforeClose(() => {
        });
      });
    },
    open() {
      this.visible = true;
      // 调用接口查询是否有草稿数据
      this.$nextTick(() => {
        this.$refs.addFormRef.loadData();
      })
    },
  },
}
</script>

<style lang='less' scoped>

/deep/ .fks-date-editor.fks-input {
  width: 100%;
}

.dialog {
  font-size: 32px;
}

.table-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: space-between;
  height: 100%;
}

.sub-btn {
  padding: 5px 10px;
  color: #333;
  transition: all 0.3s ease; /* 平滑过渡效果 */
}

.sub-btn:hover {
  color: #333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.sub-btn:active {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
  transform: translateY(5px); /* 模拟点击下沉效果 */
}

@import "~@/styles/button";
@import '~@/styles/disabled';
@import "~@/styles/input";
</style>
