<template>
  <div class="table-container">
    <div  class="full-width">
      <fks-input
        type="textarea"
        :autosize="{ minRows: 4, maxRows: 4 }"
        placeholder="粘贴或输入多个电话号码，使用逗号或空格隔开，可从excel表格中复制粘贴。"
        @blur="searchData"
        v-model="searchKey"
      >
      </fks-input>
      <header class="flex row-between">
        <common-title title="查询结果" />
        <div class="buttons">
          <fks-popconfirm title="确认删除？" @onConfirm="handleDelete" style="margin-left: 15px">
            <fks-button slot="reference" dangerText icon="fks-icon-delete">删除</fks-button>
          </fks-popconfirm>
          <fks-button
            style="margin-left: 15px"
            text
            icon="fks-icon-circle-plus-outline"
            @click="handleAdd"
          >新增行</fks-button
          >
        </div>
      </header>
      <p class="fks-icon-warning" style="font-size: 12px;
                font-weight: normal;
                line-height: 20px;
                letter-spacing: 0.34px;
                color: #FF4143;">
        选中数据将会覆盖项目中已存在的数据：名称，手机号，关联关系等。</p>
    </div>
    <div class="full-width" style="overflow-y: auto; height: 70%">
      <fks-table
        ref="table"
        :data="dataList"
        :header-cell-style="{
            background: 'transparent',
            color: '#333333aa !important',
            fontWeight: 'unset !important',
          }"
        :cell-style="{ color: '#333333 !important' }"
        style="height: 100%; overflow-y: auto;"
        :sticky="true"
        @selection-change="handleSelectionChange"
      >
        <!-- 序号 -->
        <fks-table-column type="index" label="#" width="50" />
        <fks-table-column label="联系电话" width="220">
          <template slot-scope="scope">
            <fks-input
              v-model="scope.row.driverPhone"
              placeholder="请输入联系电话"
              @blur="phoneBlur(scope.row)"
              @input="handleCarNumberInput(scope.row)"
            />
          </template>
        </fks-table-column>

        <fks-table-column :label="item.key" v-for="(item, index) in tableConfig" :key="index">
          <template slot-scope="scope">
            <div>
              <div
                v-if="scope.row[item.value]"
                style="
                    display: inline-block;
                    border-radius: 11px;
                    color: #3c83ff;
                    background: rgba(60, 131, 255, 0.2);
                    padding: 4px 8px;
                    font-size: 11px;
                  "
              >
                {{ scope.row[item.value] }}
              </div>
              <span v-else> / </span>
            </div>
          </template>
        </fks-table-column>

        <fks-table-column type="selection" width="50" />
      </fks-table>
    </div>
<!--    <div class="flex-grow-1">-->
<!--      <common-title title="同步" />-->
<!--      <div class="table-header" style="height: 40px">-->
<!--        <div style="margin-right: 40px">-->
<!--          <span>同步前</span>-->
<!--          <span class="m-l-20">-->
<!--            <card-tag class="m-l-10" :tag="{ color: '#3C83FF', text: projectInfo.projectName }" />-->
<!--          </span>-->
<!--        </div>-->
<!--        <div>-->
<!--          <span>同步后</span>-->
<!--          <fks-checkbox style="margin-left: 15px"  v-model="checked">同步关联的车辆</fks-checkbox>-->
<!--        </div>-->
<!--      </div>-->
<!--      <div class="table-box" style="height: 300px; overflow-y: auto">-->
<!--        <div class="before-table" style="margin-right: 40px">-->
<!--          <SyncResultItem :merged-list="mergedList" :filed-list="beforeConfig"></SyncResultItem>-->
<!--        </div>-->
<!--        <div class="after-table">-->
<!--          <SyncResultItem :merged-list="mergedList" :filed-list="afterConfig"></SyncResultItem>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
  </div>
</template>
<script>
import FormUpload from '@modules/FormCenter/components/FormUpload/index.vue'
import emmiter from 'fawkes-lib/lib/mixins/emitter'
import { mapState } from 'vuex'
import * as types from '@store/Getter/getterTypes.js'
import validator from '@/mixins/validator.js'
import BatchAddForm from './BatchAddForm.vue'
import { batchAddProject, getVdUserInfo } from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import {
  getCar,
  getCarNum, getDrierByPhone,
  getDrierByPhoneList
} from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import CardTag from '@components/CardFlow/components/tag.vue'
import SyncResultItem
  from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/SyncResultItem.vue'

export default {
  mixins: [emmiter, validator],
  components: {
    CardTag,
    FormUpload,
    BatchAddForm,
    SyncResultItem
  },
  data() {
    return {
      formData: {},
      vdUserInfo: {},
      loading: false,
      dataList: [],
      checked: true,
      searchKey: '',
      activeTab: 'car',
      tableConfig: [
        { key: '司机', value: 'driverFullName' },
        { key: '车辆', value: 'carNum' },
      ],
      before: [
        { key: 'oldPhone', compareKey: null },
        { key: 'oldName', compareKey: null },
        { key: 'oldCar', compareKey: null },
      ],
      after: [
        { key: 'driverPhone', compareKey: null },
        { key: 'driverFullName', compareKey: "oldName" },
        { key: 'carNum', compareKey: "oldCar" },
      ],
      carNumList: [],
      selectedRows: [],
      mergedList: [],
    }
  },
  props: {
    type: {
      type: String,
      default: 'plus',
    },
    tab: {
      type: String,
      default: 'car',
    },
    projectInfo: {
      type: Object,
      default: null,
    },
    originList: {
      type: Array,
      default: null
    }
  },
  computed: {
    ...mapState(['portal']),
    theme() {
      return this.$store.getters[types.THEME]
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    username() {
      return this.$storage.get('username')
    },
    userFullName() {
      return this.$storage.get('userFullname')
    },
    beforeConfig() {
      let list = JSON.parse(JSON.stringify(this.before));
      if (!this.checked) {
        list = list.splice(0, 2);
      }
      return list;
    },
    afterConfig() {
      let list = JSON.parse(JSON.stringify(this.after));
      if (!this.checked) {
        list = list.splice(0, 2);
      }
      return list;
    }
  },

  watch: {
    selectedRows: {
      handler(newSelectedRows) {
        if (newSelectedRows.length < 1) {
          this.$set(this.$data, "mergedList", []);
          return;
        }

        let list = newSelectedRows.map(selectedItem => {
          let result = JSON.parse(JSON.stringify(selectedItem))
          const originItem = this.originList?.find(item => item.driverPhone === result.driverPhone) ?? null;

          if (originItem) {
            result.oldData = originItem;
            result.oldPhone = originItem.driverPhone;
            result.oldName = originItem.driverFullName;
            result.oldCar = originItem.carNum;
            result.oldCarId = originItem.carId
            if (result.carId == null) {
              result.carId = originItem.carId;
            }
          } else {
            result.oldData = null;
            result.oldPhone = null;
            result.oldName = null;
            result.oldCar = null;
            result.oldCarId = null;
          }
          return result;
        });
        this.$set(this.$data, "mergedList", list);
      },
      deep: true  // 深度监听
    }
  },
  mounted() {
    getVdUserInfo(this.userInfo.id).then((res) => {
      if (res.status) {
        this.vdUserInfo = res.data || {}
      }
    })
  },
  methods: {
    searchData() {
      let list = this.searchKey.split(/[,，\s\n]+/).filter((item) => item.trim() !== '')
      if (list.length < 1) return;
      let param = {
        projectId: this.portal.id,
        phones: list.map((item) => item),
      }
      getDrierByPhoneList(param).then((res) => {
        this.dataList = Object.keys(res.data).map((key) => {
          let result =  res.data[key];
          return result;
        })

        this.$nextTick(() => {
          this.dataList.forEach((row) => {
            this.$refs.table.toggleRowSelection(row, true); // 勾选每一行
          });
        });

        let driverIds = this.dataList.map((item) => item.id).join(',')
        getCar({driverIds}).then((res) => {
          let carData = res.data
          if (carData) {
            this.dataList.forEach((item) => {
              if (carData[item.id]) {
                this.$set(item, 'carNum', carData[item.id].carNum)
                this.$set(item, 'carId', carData[item.id].id)
              }
            })
          }
        })
      })
    },
    handleCarNumberInput(row) {
      const maxLength = 15; // 设置最大长度
      const carNumber = row.driverPhone;
      // 如果输入超过最大长度，截取前 maxLength 个字符
      if (carNumber && carNumber.length > maxLength) {
        row.driverPhone = carNumber.slice(0, maxLength);
      }
    },
    phoneBlur(item) {
      let projectId = this.$route.params.projectId || this.portal.id;
      let phone = item.driverPhone;
      getDrierByPhone({
        phone, projectId
      }).then((res) => {
        let driver = res.data;
        if (driver) {
          Object.keys(driver).forEach(key => {
            this.$set(item, key, driver[key]);
          });
          this.$set(item, 'originDriver', driver.driverFullName);

          getCar({driverIds: item.id}).then((res) => {
            let carData = res.data
            if (carData) {
              this.dataList.forEach((item) => {
                if (carData[item.id]) {
                  this.$set(item, 'carNum', carData[item.id].carNum)
                  this.$set(item, 'carId', carData[item.id].id)
                }
              })
            }
          })
          console.log('liebiao', this.dataList)
        }
      })
    },
    handleSelectionChange(selection) {
      console.log("选择的列", selection);
      this.selectedRows = selection
    },
    beforeClose(done) {
      this.selectedRows = [];
      this.dataList = []
      this.loading = false
      this.visible = false
      this.mergedList = [];
      this.searchKey = "";
      done()
    },
    handleDelete() {
      this.dataList = this.dataList.filter(row => !this.selectedRows.includes(row));
      this.selectedRows = []; // 清空选中的行
    },
    handleAdd() {
      let list = this.dataList
      let newItem = {};
      list.push(newItem);
      this.$set(this.$data, "dataList", list);

      // 选中新添加的行
      this.$nextTick(() => {
        this.$refs.table.toggleRowSelection(newItem, true);
      });
    },
    open() {
      this.visible = true
      // 调用接口查询是否有草稿数据
      this.$nextTick(() => {})
    },
  },
}
</script>

<style lang="less" scoped>
/deep/ .fks-date-editor.fks-input {
  width: 100%;
}

.dialog {
  font-size: 32px;
}


.table-header {
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: space-around;
  padding: 4px;
  div {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}

.table-box {
  display: flex;
  justify-content: space-around;
  align-content: center;
  width: 100%;
  .before-table, .after-table {
    width: 45%;
    height: 100%;
  }
}

@import "~@/styles/headers";
</style>
