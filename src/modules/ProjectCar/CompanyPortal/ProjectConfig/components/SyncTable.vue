<template>
  <fks-table
    ref="table"
    :data="tableList"
    :header-cell-style="{
        background: 'transparent',
        color: '#333333aa !important',
        fontWeight: 'unset !important',
      }"
    :cell-style="{ color: '#333333 !important' }"
    style="overflow-y: auto; height: 250px"
    :sticky="true"
  >
    <!-- 序号 -->
    <fks-table-column type="index" label="#" width="50" />

    <fks-table-column :label="item.key" v-for="(item, index) in tableConfig" :key="index">
      <template slot-scope="scope">
        <div>
          <div
            v-if="scope.row[item.value]"
            style="
                display: inline-block;
                border-radius: 11px;
                color: #3c83ff;
                background: rgba(60, 131, 255, 0.2);
                padding: 4px 8px;
                font-size: 11px;
              "
          >
            {{ scope.row[item.value] }}
          </div>
          <span v-else> / </span>
        </div>
      </template>
    </fks-table-column>
  </fks-table>
</template>

<script>
import validator from '@/mixins/validator.js';
import {mapState} from "vuex";

export default {
  name: "AddForm",
  mixins: [validator],
  components: {},
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    // 是否关联车辆
    lineCar: {
      type: Boolean,
      default: false
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    },
    projectInfo: {
      type: Object,
      default: null
    },
    tableList: {
      type: Array,
      default: null
    },
    tableConfig: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      loadingCarNum: false,
    }
  },
  watch: {
    // 监听 tableData 的变化
    tableData: {
      handler(newVal) {
        if (!this.isDraftLoading) {
          if (newVal && newVal.length > 0) {
            const hasData = newVal.some(item => {
              const formData = item;
              return formData.projectName || formData.projectSign;
            });
            if (hasData) {
              this.saveDrafts();
            }
          }
        }
      },
      deep: true, // 深度监听，以监听到对象内部的变化
    },
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    span() {
      return this.layoutSingleLine ? 24 : 12;
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
  },
  mounted() {
  },
  methods: {
  }
}
</script>

<style scoped lang="less"></style>
