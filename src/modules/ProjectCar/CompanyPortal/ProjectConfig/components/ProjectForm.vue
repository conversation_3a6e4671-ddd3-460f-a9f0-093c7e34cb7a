<template>
  <div class="flex flex-column full-height">
    <header class="card-header" v-if="showHeader">
      <div class="flex flex-column col-baseline m-b-24">
        <div class="card-title" style="width: 99%">{{ currentData.projectName }}</div>
        <div
          v-if="currentData && currentData.dispatcher && currentData.dispatcher.length"
          class="card-desc flex col-center mt-2"
        >
          <span>车辆调度员</span>
          <fks-divider direction="vertical" />
          <div class="flex col-center">
            <card-tag
              v-for="(tag, index) in currentData.dispatcher"
              :key="index"
              :tag="{ color: '#3C83FF', text: tag.userFullname }"
              :class="{ 'm-l-10': index > 0 }"
            />
          </div>
        </div>
      </div>
    </header>
    <main
      class="flex-grow-1 flex flex-column overflow-y-auto overflow-x-hidden"
      style="max-width: 70vw"
    >
      <div class="flex-grow-1 overflow-y-auto form full-width overflow-x-hidden">
        <add-form :project-info="currentData" ref="addFormRef" :type="type" layout-single-line />
        <sync-table
          v-if="type !== 'plus'"
          @refresh="handleRefresh"
          ref="syncTab"
          :project-info="currentData"
          style="margin: 10px 0"
          :type="type"
        ></sync-table>
        <role-table
          v-if="type !== 'plus'"
          @refresh="handleRefresh"
          ref="roleTab"
          :project-info="currentData"
          :type="type"
        ></role-table>
      </div>
      <div class="footer-buttons" v-if="type === 'view'">
        <fks-button
          slot="reference"
          @click="doEdit"
          v-if="getAuth('edit')"
          :loading="buttonLoading"
          icon="fks-icon-edit"
          text
        >
          修改
        </fks-button>
        <fks-popconfirm
          v-if="getAuth('delete')" title="确认删除？" @onConfirm="handleDelete" class="m-l-10">
          <fks-button slot="reference" :loading="buttonLoading" icon="fks-icon-delete" dangerText>
            删除项目
          </fks-button>
        </fks-popconfirm>

        <fks-button
          v-if="currentData.projectStatus === 200 && getAuth('edit')"
          text
          icon="fks-icon-unlock"
          @click="openProject"
        >
          激活项目
        </fks-button>
        <fks-button
          v-if="currentData.projectStatus === 100 && getAuth('edit')"
          dangerText
          icon="fks-icon-lock"
          @click="closeProject"
        >
          关闭项目
        </fks-button>
      </div>
      <div class="footer-buttons" v-if="type === 'edit' || type === 'plus'">
        <fks-popconfirm title="确认提交？" @onConfirm="handleConfirm"
                        v-if="getAuth('edit')">
          <fks-button slot="reference" :loading="buttonLoading" icon="fks-icon-check" text>
            提交
          </fks-button>
        </fks-popconfirm>

        <fks-button
          @click="editCancel"
          slot="reference"
          :loading="buttonLoading"
          icon="fks-icon-close"
          text
          v-if="type === 'edit' && getAuth('edit')"
        >
          取消
        </fks-button>
      </div>
    </main>

    <fks-dialog :visible.sync="closeProjectDialog" :before-close="onCancel" size="small">
      <div slot="title" class="header-title">
        <span style="color: #ff4143; font-size: 16px">关闭项目</span>
      </div>

      <div style="padding: 8px 20px">
        <p>关闭项目后，本项目的车辆与司机将处于停用状态，如需同步至其他项目，可另行操作。</p>
        <span>您确认关闭本项目吗？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="onCancel">取 消</fks-button>
        <fks-button type="primary" @click="() => onConfirm('close')">确 定</fks-button>
      </span>
    </fks-dialog>
    <fks-dialog
      title="激活项目"
      :visible.sync="openProjectDialog"
      :before-close="onCancel"
      size="small"
    >
      <div style="padding: 16px 20px">
        <span>您确定激活本项目吗？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="onCancel">取 消</fks-button>
        <fks-button type="primary" @click="() => onConfirm('open')">确 定</fks-button>
      </span>
    </fks-dialog>
  </div>
</template>

<script>
import CardTag from '@components/CardFlow/components/tag.vue'
import AddForm from './AddForm.vue'
import SyncTable from './Sync.vue'
import RoleTable from './RoleTable.vue'
import dayjs from 'dayjs'
import { mapState } from 'vuex'
import {
  addProject, closeProject,
  delProject,
  getVdUserInfo, openProject,
  updateProject
} from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import EventBus from '@utils/eventBus'
import BatchAddManage from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/BatchAdd.vue'
import { getAuth } from '@utils/buttonAuth'
import * as actionTypes from '@store/Action/actionTypes'
import * as mutationTypes from '@store/Mutation/mutationTypes'

export default {
  name: 'ProjectForm',
  components: { BatchAddManage, CardTag, AddForm, SyncTable, RoleTable },
  props: {
    type: {
      type: String,
      default: 'view',
    },
    currentData: {
      type: Object,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      buttonLoading: false,
      vdUserInfo: {},
      createTime: '',
      openProjectDialog: false,
      closeProjectDialog: false,
      chooseProjectId: null,
    }
  },
  computed: {
    ...mapState(['portal']),
    userInfo() {
      return this.$storage.getObject('user')
    },
    username() {
      return this.$storage.get('username')
    },
    userFullName() {
      return this.$storage.get('userFullname')
    },
  },
  mounted() {
    this.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    getVdUserInfo(this.userInfo.id).then((res) => {
      if (res.status) {
        this.vdUserInfo = res.data || {}
      }
    })
  },
  methods: {
    getAuth,

    async onConfirm(option) {
      this.openProjectDialog = false;
      this.closeProjectDialog = false;

      try {
        let res;
        if (option === 'open') {
          res = await openProject(this.chooseProjectId);
        } else {
          res = await closeProject(this.chooseProjectId);
        }

        if (res.status) {
          this.$message.success('操作成功！');
          this.$emit('refresh', this.type);
        } else {
          this.$message.error('操作失败！');
        }
      } catch (error) {
        console.error('操作失败:', error);
        this.$message.error('操作失败，请稍后再试！');
      }
    },
    onCancel() {
      this.openProjectDialog = false
      this.closeProjectDialog = false
    },
    openProject() {
      let projectId = this.currentData.id
      this.chooseProjectId = projectId
      this.openProjectDialog = true
    },
    closeProject() {
      let projectId = this.currentData.id
      this.chooseProjectId = projectId
      this.closeProjectDialog = true
    },
    handleConfirm() {
      const formRef = this.$refs['addFormRef'].$refs.form
      formRef.validate(async (valid) => {
        if (valid) {
          this.buttonLoading = true
          let formData = this.$refs.addFormRef.formData;
          const payload = Object.assign({}, formData)
          payload.projectUseCarNature = payload.projectUseCarNature.join(",");
          let res = null
          if (payload.id != null) {
            res = await updateProject(payload)
          } else {
            res = await addProject(payload)
          }
          this.buttonLoading = false
          if (res.status) {
            if (payload.id != null) {
              this.$message.success('编辑成功')
            } else {
              let portalList = await  this.$store.dispatch(actionTypes.GET_PORTALS)
              this.$store.commit(mutationTypes.SET_PORTALS, portalList)
              this.$message.success('新增成功')
            }
            // 编辑成功后，通知CardFlow组件更新数据, 如果涉及到删除数据则传true，编辑数据则传false
            this.$emit('refresh', this.type)
          }
        }
      })
    },
    handleRefresh() {
      this.$refs.syncTab.reload()
      this.$refs.roleTab.reload()
      // this.$emit('refresh')
    },

    async handleDelete() {
      if (this.currentData.portalId) {
        EventBus.$emit('scroll', false)
        this.buttonLoading = true
        let res = await delProject(this.currentData.portalId)
        this.buttonLoading = false
        EventBus.$emit('scroll', true)
        if (res.status) {
          this.$message.success('删除成功')
          // 编辑成功后，通知CardFlow组件更新数据, 如果涉及到删除数据则传true，编辑数据则传false
          this.$emit('refresh', 'delete')
        }
      }
    },
    doEdit() {
      this.$refs.addFormRef.doEdit()
      this.$emit('update:type', 'edit')
    },
    editCancel() {
      this.$refs.addFormRef.editCancel()
      this.$emit('update:type', 'view')
    },
  },
  watch: {
    currentData: {
      immediate: true,
      handler(newVal) {
        this.$nextTick(() => {
          this.$refs.addFormRef.setFormData(newVal)
          // 同步组件和用户组件重载一下数据
          if (this.type !== 'plus') {
            this.$refs.syncTab.reload()
            this.$refs.roleTab.reload()
          }
          // this.$refs.addFormRef.initProjectSign() //自动生成项目标识
        })
      },
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@/styles/headers';
@import '~@/styles/button';
</style>
