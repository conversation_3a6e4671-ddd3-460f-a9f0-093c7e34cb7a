<template>
  <fks-form
    ref="form"
    :model="formData"
    :rules="rules"
    class="form flex-grow-1 newTheme"
    label-position="left"
    label-width="210px"
  >
    <common-title class="m-b-36" title="项目信息"/>
    <fks-form-item
      :rules="[{ required: true, message: '请输入项目名称'}]" label="项目名称"
      prop="projectName"
      required
    >
      <fks-input
        v-model="formData.projectName"
        :disabled="flowConfig.projectName === 'readonly'"
        :readonly="flowConfig.projectName === 'readonly'"
        placeholder="请输入项目名称"
      />
    </fks-form-item>
    <fks-form-item
      :rules="[{ required: true, message: '请输入项目简称'}]" label="项目简称"
      prop="projectNameAbbreviation"
      required
    >
      <fks-input
        v-model="formData.projectNameAbbreviation"
        :disabled="flowConfig.projectNameAbbreviation === 'readonly'"
        :readonly="flowConfig.projectNameAbbreviation === 'readonly'"
        placeholder="请输入项目简称"
      />
    </fks-form-item>
    <fks-form-item  label="项目编码"
                    prop="projectSign"
    >
      <fks-input
        v-model="formData.projectSign"
        disabled
        placeholder="系统自动生成"
        :readonly="flowConfig.projectSign === 'readonly' "
      />
    </fks-form-item>

    <fks-form-item  label="所在地区"
                    prop="projectProvince"
    >
      <fks-select
        v-model="formData.projectProvince"
        filterable
        placeholder="请选择所在地区"
        class="full-width"
        :disabled="type === 'view'"
      >
        <fks-option
          v-for="item in provinces"
          :key="item.id"
          :label="item.name"
          :value="item.name"
        />
      </fks-select>
    </fks-form-item>
    <fks-form-item  label="项目所属二级单位"
                    prop="projectManageDeptName"
    >
      <feishu-department-select
        :initial-value="formData.projectManageDeptName"
        :trigger-on-focus="true"
        :level="level"
        :disabled="type === 'view'"
        placeholder="请输入二级单位名称"
        @dept-change="handleDept2Change"
        @value-change="formData.projectManageDeptName = $event"
        @input="level = 2"
        @focus="level = 3"
      />
    </fks-form-item>
    <fks-form-item label="项目所属三级部门" prop="projectManageDept2Name">
      <feishu-department-selector
        :initialValue="formData.projectManageDept2Name"
        :level="3"
        :disabled="type === 'view'"
        :deptId="formData.projectManageDeptId"
        :triggerOnFocus="Boolean(formData.projectManageDeptId)"
        placeholder="请输入三级部门名称"
        @dept-change="formData.projectManageDept2Id = $event"
        @value-change="formData.projectManageDept2Name = $event"
      />
    </fks-form-item>
    <fks-form-item label="项目用车性质" prop="projectUseCarNature">
      <fks-select v-model="formData.projectUseCarNature" filterable :disabled="type === 'view'"
                  class="full-width" placeholder="请选择项目用车性质" multiple>
        <fks-option v-for="option in enums.UseCarNatureEnums" :key="option.key" :label="option.value" :value="option.key" />
      </fks-select>
    </fks-form-item>
    <common-title class="m-b-36" title="填写信息"/>
    <fks-form-item label="填写人">
      <fks-input :value="userFullName" disabled readonly/>
    </fks-form-item>
    <fks-form-item label="填写时间">
      <fks-input :value="createTime" disabled readonly/>
    </fks-form-item>
    <fks-form-item label="二级单位">
      <fks-input :value="depart" disabled readonly/>
    </fks-form-item>
    <fks-form-item label="三级部门">
      <fks-input :value="depart2" disabled readonly/>
    </fks-form-item>
  </fks-form>
</template>

<script>
import FormUpload from "@modules/FormCenter/components/FormUpload/index.vue";
import FeishuDepartmentSelect from "@/modules/ProjectCar/CompanyPortal/UserManagement/components/feishu-department-selector.vue";
import validator from '@/mixins/validator.js';
import {getCarList} from "@modules/ProjectCar/ProjectPortal/CarManage/api";
import {saveDraftDebounced, deleteDraft, getDraft} from "@utils/draftUtil"
import {mapState} from "vuex";
import dayjs from 'dayjs'
import {
  getProvince,
  getVdUserInfo,
  projectGenerateSign
} from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import FeishuDepartmentSelector
  from "@modules/ProjectCar/CompanyPortal/UserManagement/components/feishu-department-selector.vue";

export default {
  name: "AddForm",
  mixins: [validator],
  components: {FeishuDepartmentSelector, FormUpload, FeishuDepartmentSelect},
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    },
    projectInfo: {
      type: Object,
      default: null,
    }
  },
  data() {
    return {
      level :3,
      carInfo: {}, // 选择的车辆信息
      loadingCarNum: false,
      carNumList: [], // 车辆列表
      formData: {
        projectName: '',
        projectSign: '',
        projectProvince: '',
        status: 1
      },
      backFromData: {},
      vdUserInfo: {},
      deptId: ''
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['provinces']),
    span() {
      return this.layoutSingleLine ? 24 : 12;
    },
    isEdit() {
      return this.type === 'edit';
    },
    isView() {
      return this.type === 'view';
    },
    isAdd() {
      return this.type === 'plus';
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    username() {
      return this.$storage.get('username')
    },
    projectCarType() {
      return this.enums.ProjectUseCarTypeEnums
    },
    userFullName() {
      if (this.type === 'view') {
        return this.projectInfo.createUser
      }
      return this.$storage.get('userFullname')
    },
    createTime() {
      if (this.type === 'plus') {
        return dayjs().format('YYYY-MM-DD HH:mm:ss')
      } else {
        return dayjs(this.projectInfo.updateDate).format('YYYY-MM-DD HH:mm:ss');
      }
    },
    depart() {
      if (this.type === 'view') {
        return this.projectInfo.userDepName
      }
      return this.vdUserInfo.userDepName
    },
    depart2() {
      if (this.type === 'view') {
        return this.projectInfo.userDepName2
      }
      return this.vdUserInfo.userDepName2
    },
    flowConfig() {
      if (this.type === 'view') {
        return Object.keys(this.formData).reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      return {}
    },
    rules() {
      const that = this;
      return {
        projectName: [
          {required: true, message: '请输入项目信息', trigger: 'change'}
        ],
        projectNameAbbreviation: [
          {required: true, message: '请输入项目简称', trigger: 'change'}
        ],
        projectProvince: [
          {required: true, validator(rule, value, callback) {
              if (value) {
                callback();
              } else {
                callback(new Error('请选择所在地区'));
              }
            }, trigger: 'change'}
        ],
        projectManageDeptName: [
          {required: true, validator(rule, value, callback) {
              if (value) {
                callback();
              } else {
                callback(new Error('请选择项目所属二级单位'));
              }
            }, trigger: 'change'}
        ],
        projectManageDept2Name: [
          {required: true, validator(rule, value, callback) {
              if (that.formData.projectManageDept2Name) {
                callback();
              } else {
                callback(new Error('请选择项目所属三级部门'));
              }
            }, trigger: 'change'}
        ],
        projectUseCarNature: [
          {required: true, validator(rule, value, callback) {
              if (value && value.length > 0) {
                callback();
              } else {
                callback(new Error('请选择请选择项目用车性质'));
              }
            }, trigger: 'change'}
        ],
        // projectSign: [
        //   {
        //     required: true,
        //     trigger: 'change',
        //     validator(rule, val, callback) {
        //       const reg = /^[a-zA-Z][a-zA-Z_0-9]*$/
        //       if (val) {
        //         if (reg.test(val)) {
        //           callback()
        //         } else {
        //           callback(new Error('格式错误：只能包含字母，数字和下划线'))
        //         }
        //       } else {
        //         callback(new Error('请输入项目标识'))
        //       }
        //     }
        //   }
        // ]
      }
    },
  },
  mounted() {
    getVdUserInfo(this.userInfo.id).then(res => {
      if (res.status) {
        this.vdUserInfo = res.data || {};
      }
    })
  },
  methods: {
    handleDept2Change(payload) {
      if (this.formData.projectManageDeptId && (this.formData.projectManageDeptId !== payload)) {
        // 清空三级部门
        this.formData.projectManageDept2Id = '';
        this.formData.projectManageDept2Name = '';
      }
      this.$set(this.formData, 'projectManageDeptId', payload);
    },
    setFormData(data) {
      if (data.projectUseCarNature) {
        data.projectUseCarNature = data.projectUseCarNature.split(",").map(item => Number(item));
      }
      this.formData = JSON.parse(JSON.stringify(data))
    },
    initProjectSign() {
      this.projectBlur(this.formData)
    },
    projectBlur(item) {
      if (this.type === 'plus') {
        let name = item.projectName ? [item.projectName] : ["空名字"];
        if (!item.projectSign) {
          projectGenerateSign(name).then((res) => {
            let list = res.data;
            if (list.length > 0) {
              this.$set(item, 'projectSign', list[0]);
            }
          })
        }
      }
    },
    doEdit() {
      this.backFromData = JSON.parse(JSON.stringify(this.formData));
    },
    editCancel() {
      this.formData = JSON.parse(JSON.stringify(this.backFromData));
    }
  }
}
</script>

<style scoped lang="less">
@import "~@/styles/disabled";
@import "~@/styles/input";
</style>
