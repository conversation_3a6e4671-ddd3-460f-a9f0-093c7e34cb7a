<template>
  <div>
    <TitleKit>
      <template #left>
        <div style="font-size: 16px; font-weight: 600">项目列表</div>
      </template>
    </TitleKit>
    <TempTable
      :data-list="tableData"
      :table-config.sync="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      @add-item="handleAdd"
      @delete-item="handleDelete"
      @batch-add="handleBatchAdd"
      @export-data="handleExport"
      @searchData="searchData"
      @clickRow="handleClickRow"
      :showButtons="showButtons"
      :current-page.sync="pageNo"
    >
      <template v-slot:column-projectStatus="{ scope }">
        <card-tag v-if="scope.row.projectStatus"  class="p-24 p-t-8 p-b-8" :tag="{color: getTagType(scope.row.projectStatus), text: getTagText(scope.row.projectStatus, 'projectStatus')}" />
      </template>

      <template v-slot:column-projectUseCarNature="{scope}">
        {{ getUseCarNature(scope) }}
      </template>

      <template v-slot:column-driverStatus="{scope}">
        <card-tag v-if="scope.row.driverStatus"
                  class="p-24 p-t-8 p-b-8"
                  :tag="{
                    color: getTagType(scope.row.driverStatus, 'driverStatus'),
                    text: getTagText(scope.row.driverStatus, 'driverStatus')
        }" />
      </template>
      <template v-slot:column-option="{ scope }">
        <fks-button
          v-if="scope.row.projectStatus === 200 && getAuth('edit')"
          :loading="loading"
          text
          @click="openProject(scope.row)"
        >
          激活项目
        </fks-button>
        <fks-button
          v-if="scope.row.projectStatus === 100 && getAuth('edit')"
          :loading="loading"
          dangerText
          @click="closeProject(scope.row)"
        >
          关闭项目
        </fks-button>
      </template>
    </TempTable>

    <fks-drawer
      :before-close="beforeClose"
      :visible.sync="visible"
      :wrapperClosable="true"
      :modal="false"
      class="project-drawer"
      direction="rtl"
      @open="onDrawerOpen"
    >
      <template slot="title" v-if="type === 'plus'">
        <div class="dialog-title flex col-center">
          <i class="fks-icon-plus m-r-10" />
          <span>新增项目信息</span>
        </div>
      </template>
      <header class="card-header" slot="title" v-else>
        <div class="flex flex-column col-baseline m-b-24">
          <div class="card-title" style="width: 99%; color: #333333" v-if="visible && currentData.projectName">
            {{ currentData.projectName }}
          </div>
          <div
            v-if="currentData && currentData.dispatcher && currentData.dispatcher.length"
            class="card-desc flex col-center mt-2"
          >
            <span style="color: #333333">车辆调度员</span>
            <fks-divider direction="vertical" />
            <div class="flex col-center">
              <card-tag
                v-for="(tag, index) in currentData.dispatcher"
                :key="index + tag.userFullname"
                :tag="{ color: '#3C83FF', text: tag.userFullname }"
                :class="{ 'm-l-10': index > 0 }"
              />
            </div>
          </div>
        </div>
      </header>
      <ProjectForm
        @refresh="reSearchData"
        :current-data="currentData"
        :type.sync="type"
        :show-header="false"
        ref="ProjectForm"
      />
    </fks-drawer>
    <BatchAddManage ref="batchAdd" @updateTable="reSearchData" @openDialog="openDialog"/>
    <fks-dialog :visible.sync="closeProjectDialog" :before-close="onCancel" size="small">
      <div slot="title" class="header-title">
        <span style="color: #ff4143; font-size: 16px">关闭项目</span>
      </div>

      <div style="padding: 8px 20px">
        <p>关闭项目后，本项目的车辆与司机将处于停用状态，如需同步至其他项目，可另行操作。</p>
        <span>您确认关闭本项目吗？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="onCancel">取 消</fks-button>
        <fks-button type="primary" @click="() => onConfirm('close')">确 定</fks-button>
      </span>
    </fks-dialog>
    <fks-dialog
      title="激活项目"
      :visible.sync="openProjectDialog"
      :before-close="onCancel"
      size="small"
    >
      <div style="padding: 16px 20px">
        <span>您确定激活本项目吗？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="onCancel">取 消</fks-button>
        <fks-button type="primary" @click="() => onConfirm('open')">确 定</fks-button>
      </span>
    </fks-dialog>

    <fks-dialog
      title="关闭"
      :visible.sync="tipVisible"
      size="small"
    >
      <div style="padding: 16px 20px">
        <span>表单内容发生改变是否继续关闭?</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="handelCancle">取 消</fks-button>
        <fks-button type="primary" @click="onConfirmTip">确 定</fks-button>
      </span>
    </fks-dialog>
  </div>
</template>

<script>
import TempTable from '@components/CustomerTable/CustomerTable.vue'

import { getAuth } from '@utils/buttonAuth'
import { mapState, mapActions, mapGetters } from 'vuex'
import * as ActionTypes from "@store/Action/actionTypes";
import ProjectForm from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/ProjectForm.vue'
import BatchAddManage from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/BatchAdd.vue'
import {
  batchDelProject,
  closeProject,
  delProject, exportProjectInfo,
  getProjectCarDispatchers,
  getProjectList,
  getProjectListByPost,
  getProjectSearchParam,
  getProvince,
  openProject
} from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import { getUserListByName } from '@modules/ProjectCar/CompanyPortal/UserManagement/api'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import { mapMutations } from 'vuex'
import CardTag from '@components/CardFlow/components/tag.vue'
import { exportDriverInfo } from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import { fileReader } from '@utils/exportFile'
import * as StateTypes from "@store/State/stateTypes";
import globalSearchMixin from "@/mixins/globalSearchMixin";
import * as actionTypes from "@store/Action/actionTypes";
import * as GetterTypes from '@store/Getter/getterTypes'

export default {
  name: 'ProjectConfig',
  mixins: [globalSearchMixin],
  components: {
    CardTag,
    TitleKit,
    BatchAddManage,
    ProjectForm,
    TempTable,
  },
  data() {
    return {
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['add', 'batchAdd', 'delete', 'export', 'filter', 'filedConfig'],
      queryParams: {
        conditions: [],
      },
      visible: false,
      tableData: [],
      currentData: {},
      type: 'view',
      selectedFields: [],
      tableConfig: [
        // {
        //   label: '用车类型',
        //   prop: 'projectUseCarType',
        //   group: '项目信息',
        // },
        {
          label: '项目名称',
          prop: 'projectName',
          group: '项目信息',
          width: 250,
          clickable: true,
        },
        {
          label: '项目简称',
          prop: 'projectNameAbbreviation',
          group: '项目信息',
          width: 120,
        },
        // {
        //   label: '项目标识',
        //   prop: 'projectSign',
        //   group: '项目信息',
        // },
        {
          label: '所在地区',
          prop: 'projectProvince',
          group: '项目信息',
          width: 120,
        },
        {
          label: '项目所属二级单位',
          prop: 'projectManageDeptName',
          group: '项目信息',
          width: 200,
        },
        {
          label: '车辆数',
          prop: 'carNums',
          group: '车辆信息',
          width: 80,
        },
        {
          label: '车辆调度员',
          prop: 'carDispatcher',
          group: '人员信息',
          width: 350,
        },
        {
          label: '项目状态',
          prop: 'projectStatus',
          group: '项目信息',
          width: 100,
          customer: true,
        },
        {
          label: '用车性质',
          prop: 'projectUseCarNature',
          group: '项目信息',
          width: 280,
          customer: true,
        },
        {
          label: '操作',
          prop: 'option',
          group: '系统操作',
          fixed: 'right',
          width: 120,
          customer: true,
        },
      ],

      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      openProjectDialog: false,
      closeProjectDialog: false,
      chooseProjectId: null,
      tipVisible: false,
      type1: 'add'
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    projectCarType() {
      return this.enums.ProjectUseCarTypeEnums
    },
    projectStatus() {
      return this.enums.ProjectStatusEnums
    },
  },
  created() {
    this.selectedFields = this.tableConfig.map((item) => item.prop)
    this.tableConfig.forEach((item) => {
      if (item.prop === 'projectStatus') {
        item.enums = this.projectStatus
      }
    })
  },
  mounted() {
    getProvince().then((res) => {
      if (res.status) {
        this.SET_PROVINCES(res.data)
      }
    })
    this.getData(this.queryParams)
    getProjectSearchParam().then((res) => {
      this.searchConfigs = res.data
    })
  },
  methods: {
    ...mapMutations(['SET_PROVINCES']),
    ...mapActions([ActionTypes.GET_AUTH_PORTAL]),
    getAuth,
    getUseCarNature(scope) {
      const enums = JSON.parse(localStorage.getItem('enums'));
      let nature = scope.row.projectUseCarNature
      if (enums && enums.UseCarNatureEnums && nature) {
        let result = [];
        let list = enums.UseCarNatureEnums;
        let natureList = nature.split(',');
        natureList.forEach(n => {
          // 去除空格，确保匹配准确
          const key = n.trim();
          let item = list.find(item => item.key == key);
          if (item) {
            result.push(item.value); // 如果需要其它字段，比如 label，可以替换为 item.label
          }
        });
        return result.join(', ');
      }
      return '';
    },
    openDialog() {
      this.tipVisible = true
      this.type1 = 'batchAdd'
    },
    globalSearch() {
      this.tableData = [];
      this.reSearchData();
    },
    onSuccess() {
      this.$message.success('操作成功！')
      this.reSearchData(true)
      // 更新门户状态
      this.$store.dispatch(ActionTypes.GET_AUTH_PORTAL)
    },
    onConfirm(option) {
      this.openProjectDialog = false
      this.closeProjectDialog = false
      if (option === 'open') {
        openProject(this.chooseProjectId).then((res) => {
          if (res.status) {
            this.onSuccess();
          }
        })
      } else {
        closeProject(this.chooseProjectId).then((res) => {
          if (res.status) {
            this.onSuccess();
          }
        })
      }
    },
    onCancel() {
      this.openProjectDialog = false
      this.closeProjectDialog = false
    },
    openProject(row) {
      let projectId = row.id
      this.chooseProjectId = projectId
      this.openProjectDialog = true
    },
    closeProject(row) {
      let projectId = row.id
      this.chooseProjectId = projectId
      this.closeProjectDialog = true
    },
    reSearchData(refresh) {
      this.visible = false // 关闭
      if (!refresh || refresh == false) {
        this.pageNo = 1
      }
      this.getData(this.queryParams)
    },
    async handleDelete(rows) {
      if (rows && rows.length > 0) {
        let ids = rows.map((item) => item.portalId).join(',')
        let res = await batchDelProject(ids)
        if (res.status) {
          this.$message.success('删除成功')
          this.reSearchData(true)
        }
      }
    },
    handleExport(list) {
      list = list.filter(item => {
        let configItem = this.tableConfig.find(config => config.prop === item.field);
        return item.fieldName !== '操作';
      })
      let data = {
        pageGetParam: {...this.queryParams},
        fields: list
      }
      exportProjectInfo(data).then(res => {
        try {
          fileReader(res)
        } catch (error) {
          console.error("导出失败:", error);
          alert("导出失败，请稍后重试。");
        }
      })
    },
    handleBatchAdd() {
      this.$refs.batchAdd.open()
    },
    handleAdd() {
      this.type = 'plus'
      this.currentData = {}
      this.visible = true
    },
    handleClickRow(row, prop) {
      this.type = 'view'
      this.currentData = { ...row }
      this.visible = true
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    async getData(query) {
      this.loading = true
      this.tableData = [];
      this.queryParams = { ...this.queryParams, ...query }
      try {
        const params = {
          ...this.queryParams,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
        }
        const { data } = await getProjectListByPost(params, this[GetterTypes.GET_GLOBAL_STATE])
        this.total = data.total
        this.finished = data.isLastPage
        const newData = await this.addProjectDispatchersToTable(data.list)
        if (newData.length > 0) {
          // 查询并填充用户信息
          await this.searchUserInfo(newData)
        }
        let list = [...newData]
        this.$set(this.$data, 'tableData', list)
        this.loading = false
      } catch (e) {
        console.log('异常', e)
        this.loading = false
      }
    },
    async addProjectDispatchersToTable(tableData) {
      const ids = tableData.map((item) => item.portalId).join(',')
      const { data: dispatchers } = await getProjectCarDispatchers({ ids })
      return tableData.map((item) => {
        return { ...item, dispatcher: dispatchers[item.portalId] }
      })
    },

    async searchUserInfo(list) {
      // 根据updateBy去重
      let uniqueUpdateBySet = new Set(list.map((item) => item.updateBy))
      let res = await getUserListByName([...uniqueUpdateBySet].join(','))
      const userMap = res.data.reduce((map, item) => {
        map[item.userName] = item
        return map
      }, {})

      list.forEach((item) => {
        if (userMap[item.updateBy]) {
          item.createUser = userMap[item.updateBy].userFullName
          item.userDepName = userMap[item.updateBy].userDepName
          item.userDepName2 = userMap[item.updateBy].userDepName2
        } else {
          item.createUser = item.createBy
        }
      })
    },
    getTagText(value, prop) {
      if (prop === 'projectStatus') {
        return this.projectStatus.find((item) => item.key == value).value
      }
      return value
    },
    getTagType(value) {
      let status = {
        100: '#40bb5a',
        200: '#ff4d4f',
        // 100: 'success',
        // 200: 'danger',
      }
      return status[value]
    },
    formatDate(row, column) {
      if (!value) return '' // 如果值为空，返回空字符串
      const date = new Date(value)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}` // 返回格式化后的日期字符串 "YYYY-MM-DD"
    },

    onDrawerOpen() {},
    handelCancle() {
      this.tipVisible = false
      this.type1 = 'add'
    },
    onConfirmTip() {
      this.visible = false
      // 批量新增抽屉关闭
      this.$refs.batchAdd.visible = false
      this.tipVisible = false
      if (this.type1 === 'batchAdd') {
        this.$refs.batchAdd.cleanData()
      }
    },
    beforeClose(done) {
      if (this.type === 'plus') {
        const formData = this.$refs.ProjectForm.$refs.addFormRef.formData
        if (Object.values(formData).some(item => item)) {
          this.tipVisible = true
        } else {
          this.visible = false
          done()
        }
      }
      if (this.type === 'view') {
        this.visible = false
        done()
      }

      if (this.type === 'edit') {
        const formData = this.$refs.ProjectForm.$refs.addFormRef.formData
        const backFromData = this.$refs.ProjectForm.$refs.addFormRef.backFromData
        let flag = this.isEqual(formData,backFromData)
        if (flag) {
          this.visible = false
          done()
        } else {
          this.tipVisible = true
        }
      }
    },
    isEqual(data1,data2) {
      let flag = true
      for (let key in data1) {
        if (key === 'dispatcher') continue
        if (key in data2 && data2[key] === data1[key]) continue
        flag = false
      }
      return flag
    },
    clear() {
      this.visible = false
      this.currentData = {}
      this.pageNo = 1
      this.getData(this.queryParams)
    },
  },
}
</script>

<style scoped lang="less">
@import '~@/styles/headers';
@import '~@/styles/button';
@import "./exclude/projectConfigTable.css";

.dialog-title {
  font-size: 32px;
}

.sub-btn {
  padding: 5px 10px;
  transition: all 0.3s ease; /* 平滑过渡效果 */
}
</style>
