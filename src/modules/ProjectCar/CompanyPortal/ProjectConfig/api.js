import request from '@/utils/request'

// 新增项目
export function addProject(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/add',
    method: 'post',
    data: {
      vdProject: data
    }
  })
}

export function batchAddProject(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/batch/add',
    method: 'post',
    data: data
  })
}

// 获取项目列表
export function getProjectList(params) {
  return request({
    url: '/vehicle-dispatch/vd/project/page',
    params
  })
}

// 获取项目列表
export function getProjectSearchParam(params) {
  return request({
    url: '/vehicle-dispatch/vd/project/page/getParam',
    params
  })
}

// 车辆列表
export function getProjectListByPost(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/project/pageV2',
    method: 'post',
    data,
    params
  })
}

// 导出项目列表
export function exportProjectInfo(params) {
  return request({
    url: '/vehicle-dispatch/vd/project/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}


// 生成项目标识
export function projectGenerateSign(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/generate/sign',
    method: 'post',
    data: data
  })
}

// 修改项目
export function updateProject(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/update',
    method: 'post',
    data: {
      vdProject: data
    }
  })
}

// 修改项目
export function delProject(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/del',
    method: 'post',
    params: {
      projectId: data
    }
  })
}


// 批量删除
export function batchDelProject(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/multiDel',
    method: 'get',
    params: {
      ids: data
    }
  })
}

export function closeProject(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/close',
    method: 'post',
    params: {
      id: data
    }
  })
}

export function openProject(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/open',
    method: 'post',
    params: {
      id: data
    }
  })
}

// 获取项目车辆调度员
export function getProjectCarDispatchers(params) {
  return request({
    url: '/vehicle-dispatch/vd/project/dispatcher',
    params
  })
}

// 获取用户信息
export function getVdUserInfo(id) {
  return request({
    url: '/vehicle-dispatch/user/info',
    params: {id}
  })
}

// 为用户授权角色
export function authUserToProject(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/batch/assign/admin',
    method: 'post',
    data: data
  })
}


// 同步项目信息
export function syncProject(data) {
  return request({
    url: '/vehicle-dispatch/vd/project/carDriver/sync',
    method: 'post',
    data
  })
}


// 获取项目列表
export function getUserList(params) {
  return request({
    url: '/sys-user/users/page',
    method: 'GET',
    params: params
  })
}

// 获取项目列表
export function getUserListByIds(params) {
  return request({
    url: '/sys-user/users',
    method: 'GET',
    params: params
  })
}
// 获取项目列表
export function getUserListByUserIds(params) {
  return request({
    url: '/vehicle-dispatch/user/list',
    method: 'GET',
    params
  })
}


// 同步项目信息
export function setRoleUser(data) {
  return request({
    url: '/vehicle-dispatch//user/role/put',
    method: 'post',
    data
  })
}

// 同步项目信息
export function delRoleUser(data) {
  return request({
    url: '/vehicle-dispatch//user/role/del',
    method: 'post',
    data
  })
}

// 获取省份
export function getProvince() {
  return request({
    url: '/sys-system/region/sub',
    method: 'get'
  })
}
