<template>
  <CardFlow
    :cards="cards"
    :loading="loading"
    :finished="finished"
    left-description="暂无项目"
    @indexChange="handleIndexChange"
    @loadMore="loadMore"
    @searchChange="searchData"
    ref="cardFlowRef"
  >
    <template slot="card-header">
      <div class="flex flex-end">
        <BatchAddManage ref="batchAddProject" @updateTable="clear" />
        <fks-button style="color: #333" text icon="fks-icon-circle-plus-outline" @click="handleBatchAdd">批量新增</fks-button>
        <fks-button style="color: #333" text icon="fks-icon-circle-plus-outline" @click="handleAdd">新增</fks-button>
      </div>
    </template>
    <template v-slot:card="{card}">
      <showcase v-bind="card"/>
    </template>
    <template slot="panel">
      <div class="flex flex-column full-height">
        <ProjectForm
          @refresh="handleRefresh"
          :current-data="currentData"
          :type.sync="type"
        />
      </div>
    </template>
  </CardFlow>
</template>

<script>
import {
  addProject,
  getProjectCarDispatchers,
  getProjectList,
  getVdUserInfo,
  updateProject
} from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import {
  getUserList,
  getUserListByName
} from '@modules/ProjectCar/CompanyPortal/UserManagement/api'
import CompactedSearchBar from "@components/CompactedSearchBar/index.vue";
import dayjs from "dayjs";
import * as types from "@store/Action/actionTypes";
import * as mutationTypes from "@store/Mutation/mutationTypes";
import {mapActions, mapMutations, mapState} from "vuex";
import CardFlow from "@components/CardFlow/index.vue";
import Showcase from "@components/CardFlow/components/showcase.vue";
import CardTag from "@components/CardFlow/components/tag.vue";
import BatchAddManage from "@modules/ProjectCar/CompanyPortal/ProjectConfig/components/BatchAdd.vue";
import ProjectForm from "@modules/ProjectCar/CompanyPortal/ProjectConfig/components/ProjectForm.vue";
import * as StateTypes from "@store/State/stateTypes";
import {sleep} from "@utils/util";

export default {
  name: "ProjectConfig",
  components: {CardTag, Showcase, CardFlow, CompactedSearchBar, BatchAddManage, ProjectForm},
  data() {
    return {
      finished: false,
      currentData: null,
      // 分割线 ----------
      tableData: [],
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      exportLoading: false,
      buttonLoading: false,
      userList: [],
      userMap: [],
      type: 'edit',
      queryForm: {
        searchValue: ''
      },
      formData: {
        projectName: '',
        projectSign: '',
        status: 1
      },
      vdUserInfo: {},
      params: {},
      createTime: null,
      rules: {
        projectName: [
          {required: true, message: '请输入项目信息', trigger: 'change'}
        ],
        projectSign: [
          {
            required: true,
            trigger: 'change',
            validator(rule, val, callback) {
              const reg = /^[a-zA-Z][a-zA-Z_0-9]*$/
              if (val) {
                if (reg.test(val)) {
                  callback()
                } else {
                  callback(new Error('格式错误：只能包含字母，数字和下划线'))
                }
              } else {
                callback(new Error('请输入项目编码'))
              }
            }
          }
        ]
      }
    }
  },
  computed: {
    ...mapState([StateTypes.GLOBAL_TABLE_DATA, StateTypes.TABLE_LOADING, StateTypes.GLOBAL_QUERY_PARAMS]),
    cards() {
      return this.tableData.map(item => {
        return {
          ...item,
          title: item.projectName,
          descriptions: [
            {label: '填写人', value: item.createUser},
            {label: '二级单位', value: item.userDepName},
            {label: '三级部门', value: item.userDepName2},
            {label: '填写时间', value: this.$dayjs(item.updateDate).format('YYYY-MM-DD HH:mm:ss')},
          ]
        }
      })
    },
    flowConfig() {
      if (this.type === 'view') {
        return Object.keys(this.formData).reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      return {}
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    username() {
      return this.$storage.get('username')
    },
    userFullName() {
      return this.$storage.get('userFullname')
    },
    title() {
      switch (this.type) {
        case 'plus':
          return '新增'
        case 'edit':
          return '编辑'
        case 'view':
          return '查看'
      }
    }
  },
  methods: {
    ...mapActions([types.GET_PORTALS]),
    ...mapMutations([mutationTypes.SET_PORTALS]),
    handleIndexChange(item) {
      this.$refs.cardFlowRef.disableContentLoading();
      this.type = 'view';
      if (item) {
        const {projectName, projectSign, createDate} = item;
        this.currentData = item;
        this.formData = {
          projectName,
          projectSign
        }
        this.createTime = this.$dayjs(createDate).format('YYYY-MM-DD HH:mm')
      } else {
        this.$refs.cardFlowRef.resetActiveIndex(null);
        this.currentData = null;
      }
    },
    loadMore(value) {
      this.pageNo += 1
      this.query(this.pageNo, value)
    },
    searchData(value) {
      this.pageNo = 1;
      this.tableData = [];
      this.query(1, value)
    },
    open() {
      this.$refs.formRef.clearValidate();
    },
    async fetchData(pageNo, type) {
      const {data} = await getProjectList({
        ...this.queryForm,
        pageNo: pageNo + 1,
        pageSize: 1,
      })
      const {list} = data || [];
      if (list.length) {
        const newData = await this.addProjectDispatchersToTable(list);
        const item = newData[0];
        if (type === 'edit') {
          // 替换
          this.tableData.splice(pageNo, 1, item)
        } else {
          // 新增
          this.tableData.unshift(item)
        }
      } else {
        // 没数据就直接展示空数组
        this.tableData = [];
      }
    },
    async handleRefresh(type) {
      // 当新增或删除时，门户列表需要更新
      if ((this.type === 'plus') || type === 'delete') {
        let portalList = await this[types.GET_PORTALS]()
        if (portalList.length > 0) {
          this[mutationTypes.SET_PORTALS](portalList)
        }
      }
      const activeIndex = type === 'plus' ? 0 : this.$refs.cardFlowRef.activeIndex;
      if (type === 'delete') {
        this.tableData.splice(activeIndex, 1);
        const index = activeIndex <= this.cards.length - 1 ? activeIndex : 0;
        const item = this.cards[index];
        if (item) {
          this.$refs.cardFlowRef.resetActiveIndex(index);
        } else {
          this.currentData = null;
          this.$refs.cardFlowRef.resetActiveIndex(null);
        }
      } else {
        this.fetchData(activeIndex, type).then(() => {
          const index = activeIndex <= this.cards.length - 1 ? activeIndex : 0;
          const item = this.cards[index];
          if (item) {
            this.$refs.cardFlowRef.resetActiveIndex(index);
          } else {
            this.currentData = null;
            this.$refs.cardFlowRef.resetActiveIndex(null);
          }
        });
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          const fn = this.type === 'plus' ? addProject : updateProject;
          fn(this.formData).then(async (res) => {
            if (res.status) {
              this.$message.success(this.type === 'plus' ? '新增成功' : '编辑成功')
              this.tableData = [];
              await this.query(1);
              if (this.type === 'plus') {
                let portalList = await this[types.GET_PORTALS]()
                if (portalList.length > 0) {
                  this[mutationTypes.SET_PORTALS](portalList)
                }
              }
            }
          }).finally(() => {
            this.buttonLoading = false
          })
        }
      })
    },
    beforeClose(done) {
      this.$refs.formRef.resetFields();
      this.$refs.formRef.clearValidate();
      this.formData = {
        projectName: '',
        projectSign: '',
        status: 1
      }
      done()
    },
    handleAdd() {
      this.type = 'plus';
      this.$refs.cardFlowRef.resetActiveIndex(-1);
      const initFormData = {
        projectName: '',
        projectSign: '',
        status: 1
      }
      this.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      this.currentData = initFormData;
      this.formData = JSON.parse(JSON.stringify(initFormData))
    },
    handleBatchAdd() {
      this.$refs.batchAddProject.open({});
    },
    clear() {
      for (let i in this.queryForm) {
        this.queryForm[i] = '';
      }
      this.pageNo = 1;
      this.tableData = []
      this.query();
    },
    async addProjectDispatchersToTable(tableData) {
      const ids = tableData.map(item => item.portalId).join(',');
      const {data: dispatchers} = await getProjectCarDispatchers({ids})
      return tableData.map(item => {
        return {...item, dispatcher: dispatchers[item.portalId]}
      })
    },
    async query(pageNo, value) {
      if (!pageNo || pageNo < 2) {
        this.loading = true;
      }
      const params = {
        ...this.queryForm,
        pageNo: pageNo || this.pageNo,
        pageSize: this.pageSize,
        searchValue: value,
      }
      const {data} = await getProjectList(params)
      this.total = data.total;
      this.finished = data.isLastPage;
      const newData = await this.addProjectDispatchersToTable(data.list)
      // 查询并填充用户信息
      await this.searchUserInfo(newData);
      let list = [...this.tableData, ...newData];
      this.$set(this.$data, "tableData", list);
      this.loading = false;
    },
    async searchUserInfo(list) {
      // 根据updateBy去重
      let uniqueUpdateBySet = new Set(list.map(item => item.updateBy));
      let res = await getUserListByName([...uniqueUpdateBySet].join(","));
      const userMap = res.data.reduce((map, item) => {
        map[item.userName] = item;
        return map;
      }, {});

      list.forEach(item => {
        if (userMap[item.updateBy]) {
          item.createUser = userMap[item.updateBy].userFullName
          item.userDepName = userMap[item.updateBy].userDepName
          item.userDepName2 = userMap[item.updateBy].userDepName2
        } else {
          item.createUser = item.createBy
        }
      })
    }
  },
  created() {
    this.query()
  },
  mounted() {
    getVdUserInfo(this.userInfo.id).then(res => {
      if (res.status) {
        this.vdUserInfo = res.data || {};
      }
    })
  },
  watch: {
    [StateTypes.GLOBAL_TABLE_DATA]: {
      async handler(newVal) {
        this.pageNo = 1; // 手动重置index
        this.finished = newVal.isLastPage;
        this.tableData = newVal.list;
        this.$refs.cardFlowRef.resetActiveIndex(null);
      }
    }
  },
}
</script>

<style lang="less" scoped>
@import "~@/styles/headers";
@import "~@/modules/FormCenter/CarApply/index.less";
@import "~@/styles/disabled";
@import "~@/styles/input";

footer {
  padding-top: 8px;
  margin-top: 12px;
  border-top: 1px solid #F1F1F0;
}

.form {
  max-width: 1400px;
  margin: 0;
}

.dialog-title {
  font-size: 32px;
}


/deep/ .fks-input.is-disabled .fks-input__inner {
  background: #F4F4F4;
  color: #333 !important;
}

/deep/ .fks-textarea.is-disabled .fks-textarea__inner {
  background: #F4F4F4;
  color: #333 !important;
}
</style>
