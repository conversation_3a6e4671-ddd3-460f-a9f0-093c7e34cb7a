.mobile-container {
  position: relative;
  .mobile-box-bottom {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 330px;
    border-radius: 15px 15px 0 0;
    background-color: #fff;
    .middle-center {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      flex-grow: 1; /* 让middle-center可以占满剩余空间 */
      padding: 40px 0; /* 可调整间距 */
      .text-cell {
        font-size: 27px;
        color: #333333;
        margin-bottom:  20px;
        opacity: 0.6;
      }
      .text-cell-big {
        font-size: 31px;
        font-weight: 600;
        color: #191919;
        margin-bottom:  20px;
      }
      .password-input-cell {
        // width: 448px;
        width: 468px;
        margin-top: 20px;
      }

      /deep/ .fm-password-input__item {
        border: none !important; /* 移除默认边框 */
        border-bottom: 5px solid rgba(20, 96, 255, 0.3) !important;
        border-radius: 0 !important;
        margin: 0 8px;
      }

      /* 激活状态样式 */
      /deep/ .fm-password-input__item--focus {
        border-bottom-color: rgba(20, 96, 255, 1) !important;
      }
    }
    /deep/ .fm-number-keyboard {
      background-color: #ced1d7 !important;
    }
  }
}
