<template>
    <div v-loading="loading">
        <div v-if="isPC" class="pc-container">
            <div class="header" style="user-select: none;">
                <img :src="require('@/assets/img/homepage/new-logo.png')" width="66px" height="66px"
                    style="border-radius: 6px;object-fit: contain;"/>
                <span style="font-size: 28px; color: #333333; " class="second-title">华智车管</span>
            </div>
            <div class="pc-center">
                <div class="center-box">
                    <img :src="require('@/assets/img/homepage/pc-phone.png')" width="100%" height="180px" style="object-fit: cover; border-radius: 12px 12px 0 0;"/>
                    <span class="text-pc-cell">为保护客户的隐私安全，请输入用车人联系方式的后四位</span>
                    <span class="text-pc-code">进行验证</span>
                    <verify :phoneNumber="phoneNumber" @updatePhoneNumber="updatePhoneNumber" />
                </div>
            </div>
        </div>
        <div class="mobile-container" v-else>
            <div class="mobile-box-top">
                <fm-image
                    width="100%"
                    height="100%"
                    fit="contain"
                    :src="require('@/assets/img/detail/phone.png')"
                />
            </div>
            <div class="mobile-box-bottom">
                <div class="middle-center">
                    <span class="text-cell">为保护客户的隐私安全</span>
                    <span class="text-cell-big">请输入用车人联系方式的后四位</span>
                    <span class="text-cell">进行验证</span>
                    <!-- <verify :phoneNumber="phoneNumber" @updatePhoneNumber="updatePhoneNumber" @openKeyBoard="openKeyBoard" /> -->
                    <fm-password-input
                        :value="phoneNumber"
                        class="password-input-cell"
                        :gutter="10"
                        :length="4"
                        :mask="false"
                        :focused="showKeyboard"
                        @focus="showKeyboard = true"
                    />
                </div>
                 <!-- 数字键盘 -->
                <fm-number-keyboard
                    v-model="phoneNumber"
                    :show="showKeyboard"
                    @blur="showKeyboard = false"
                />
            </div>
        </div>
    </div>
</template>
<script>
import {
  getCodeBizid,
  getAuthPhone
} from "@modules/ProjectCar/CompanyPortal/UserManagement/api";
import platform from "@/mixins/platform";
import Verify from './verify.vue'
import {Toast} from 'fawkes-mobile-lib'
import { IS_PHONE_VERIFY } from '@/store/Mutation/mutationTypes'
export default {
    mixins: [platform],
    components: {
        Verify
    },
    data() {
        return {
            loading: false,
            phoneNumber: '',
            code: '',
            showKeyboard: true
        }
    },
    watch: {
        phoneNumber(val) {
            if(val.length === 4) {
                this.getInitJudement()
            }
        },
    },
    methods: {
        getInitJudement() {
            this.code = this.$route.params.code;
            getAuthPhone({code: this.code,phone:this.phoneNumber}).then(res => {
                if (res.data) {
                    this.loading = true;
                    this.$store.commit(IS_PHONE_VERIFY, true)
                    getCodeBizid({code:this.code}).then(res => {
                        if (res.status) {
                            this.$router.push({path:'/projectCar/details',query:{type:'view',bizId:res.data}})
                            this.loading = false;
                        }else{
                            this.loading=false
                            if(this.isPC) {
                                this.$message.error(res.message);
                            }else {
                                Toast(res.message);
                            }
                        }
                    })   
                }else{
                    this.phoneNumber = ''
                    if(this.isPC) {
                        this.$message.error('手机尾号错误，请重新输入');
                    }else {
                        Toast('手机尾号错误，请重新输入');
                    }
                }
            })
        },
        updatePhoneNumber(newPhoneNumber) {
            this.phoneNumber = newPhoneNumber;
        },
        // openKeyBoard() {
        //     this.showKeyboard = true;
        //     this.isClickOnInput = true;
        // }
    }
}
</script>

<style scoped lang='less'>
@import "./index.less";
.pc-container {
    width: 100%;
    height: 100%;
    margin: 40px 50px;
    position: relative;
    .header {
        display: flex;
        align-items: center;
        span {
            display: inline-block;
            margin: 0 20px;
            color: #000000;
            line-height: 42px;
            text-align: left;
            font-style: normal;
            white-space: nowrap;
        }
    }
    .pc-center {
        position: absolute;
        width: 1172px;
        height: 852px;
        top: 360px;
        left: 50%;  /* 使元素左边缘与容器中心对齐 */
        transform: translateX(-50%);  /* 将元素水平偏移自身宽度的50%，实现居中 */
        border-radius: 24px;
        background: linear-gradient(185deg, #D6E1FF -2%, #FFFFFF 49%);
        .center-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
            .text-pc-cell {
                font-size: 32px;
                font-weight: 500;
                color: #333333;
                margin-top: 60px;
                margin-bottom: 20px;
            }
            .text-pc-code {
                font-size: 28px;
                color: #333333;
                opacity: 0.6;
                margin-bottom: 100px;
            }
        }
    }
}
</style>