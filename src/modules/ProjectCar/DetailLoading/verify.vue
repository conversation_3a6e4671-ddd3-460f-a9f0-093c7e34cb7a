<template>
  <div class="code-box">
      <div
        v-for="(digit, i) in 4"
        :key="i"
        class="code-item"
        :class="{ focused: currentIndex === i }"
      >
        {{ code[i] || '' }}
        <span v-if="currentIndex === i && !code[i]" class="cursor"></span>
      </div>
      <input
        ref="input"
        type="tel"
        maxlength="4"
        v-model="localPhoneNumber"
        class="hidden-input"
        @input="handleInput"
        @focus="updateFocus" 
      />
  </div>
</template>

<script>
export default {
  name: 'PhoneVerifyInput',
  props: {
    phoneNumber: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localPhoneNumber: this.phoneNumber,
      code: [],
      currentIndex: 0
    };
  },
  watch: {
    localPhoneNumber(val) {
      this.code = val.split('').slice(0, 4);
      this.currentIndex = this.code.length;
    },
    phoneNumber(val) {
      this.localPhoneNumber = val;
    }
  },
  mounted() {
    this.$refs.input.focus();
  },
  methods: {
    handleInput() {
      // 只允许输入数字
      this.localPhoneNumber = this.localPhoneNumber.replace(/\D/g, '');
      this.$emit('updatePhoneNumber', this.localPhoneNumber);
    },
    updateFocus() {
      this.currentIndex = this.code.length;
      this.$emit('openKeyBoard', true);
    }
  }
};
</script>

<style scoped>
.code-box {
  position: relative;
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 12px;
}

.code-item {
  width: 140px;
  height: 80px;
  border-bottom: 8px solid rgba(20, 96, 255, 0.3);
  font-size: 48px;
  line-height: 80px;
  text-align: center;
}

.code-item.focused {
  border-color: rgba(20, 96, 255, 1) !important;
}

.hidden-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  border: none;
  outline: none;
  z-index: 10;
}

.cursor {
  display: inline-block;
  width: 1px;
  height: 48px;
  background-color: #3c83ff;
  animation: blink 1s steps(2, start) infinite;
  vertical-align: middle;
  margin-left: 1px;
}

@keyframes blink {
  to {
    visibility: hidden;
  }
}

</style>
