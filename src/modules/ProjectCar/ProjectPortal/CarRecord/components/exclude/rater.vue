<template>
  <div class="rate-container">
    <div class="full-width flex col-center row-between" style="margin-bottom: 12px">
      <div class="title">您对本次用车满意吗？</div>
      <i class="cursor-pointer fks-icon-close" @click="onCancel"/>
    </div>
    <div class="flex col-center">
      <img
        v-for="star in stars"
        :key="star"
        :style="{cursor: isView ? 'not-allowed' : 'pointer'}"
        :src="star > evaluateScore ? require('@/assets/img/car/gray-star.svg') : require('@/assets/img/car/star.svg')"
        width="40px"
        height="40px"
        @click="handleClick(star)"
      />
      <span class="comment">{{ comment }}</span>
    </div>
    <div v-if="!isView" class="shortcuts">
      <div
        v-for="(text, index) in shortcuts"
        :key="index"
        :class="{active: selectedComments.findIndex(c => c === text) > -1}"
        class="shortcut-item"
        @click="handleCommentClick(text)"
      >
        {{ text }}
      </div>
    </div>
    <div class="title" style="margin-bottom: 12px;margin-top: 20px">您对本次用车的建议反馈</div>
    <fks-input
      v-if="isView"
      :value="evaluateContent"
      type="textarea"
      id="story"
      name="story"
      :rows="3"
      placeholder=""
      readonly
    />
    <fks-input
      v-else
      v-model="evaluateContent"
      type="textarea"
      id="story"
      name="story"
      :rows="3"
      placeholder="请输入您的建议反馈"
    />
    <div class="buttons" v-if="!isView">
      <fks-button :disabled="loading" @click="onCancel">取 消</fks-button>
      <fks-button
        :disabled="loading"
        :loading="loading"
        style="margin-left: 0"
        type="primary"
        @click="onSubmit"
      >
        提交评价
      </fks-button>
    </div>
  </div>

</template>
<script>
import {
  createRate,
  getRateContent,
  getRateDetail
} from "@modules/ProjectCar/ProjectPortal/CarRecord/api";
import storage from "@utils/storage";

const rateText = ['非常差', '差', '一般', '满意', '非常满意']

export default {
  name: 'Rater',
  props: ['type', 'row', 'suggestions', 'visible'],
  data() {
    return {
      stars: [1, 2, 3, 4, 5],
      evaluateScore: 5,
      evaluateContent: '',
      rateText,
      selectedComments: [],
      loading: false,
    }
  },
  computed: {
    comment() {
      return rateText[this.evaluateScore - 1];
    },
    shortcuts() {
      return this.suggestions[this.evaluateScore];
    },
    isView() {
      return this.type === 'view'
    }
  },
  methods: {
    handleCommentClick(text) {
      if (this.isView) return;
      // 如果text在selectComments里，则取消；否则添加
      const i = this.selectedComments.findIndex(comment => comment === text);
      if (i > -1) {
        this.selectedComments.splice(i, 1);
      } else {
        this.selectedComments.push(text);
      }
    },
    handleClick(val) {
      if (this.isView) return;
      this.evaluateScore = val;
    },
    onCancel() {
      this.$emit('visible', false)
    },
    onSubmit() {
      const userName = storage.get('username');
      const data = {
        vdApplyFormId: this.row.id,
        vdCarInfoId: this.row.vdCarInfoId,
        vdDriverInfoId: this.row.vdDriverInfoId,
        evaluateScore: this.evaluateScore,
        evaluateContent: this.evaluateContent,
        userName
      }
      this.loading = true;
      createRate(data)
        .then(res => {
          if (res.status) {
            this.$message.success('评价成功')
            this.$emit('updateView')
          }
        })
        .finally(() => {
          this.loading = false;
        })
    },
    async getRateData(row) {
      const res = await getRateDetail({
        id: row.id,
        userName: storage.get('username')
      })
      if (res.status) {
        this.evaluateScore = res.data.evaluateScore
        this.evaluateContent = res.data.evaluateContent
      }
    }
  },
  watch: {
    evaluateScore() {
      // 评分改变时，需要重置快捷评价
      this.selectedComments = [];
    },
    selectedComments(newVal) {
      if (!this.isView) {
        this.evaluateContent = newVal.join('，');
      }
    },
    row(newVal) {
      if (newVal.id) {
        this.getRateData(newVal)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.rate-container {
  padding: 10px;
  font-family: Source Han Sans;

  .title {
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    text-align: left;
    letter-spacing: 0px;
    color: #191919;
  }

  .comment {
    display: inline-block;
    margin-left: 24px;
  }

  .shortcuts {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 12px;
    grid-row-gap: 8px;
    margin-top: 12px;

    .shortcut-item {
      box-sizing: border-box;
      cursor: pointer;
      text-align: center;
      border-radius: 66px;
      padding: 5px 12px;
      color: #333333;
      background: #FFFFFF;
      border: 1px solid rgba(51, 51, 51, 0.3);

      &.active {
        color: #3C83FF;
        background: rgba(220, 228, 250, 0.5);
        border: 1px solid rgba(220, 228, 250, 0.5);
      }
    }
  }


  .buttons {
    margin-top: 28px;
    display: grid;
    grid-template-columns: 3fr 7fr;
    grid-column-gap: 16px;
  }
}

/deep/ .fks-textarea {
  textarea {
    width: 100%;
    background-color: #f4f7fd;
    border-radius: 4px;
    border: none;
    font-size: 14px;
    resize: none;
    padding: 12px;
    box-sizing: border-box;
    color: #191919;

    &::placeholder {
      color: #1919194D;
      letter-spacing: 1.27px;
    }
  }
}
</style>
