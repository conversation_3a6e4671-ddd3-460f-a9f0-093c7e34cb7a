
<template>
  <div id="carApply" class="main" :class="{'bg-white': formList.length === 0}">
    <tool-bar />
    <div v-if="showSearch" class="flex row-between col-center full-width bg-white search-apply">
      <fm-search
        ref="searchInput"
        v-model="form.searchValue"
        clearable
        maxlength="50"
        :placeholder="'请输入位置名称、位置详细地址'"
        @clear="onClear"
        @search="onSearch"
      >
<!--        todo 暂时隐藏过滤搜索功能-->
<!--        <template #left-icon>-->
<!--          <i class="fm-icon fm-icon-search" @click="onSearch"></i>-->
<!--        </template>-->
      </fm-search>
<!--      <img src="@/assets/img/car/filter.png" class="filter-img" @click="showFilter = true;">-->
    </div>
    <fm-popup class="filter-popup" :visible.sync="showFilter" position="top" :style="{ height: 'calc(50vh + 20px)','max-height': 'calc(50vh + 20px)', top: '52px', 'overflow-y': 'auto' }" get-container="#carApply" :append-to-body="false">
      <div class="font-32 font-bold m-32">按申请时间选择</div>
      <div class="flex flex-wrap col-center row-between m-32">
        <div
          v-for="item in timeList"
          :key="item.value"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': timeApplyChoose.includes(item.value)}"
          @click="timeApplyChoose = [item.value]; form.startApplyTime = ''; form.endApplyTime = '';"
        >
          {{ item.text }}
        </div>

        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.startApplyTime, 'no-time': !form.startApplyTime }"
          @click="showStartApplyTime = true"
        >
          {{ form.startApplyTime ? form.startApplyTime : '开始时间' }}
        </div>
        <div class="time-line m-b-32"></div>
        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.endApplyTime, 'no-time': !form.endApplyTime }"
          @click="showEndApplyTime = true"
        >
          {{  form.endApplyTime ? form.endApplyTime : '结束时间' }}
        </div>
        <date-time-picker
          :show.sync="showStartApplyTime"
          :time.sync="form.startApplyTime"
          title="开始时间"
          type="date"
          @confirm="timeApplyChoose = [];"
        ></date-time-picker>
        <date-time-picker
          :show.sync="showEndApplyTime"
          :time.sync="form.endApplyTime"
          title="结束时间"
          type="date"
          @confirm="timeApplyChoose = [];"
        ></date-time-picker>
      </div>
      <div class="font-32 font-bold m-32">按出发时间选择</div>
      <div class="flex flex-wrap col-center row-between m-32">
        <div
          v-for="item in timeList"
          :key="item.value"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': timeChoose.includes(item.value)}"
          @click="timeChoose = [item.value]; form.startTime = ''; form.endTime = '';"
        >
          {{ item.text }}
        </div>

        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.startTime, 'no-time': !form.startTime }"
          @click="showStartTime = true"
        >
          {{ form.startTime ? form.startTime : '开始时间' }}
        </div>
        <div class="time-line m-b-32"></div>
        <div
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          :class="{'primary': !!form.endTime, 'no-time': !form.endTime }"
          @click="showEndTime = true"
        >
          {{  form.endTime ? form.endTime : '结束时间' }}
        </div>
        <date-time-picker
          :show.sync="showStartTime"
          :time.sync="form.startTime"
          title="开始时间"
          type="date"
          @confirm="timeChoose = [];"
        ></date-time-picker>
        <date-time-picker
          :show.sync="showEndTime"
          :time.sync="form.endTime"
          title="结束时间"
          type="date"
          @confirm="timeChoose = [];"
        ></date-time-picker>
      </div>
      <div class="font-32 font-bold m-32">按状态选择</div>
      <div class="flex flex-wrap col-center m-32">
        <div
          v-for="item in applyFormState"
          :key="item.code"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': statusChoose.includes(item.key)}"
          @click="statusChoose = [item.key]"
        >
          {{ item.value }}
        </div>
      </div>
      <div class="font-32 font-bold m-32">按用车类型选择</div>
      <div class="flex flex-wrap col-center m-32 p-b-100">
        <div
          v-for="item in useCarType"
          :key="item.code"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          :class="{'primary': useCarTypeChoose.includes(item.key)}"
          @click="useCarTypeChoose = [item.key]"
        >
          {{ item.value }}
        </div>
      </div>
      <div class="foot foot-filter">
        <fm-button
          class="flow-btn btn-50 m-r-30"
          type="primary"
          plain
          :disabled="loading"
          :loading="loading"
          @click="clearFilter"
        >重置</fm-button>
        <fm-button
          class="flow-btn btn-50"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="onSearchFilter"
        >确定</fm-button>
      </div>
    </fm-popup>
    <empty-data v-if="formList.length === 0 && loading === false"></empty-data>
    <div v-else-if="formList.length > 0" class="apply-container" @scroll="handleScroll">
      <!-- 下拉刷新 -->
      <fm-pull-refresh
        v-model="isRefresh"
        refresh-layer-color="#4b8bf4"
        success-text="刷新成功"
        @refresh="handleRefresh"
      >
        <!-- 上拉加载 -->
        <fm-list
          v-model="loading"
          :finished="finished"
          :immediate-check="false"
          finished-text="已经到底啦~"
          @load="onLoad"
        >
          <!-- 滑动 -->
          <fm-swipe-cell v-for="(item, i) in formList" :key="i">
            <!-- 卡片 -->
<!--            :class="item.applyFormUserState === 9 ? 'end' : item.applyFormState === 3 && item.processState === '3' ? 'start' : item.applyFormState === 4 && item.processState === '0' ? 'danger' : ['driving', 'end', 'end', 'driving'][+item.processState]"
              -->
            <fm-cell-group
              :class="['driving', 'end', 'danger', 'start'][+item.processState]"
              inset
              @click="handleView(item,  showTripDetail(item.applyFormUserState) ? 3 : 1)">
              <div :class="['status-active', '', 'status-danger', 'status-start'][+item.processState]"
                   class="status flex col-center row-center">
                <span v-if="item.applyFormUserState !== null">
                  {{ item.applyFormUserState | transferEnums('ApplyFormUserStateEnums') }}
                </span>
              </div>
              <div class="car-cell p-r-32">
                <div class="flex col-center row-between full-width">
                  <div class="flex col-center">
                    <img class="address-text m-r-10" src="@/assets/img/car/icon_start.png">
                    <span class="address-city">
                      {{ getCity(item, 'startAddress') }}
                    </span>
                  </div>
                  <div class="address text-right m-l-40">
                    {{ getAddress(item, 'startAddress') }}
                    <!--                    {{ item.startAddress === '其他' || item.startAddress === '9'  ? item.startAddressDetail.includes('市') -->
                    <!--                    ? item.startAddressDetail.substring(item.endAddress.lastIndexOf('市') + 1) : item.startAddressDetail : item.startAddress }}-->
                  </div>
                </div>
                <div class="flex col-center">
                  <div :class="['address-line-driving', 'address-line-end', 'address-line-end', 'address-line-driving'][+item.processState]"
                       class="address-line m-l-16"></div>
                  <img v-if="['0', '3'].includes(item.processState)"
                       class="round-trip-img m-l-10 m-r-10"
                       src="@/assets/img/car/icon_round_trip_active.png">
                  <img v-else class="round-trip-img m-l-10 m-r-10"
                       src="@/assets/img/car/icon_round_trip.png">
                </div>
                <div class="flex col-center row-between full-width">
                  <div class="flex col-center">
                    <img class="address-text m-r-10" src="@/assets/img/car/icon_end.png">
                    <span class="address-city d-inline-block">
                      {{ getCity(item, 'endAddress') }}
                    </span>
                  </div>
                  <div class="address text-right m-l-40">
                    {{ getAddress(item, 'endAddress') }}
                  </div>
                </div>
              </div>
              <div class="car-line m-l-32 m-r-32 m-t-32"></div>
              <div class="flex row-between car-cell">
                <div class="flex">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_corp.png">
                  <span class="time">所属项目：</span>
                </div>
                <div class="time text-break text-right time-info">{{item.projectName }}</div>
              </div>
              <div class="flex row-between car-cell">
                <div class="flex">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_driver.png">
                  <span class="time">申请人：</span>
                </div>
                <div class="time text-break text-right time-info">{{item.task0FullName }}</div>
              </div>
              <div class="flex col-center row-between car-cell">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_corp.png">
                  <span class="time">所属部门：</span>
                </div>
                <div class="time time-info text-right">{{item.task0TopDepartment }}</div>
              </div>
              <div class="flex col-center row-between car-cell">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_time.png">
                  <span class="time">申请时间：</span>
                </div>
                <div class="time time-info text-right">{{
                    item.task0Time ? $dayjs(item.task0Time).format('YYYY-MM-DD HH:mm') : ''
                  }}
                </div>
              </div>
              <div class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_time.png">
                  <span class="time">出发时间：</span>
                </div>
                <div class="time time-info text-right">
                  {{ getStartTime(item) }}
                </div>
              </div>
              <div v-if="item.vdCarInfo && item.vdCarInfo.carNum" class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_car_info.png">
                  <span class="time">车辆信息：</span>
                </div>
                <div class="time time-info">
                  <span>{{ item.vdCarInfo.carType }}</span>
                  <span class="m-l-16">{{ item.vdCarInfo.carNum }}</span>
                </div>
              </div>
              <div v-if="item.vdDriverInfo && item.vdDriverInfo.driverPhone" class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_driver.png">
                  <span class="time">司机信息：</span>
                </div>
                <div class="time time-info">
                  <a @click.stop="" :href="`tel:${item.vdDriverInfo.driverPhone}`">
                    <img class="m-l-16" width="14px" height="14px" :src="require('@/assets/img/detail/dianhua.png')"/>
                    <span class="m-l-16">{{ item.vdDriverInfo.driverPhone }}</span>
                    <span>{{ item.vdDriverInfo.driverFullName }}</span>
                  </a>
                </div>
              </div>
              <div class="m-t-24"></div>
              <div v-if="item.buttonsSmallMore && item.buttonsSmallMore.length > 0" class="flex row-between col-center">
                <div @click.stop="" :id="item.id">
                  <fm-popover
                    class="m-l-32 m-b-32"
                    :visible.sync="item.showPopover"
                    trigger="click"
                    placement="bottom-start"
                    :actions="item.buttonsSmallMore"
                    get-container="'#carApply'"
                    @select="it => handleOpt(it, item)"
                  >
                    <template #reference>
                      <fm-button text size="small">更多<i class="fm-icon fm-icon-arrow"></i></fm-button>
                    </template>
                  </fm-popover>
                </div>
                <div class="flex row-end m-l-32 m-r-32">
                  <template v-for="(it, i) in item.buttonsSmall.filter(btn => btn.buttonKey !== 'XC_PJ')">
                    <div
                      v-if="it.buttonSizeType === 'small'"
                      class="obt-btn other-btn text-center m-b-32"
                      :class="{'m-r-10': i + 1 < item.buttonsSmall.length}"
                      @click.stop="handleOpt(it, item)"
                      :key="it.buttonKey + item.id"
                      :style="{'border-color': `rgb(${it.buttonColor})`, color: `rgb(${it.buttonColor})`}"
                    >
                      {{ it.buttonValue }}
                    </div>
                  </template>
                </div>
              </div>
              <div v-if="item.buttonsSmall && item.buttonsSmall.length > 0" class="flex row-end m-l-32 m-r-32">
                <template v-for="(it, i) in item.buttonsSmall.filter(btn => btn.buttonKey !== 'XC_PJ')">
                  <div
                    v-if="it.buttonSizeType === 'small'"
                    class="obt-btn other-btn text-center m-b-32"
                    :class="{'m-r-10': i + 1 < item.buttonsSmall.length}"
                    @click.stop="handleOpt(it, item)"
                    :key="it.buttonKey + item.id"
                    :style="{'border-color': `rgb(${it.buttonColor})`, color: `rgb(${it.buttonColor})`}"
                  >
                    {{ it.buttonValue }}
                  </div>
                </template>
                <template v-for="(it, i) in item.buttonsBig">
                  <div
                    v-if="it.buttonValue !== '查看'"
                    class="obt-btn other-btn text-center m-b-32 m-l-10"
                    :style="{'border-color': `rgb(${it.buttonColor})`, color: `rgb(${it.buttonColor})`}"
                    @click.stop="handleOpt(it, item)"
                    :key="item.id + i + it.buttonValue"
                  >
                    {{ it.buttonValue }}
                  </div>
                </template>
              </div>
            </fm-cell-group>
          </fm-swipe-cell>
        </fm-list>
      </fm-pull-refresh>
    </div>
    <rate-popup ref="ratePopup" :cur-car.sync="curCar" :is-cur="true" :rate-type="rateType"
                @refreshList="onRefresh"></rate-popup>
    <rate-drawer ref="rateDrawer" :cur-car.sync="curCar" :is-cur="true" :rate-type="rateType"
                 @refreshList="onRefresh"></rate-drawer>
    <revocation-drawer
      ref="revocationDrawer"
      :cur-car.sync="curCar"
      :is-cur="true"
      @refreshList="onRefresh"
    ></revocation-drawer>
    <AnnouncementDialog/>

  </div>
</template>

<script>


import EmptyData from '@/components/EmptyData/index.vue';
import DateTimePicker from '@/modules/FormCenter/components/DateTimePicker/index.vue';
import mixins from './mixins';
import mobileCarMixin from "@/mixins/mobileCarMixin";
import AnnouncementDialog from '@components/AnnouncementDialog/index.vue'
import ToolBar from "@components/Toolbar/index.vue";
import EventBus from "@utils/eventBus";
export default {
  name: 'mobile-view',
  mixins: [mixins, mobileCarMixin],
  components: {ToolBar, EmptyData, DateTimePicker, AnnouncementDialog },
  beforeRouteLeave(to, from, next) {
    // 导航离开该组件的对应路由时调用
    // 可以访问组件实例 `this`
    const el = document.querySelector('.apply-container')
    if (el) {
      const shouldMemo = to.name === 'formView' || to.name === 'tripDetail'
      this.SET_SCROLL_TOP(shouldMemo ? el.scrollTop : 0)
    }
    next();
  },
  data() {
    return {
      showSearch: false,
    }
  },
  mounted() {
    EventBus.$on('on-search-show', (val) => {
      this.showSearch = val;
    })
  },
  beforeDestroy() {
    EventBus.$off('on-search-show');
  },
  watch: {
    showSearch(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.focusSearch();
        });
      }
    }
  },
  methods: {
    getStartTime(item) {
      const time = item.startTime2 || item.startTime || '';
      if (time) {
        return this.$dayjs(time).format('YYYY-MM-DD HH:mm')
      } else {
        return ''
      }
    },
    focusSearch() {
      setTimeout(() => {
        try {
          const input = (
            this.$refs.searchInput?.$el ||  // Vue 组件
            this.$refs.searchInput          // 原生 DOM
          )?.querySelector?.('input');
          if (input) {
            input.focus();
            input.click?.(); // 某些浏览器需要
            input.addEventListener('blur', this.handleBlur);
          }
        } catch (e) {
          console.error('聚焦失败:', e);
        }
      }, 100); // 适当延迟
    },
    handleBlur(event) {
      if(!this.form.searchValue) {
        this.showSearch = false;
      }
    },
    handleScroll() {
      // 获取输入框 DOM（兼容 Vue 组件和原生 DOM）
      const input = (
        this.$refs.searchInput?.$el ||  // Vue 组件（如 fm-search）
        this.$refs.searchInput          // 原生 input
      )?.querySelector?.('input');      // 如果包裹在 div 里，用 querySelector 找 input

      if (input) {
        input.blur(); // 让输入框失去焦点，关闭软键盘
      }
    },
  }
}
</script>


<style scoped lang='less'>
@import "./mobile-view";
</style>
