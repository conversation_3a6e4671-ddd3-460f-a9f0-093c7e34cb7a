import request from '@/utils/request'

export function createRate(data) {
  return request({
    url: '/vehicle-dispatch/vd/af/uc/complete/evaluate',
    method: 'POST',
    data
  })
}

export function getRateList(params) {
  return request({
    url: '/vehicle-dispatch/vd/af/uc/complete/isEvaluate',
    method: 'GET',
    params
  })
}

export function getRateContent() {
  return request({
    url: '/vehicle-dispatch/vd/af/uc/complete/default/evaluateContent',
    method: 'GET',
  })
}

export function getRateDetail(params) {
  return request({
    url: '/vehicle-dispatch/vd/af/uc/complete/evaluate/info',
    method: 'GET',
    params
  })}

export function deleteCarRecord(id) {
  return request({
    url: '/vehicle-dispatch/vd/flow/delete',
    method: 'POST',
    params: {id}
  })
}


export function exportPdf(id) {
  return request({
    url: '/vehicle-dispatch/vd/flow/export/pdf',
    method: 'POST',
    params: {id},
    responseType: 'blob'
  })
}
