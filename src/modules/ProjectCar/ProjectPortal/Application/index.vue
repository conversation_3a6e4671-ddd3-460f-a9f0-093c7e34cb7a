<template>
  <FormCenter :notify="true" v-if="params" :params="params" />
</template>

<script>
import FormCenter from "@modules/FormCenter/index.vue";
import {applicationDefaultParams} from "@/mixins/menuRouteMixin";
import storage from "@utils/storage";
export default {
  name: "Application",
  components: {FormCenter},
  data() {
    return {
      params: null
    }
  },
  created() {
    const to1 = JSON.parse(storage.get('feishu_link'))
    if (to1) {
      this.params = {...to1.query, taskKey: to1.query.userTask, fromPage: 'feishu'}
      const el = document.querySelector('.fks-menu-item.is-active')
      if (el) {
        el.classList.remove('is-active')
      }
      setTimeout(()=>{
        storage.remove('feishu_link');
      })
    } else {
      this.params = this.$route.params.type ? this.$route.params : applicationDefaultParams;
    }
  },
}
</script>

<style scoped lang="scss">

</style>
