<template>
  <div class="custom-table">
    <fks-table
      ref="table"
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      :height="350"
      :sticky="true"
      @select="handleRowSelect"
      :selectable="selectedRows"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
      :header-cell-class-name="cellClass"
    >
      <!-- 选择列 -->
      <fks-table-column type="selection" width="50" fixed="left" />

      <!-- 动态列 -->
      <fks-table-column
        v-for="(column, index) in tableConfig"
        :key="column.prop + index"
        :label="column.label"
        :prop="column.prop"
        :formatter="column.formatter"
        :fixed="column.fixed"
        :width="column.width"
        :cell-style="{ textAlign: 'left' }"
      >
        <!-- 自定义列内容插槽 -->
        <template v-if="column.customer" v-slot="scope">
          <slot :name="`column-${column.prop}`" :scope="scope" />
        </template>

        <!-- 默认列内容 -->
        <template v-else v-slot="scope">
          <div
            class="text-ellipsis"
            :style="{ maxWidth: column.width || '100%' }"
            :title="formatColumnData(scope.row[column.prop], column)"
          >
            <div
              v-if="column.clickable"
              @click="handleColumnClick(scope.row, column.prop)"
              style="cursor: pointer; text-decoration: underline; color: #409EFF"
            >
              {{ formatColumnData(scope.row[column.prop], column) }}
            </div>
            <div v-else>
              {{ formatColumnData(scope.row[column.prop], column) }}
            </div>
          </div>
        </template>
      </fks-table-column>
    </fks-table>
  </div>
</template>

<script>
export default {
  props: {
    tableData: Array,
    tableConfig: Array,
    loading: Boolean,
  },
  data() {
    return {
      selectedRows: [],
    }
  },
  methods: {

    cellClass(row){
      if (row.columnIndex === 0) {
        return 'disabledCheck'
      }
    },
    handleRowSelect(row) {
      this.$emit("row-select", row);
    },
    // 格式化列数据：根据列配置判断是否格式化
    formatColumnData(value, column) {
      if (column.formatDate) {
        return this.formatDate(value);
      }
      if (column.enums) {
        if (value) {
          let list = column.enums;
          return list.find(item => item.key == value).value;
        }
      }
      return value;
    },
    handleColumnClick(row, prop) {
      this.$emit("column-click", { row, prop });
    },
  },
  computed: {
    headerCellStyle() {
      return {
        background: "#FAFAFA",
        color: "#333333 !important",
        fontWeight: "normal",
        fontSize: "14px",
      };
    },
    cellStyle() {
      return { color: "#333333 !important" };
    },
  },
};
</script>

<style lang="less">
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.disabledCheck .fks-checkbox {
  // display: none;//设置不成功，页面卡顿
  visibility: hidden;
}
</style>
