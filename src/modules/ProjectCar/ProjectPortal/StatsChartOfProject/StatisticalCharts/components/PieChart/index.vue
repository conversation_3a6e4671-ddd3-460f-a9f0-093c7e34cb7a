<template>
  <div :id="chartId" class="full-height full-width"></div>
</template>

<script>
import * as echarts from 'echarts';
import chartIdMixin from "../chartIdMixin";
let elementResizeDetectorMaker = require('element-resize-detector');
export default {
  name: 'PieChart',
  mixins: [chartIdMixin],
  props: {
    chartData: {
      type: Object,
      default() {
        return {
          data: [ { value: 1048, name: '正常' },
            { value: 735, name: '维修' },
            { value: 580, name: '退场' }]
        }
      }
    },
    type: {
      type: String,
      default: 'line'
    }
  },
  data() {
    const _this = this;
    return {
      // right: '13.32%',
      right: 0,
      ratio: 1,
      chart: null,
      option: {
        color: [],
        grid: {
          top: 0,
          // bottom: 75 * this.ratio,
          bottom: 75,
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical', // vertical 设置图例垂直排列 | 水平 horizontal
          right: 0,
          // bottom: 20,
          top: '10%',
          icon: 'none',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 16, // 图例之间间距
          // width: '50%',
          formatter: function (name) {
            let data = _this.option.series[0].data
            let total = 0
            let tarValue
            const icon = '';
            let index = -1;
            for (let i = 0; i < data.length; i++) {
              total += data[i].value
              if (data[i].name === name) {
                tarValue = data[i].value
                index = i;
                break;
              }
            }
            let v = tarValue
            return [`{icon${index}|${icon}}` + `{name|${name}}` + `{value|${v}}`].join(''); // 数据右对齐样式
          },
          textStyle: {  // 数据右对齐样式
            color: 'rgba(0,0,0,0.65)',
            padding: [12, 15],
            borderWidth: 1,
            borderColor: '#EEEEEE',
            borderRadius: 4,
            backgroundColor: new echarts.graphic.LinearGradient(0, 0, 1, 1,
              [{
                offset: 0,
                color: '#EFF5FC'
              },
                {
                  offset: 1,
                  color: '#FCFDFD'
                }
              ]),
            rich: {
              name: {
                // legend左边的文字
                fontSize: 14,
                // padding: [0, 135, 0, 6] // 1.左边的文字添加右边距10(可自己调整)
                padding: [0, 135, 0, 6] // 1.左边的文字添加右边距10(可自己调整)
              },
              value: {
                // legend右边的值
                fontSize: 14,
                // padding: [12, 15], // 1.左边的文字添加右边距10(可自己调整)
                align: 'right',//3.右对齐
                // padding: [5, -100, 0, 0],//4.设置右边距为-100(-70/-80..可自己调整)
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            center: ['25%', '38%'],
            radius: ['50%', '60%'],
            avoidLabelOverlap: false,
            labelLine: {
              show: false
            },
            label: {
              show: true,
              position: 'center',
              formatter:() => {
                return ['{sum|' + '-' + '}', '{title|车辆总数}'].join('\n');
              },
              rich: {
                title: {
                  fontSize: 16,
                  color: 'rgba(0,0,0,0.65)',
                  lineHeight: 30
                },
                sum: {
                  // fontSize: 24 * this.ratio,
                  fontSize: 24,
                  color: 'rgba(0,0,0,0.85)',
                  fontWeight: 600
                }
              }
            },
            data: [
              { value: 1048, name: '正常' },
              { value: 735, name: '维修' },
              { value: 580, name: '退场' }
            ],
            z: 666,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          },
          {
            type: 'pie',
            z: 1,
            avoidLabelOverlap: false,
            // radius: '50%',
            center: ['25%', '38%'],
            radius: ['40%', '70%'],
            emphasis: {
              disabled: true // 是否关闭高亮状态
            },
            tooltip: { // 提示框组件
              show: false
            },
            data: [
              { value: 0 }
            ],
            itemStyle: {
              normal: {
                color: function(params) {
                  const colorList = ['#EFF5FC', '#FCFDFD'];
                  return new echarts.graphic.LinearGradient(0, 0, 0, 1,
                    [{
                      offset: 0,
                      color: colorList[0]
                    },
                      {
                        offset: 1,
                        color: colorList[1]
                      }
                    ]);
                }
              }
            }
          }
        ]
      }
    }
  },
  computed: {
    // ratio() {
    //   return 1 / window.devicePixelRatio;
    // },
  },
  watch: {
    'chartData': {
      handler() {
        this.chart && this.chart.dispose();
        this.initChart();
      },
      deep: true
    }
  },
  mounted() {
   this.$nextTick(() => {
     this.chart && this.chart.dispose();
     this.initChart();
   })
  },
  methods: {
    initChart() {
      this.$nextTick(() => {
        const windowWidth = window.outerWidth
        if (windowWidth < 1500) {
          // this.right = 0;
        }
        this.chart = echarts.init(document.getElementById(this.chartId),  null, {
          width: 'auto', // 必须设置为 'auto'
          height: 'auto', // 必须设置为 'auto'
          devicePixelRatio: 1 // 设置为你需要的像素比
        });
        this.setOptionData(this.chartData);
        this.chart.setOption(this.option);
        let _this = this;
        window.addEventListener('resize', () => {
          // 获取视口宽度
          const viewportWidth = window.innerWidth;
          // 获取整个窗口的宽度
          const windowWidth = window.outerWidth
          this.ratio = windowWidth / 1920;
          if (windowWidth < 1500) {
            // this.right = 0;
          }
          _this.$nextTick(() => {
            setTimeout(() => {
              this.chart.resize();
            }, 500);
          });
        });
        // 设置高亮 --- 高亮指定的数据图形
        // this.chartData.data.map((item, i) => {
        //   this.chart.dispatchAction({
        //     type: 'highlight',
        //     seriesIndex: 0, // series 数据数组第几个
        //     dataIndex: i // 可选，数据的 index
        //   })
        // });
      });
    },
    setOptionData(data) {
      const total = data.total || '-'
      this.option.series[0].data = data.data;
      this.option.series[0].label.formatter = () => {
        return ['{sum|' + total + '}', '{title|车辆总数}'].join('\n');
      }
      data.color.map((item, i) => {
        this.option.legend.textStyle.rich[`icon${i}`] = {
        borderRadius: 5,
        backgroundColor:  new echarts.graphic.LinearGradient(0, 0, 1, 1,
        [{
          offset: 0,
          color: item[0]
        },{
            offset: 1,
            color: item[1]
          }
        ]),
        width: 10,
        height: 10
        };
      });
      this.option.series[0].itemStyle = {
        normal: {
          color: function(params) {
            const colorList = data.color;
            const index = params.dataIndex;
            return new echarts.graphic.LinearGradient(0, 0, 1, 1,
              [{
                offset: 0,
                color: colorList[index][0]
              },
                {
                  offset: 1,
                  color: colorList[index][1]
                }
              ]);
          }
        }
      }
    }
  }
}
</script>


<style scoped lang='less'>

</style>
