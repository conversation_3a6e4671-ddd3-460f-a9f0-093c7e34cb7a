<template>
  <div :id="chartId" ref="chart" class="full-width full-height bg-white"></div>
</template>

<script>
import * as echarts from 'echarts';
import chartIdMixin from "../chartIdMixin";
export default {
  name: 'LineChart',
  mixins: [chartIdMixin],
  props: {
    chartData: {
      type: Object
    },
    type: {
      type: String,
      default: 'line'
    }
  },
  data() {
    return {
      showLine: true,
      chart: null,
      option: {
        // 这是滚动条
        dataZoom: [
          {
            show: false, // 是否展示
            xAxisIndex: [0],// 控制第一个x轴
            height: 10,
            left: '5%',
            right: '5%',
            bottom: 10,
            start: 0, // 数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
            handleSize: "120%",
            zoomLock: true, //是否锁定选择区域（或叫做数据窗口）的大小。如果设置为 true
          },
        ],
        grid: {
          // top: 70,
          left: 0, // 左侧留白百分比，可以根据实际情况调整
          right: '0%', // 右侧留白百分比，可以根据实际情况调整
          top: 100,
          bottom: 25,
          containLabel: true // 容纳标签，确保标签不被裁剪
        },
        toolbox: {
          top: 0,
          left: '2%',
          feature: {
            // restore: { show: true }, // 重置
            myFull: {
              // 全屏
              show: false,
              title: '全屏',
              icon: 'path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891',
              onclick: (e) => {
                // let fullFlag = true;
                let element = document.getElementById(this.chartId);
                // 一些浏览器的兼容性
                if (element.requestFullScreen) {
                  // HTML W3C 提议
                  element.requestFullScreen();
                } else if (element.msRequestFullscreen) {
                  // IE11
                  element.msRequestFullScreen();
                } else if (element.webkitRequestFullScreen) {
                  // Webkit (works in Safari5.1 and Chrome 15)
                  element.webkitRequestFullScreen();
                } else if (element.mozRequestFullScreen) {
                  // Firefox (works in nightly)
                  element.mozRequestFullScreen();
                }

                // 退出全屏
                if (element.requestFullScreen) {
                  document.exitFullscreen();
                } else if (element.msRequestFullScreen) {
                  document.msExitFullscreen();
                } else if (element.webkitRequestFullScreen) {
                  document.webkitCancelFullScreen();
                } else if (element.mozRequestFullScreen) {
                  document.mozCancelFullScreen();
                }
              }
            }
          }
        },
        legend: {
          data: [],
          textStyle: {
            fontSize: 14,
            color: 'rgba(0,0,0,0.65)'
          },
          itemHeight: 10,
          top: 0,
          // y: 'top',
          right: 0,
          // bottom: 0,
          icon: 'line' // circle, rect, roundRect, triangle, diamond, pin, arrow, none
        },
        xAxis: {
          type: 'category',
          // 刻度线
          axisTick: {
            show: false
          },
          // x轴文字的配置
          axisLabel: {
            show: true,
            padding: 10,
            textStyle: {
              color: 'rgba(0,0,0,0.65)',
              fontSize: 14
            }
          },
          axisLine: {
            lineStyle: {
              color: '#CCCCCC'
            }
          },
          // boundaryGap: false,
          data: []
        },
        // visualMap: {
        //   min: 1,
        //   max: 10 // 确保视觉映射的最大值能覆盖数据的最大值
        // },
        yAxis: [
          {
            type: 'value',
            name: '',
            scale: true,
            // x轴文字的配置
            axisLabel: {
              show: true,
              margin: 5,
              textStyle: {
                color: 'rgba(0,0,0,0.65)',
                fontSize: 14
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: ['#EEEEEE']
              }
            },
            nameTextStyle: {
              color: 'rgba(0,0,0,0.65)',
              fontSize: 14
            },
            axisLine: {
              lineStyle: {
                color: '#CCCCCC'
              }
            }
          },
          {
            type: 'value',
            name: '',
            scale: true,
            // x轴文字的配置
            axisLabel: {
              show: true,
              margin: 5,
              textStyle: {
                color: 'rgba(0,0,0,0.65)',
                fontSize: 14
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: ['#EEEEEE']
              }
            },
            // y轴 name 样式
            nameTextStyle: {
              color: 'rgba(0,0,0,0.65)',
              fontSize: 14
            },
            axisLine: {
              lineStyle: {
                color: '#CCCCCC'
              }
            }
          }
        ]
      }
    };
  },
  watch: {
    chartData() {
      this.chart && this.chart.dispose();
      this.initChart();
    }
  },
  methods: {
    initChart() {
      this.$nextTick(() => {
        this.chart = echarts.init(document.getElementById(this.chartId));
        this.setOptionData(this.chartData);
        this.chart.setOption(this.option);
        let _this = this;
        const zoomSize = 6;
        const dataAxis = this.chartData.xData;
        const data = this.chartData.yData;
        // 点击缩放
        this.chart.on('click', function (params) {
          console.log(dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)]);
          _this.chart.dispatchAction({
            type: 'dataZoom',
            startValue: dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)],
            endValue:
              dataAxis[Math.min(params.dataIndex + zoomSize / 2, data.length - 1)]
          });
        });
        window.addEventListener('resize', () => {
          _this.$nextTick(() => {
            setTimeout(() => {
              this.chart.resize();
            }, 500);
          });
        });
      });
    },
    // 设置最小高度 3
    renderBarMinHeight(barMinHeight = 3) {
      try {
        // 获取渲染的 chart 示例
        const chart = this.$refs.chart.getInstance();
        const option = chart.getOption();
        const series = option.series;
        const yAxis = option.yAxis;
        if (yAxis && yAxis.length) {
          yAxis.forEach((axis, idx) => {
            const serie = series.find((item) => item.yAxisIndex === idx) || series[0];
            if (serie.type === 'bar') {
              // _extent 里面是 y 轴的最小值和最大值，格式如 [2,100]
              const { _extent: yAxisScale } = chart.getModel().getComponent('yAxis', idx).axis.scale;
              const yMin = yAxisScale[0];
              // 将 y 轴上的点转为 px 长度值，坐标原点为左上角，越往下，px 越大，barMinHeight 是从 y 轴为 0 处开始计算
              // y 轴 0 点距离左上角坐标原点的高度
              const zeroPx = chart.convertToPixel({ yAxisIndex: idx }, 0);
              // y 轴起点点距离左上角坐标原点的高度
              const minPx = chart.convertToPixel({ yAxisIndex: idx }, yMin);
              // zeroPx - minPx: y 轴从 0 到起点，被隐藏的高度， 加上 barMinHeight，显示出来的最小高度即为设置的最小高度
              serie.barMinHeight = zeroPx - minPx + barMinHeight;
            }
          });
        }
        chart.setOption({ series });
      } catch (e) {
      }
    },
    setOptionData(data) {
      try {
        const {
          legend,
          lineStyle,
          type,
          title,
          yName,
          xData,
          yData,
          colors,
          yCount,
          dataZoomShow,
          unit,
          legendShow,
          dataTotal
        } = data
        this.option.title = {
          show: false,
          text: ` ${title}`, // 主标题文本
          subtext: '', // 副标题文本
          left: 'center', // 标题水平位置
          top: 'top',
          textStyle: { // 主标题样式
            color: '#191919',
            fontSize: 14,
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: 500
          }
        }
        this.option.legend.show = legendShow
        this.option.grid.top = legendShow ? yData.length > 6 ? 80 : 40 : 30
        this.option.legend.data = data.legendIcon ? legend.map((it, i) => {
          const itemStyle = data.legendColor ? {
            backgroundColor: data.legendColor[i]
          } : {}
          const iconStyle = data.legendIcon[i] === 'line' ? {
            itemHeight: 2,
            itemWidth: 20
          } : {}
          return {
            name: it,
            icon: data.legendIcon[i] === 'line' ? 'rect' : data.legendIcon[i],
            ...iconStyle,
            ...itemStyle
          }
        }) : legend
        yCount ? this.option.yAxis = this.option.yAxis.map((item, i) => {
          item.name = yName[i]
          return item
        }) : this.option.yAxis[0].name = yName
        if (data.nameTextStyle) {
          this.option.yAxis[0].nameTextStyle = { ...this.option.yAxis[0].nameTextStyle, ...data.nameTextStyle }
        }
        if (data.nameTextStyle1) {
          this.option.yAxis[1].nameTextStyle = { ...this.option.yAxis[1].nameTextStyle, ...data.nameTextStyle1 }
        }
        this.option.dataZoom[0].show = dataZoomShow || false
        if (this.option.dataZoom[0].show) {
          this.option.dataZoom[0].end = Math.floor(100 / (xData.length / 3))
        }
        this.option.xAxis.data = xData
        const minArr = []
        this.option.series = yData.map((item, index) => {
          const showLine = type && type[index] ? type[index] === 'line' : this.type === 'line'
          const yAxisName = legend[index]
          const color = colors.length > index ? colors[index] : showLine ? ['#89D0F9', '#27A3EA'] : ['#89D0F9', '#27A3EA']
          // const yItemData = showLine ? item.map( it => it || '') : item;
          const yItemData = item;
          const yMin = Math.min.apply(null, yItemData)
          const yMax = Math.max(...yItemData)
          // zeroPx - minPx: y 轴从 0 到起点，被隐藏的高度， 加上 barMinHeight，显示出来的最小高度即为设置的最小高度
          minArr.push(yMin)
          minArr.push(yMax)
          if (showLine) {
            return {
              type: type[index] || this.type,
              yAxisIndex: yCount && index + 1 === yData.length ? 1 : 0,
              smooth: true, // 是否平滑曲线
              smoothMonotone: 'x',
              symbolSize: 10,
              showSymbol: false, // 在 tooltip hover 的时候显示
              // symbol: 'none', // 设置为 'none' 不显示圆点
              scale: true,
              name: yAxisName,
              data: yItemData,
              itemStyle: {
                normal: {
                  color: '#4EB5F0',
                  lineStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                      offset: 0,
                      color: color[0] // 渐变起始颜色
                    }, {
                      offset: 1,
                      color: color[0] // 渐变结束颜色
                    }]),
                    type: lineStyle && lineStyle.length > index ? lineStyle[index] : 'solid'// 'dotted' 虚线 'solid' 实线 dashed 虚线
                    // type: 'dotted' // 'dotted' 虚线 'solid' 实线
                  }
                }
              }
            }
          } else {
            return {
              type: 'bar',
              name: yAxisName,
              barGap: '0%', //坐标轴占比
              yAxisIndex: yCount && index + 1 === yData.length ? 1 : 0,
              data: item,
              barWidth: 10,
              minPointLength: 10,
              barMinHeight: 10,
              backgroundStyle: {
                color: '#F1F5FE'
              },
              showBackground: false,
              emphasis: {
                focus: 'series'
              },
              stack: 'total', // 柱状图叠一起
              label: {
                show: index + 1 === yData.length,
                formatter: function(params) {
                  // params 是一个对象，包含数据的信息
                  // 返回自定义的标签内容，这里是总数的形式
                  return dataTotal[params.dataIndex];
                },
                position: 'top',
                textStyle: {
                  color: 'rgba(0,0,0,0.65)',
                  'font-weight': 'bold',
                  'font-size': '14px'
                }
              },
              itemStyle: {
                // borderWidth: 0,
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: color[0] },
                    { offset: 1, color: color[1] }
                  ])
                }
              }
            }
          }
        })
        const minVal = Math.min.apply(null, minArr)
        this.option.yAxis.min = minVal - 1
        this.option.tooltip = {
          trigger: 'axis',
          backgroundColor: 'white',
          borderColor: '#ECEFF5',
          className: 'custom-tooltip-box',
          textStyle: {
            color: '#191919'
          },
          borderWidth: 1,
          axisPointer: yData.length > 2 ? {
            type: 'shadow',
            shadowStyle: {
              color:  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(241, 245, 254, 1)'
                },
                {
                  offset: 1,
                  color: 'rgba(241, 245, 254, 0.1)'
                }
              ]),
              width: 'auto'
            }
          } : {},
          // padding: [15, 15, 15, 15],
          formatter(val) {
            return val
              .map((item) => {
                let color = [];
                let m = '';
                if (item.color.colorStops instanceof Array) {
                  color = item.color.colorStops.map(it => it.color);
                  m = 'margin-top: 10px';
                } else {
                  color = colors[0];
                  m = 'margin-top: 0';
                }
                const marker = `<span style='display:inline-block;margin-right:5px;border-radius:0;width:10px;height:6px;background: linear-gradient( 180deg, ${color[0]} 0%,  ${color[1]} 98%);'></span>`
                return `
                    <div style='display: flex;align-items:center; ${m}; width: 200px;justify-content: space-between;'>
                      <div>
                        ${marker}
                        <span style='font-size: 14px;color: rgba(0,0,0,0.65);margin-right:4px'>${item.seriesName}</span>
                      </div>
                      <div style='font-size: 14px;color: rgba(0,0,0,0.65);'>${item.data || 0}</div>
                    </div>
                  `
              })
              .join('');
            // return `<div>
            //     <div style='font-weight: 500;font-size: 12px;color: #333'>${label}</div>
            //     ${items}
            //   </div>`
          }
        }
      } catch (e) {
        console.log(e)
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', () => {
      this.chart.resize();
    });
  }
};
</script>
<style lang="less">
/deep/ .custom-tooltip-box {
  padding-top: 0;
  box-shadow: 0 4px 10px 0 rgba(0,0,0,0.1);
  border-radius: 4px;
}
</style>
