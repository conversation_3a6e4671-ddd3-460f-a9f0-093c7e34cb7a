.search-view {
  min-height: 232px;
  padding-top: 36px;
  padding-bottom: 4px;
  background: #FFFFFF;
  border-radius: 12px;
  border: 2px solid #DFE0E2;
}
@h: 64px;
/deep/ .fks-form-item--medium {
  .fks-input__inner,
  .fks-form-item__content,
  .fks-form-item__label {
    height: @h;
    line-height: @h;
  }
}
.search-form {
  width: calc(100% - 296px);
  margin-right: 490px;
}
.search-opt {
  margin-right: 60px;
  width: 296px;
  height: @h;
}
.clear-img {
  height: 32px;
  width: 32px;
}

.clear-text {
  width: 56px;
  height: 36px;
  margin-left: 8px;
  font-family: MicrosoftYaHei, MicrosoftYaHei;
  font-weight: normal;
  font-size: 28px;
  line-height: 36px;
  //color: #C2C2C2;
  text-align: center;
  font-style: normal;
  text-transform: none;
  &.search-text {
    //color: #333333;
  }
}

.blank {
  width: 100%;
  height: 16px;
  background: #F8F9FA;
}

.chart-view {
  background: #F8F9FA;
}

.chart-box {
  //width: calc((100% - 16px) / 2);
  //min-width: 600px;
  //max-width: 100%;
  height: 932px;
  padding: 56px 60px 60px;
  background: #FFFFFF;
  border-radius: 12px;
  border: 2px solid #DFE0E2;

  &:nth-of-type(odd){
    margin-right: 16px;
  }
  .chart-line {
    width: 4px;
    height: 28px;
    margin-right: 16px;
    background: #3C83FF;
  }
  .chart-title-box {
    margin-bottom: 40px;
  }
  .chart-title {
    height: 44px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    line-height: 44px;
  }
  .chart {
    height: calc(100% - 76px);
  }
}

.chart-btn {
  cursor: pointer;
  color: #333333;
  padding: 16px 24px !important;
  &:hover {
    background: #e8e9e9 !important;
  }
  &.disabled {
    color: #C2C2C2;
    cursor: not-allowed;
  }
}


@media only screen and (min-width: 1400px) {
  /deep/.fks-col-xl-12 {
    width: calc((100% - 16px) / 2);
  }
}
@media only screen and (max-width: 1400px) {
  /deep/.fks-col-xl-12 {
    width: 100%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 1400px) {
  .chart-box{
    &:nth-of-type(odd){
      margin-right: 0;
    }
  }
}