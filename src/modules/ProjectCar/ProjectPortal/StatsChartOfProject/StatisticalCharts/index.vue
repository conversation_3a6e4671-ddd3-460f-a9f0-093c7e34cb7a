<template>
<div class="full-width full-height overflow-y-auto stats-container">
  <div class="search-view full-width flex row-between col-start">
    <fks-row>
      <fks-form ref="form" :model="formData" label-width="100px" class="search-form">
        <fks-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
          <fks-form-item  label="车牌号" prop="plateNumber">
            <fks-select v-model="formData.plateNumber" filterable placeholder="请选择车牌号" clearable @change="getData" @clear="clear" class='full-width'>
              <fks-option v-for="item in plateNumberList" filterable :key="item.carNum" :label="item.carNum" :value="item.carNum" />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
          <fks-form-item  label="车辆来源" prop="carResourceStr">
            <fks-input v-model="formData.carResourceStr" placeholder="" disabled/>
          </fks-form-item>
        </fks-col>
        <fks-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
          <fks-form-item  label="出租方名称" prop="carCompName">
            <fks-input v-model="formData.carCompName" placeholder="" disabled/>
          </fks-form-item>
        </fks-col>
        <fks-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
          <fks-form-item  label="品牌型号" prop="carType">
            <fks-input v-model="formData.carType" placeholder="" disabled/>
          </fks-form-item>
        </fks-col>
        <fks-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
          <fks-form-item  label="司机" prop="driverFullName">
            <fks-input v-model="formData.driverFullName" placeholder="" disabled />
          </fks-form-item>
        </fks-col>
        <fks-col v-if="formData.carStatus" :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
          <fks-form-item label="车辆状态" prop="statusVal">
            <fks-input v-model="formData.carStatusStr" placeholder="" disabled />
  <!--          <fks-tag :type="statusType[formData.carStatus]" size="small">{{ formData.carStatusStr }}</fks-tag>-->
          </fks-form-item>
        </fks-col>
      </fks-form>
    </fks-row>
    <div class="search-opt flex row-start">
      <div class="flex col-center chart-btn" :class="{'disabled': !formData.plateNumber}" @click="clear">
        <i v-if="loading" class="fks-icon-loading font-32"></i>
        <img v-else src="@/assets/img/statisticalCharts/clear.png" alt="clear" class="clear-img"/>
        <span class="clear-text">清空</span>
      </div>
      <div class="flex col-center ml-31 chart-btn" :class="{'disabled': !formData.plateNumber}" @click="getData('')">
        <i v-if="loading" class="fks-icon-loading font-32"></i>
        <img v-else src="@/assets/img/statisticalCharts/search.png" alt="search" class="clear-img"/>
        <span class="clear-text search-text">搜索</span>
      </div>
    </div>
  </div>
  <div class="blank"></div>
    <fks-row class="full-width flex flex-wrap row-between chart-view">
      <fks-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12"  class="chart-box m-b-16" v-loading="chartLoading">
        <div class="flex col-center chart-title-box">
          <div class="chart-line"></div>
          <div class="chart-title">车辆状态</div>
        </div>
        <pie-chart :chart-data.sync="pieData" ></pie-chart>
      </fks-col>
      <template v-for="(item, index) in lineChartData">
        <fks-col
          :xs="24" :sm="24" :md="12" :lg="12" :xl="12"
          class="chart-box m-b-16"
          :key="index + 'line-chart'"
          v-loading="item.chartLoading"
          v-if="(index === 2 && formData.plateNumber) || (index === 3 && !formData.plateNumber) || [0, 1, 4].includes(index)"
        >
          <div class="flex col-center chart-title-box">
            <div class="chart-line"></div>
            <div class="chart-title">{{ item.chartData.title }}</div>
          </div>
          <fks-empty description="暂无数据" v-if="empty" />
          <line-chart v-else :chart-data=" item.chartData" class="chart" ></line-chart>
        </fks-col>
      </template>
    </fks-row>
<!--  <div class="full-width flex flex-wrap row-between chart-view">-->
<!--    <div class="chart-box m-b-16">-->
<!--      <div class="flex col-center chart-title-box">-->
<!--        <div class="chart-line"></div>-->
<!--        <div class="chart-title">车辆状态</div>-->
<!--      </div>-->
<!--      <pie-chart :chart-data.sync="pieData" ></pie-chart>-->
<!--    </div>-->
<!--    <div class="chart-box m-b-16" v-for="(item, index) in lineChartData" :key="index + 'line-chart'" :class="{'m-l-16': index % 2 === 0 }">-->
<!--      <div class="flex col-center chart-title-box">-->
<!--        <div class="chart-line"></div>-->
<!--        <div class="chart-title">{{ item.chartData.title }}</div>-->
<!--      </div>-->
<!--      <line-chart :chart-data="item.chartData" class="chart" ></line-chart>-->
<!--    </div>-->
<!--  </div>-->
</div>
</template>

<script>
import PieChart from './components/PieChart/index.vue'
import LineChart from './components/LineChart/index.vue'
// 虚线
// const icon = 'path://M234.666667 490.666667h-153.6a25.6 25.6 0 1 0 0 51.2h153.6a25.6 25.6 0 1 0 0-51.2zM473.6 490.666667h-153.6a25.6 25.6 0 1 0 0 51.2h153.6a25.6 25.6 0 1 0 0-51.2zM934.4 490.666667h-136.533333a25.6 25.6 0 1 0 0 51.2h136.533333a25.6 25.6 0 1 0 0-51.2zM712.533333 490.666667h-153.6a25.6 25.6 0 1 0 0 51.2h153.6a25.6 25.6 0 1 0 0-51.2z';
// 直线
const iconLine = 'path://m0.010277,5.945418l24.979446,0l0,2.109164l-24.979446,0l0,-2.109164z';
import {
  getCarTotal,
  getCarList,
  getDriverInfoByCar,
  getCarStatus,
  getStatistics
} from '@modules/ProjectCar/ProjectPortal/CarManage/api'
import { mapState } from 'vuex';
import dayjs from "dayjs";
export default {
  name: "StatisticalCharts",
  components: {
    PieChart,
    LineChart
  },
  data() {
    return {
      // carInfo: {
      //   carNum: 5,
      //   ownNum: 3,
      //   rentalNum: 2
      // },
      empty: false,
      carId: '',
      carInfo: [],
      formData: {
        plateNumber: '', // 车牌号
        carResource: '', // 车辆来源
        carResourceStr: '',
        carCompName: '', // 出租方名称
        carType: '', // 品牌
        driverUserName: '', // 司机 拼音
        driverFullName: '', // 司机 汉字
        carStatus: '', // 状态
        carStatusStr: ''
      },
      loading: false,
      loadingCarNum: false, // 车牌号请求等待
      plateNumberList: [], // 车牌号下拉选项
      statusType: {
        100: 'success',
        200: 'warning',
        300: 'info',
        400: 'danger',
      },
      chartLoading: false,
      // 'success', 'danger', 'info'
      pieData: {
       // color: ['#00d186', '#1890ff', '#ff4d4f', '#ffcb27'],
       color: [['#6CC7FF', '#306BFE'], ['#1AC477', '#73F0A1'], ['#F8A441', '#FFDAAF'], ['#FF4D4F', '#F4ABAC']],
       data: [
         { value: 1048, name: '正常' },
         { value: 735, name: '维修' },
         { value: 580, name: '退场' }]
      },
      lineChartData: [
        {
          chartLoading: false,
          chartData: {},
          chartSet: {
            title: `车辆月平均油耗`,
            legend: ['月均油耗'],
            unit: '升/百公里',
            nameTextStyle: {
              padding: [0, 0, 0, 100]
            },
            yName: '月均油耗(升/百公里)',
            colors: [['#89D0F9', '#27A3EA']],
            lineStyle: ['solid'],
            legendIcon: [iconLine],
            type: ['line'],
            yData: [[150, 230, 224, 218]]
          }
        },
        {
          chartLoading: false,
          chartData: {},
          chartSet: {
            title: `月行车里程`,
            nameTextStyle: {
              padding: [0, 0, 0, 30]
            },
            legend: ['行车里程'],
            yName: '行车里程(公里)',
            unit: '公里',
            colors: [['#89D0F9', '#27A3EA']],
            lineStyle: ['solid'],
            legendIcon: [iconLine],
            type: ['line'],
            yData: [[150, 230, 224, 218]]
          }
        },
        {
          chartLoading: false,
          chartData: {},
          chartSet: {
            title: `月出车次数`,
            legend: ['出车次数'],
            yName: '出车次数',
            unit: '次',
            // colors: ['#40BB5A', '#027AFF'],
            colors: [['#89D0F9', '#27A3EA']],
            lineStyle: ['solid'],
            legendIcon: [iconLine],
            type: ['line'],
            yData: [[150, 230, 224, 218]]
         }
       },
       // 车牌号为空时
        {
          chartLoading: false,
          chartData: {},
          chartSet: {
          title: `月出车次数`,
            legend: ['车辆1', '车辆2'],
            yName: ['各车辆出车次数'],
            // colors: [['#1AC477', '#73F0A1'], ['#FF4D4F', '#F4ABAC'], ['#F8A441', '#FFDAAF'], ['#6CC7FF', '#306BFE'], ['#F1DB15', '#FCEE73']],
            colors: [
              ['#71B7F1', '#399AEA'], ['#F8A441', '#FFDAAF'], ['#1AC477', '#73F0A1'], ['#FF4D4F', '#F4ABAC'],
              ['#87F0FA', '#5AD8E5'], ['#9CD03A', '#C8F474'], ['#FFCB27', '#FFE595'], ['#F596AA', '#FFC2CF'],
              ['#897FF7', '#A69FF7'], ['#9F6EFF', '#C2A4FC']
            ],
            colors1: [[2, 122, 255], [90, 216, 229], [111, 98, 255], [134, 223, 108], [33, 204, 255], [251, 187, 14], [251, 123, 14], [17, 194, 194], [3, 184, 229], [255, 77, 79], ['#89D0F9', '#27A3EA']],
            type: ['bar', 'bar', 'bar', 'bar', 'bar', 'line'],
            legendIcon: ['rect', 'rect', 'rect', 'rect', 'rect'],
            legendColor: ['#027AFF', '#5AD8E5', '#6F62FF', '#86DF6C', '#21CCFF', '#FBBB0E', '#FB7B0E', '#11C2C2', '#03B8E5', '#FF4D4F', '#40BB5A'],
            // yCount: 1,
            nameTextStyle: {
              padding: [0, 0, 0, 85]
            },
            nameTextStyle1: {
              padding: [0, 50, 0, 0]
            },
            unit: '次',
            yData: [
            [50, 30, 24, 18]
            // [34, 66, 29, 98],
            // [44, 56, 46, 56],
            // [88, 123, 149, 22],
            // [56, 77, 153, 33]
            // [154, 166, 229, 198],
          ] }
        },
        {
          chartLoading: false,
          chartData: {},
          chartSet: {
            title: `月度费用统计`,
            legend: ['车辆燃油费', '维修维护费', '过桥过路费', '租赁费', '其他费', '总费用'],
            yName: '费用(元)',
            unit: '元',
            colors1: [[64, 187, 90], [2, 122, 255], [90, 216, 229], [111, 98, 255], [134, 223, 108], [33, 204, 255], [251, 187, 14], [251, 123, 14], [17, 194, 194], [3, 184, 229], [255, 77, 79]],
            // 过桥过路费 / 住宿费 / 维修保养费 / 油费 / 其他费用 / 费用总计
            colors: [['#1AC477', '#73F0A1'], ['#FF4D4F', '#F4ABAC'], ['#F8A441', '#FFDAAF'], ['#6CC7FF', '#306BFE'], ['#F1DB15', '#FCEE73']],
            type: ['bar', 'bar', 'bar', 'bar', 'bar'],
            legendIcon: ['rect', 'rect', 'rect', 'rect', 'rect'],
            legendColor: ['#40BB5A', '#027AFF', '#5AD8E5', '#6F62FF', '#86DF6C', '#21CCFF', '#FBBB0E', '#FB7B0E', '#11C2C2', '#03B8E5', '#FF4D4F'],
            nameTextStyle: {
              padding: [0, 0, 0, 0]
            },
            yData: [
              [50, 30, 24, 18],
              [34, 66, 29, 98],
              [44, 56, 46, 56],
              [88, 123, 149, 22],
              [56, 77, 153, 33],
              [154, 166, 229, 198],
            ]
          }
        },
      ]
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    // 车辆来源
    carResource() {
      return this.enums?.CarResourceEnums || [];
    },
    // 车辆状态
    carStatus() {
      return this.enums?.CarStatusEnums || [];
    },
  },
  created() {
    this.getCarStatusInfo();
    this.remoteMethod('');
  },
  methods: {
    // 初始化图表
    initChart(carId) {
      this.loading = true;
      this.lineChartData.map(async (item, i) => {
        const params = {
          time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          carId
        }
        if (!carId) {
          let res = '';
          if (i === 3) {
            params.carId = this.plateNumberList.map(it => {
              return it.id;
            });
            item.chartLoading = true;
            res = await getStatistics(params, i);
            item.chartLoading = false;
          }
          if (i !== 2 ) {
            item.chartLoading = true;
            res = await getStatistics(params, i);
            item.chartLoading = false;
          }
          item.chartLoading = true;
          await this.getLineChatData(item, res, i);
          item.chartLoading = false;
          return false;
        }
        if ( i !== 3 ) {
          item.chartLoading = true;
          const res = await getStatistics(params, i);
          item.chartLoading = false;
          await this.getLineChatData(item, res, i);
        }
      });
    },
    // 车辆状态统计/车辆总数统计
    async getCarStatusInfo() {
      this.chartLoading = true;
      const res = await getCarStatus(); // 车辆状态统计
      if (!res.status) {
        this.chartLoading = false;
        return false;
      }
      const resTotal = await getCarTotal(); // 车辆总数统计
      this.chartLoading = false;
      if (!resTotal.status) {
        return false;
      }
      this.pieData.total = resTotal.data[0].value;
      this.$set(this.pieData, 'data',  res.data.map(item => {
        item.value = +item.value;
        return item;
      }));
      this.update = false;
      this.$nextTick(() => {
        this.update = false;
      })
    },
    // 获取车牌号下拉
    remoteMethod(carNum) {
      this.loadingCarNum = true;
      getCarList({carNum}).then(res => {
        this.loadingCarNum = false;
        this.plateNumberList = res.data.list || [];
        // this.plateNumberList.length > 0 ? this.getData(this.plateNumberList[0].carNum) : '';
        this.initChart('');
      }).catch(() => {
        this.loadingCarNum = false;
      })
    },
    // 清空
    clear() {
      this.formData = {
        plateNumber: '', // 车牌号
        carResource: '', // 车辆来源
        carResourceStr: '',
        carCompName: '', // 出租方名称
        carType: '', // 品牌
        driverUserName: '', // 司机 拼音
        driverFullName: '', // 司机 汉字
        carStatus: '', // 状态
        carStatusStr: ''
      }
      this.carId = '';
      this.initChart('');
    },
    // 选择车牌号
    async getData(plateNumber = '') {
      if (!plateNumber) {
        await this.initChart(this.carId);
        return false;
      }
      const list = this.plateNumberList.find(item => item.carNum === plateNumber);
      const { carResource, carStatus, carCompName, carType, id } = list;
      const carResourceStr = this.carResource.find(it => it.key === carResource).value;
      const carStatusStr = this.carStatus.find(it => it.key === carStatus).value;
      const res = await getDriverInfoByCar(id);
      this.empty = !Boolean(res.data[id]);
      if (res.data[id]) {
        const { driverFullName, driverUserName } = res.data[id];
        this.formData = {
          plateNumber, // 车牌号
          carResource, // 车辆来源
          carResourceStr,
          carCompName, // 出租方名称
          carType, // 品牌
          driverUserName, // 司机 拼音
          driverFullName, // 司机 汉字
          carStatus, // 状态
          carStatusStr
        };
        this.carId = id;
        await this.initChart(this.carId);
      } else {
        this.formData = {
          plateNumber, // 车牌号
          carResource, // 车辆来源
          carResourceStr,
          carCompName, // 出租方名称
          carType, // 品牌
          driverUserName: '', // 司机 拼音
          driverFullName: '', // 司机 汉字
          carStatus, // 状态
          carStatusStr
        }
        this.carId = ''
      }

    },
    async getLineChatData(data, res, type) {
      this.loading = type === 4;
      if (!res) {
        const xData = ['2024年1月', '2024年2月', '2024年3月', '2024年4月'];
        data.chartData = {
          xData,
          ...data.chartSet
        };
        return false;
      }
      if (!res.status) {
        return false;
      }
      const { times, values } = res.data;
      const legend = [];
      const chatType = [];
      const legendIcon = [];
      values.map((item, i) => {
        item.values = item.values.map(it => {
          return +it;
        });
        let chatTypeName = '';
        let legendIconName = '';
        let legendName = '';
        let yData = [];
        const flag = ![3, 4].includes(type);
        chatTypeName = flag ? 'line' : 'bar';
        legendIconName = flag ? iconLine : 'rect';
        legendName = item.name;
        yData = item.values;
        if (type === 2 && this.formData.plateNumber) {
          chatTypeName = 'line';
          legendIconName = iconLine;
        }

        if (['总次数', '费用总计'].includes(item.name)  ? false : chatTypeName) {
          chatType.push(chatTypeName);
          legendIcon.push(legendIconName);
          legend.push(legendName);
          data.chartSet.yData[i] = yData;
        }
      })
      data.chartSet.legend = legend;
      data.chartSet.type = chatType;
      data.chartSet.legendIcon = legendIcon;
      data.chartData = {
        ...data.chartSet,
        legendShow: [3, 4].includes(type),
        dataZoomShow: false,
        dataTotal: [3, 4].includes(type) ? values[values.length - 1].values : [],
        // dataZoomShow: [3, 4].includes(type),
        xData: times
      };
      if (type === 4 && data.chartData.yData.length === values.length) {
        data.chartData.yData.pop();
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./index";
.stats-container {
  padding: 20px;
  overflow-x: hidden;
  box-sizing: border-box;
}
</style>
