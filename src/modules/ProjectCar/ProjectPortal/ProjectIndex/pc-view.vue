<template>
  <div id="projectIndexContainer" v-loading="loading">
    <fks-row :gutter="8">
      <fks-col
        :lg="responsive.lg"
        :md="responsive.md"
        :sm="responsive.sm"
        :xl="responsive.xl"
        :xs="responsive.xs"
        style="margin-bottom: 8px"
      >
        <wrapper title="用车申请">
          <template v-slot:time="{time}" name="time">
            <application-charts :time="time"/>
          </template>
        </wrapper>
      </fks-col>
      <fks-col
        :lg="responsive.lg"
        :md="responsive.md"
        :sm="responsive.sm"
        :xl="responsive.xl"
        :xs="responsive.xs"
        style="margin-bottom: 8px"
      >
        <wrapper title="出车数据">
          <template v-slot:time="{time}" name="time">
            <car-data :time="time"/>
          </template>
        </wrapper>
      </fks-col>
      <fks-col
        :lg="responsive.lg"
        :md="responsive.md"
        :sm="responsive.sm"
        :xl="responsive.xl"
        :xs="responsive.xs"
        style="margin-bottom: 8px"
      >
        <wrapper title="司机评分">
          <template v-slot:time="{time}" name="time">
            <driver-rank :time="time"/>
          </template>
        </wrapper>
      </fks-col>
      <fks-col
        :lg="responsive.lg"
        :md="responsive.md"
        :sm="responsive.sm"
        :xl="responsive.xl"
        :xs="responsive.xs"
        style="margin-bottom: 8px"
      >
        <wrapper title="里程排行" show-fullscreen>
          <template v-slot:time="{time,fullScreen}" name="time">
            <mileage :time="time" :fullScreen="fullScreen"/>
          </template>
        </wrapper>
      </fks-col>
      <fks-col
        :lg="responsive.lg"
        :md="responsive.md"
        :sm="responsive.sm"
        :xl="responsive.xl"
        :xs="responsive.xs"
        style="margin-bottom: 8px"
      >
        <wrapper title="用车费用" show-fullscreen>
          <template v-slot:time="{time,fullScreen}" name="time">
            <car-fee :time="time" :fullScreen="fullScreen"/>
          </template>
        </wrapper>
      </fks-col>
      <fks-col
        :lg="responsive.lg"
        :md="responsive.md"
        :sm="responsive.sm"
        :xl="responsive.xl"
        :xs="responsive.xs"
        style="margin-bottom: 8px"
      >
        <wrapper title="百公里油耗" show-fullscreen>
          <template v-slot:time="{time,fullScreen}" name="time">
            <fuel-consumption :time="time" :fullScreen="fullScreen"/>
          </template>
        </wrapper>
      </fks-col>
      <fks-col :span="24">
        <!--  { label: '车辆及司机信息', key: 'carInfo' }, -->
        <!--  { label: '车辆信息', key: 'carInfo' },
          { label: '司机信息', key: 'driverInfo' } -->
        <WrapperTab
          :tabs="[
          { label: '车辆信息', key: 'carInfo' },
          { label: '司机信息', key: 'driverInfo' }
        ]"
          :default-tab.sync="activeTab">

          <template #default="{ activeTab }">
            <info-panel
              ref="carChart"
              v-show="activeTab === 'carInfo'"
              :list="carInfos"
              id="carInfo"
              category="carResource"
              title="车辆数"
            />
            <info-panel
              ref="driverChart"
              v-show="activeTab === 'driverInfo'"
              :list="driverInfos"
              id="driverInfo"
              category="driverResource2"
              title="司机数"
            />
            <!-- <info-panel-new
              ref="carChart"
              v-show="activeTab === 'carInfo'"
              :list="carInfos"
              :list1="driverInfos"
              :title="'车辆数'"
              :title1="'司机数'"
            /> -->
          </template>
        </WrapperTab>
      </fks-col>
    </fks-row>
    <AnnouncementDialog></AnnouncementDialog>
  </div>
</template>
<script>
import Wrapper from './components/exclude/wrapper.vue'
import WrapperTab from './components/exclude/wrapper-tab.vue'
import ApplicationCharts from './components/exclude/application-chart.vue'
import DriverRank
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/driver-rank.vue";
import FuelConsumption
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/fuel-consumption.vue";
import CarFee from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/car-fee.vue";
import Mileage from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/mileage.vue";
import CarData
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/car-data.vue";
import InfoPanel
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/info-panel.vue";
import InfoPanelNew
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/info-panel-new.vue";
import {getCarData, getDriverData} from "@modules/ProjectCar/ProjectPortal/ProjectIndex/api";
import AnnouncementDialog from '@components/AnnouncementDialog/index.vue'

export default {
  name: 'pc-view',
  components: {
    InfoPanelNew,
    InfoPanel,
    CarData,
    Mileage, CarFee, FuelConsumption, Wrapper, ApplicationCharts, DriverRank, WrapperTab, AnnouncementDialog
  },
  data() {
    return {
      carInfos: [],
      driverInfos: [],
      loading: false,
      activeTab: 'carInfo',
      responsive: {
        xl: 8,
        lg: 8,
        md: 12,
        sm: 24,
        xs: 24,
      }
    }
  },
  watch: {
    activeTab: {
      deep: true,
      handler(newTab, oldVal) {
        this.$nextTick(() => {
          if (newTab === 'carInfo') {
            this.$refs.carChart?.resizeChart();
          } else if (newTab === 'driverInfo') {
            this.$refs.driverChart?.resizeChart();
          }
        });
      }
    }
  },
  created() {
    getCarData().then(res => {
      if (res.status) {
        this.carInfos = res.data;
      }
    })
    getDriverData().then(res => {
      if (res.status) {
        this.driverInfos = res.data;
      }
    })


  },
}
</script>
<style lang="scss" scoped>
#projectIndexContainer {
  background: #F8F9FA;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}
</style>
