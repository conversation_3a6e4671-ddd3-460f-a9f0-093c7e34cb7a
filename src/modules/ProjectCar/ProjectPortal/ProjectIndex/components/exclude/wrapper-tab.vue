<template>
  <div class="wrapper-tab full-width">
    <header class="flex col-center row-between full-width">
      <div class="tabs">
        <span
          v-for="(tab, index) in tabs"
          :key="index"
          :class="['tab', { active: activeTab === tab.key }]"
          @click="handleTabClick(tab.key)">
          {{ tab.label }}
        </span>
      </div>
    </header>
    <transition name="tab-fade">
      <div v-if="isContentVisible" class="tab-content full-width">
        <slot :active-tab="activeTab" />
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  props: {
    tabs: {
      type: Array,
      default: () => []
    },
    defaultTab: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeTab: this.defaultTab || (this.tabs.length > 0 ? this.tabs[0].key : ''),
      isContentVisible: false // 控制内容的显示状态
    };
  },
  mounted() {
    this.isContentVisible = true; // 初始加载时内容可见
  },
  methods: {
    handleTabClick(newTab) {
      if (this.activeTab !== newTab) {
        this.isContentVisible = false;
        setTimeout(() => {
          this.activeTab = newTab;
          this.isContentVisible = true;
        }, 150); // 与动画时间保持一致
      }
    }
  },
  watch: {
    activeTab(newTab) {
      this.$emit('update:defaultTab', newTab);
    }
  }
};
</script>

<style lang="less" scoped>
.wrapper-tab {
  border-radius: 6px;
  border: 1px solid #DFE0E2;
  height: 316px;
  opacity: 1;
  background: #FFFFFF;
  box-sizing: border-box;
  padding: 20px 30px;
  display: flex;
  flex-direction: column;

  .tabs {
    display: flex;
    gap: 16px;

    .tab {
      font-size: 16px;
      font-weight: 500;
      color: rgba(51, 51, 51, 0.4);
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: color 0.3s ease, border-color 0.3s ease;
      position: relative;
    }

    .tab.active {
      color: #333;
      border-color: transparent;
      &:after {
        content: '';
        position: absolute;
        display: block;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 5px;
        background: rgba(60, 131, 255, 0.2);
        border-radius: 4px;
      }
    }
  }

  .tab-content {
    flex-grow: 1;
    padding-top: 15px;
  }
}

.tab-fade-enter-active, .tab-fade-leave-active {
  transition: opacity 0.15s ease, transform 0.15s ease;
}

.tab-fade-enter {
  opacity: 0;
  transform: translateX(30px);
}

.tab-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
