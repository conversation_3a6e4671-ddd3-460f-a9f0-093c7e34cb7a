<template>
  <fks-empty v-if="isEmpty" />
  <basic-echarts
    v-else
    v-loading="loading"
    id="fuel-consumption"
    ref="fuelConsumption"
    height="auto"
    immediate
    :option="option"
  />
</template>

<script>
import BasicEcharts from "@components/Chart/index.vue";
import {
  getDataZoomCommonConfig,
  xAxisCommonConfig,
  yAxisCommonConfig
} from "@components/Chart/common";
import * as echarts from "echarts";
import {getFuelConsumption} from "@modules/ProjectCar/ProjectPortal/ProjectIndex/api";

export default {
  name: 'MileAge',
  components: {BasicEcharts},
  props: ['time','fullScreen'],
  data() {
    return {
      loading: false,
      isEmpty: false,
      chartList:[],
      option: {
        dataZoom: [],
        tooltip: {
          borderRadius: 4,
          backgroundColor: '#fff',
          borderColor: '#fff',
          trigger: 'item'
        },
        xAxis: {
          ...xAxisCommonConfig,
          type: 'category',
          data: []
        },
        yAxis: {
          ...yAxisCommonConfig,
          type: 'value',
          name: '(升/百公里)'
        },
        series: [
          {
            name: '升/百公里',
            type: 'bar',
            z: 2,
            tooltip: {},
            data: [],
            barWidth: 10, // 设置柱状条宽度为10px
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#1890FF' },
                { offset: 1, color: '#A2D8FF' }
              ]),
              shadowBlur: 26, // 阴影宽度
              shadowColor: 'rgba(0, 0, 0, 0.2)', // 阴影颜色
              shadowOffsetX: 0,
              shadowOffsetY: 4,
              borderRadius: [4, 4, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              color: '#333',
              fontSize: 12
            }
          },
          {
            name: 'shadow',
            z: 1,
            tooltip: {
              show: false
            },
            emphasis: {
              disabled: true
            },
            type: 'bar',
            barGap: '-175%',
            data: [],
            barWidth: 26,
            itemStyle: {
              color: 'rgba(241, 245, 254, 0.6)'
            }
          }
        ],
        grid: {
          top: '50px',
          left: '10%',
          right: '5%',
          bottom: '20%'
        }
      }
    }
  },
  watch: {
    time: {
      immediate: true,
      handler(newVal) {
        this.loading = true;
        getFuelConsumption(newVal).then(res => {
          if (res.status) {
            const list = res.data || [];
            this.chartList=list;
            this.isEmpty = !list.length;
            list.sort((a, b) => b.value - a.value)
            const maxValue = Math.max(...list.map(item => item.value));
            const xAxisData = list.map(item => item.name);
            this.option.xAxis.data = xAxisData;
            this.option.series[0].data = list.map(item => item.value);
            this.option.series[1].data = xAxisData.map(() => maxValue);
            this.$nextTick(() => {
              this.option.dataZoom=[]
              this.option.dataZoom.push(getDataZoomCommonConfig(list.length,false))
              // this.$refs.fuelConsumption.renderChart(this.option)
            });
          }
        }).finally(() => {
          this.loading = false;
        })
      }
    },
    fullScreen:{
      immediate: true,
      handler(newVal) {
        this.$nextTick(() => {
            this.option.dataZoom=[]
            this.option.dataZoom.push(getDataZoomCommonConfig(this.chartList.length,newVal))
            // this.$refs.fuelConsumption.renderChart(this.option)
          });
      }
    }
  }
}
</script>
