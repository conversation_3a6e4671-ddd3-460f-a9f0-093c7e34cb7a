<template>
  <fks-empty v-if="isEmpty" />
  <normal-pie
    v-else
    v-loading="loading"
    id="application-chart"
    :data="data"
    :legend="legendConfig"
    :title-config="titleConfig"
  />
</template>

<script>
import {getApplicationChartsData} from '../../api'
import NormalPie
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/normal-pie.vue";
import {richTextGenerator, sleep} from "@utils/util";

export default {
  components: {NormalPie},
  props: ['time'],
  data() {
    return {
      loading: false,
      data: [],
      isEmpty: false,
      legendConfig: {
        bottom: 0,
        left: 'center',
        icon: 'rect',
        orient: 'horizontal',
        textStyle: {
          rich: {
            title: {
              color: 'rgba(0, 0, 0, 0.65)',
              fontSize: 14,
              fontWeight: 200,
              fontFamily: 'Source Han Sans SC'
            },
            value: {
              color: '#146BFF',
              fontSize: 20,
              fontWeight: 300,
              fontFamily: 'Source <PERSON>s SC',
              padding: [0, 0, 0, 28]
            }
          }
        },
        formatter: (params) => {
          const title = richTextGenerator('title', params)
          const item = this.data.find(item => item.name === params);
          const value = richTextGenerator('value', item ? item.value : '')
          return `${title} ${value}`
        }
      },
      titleConfig: {
        text: '',
        subtext: '总计',
        textStyle: {
          color: '#333333',
          fontSize: 16,
          fontWeight: 400,
        },
        left: 'center',
        top: '35%'
      }
    }
  },
  methods: {
    async fetchData(time) {
      this.loading = true;
      try {
        const res = await getApplicationChartsData(time);
        if (res.status) {
          this.isEmpty = res.data.length === 0;
          this.titleConfig.text = res.data.reduce((prev, cur) => prev + cur.value, 0);
          this.data = res.data;
        }
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  },
  watch: {
    time: {
      immediate: true,
      async handler(val) {
        await this.fetchData(val);
      }
    }
  }
}
</script>
