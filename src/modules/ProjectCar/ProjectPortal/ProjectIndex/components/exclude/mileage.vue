<template>
  <fks-empty v-if="isEmpty" />
  <basic-echarts
    v-else
    v-loading="loading"
    id="mileage"
    height="auto"
    immediate
    ref="mileage"
    :option="option"
  />
</template>

<script>
import BasicEcharts from "@components/Chart/index.vue";
import {
  getDataZoomCommonConfig,
  xAxisCommonConfig,
  yAxisCommonConfig
} from "@components/Chart/common";
import * as echarts from "echarts";
import {getMileageData} from "@modules/ProjectCar/ProjectPortal/ProjectIndex/api";

export default {
  name: 'MileAge',
  components: {BasicEcharts},
  props: ['time','fullScreen'],
  data() {
    return {
      loading: false,
      isEmpty: false,
      chartList:[],
      option: {
        dataZoom: [],
        tooltip: {
          trigger: 'item',
          borderRadius: 4,
          backgroundColor: '#fff',
          borderColor: '#fff',
        },
        xAxis: {
          ...xAxisCommonConfig,
          type: 'category',
          data: []
        },
        yAxis: {
          ...yAxisCommonConfig,
          type: 'value',
          name: '(公里)'
        },
        series: [
          {
            name: '公里',
            type: 'bar',
            z: 2,
            tooltip: {},
            data: [],
            barWidth: 10, // 设置柱状条宽度为10px
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#ff8732' },
                { offset: 1, color: 'rgba(255, 135, 50, 0.1)' }
              ]),
              borderRadius: [0, 90, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              color: '#333',
              fontSize: 12
            }
          },
          {
            name: 'shadow',
            z: 1,
            tooltip: {
              show: false
            },
            emphasis: {
              disabled: true
            },
            type: 'bar',
            barGap: '-175%',
            data: [],
            barWidth: 26,
            itemStyle: {
              color: 'rgba(241, 245, 254, 0.6)'
            }
          }
        ],
        grid: {
          top: '50px',
          left: '10%',
          right: '5%',
          bottom: '20%'
        }
      }
    }
  },
  watch: {
    time: {
      immediate: true,
      handler(newVal) {
        this.loading = true;
        getMileageData(newVal).then(res => {
          if (res.status) {
            const list = res.data || [];
            this.isEmpty = !list.length;
            list.sort((a, b) => b.value - a.value)
            this.chartList=list;
            const maxValue = Math.max(...list.map(item => item.value));
            const xAxisData = list.map(item => item.name);
            this.option.xAxis.data = xAxisData;
            this.option.series[0].data = list.map(item => item.value);
            this.option.series[1].data = xAxisData.map(() => maxValue);
            this.$nextTick(() => {
              this.option.dataZoom=[]
              this.option.dataZoom.push(getDataZoomCommonConfig(list.length,false))
              // this.$refs.mileage.renderChart(this.option)
            });
          }
        }).finally(() => {
          this.loading = false;
        })
      }
    },
    fullScreen:{
      immediate: true,
      handler(newVal) {
        this.$nextTick(() => {
            this.option.dataZoom=[]
            this.option.dataZoom.push(getDataZoomCommonConfig(this.chartList.length,newVal))
            // this.$refs.mileage.renderChart(this.option)
          });
      }
    }
  }
}
</script>
