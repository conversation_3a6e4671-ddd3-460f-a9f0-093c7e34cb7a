<template>
  <fks-table
    :data="data"
    :show-header="false"
    height="250px"
    class="custom-table"
  >
    <fks-table-column
      v-for="(column) in tableColumns"
      :key="column"
      :prop="column"
      :width="columnWidth(column)"
      align="left"
    >
      <template slot-scope="{row}">
        <div v-if="column === 'carResource'" class="second-text">
          {{ getCarResource(row[column]) }}
        </div>
        <div v-else-if="column === 'carAge'" class="second-text">
          {{ row[column] ? `车龄${row[column]}年` : '/' }}
        </div>
        <div v-else-if="column === 'carXslcLj'" class="second-text">
          {{ row[column] ? `${row[column]}公里` : '/' }}
        </div>
        <card-tag
          v-else-if="column === 'carStatus'"
          :tag="getCarStatus(row[column])"
          style="line-height: 12px"
        />
        <card-tag
          v-else-if="column === 'useCar'"
          :tag="getUseCar(row[column])"
          style="line-height: 12px"
        />
        <div v-else-if="column === 'driverResource2'" class="second-text">
          {{ getDriverResource(row[column]) }}
        </div>
        <div v-else-if="column === 'driverAge2'" class="second-text">
          {{ row[column] ? `${row[column]}岁` : '/' }}
        </div>
        <div v-else-if="column === 'driverAge'" class="second-text">
          {{ row[column] ? `驾龄${row[column]}年` : '/' }}
        </div>
        <card-tag
          v-else-if="column === 'driverStatus'"
          :tag="getDriverStatus(row[column])"
          style="line-height: 12px"
        />
        <div v-else class="second-text">
          {{ row[column] }}
        </div>
      </template>
    </fks-table-column>
  </fks-table>
</template>

<script>
import {mapState} from "vuex";
import CardTag from "@components/CardFlow/components/tag.vue";

export default {
  name: 'tagTable',
  components: {CardTag},
  props: ['data'],
  computed: {
    ...mapState('CarApply', ['enums']),
    tableColumns() {
      if (this.data.length) {
        return Object.keys(this.data[0])
      }
      return []
    },
    columnWidth() {
      return (prop) => {
        if (prop === 'carNum' || prop === 'driverFullName') {
          return '150px'
        }
        return 'auto'
      }
    }
  },
  methods: {
    getCarResource(value) {
      const r = this.enums.CarResourceEnums.find(el => el.key === value);
      return r ? r.value : '/';
    },
    getDriverResource(value) {
      const r = this.enums.DriverResourceEnums.find(el => el.key === value);
      return r ? r.value : '/';
    },
    getUseCar(str) {
      let color = str === '预约' ? '#FF4143' : '#40BB5A'
      return {
        text: str,
        color
      }
    },
    getCarStatus(value) {
      const r = this.enums.CarStatusEnums.find(el => el.key === value);
      const text = r ? r.value : '/';
      let color;
      switch (value) {
        case 100:
          color = '#40BB5A';
          break;
        case 200:
          color = '#FF4143';
          break;
        case 300:
          color = '#FF8000';
          break;
        case 400:
          color = '#999999';
          break;
        default:
          color = '#999999';
      }
      return {
        text,
        color
      }
    },
    getDriverStatus(value) {
      const r = this.enums.DriverStatusEnums.find(el => el.key === value);
      const text = r? r.value : '/';
      let color;
      switch (value) {
        case 100:
          color = '#40BB5A';
          break;
        case 400:
          color = '#FF4143';
          break;
        default:
          color = '#999999';
      }
      return {
        text,
        color
      }
    }
  }
}
</script>

<style lang="less" scoped>
.custom-table {
  box-sizing: border-box;
  border: 1px solid #eee;
  border-radius: 4px;
  border-bottom: none;
}
</style>
