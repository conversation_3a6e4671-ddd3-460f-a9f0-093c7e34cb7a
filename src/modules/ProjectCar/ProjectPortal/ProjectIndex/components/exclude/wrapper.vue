<template>
  <div ref="wrapperRef" class="wrapper-item full-width">
    <header class="flex col-center row-between full-width">
      <div class="title">{{title}}</div>
      <div class="flex col-center row-center">
        <fks-select v-if="showSelect" size="mini" v-model="timeDimension" filterable clearable>
          <fks-option
            v-for="(option, index) in options"
            :key="option.code"
            :value="option.key"
            :label="option.value"
          />
        </fks-select>
        <i v-if="showFullscreen && !fullScreen" class="fks-icon-full-screen font-icon" @click="handleOpen" />
      </div>

    </header>
    <div ref="wrapperChartRef" v-if="showSelect" class="flex-grow-1 full-width">
      <slot name="time" :time="timeDimension" :fullScreen="fullScreen" />
    </div>
    <div v-else class="flex-grow-1 full-width">
      <slot />
    </div>
    <fks-dialog
      :visible.sync="fullScreen"
      :before-close="beforeClose"
      :title="title"
      :close-on-click-modal="true"
    >
      <div ref="wrapperDialogRef" class="wrapper-dialog-container">

      </div>
    </fks-dialog>
  </div>
</template>

<script>
import {mapState} from "vuex";

export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    showSelect: {
      type: Boolean,
      default: true
    },
    showFullscreen: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timeDimension: 600,
      fullScreen: false
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    options() {
      return this.enums?.DimensionEnums.filter(item => item.key !== 0) || []
    }
  },
  methods: {
    beforeClose(done) {
      this.fullScreen = false;
      done();
    },
    handleOpen() {
      this.$nextTick(() => {
        this.fullScreen = true;
      })
    }
  },
  watch: {
    fullScreen(val) {
      setTimeout(() => {
        const parentContainer = this.$refs.wrapperRef
        const content = this.$refs.wrapperChartRef;
        const dialog = this.$refs.wrapperDialogRef;
        if (val) {
          // 确保浏览器支持 ViewTransition API
          if ('startViewTransition' in document) {
            dialog.appendChild(content)
          } else {
            // alert('Your browser does not support ViewTransition API');
          }
        } else {
          // 确保浏览器支持 ViewTransition API
          if ('startViewTransition' in document) {
            parentContainer.appendChild(content)
          } else {
            // alert('Your browser does not support ViewTransition API');
          }
        }
      })
    }
  }

}
</script>

<style lang="less" scoped>
/deep/ .fks-input {
  width: 148px;
}
/deep/ .fks-input__inner {
  border-radius: 6px;
  font-size: 14px;
  color: #555555;
  font-weight: normal;
}
/deep/ .fks-select .fks-input .fks-select__caret {
  color: #666666;
}
.font-icon {
  font-size: 22px;
  cursor: pointer;
  margin-left: 10px;
  display: inline-block;
  color: #D8D8D8;
}
.wrapper-item {
  border-radius: 6px;
  border: 1px solid #DFE0E2;
  height: 330px;
  opacity: 1;
  background: #FFFFFF;
  box-sizing: border-box;
  padding: 20px 30px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  header {
    margin-bottom: 15px;
  }

  .title {
    line-height: normal;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    color: #333333;
    position: relative;
    &:after {
      content: '';
      position: absolute;
      display: block;
      bottom: 3px;
      left: 0;
      right: 0;
      width: 100%;
      height: 5px;
      background: rgba(60, 131, 255, 0.2);
      border-radius: 4px;
    }
  }
}
</style>
