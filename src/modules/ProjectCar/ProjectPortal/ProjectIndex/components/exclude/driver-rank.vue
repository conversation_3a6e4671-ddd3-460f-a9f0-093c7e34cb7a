<template>
  <fks-empty v-if="list.length === 0" />
  <div v-else class="driver-rank-container" v-loading="loading">
    <div
      v-for="(item, index) in list"
      class="item"
      :key="index"
    >
      <div class="left flex col-center">
        <img v-if="index < 3" :src="require('@/assets/img/medal.png')" width="15px" alt="#">
        <div v-else class="num">{{index + 1}}</div>
        <div class="name">{{item.name}}</div>
      </div>
      <div class="right flex col-center">
        <stars :value="item.star"/>
        <div class="score">{{item.score}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {getDriverRate} from "@modules/ProjectCar/ProjectPortal/ProjectIndex/api";
import Stars from "@components/stars.vue";

export default {
  name: 'driver-rank',
  components: {Stars},
  props: ['time'],
  data() {
    return {
      isEmpty: false,
      list: [],
      loading: false
    }
  },
  watch: {
    time: {
      immediate: true,
      handler(newVal) {
        this.loading = true;
        getDriverRate(newVal).then(res => {
          if (res.status) {
            this.list = res.data || [];
            this.list.sort((a, b) => b.score - a.score);
          }
        }).finally(() => {
          this.loading = false
        })
      }
    }
  },
}
</script>

<style lang="less" scoped>
.driver-rank-container {
  display: flex;
  flex-direction: column;
  max-height: 260px;
  overflow-y: auto;
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 4px;
    padding: 8px 12px 8px 8px;
    background: linear-gradient(90deg, rgba(84, 131, 247, 0.1) 0%, rgba(84, 131, 247, 0.02) 100%);
    margin-bottom: 10px;
    &:nth-last-child {
      margin-bottom: 0;
    }

    .left {
      img {
        margin-left: 6px;
      }
      .num {
        margin-left: 6px;
        text-align: center;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        opacity: 1;
        background: rgba(84, 131, 247, 0.15);
        font-family: Source Han Sans;
        font-size: 10px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0;
        color: #5483F7;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .name {
        margin-left: 16px;
        font-family: Source Han Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0;
        color: rgba(0, 0, 0, 0.65);
        width: 100px;
      }
    }
    .right {
      .score {
        margin-left: 20px;
        font-family: Source Han Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: normal;
        text-align: right;
        letter-spacing: 0;
        color: rgba(0, 0, 0, 0.65);
        width: 20px;
        white-space: nowrap;
      }
    }
  }
}
</style>
