<template>
  <fks-empty v-if="tagList.length === 0 && tagList1.length === 0" />
  <div v-else class="infos">
    <div class="pie">
      <arc-pie ref="arcPie" id="carInfo" :data="pieData('carResource',list)" :title="title"/>
      <arc-pie ref="arcPie" id="driverInfos" :data="pieData('driverResource2',list1)" :title="title1"/>
    </div>
    <div class="table">
      <tag-table :data="list" />
      <tag-table :data="list1" />
    </div>
    <div v-show="false">
      {{ opt }}
    </div>

  </div>
</template>

<script>
import TagList
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/tag-list.vue";
import ArcPie from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/arc-pie.vue";
import {mapState} from "vuex";
import TagTable
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/tag-table.vue";

export default {
  name: 'info-panel',
  components: {TagTable, ArcPie, TagList},
  props: ['list', 'title','id','list1','title1'],
  computed: {
    ...mapState('CarApply', ['enums']),
    pieData() {
      return (category,list) => {
        if (list.length === 0) return [];
        return list
          .filter(item => item[category])
          .reduce((acc, item) => {
            // 查找是否已有记录
            const found = acc.find(entry => entry.name === item[category]);
            if (found) {
              found.value += 1; // 如果存在，计数加 1
            } else {
              acc.push({name: item[category], value: 1}); // 如果不存在，新增记录
            }
            return acc;
          }, []).map(item => {
            if (category === 'carResource') {
              const r = this.enums.CarResourceEnums.find(el => el.key === item.name);
              return {
                ...item,
                name: r.value,
              }
            } else if(category === 'driverResource2') {
              const r = this.enums.DriverResourceEnums.find(el => el.key === item.name);
              return {
                ...item,
                name: r.value,
              }
            }
          });
      }


    },
    tagList() {
      return this.list.map(item => {
        return Object.keys(item)
          // .filter(key => Boolean(item[key]))
          .map(key => {
            let value;
            let type = '';
            if (key === 'carAge') {
              value = item[key] ? `车龄：${item[key]}年` : '车龄：/ 岁';
            } else if (key === 'carType') {
              value = item[key] ? `${item[key]}` : '/';
            } else if (key === 'carResource') {
              const r = this.enums.CarResourceEnums.find(el => el.key === item[key]);
              value = r ? `${r.value}` : '/';
            } else if (key === 'carStatus') {
              const r = this.enums.CarStatusEnums.find(el => el.key === item[key]);
              value = r ? r.value : '/';
              type = item[key] === 100 ? 'success' : 'danger';
            } else if (key === 'carXslcLj') {
              value = item[key] ? `${item[key]}公里` : '/ 公里';
            } else if (key === 'useCar') {
              value = item[key] ? item[key] : '/';
              type = item[key] === '空闲' ? 'success' : 'danger';
            } else {
              value = item[key] ? item[key] : '/';
            }
            return {
              text: value,
              type: key
            };
          })
      });
    },
    tagList1() {
      return this.list1.map(item => {
        return Object.keys(item)
          // .filter(key => Boolean(item[key]))
          .map(key => {
            let value;
            let type = '';
            if (key === 'driverAge') {
              value = item[key] ? `驾龄：${item[key]}年` : '驾龄：/ 年';
            } else if (key === 'driverAge2') {
              value = item[key] ? `年龄：${item[key]}岁` : '年龄：/ 岁';
            } else if (key === 'driverResource2') {
              const r = this.enums.DriverResourceEnums.find(el => el.key === item[key]);
              value = r ? `${r.value}` : '/';
            } else if (key === 'driverStatus') {
              const r = this.enums.DriverStatusEnums.find(el => el.key === item[key]);
              value = r ? r.value : '/';
              type = item[key] === 100 ? 'success' : 'danger';
            } else {
              value = item[key] ? item[key] : '/';
            }
            return {
              text: value,
              type
            }
          })
      });
    },
    opt() {
      console.log(this.list,this.list1,'===========list===list1')
    }
  },
  methods: {
    resizeChart() {
      this.$refs.arcPie.resizeChart();
    }
  }
}
</script>

<style lang="less" scoped>
.infos {
  display: grid;
  grid-template-columns: 22% 78%;
  grid-column-gap: 20px;
  height: 100%;
}
.pie {
  display: grid;
  grid-template-columns: 50% 50%;
  // grid-column-gap: 20px;
  height: 100%;
}
.table {
  display: grid;
  grid-template-columns: 51% 47%;
  grid-column-gap: 10px;
  height: 100%;
}
</style>
