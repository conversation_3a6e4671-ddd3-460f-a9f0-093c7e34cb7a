<template>
  <fks-empty v-if="isEmpty"/>
  <div v-else class="car-data-container full-height" v-loading="loading">
    <div class="charts">
      <normal-pie
        id="miles"
        ref="normalPie1Ref"
        :data="data1"
        :title-config="titleConfig1"
        :center="['50%', '50%']"
        :radius="90"
        :unit="milesUnit"
        style="max-width: 130px"
      />
      <normal-pie
        id="times"
        ref="normalPie2Ref"
        :data="data2"
        :title-config="titleConfig2"
        :center="['50%', '50%']"
        :radius="90"
        :unit="timesUnit"
        style="max-width: 130px"
      />
      <normal-pie
        id="totalFee"
        ref="normalPie3Ref"
        :data="data3"
        :title-config="titleConfig3"
        :center="['50%', '50%']"
        :radius="90"
        :unit="totalFeeUnit"
        style="max-width: 130px"
      />
    </div>
    <div class="flex-grow-1 flex flex-column">
      <div class="titles">
        <span>行驶里程</span>
        <span>出车次数</span>
        <span>总费用</span>
      </div>
      <div class="legends flex-grow-1" style="overflow: auto;max-height: 80px">
        <div
          v-for="(legend, index) in legends"
          :key="index"
          :class="{active: legend.active}"
          class="legend-item flex col-center"
          @click="handleLegendClick(legend)"
        >
          <div :style="{backgroundColor: legend.active ? legend.color : '#eeeeee'}" class="spot"/>
          <div class="label">{{ legend.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NormalPie
  from "@modules/ProjectCar/ProjectPortal/ProjectIndex/components/exclude/normal-pie.vue";
import {getCarInfoData} from "@modules/ProjectCar/ProjectPortal/ProjectIndex/api";
import {colorList, sleep} from "@utils/util";
import EmptyData from "@components/EmptyData/index.vue";

export default {
  name: 'CarData',
  components: {EmptyData, NormalPie},
  props: ['time'],
  data() {
    return {
      loading: false,
      isEmpty: false,
      legends: [],
      data1: [],
      data2: [],
      data3: [],
      milesUnit: '',
      timesUnit: '',
      totalFeeUnit: '',
      titleConfig1: {
        text: '',
        textStyle: {
          color: '#333333',
          fontSize: 14,
          fontWeight: 400,
        },
        left: 'center',
        top: 'center'
      },
      titleConfig2: {
        text: '',
        textStyle: {
          color: '#333333',
          fontSize: 14,
          fontWeight: 400,
        },
        left: 'center',
        top: 'center'
      },
      titleConfig3: {
        text: '',
        textStyle: {
          color: '#333333',
          fontSize: 14,
          fontWeight: 400,
        },
        left: 'center',
        top: 'center'
      }
    }
  },
  methods: {
    handleLegendClick(legend) {
      const chartRef = this.$refs.normalPie1Ref.$refs.chartRef.chart;
      const chartRef2 = this.$refs.normalPie2Ref.$refs.chartRef.chart;
      const chartRef3 = this.$refs.normalPie3Ref.$refs.chartRef.chart;
      legend.active = !legend.active;
      [chartRef, chartRef2, chartRef3].forEach(ref=> {
        ref.dispatchAction({
          type: 'legendToggleSelect',
          name: legend.name
        })
      })
    },
  },
  watch: {
    time: {
      immediate: true,
      async handler(newVal) {
        try {
          this.loading = true;
          const res = await getCarInfoData(newVal);
          if (res.status) {
            const {miles, times, fees} = res.data;
            if (res.data && Object.keys(res.data).length > 0) {
              this.isEmpty = false;
              this.legends = miles.list.map((item, index) => {
                const color = colorList[index % colorList.length];
                return {name: item.name, color: Array.isArray(color) ? color[1] : color, active: true}
              });
              this.data1 = miles.list;
              this.titleConfig1.text = miles.total + miles.unit;
              this.milesUnit = miles.unit;

              this.data2 = times.list;
              this.titleConfig2.text = times.total + times.unit;
              this.timesUnit = times.unit;

              this.data3 = fees.list;
              this.titleConfig3.text = fees.total + fees.unit;
              this.totalFeeUnit = '元';
            } else {
              this.isEmpty = true;
            }
          }
        } catch (e) {
          throw e;
        } finally {
          this.loading = false;
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.car-data-container {
  display: flex;
  flex-direction: column;

  .charts {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-around;
    height: 128px;
    width: 100%;
  }

  .titles {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0px;
    color: #333333;
    text-align: center;
    margin-bottom: 30px;
  }

  .legends {
    display: grid;
    @media (min-width: 1200px) and (max-width: 1400px) {
      grid-template-columns: repeat(3, 1fr);
    }
    grid-template-columns: repeat(4, 1fr);
    grid-row-gap: 5px;

    .legend-item {
      margin: 0 auto;
      cursor: pointer;
      user-select: none;

      &.active {
        .label {
          color: #333333;
        }
      }
      .spot {
        width: 8px;
        height: 8px;
        border-radius: 2px;
        margin-right: 8px;
      }


      .label {
        font-family: Source Han Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0;
        color: #33333355;
      }
    }

  }
}
</style>
