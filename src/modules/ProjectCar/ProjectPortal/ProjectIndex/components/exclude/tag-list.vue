<template>
  <div class="list-container">
    <div
      v-for="(listItem, index) in list"
      :key="index"
      class="list-item"
    >
      <div
        v-for="tag in listItem"
        :key="tag.text"
        :class="['tag', { 'tag-with-type': tag.type }]"
        :style="{
          color: getColorByType(tag.type)
        }"
        class="tag"
      >
        {{tag.text}}
      </div>
    </div>
  </div>
</template>

<script>
import {hexToRgba} from "@utils/util";
export default {
  name: 'tag-list',
  props: ['list'],
  methods: {
    getBackgroundColor(color) {
      return hexToRgba(color, 0.1);
    },
    getColorByType(type) {
      switch (type) {
        case 'success':
          return '#00C25E';
        case 'danger':
          return '#FF4143';
        // default:
        //   return '#3C83FF'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.list-container {
  max-height: 250px;
  overflow-y: auto;
  /* 如果只需要纵向滚动，可以不用在这里设置 table 布局 */
}

.list-item {
  display: table;
  table-layout: fixed; /* 保证列宽是固定的，否则某个单元格内容过长会挤占其他单元格空间 */
  width: 100%;
  border-top: 1px solid #EEEEEE;
  box-sizing: border-box;

  &:last-child {
    border-bottom: 1px solid #EEEEEE;
  }

  .tag {
    display: table-cell;
    /* 固定列宽，或者按百分比均分。如果不知道一行多少个 tag，可以酌情调整。 */
    /* 举例：如果你确定一行里就是 4 个 tag，可以这样写：width: 25%; */
    padding: 14px;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    color: #333333;
    text-align: left;

    /* 处理内容过长时显示省略号 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle; /* 可根据需求微调 */
    &.tag-with-type {
      width: 60px !important;
    }
  }
}

</style>
