<template>
  <div class="arc-container">
    <basic-echarts ref="charts" :id="id" :option="pieOption" height="auto" immediate />
    <div class="title second-text">{{title}}</div>
  </div>
</template>

<script>
import BasicEcharts from '@/components/Chart/index.vue';
import {richTextGenerator, sleep} from '@/utils/util';

const colorList = [
  { colors: ['#399AEA', '#B2C9FF'] },
  { colors: ['#00D186', '#9ADCFA'] },
  { colors: ['#F6D365', '#FDA085'] },
];
const center = ['50%', '50%'];
export default {
  name: 'arc-pie',
  components: { BasicEcharts },
  props: ['id', 'data', 'title'],
  data() {
    return {
      pieOption: {}
    };
  },
  computed: {
    sum() {
      return this.data.reduce((total, item) => {
        return total + item.value;
      }, 0);
    }
  },
  mounted() {
    this.setOption();
  },
  methods: {
    resizeChart() {
      let option = this.setOption();
      this.$set(this.$data, 'pieOption', option);
    },
    hexToRgba(hex, opacity) {
      return (
        'rgba(' +
        parseInt('0x' + hex.slice(1, 3)) +
        ',' +
        parseInt('0x' + hex.slice(3, 5)) +
        ',' +
        parseInt('0x' + hex.slice(5, 7)) +
        ',' +
        opacity +
        ')'
      );
    },
    setOption() {
      const that = this;
      // 颜色16进制换算rgba,添加透明度

      // 颜色系列
      const color = colorList.map(item => item.colors[0]);
      let color1 = [];
      let color2 = [];
      // 设置每层圆环颜色和添加间隔的透明色
      color.forEach((item) => {
        color1.push(item, 'transparent');
        color2.push(this.hexToRgba(item, 0.4), 'transparent');
      });
      let data1 = [];
      let sum = 0;
      // 根据总值设置间隔值大小
      this.data.forEach((item) => {
        sum += Number(item.value);
      });
      // 给每个数据后添加特定的透明的数据形成间隔
      this.data.forEach((item, index) => {
        if (item.value !== 0) {
          data1.push(item, {
            name: '',
            value: sum / 70,
            labelLine: {
              show: false,
              lineStyle: {
                color: 'transparent'
              }
            },
            itemStyle: {
              color: 'transparent'
            }
          });
        }
      });
      // 每层圆环大小
      let radius2 = ['55%', '70%'];
      let radius3 = ['70%', '80%'];
      return {
        // legend: {
        //   data: this.data.map((item) => item.name),
        //   icon: 'roundRect',
        //   top: 0,
        //   left: 'center',
        //   orient: 'horizontal',
        // },
        tooltip: {
          confine: true,
          borderRadius: 4,
          backgroundColor: '#fff',
          borderColor: '#fff',
          formatter: (params) => {
            if (params.name !== '') {
              // params.color 是该系列对应的颜色
              // 小色块可以通过一个 span，用 background-color 设置
              return `
                <div">
                  <span
                    style="
                      display:inline-block;
                      margin-right:6px;
                      width:8px;
                      height:8px;
                      border-radius:2px;
                      background-color:${params.color};
                    "
                  ></span>
                  ${params.name} &nbsp; ${params.value}
                </div>
              `;
            }
          }
        },
        series: [
          // 最外层圆环
          {
            type: 'pie',
            radius: radius3,
            center,
            hoverAnimation: false,
            startAngle: 90,
            selectedMode: 'single',
            selectedOffset: 0,
            itemStyle: {
              normal: {
                color: (params) => {
                  return color1[params.dataIndex];
                }
              }
            },
            label: {
              show: true,
              position: 'center',
              formatter: function () {
                const value = richTextGenerator('a', that.sum);
                return `${value}\n\n总计`;
              },
              rich: {
                a: {
                  align: 'center',
                  fontWeight: 400,
                  fontSize: 14,
                  color: '#333333',
                  fontFamily: 'DINAlternate'
                }
              }
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 60,
              lineStyle: {
                color: '#fff',
                width: 2
              }
            },
            data: data1,
            z: 666
          },
          {
            type: 'pie',
            radius: radius2,
            center,
            hoverAnimation: false,
            startAngle: 90,
            selectedMode: 'single',
            selectedOffset: 0,
            itemStyle: {
              normal: {
                color: (params) => {
                  return color2[params.dataIndex];
                }
              }
            },
            label: {
              show: false,
              formatter: '{b}' + ' ' + '{c}'
            },
            data: data1,
            z: 666
          },
          {
            // 内圆
            type: 'pie',
            name: 'pie1',
            radius: '50%',
            center,
            z: 3,
            startAngle: 180,
            itemStyle: {
              normal: {
                color: 'rgba(77, 129, 255, 0.05)',
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            },
            hoverAnimation: false,
            label: {
              show: false
            },
            tooltip: {
              show: false
            },
            data: [
              {
                name: 'rgba(77, 129, 255, 1)',
                value: 50,
                itemStyle: {
                  shadowColor: 'rgba(127, 171, 223, 0.2)',
                  shadowBlur: 14
                }
              }
            ],
            animationType: 'scale'
          }
        ]
      };
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      async handler(newVal) {
        await sleep(1000);
        this.pieOption = this.setOption();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.arc-container {
  background-image: url('../../../../../../assets/img/statisticalCharts/pie-circle.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
  height: 150px;
  width: 150px;
  margin: auto;
  position: relative;

  .title {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
