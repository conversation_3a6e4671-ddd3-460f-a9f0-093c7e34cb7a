<template>
  <fks-empty v-if="isEmpty"/>
  <div v-else class="full-height flex flex-column col-center">
    <ul id="legend">
      <li
        v-for="item in legends"
        :key="item.key"
        :data-id="item.key"
        @click="handleLegendClick(item)"
      >
        <div :class="{rect: true, [item.color]: item.active, disable: !item.active}"/>
        <span :style="{color: item.active ? '#333333' : '#33333355'}">{{ item.name }}</span>
      </li>
    </ul>
    <basic-echarts
      id="car-fee"
      ref="carFeeRef"
      v-loading="loading"
      :option="option"
      class="flex-grow-1"
      height="237px"
      immediate
    />
  </div>
</template>
<script>
import BasicEcharts from "@components/Chart/index.vue";
import {
  getDataZoomCommonConfig,
  xAxisCommonConfig,
  yAxisCommonConfig
} from "@components/Chart/common";
import * as echarts from "echarts";
import {getUseCarFee} from "@modules/ProjectCar/ProjectPortal/ProjectIndex/api";
import Sortable from 'sortablejs'

const nameTable = {
  vehicleRentalFee: '车辆租赁费',
  driverRentalFee: '司机租赁费',
  vehicleUsageFee: '车辆使用费',
  maintenanceFee: '维修保养费'
}
const barColorSet = {
  vehicleRentalFee: ['#6cc7ff', '#306bfe'],
  driverRentalFee: ['#ffdaaf', '#f8a441'],
  vehicleUsageFee: ['#73f0a1', '#1ac477'],
  maintenanceFee: ['#b3a0fe', '#cec2ff']
}
export default {
  components: {BasicEcharts},
  props: ['time','fullScreen'],
  data() {
    return {
      loading: false,
      option: {},
      list: [],
      nameTable,
      isEmpty: false,
      sort: Object.keys(nameTable),
      legends: Object.keys(nameTable).map(key => {
        return {
          name: nameTable[key],
          color: key,
          active: true,
          key
        }
      }),
    }
  },
  methods: {
    getSum() {
      const actives = this.legends.filter(item => item.active).map(item => item.key);
      return {
        show: true,
        position: 'top',
        color: '#333',
        fontSize: 12,
        formatter: (params) => {
          const calcItem = this.list.find(item => item.name === params.name);
          const sum = actives.reduce((acc, cur) => {
            return cur in calcItem? acc + calcItem[cur] : acc;
          }, 0)
          return sum.toFixed(2) || 0;
        }
      }
    },
    handleLegendClick(legend) {
      legend.active = !legend.active;
      const chartRef = this.$refs.carFeeRef.chart;
      const action = legend.active ? 'legendSelect' : 'legendUnSelect';
      chartRef.dispatchAction({
        type: action,
        name: legend.key
      })
      this.setOption(this.list);
    },
    setOption(data) {
      // 定义X轴数据
      const xAxisData = data.map(function (item) {
        return item.name;
      });
      // 定义系列数据
      const seriesData = this.legends.map(item => {
        return {
          name: item.key,
          type: 'bar',
          barWidth: 10,
          stack: 'fee',
          itemStyle: {
            borderRadius: [2, 2, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {offset: 0, color: barColorSet[item.key][0]},
              {offset: 1, color: barColorSet[item.key][1]}
            ]),
          },
          data: data.map(el => el[item.key]),
          progressive: 0
        }
      })
        .filter(item => {
          const legendItem = this.legends.find(el => el.key === item.name);
          return legendItem.active;
        })
        .sort((item1, item2) => {
        // 获取 item1 和 item2 在数组 a 中的索引位置
        const index1 = this.sort.indexOf(item1.name);
        const index2 = this.sort.indexOf(item2.name);

        // 按照索引升序排列
        return index1 - index2;
      });

      const actives = this.legends.filter(item => item.active).map(item => item.key);
      const maxValue = Math.max(...data.map(item => {
        return actives.reduce((acc, cur) => {
          return acc + (cur in item ? item[cur] : 0);
        }, 0);
      }));
      seriesData[seriesData.length - 1].label = {
        show: true,
        position: 'top',
        color: '#333',
        fontSize: 12,
        formatter: (params) => {
          const calcItem = this.list.find(item => item.name === params.name);
          const sum = actives.reduce((acc, cur) => {
            return cur in calcItem? acc + calcItem[cur] : acc;
          }, 0)
          return sum.toFixed(2) || 0;
        }
      }
      seriesData.push({
        name: 'shadow',
        z: 1,
        tooltip: {
          show: false
        },
        emphasis: {
          disabled: true
        },
        type: 'bar',
        barGap: '-175%',
        data: data.map(item => maxValue),
        barWidth: 26,
        itemStyle: {
          color: 'rgba(241, 245, 254, 0.6)'
        }
      })

      const dataZoomConfig = getDataZoomCommonConfig(data.length,this.fullScreen);
      this.option = {
        grid: {
          bottom: '20%',
          left: '10%',
          top: '15%',
          right: '5%'
        },
        dataZoom: [
          {
            ...dataZoomConfig,
            bottom: 10
          }
        ],
        tooltip: {
          borderRadius: 4,
          backgroundColor: '#fff',
          borderColor: '#fff',
          formatter: (params) => {
            const {marker, name, seriesName} = params;
            const value = this.list.find(item => item.name === name)[seriesName];
            return `<div style="display: flex;flex-direction: column">
              <div>${this.nameTable[seriesName]}</div>
              <div style="display: flex;align-items: center">
                <div>${marker}${name}</div>
                <div style="font-weight: bold;margin-left: 10px">${value}元</div>
              </div>
            </div>`
          }
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          ...xAxisCommonConfig,
        },
        yAxis: {
          ...yAxisCommonConfig,
          type: 'value',
          name: '(元)'
        },
        series: seriesData,
        legend: {
          show: false
        }
      }
    }
  },
  watch: {
    time: {
      immediate: true,
      handler(newVal) {
        this.loading = true;
        getUseCarFee(newVal).then(res => {
          if (res.status) {
            this.isEmpty = res.data.length === 0;
            this.list = res.data.map(item => {
              return {
                ...item,
                sum: item.vehicleRentalFee + item.driverRentalFee + item.vehicleUsageFee + item.maintenanceFee
              }
            }) || [];
            this.list.sort((a, b) => b.sum - a.sum)
            this.setOption(this.list)
          }
        }).finally(() => {
          this.loading = false;
        })
      }
    },
    fullScreen:{
      immediate: true,
      handler(newVal) {
        this.setOption(this.list)
      }
    }
  },
  mounted() {
    const ul = document.getElementById('legend');
    const sortable = Sortable.create(ul, {
      animation: 150,
      onEnd: () => {
        // 获取当前排列顺序
        this.sort = sortable.toArray();
        this.setOption(this.list)
      }
    });
  }
}
</script>
<style lang="less" scoped>
ul {
  list-style: none;
  display: flex;
  padding: 0;
  margin: 0;
  align-items: center;
  flex-wrap: wrap;

  li {
    display: flex;
    align-items: center;
    margin-right: 10px;
    font-size: 14px;
    cursor: pointer;
    white-space: nowrap;

    .rect {
      border-radius: 2px;
      width: 8px;
      height: 8px;
      margin-right: 8px;

      &.disable {
        background: #EEEEEE;
      }

      &.vehicleRentalFee {
        background: linear-gradient(315deg, rgba(18, 139, 228, 0.3018) 0%, #128BE4 100%);
      }

      &.driverRentalFee {
        background: linear-gradient(315deg, rgba(241, 156, 54, 0.3018) 0%, #F19C36 100%);
      }

      &.vehicleUsageFee {
        background: linear-gradient(315deg, rgba(9, 172, 69, 0.3018) 0%, #0CD355 99%);
      }

      &.maintenanceFee {
        background: linear-gradient(315deg, rgba(153, 130, 247, 0.3018) 0%, #9982F7 100%);
      }
    }
  }


}
</style>
