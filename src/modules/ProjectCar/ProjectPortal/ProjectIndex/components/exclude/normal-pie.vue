<template>
  <basic-echarts ref="chartRef" :id="id" :height="height" :option="option" immediate/>
</template>

<script>
import BasicEcharts from '@/components/Chart/index.vue';
import {colorList, richTextGenerator} from "@utils/util";
import * as echarts from "echarts";

export default {
  name: 'NormalPie',
  props: {
    id: {
      type: String,
      required: true
    },
    titleConfig: {
      type: Object,
      default: () => ({show: false})
    },
    label: {
      type: String
    },
    data: {
      type: Array,
      required: true
    },
    legend: {
      type: Object
    },
    height: {
      type: String,
      default: 'auto'
    },
    center: {
      type: Array,
      default: () => ['50%', '40%']
    },
    radius: {
      type: Number,
      default: 80
    },
    unit: {
      type: String
    }
  },
  components: {BasicEcharts},
  data() {
    return {
      option: {}
    }
  },
  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      if (this.$refs.chartRef) {
        this.$refs.chartRef.resizeChart();
      }
    },
    setOption(newVal) {
      let option =  {
        tooltip: {
          trigger: 'item',
          borderRadius: 4,
          backgroundColor: '#fff',
          borderColor: '#fff',
          formatter: (params) => {
            if (params.name !== '') {
              // params.color 是该系列对应的颜色
              // 小色块可以通过一个 span，用 background-color 设置
              const color = Array.isArray(params.color)? params.color[1] : params.color;
              return `
                <div>
                  ${params.marker}
                  ${params.name} &nbsp; ${params.value} ${this.unit || ''}
                </div>
              `;
            }
          }
        },
        title: {
          show: false
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['60%', '70%'],
            center: ['50%', '40%'],
            hoverAnimation: true,
            color: colorList,
            labelLine: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                color: (colors) => {
                  const index = colors.dataIndex % (colorList.length)
                  const c = colorList[index];
                  if (Array.isArray(c)) {
                    return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: c[0] },
                      { offset: 1, color: c[1] }
                    ])
                  } else {
                    return c;
                  }
                }
              }
            },
            label: {
              show: false
            },
            data: []
          },
          {
            // 内圆
            type: 'pie',
            name: 'pie3',
            radius: '80%',
            center: ['50%', '40%'],
            z: 1,
            startAngle: 180,
            itemStyle: {
              normal: {
                color: 'white',
                shadowBlur: '14px',
                shadowColor: 'red',
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            },
            hoverAnimation: false,
            label: {
              show: false
            },
            tooltip: {
              show: false
            },
            data: [
              {
                name: 'white',
                value: 100,
                itemStyle: {
                  color: 'rgba(93,157,199,0.1)',
                  shadowColor: 'rgba(127, 171, 223, 0.2)',
                  shadowBlur: 24
                }
              }
            ],
            animationType: 'scale'
          },
          {
            // 内圆
            type: 'pie',
            name: 'pie1',
            radius: '50%',
            center: ['50%', '40%'],
            z: 3,
            startAngle: 180,
            itemStyle: {
              normal: {
                color: 'white',
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            },
            hoverAnimation: false,
            label: {
              show: false
            },
            tooltip: {
              show: false
            },
            data: [
              {
                name: 'white',
                value: 50,
                itemStyle: {
                  shadowColor: 'rgba(127, 171, 223, 0.2)',
                  shadowBlur: 14
                }
              }
            ],
            animationType: 'scale'
          }
        ]
      }
      option.series[0].data = newVal;
      option.title = this.titleConfig || {show: false};
      option.legend = this.legend ? {
        ...this.legend,
        data: newVal.map(item => {
          return {name: item.name, icon: 'roundRect'}
        })
      } : {show: false};
      const radius = this.radius || 80;
      option.series.forEach((item, index) => {
        item.center = this.center;
        if (index === 0) {
          item.radius = [radius - 20, radius - 10].map(item => item + '%');
        } else if (index === 1) {
          item.radius = radius + '%'
        } else if (index === 2) {
          item.radius = radius - 30 + '%'
        }
      })

      this.option = option;
    }
  },
  watch: {
    data: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal.length) {
          this.setOption(newVal);
        }
      }
    }
  }
}
</script>
