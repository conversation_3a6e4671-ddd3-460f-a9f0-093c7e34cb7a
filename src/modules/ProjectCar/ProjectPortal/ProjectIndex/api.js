import request from '@/utils/request'
import store from '@/store'
import * as StateTypes from '@/store/State/stateTypes'

export async function getApplicationChartsData(dimension) {
  const projectId = store.state[StateTypes.PORTAL].id;
  return request({
    url: '/vehicle-dispatch/vd/stats/homePage/apply',
    method: 'post',
    data: {
      dimension,
      projectIds: [projectId]
    }
  })
}

export async function getCarInfoData(time) {
  const projectId = store.state[StateTypes.PORTAL].id;
  return request({
    url: '/vehicle-dispatch/vd/stats/homePage/usingCarData',
    method: 'post',
    data:  {
      dimension: time,
      projectIds: [projectId]
    }
  })
}

export async function getDriverRate(time) {
  const projectId = store.state[StateTypes.PORTAL].id;
  return request({
    url: '/vehicle-dispatch/vd/stats/homePage/driverEvaluate',
    method: 'post',
    data:  {
      dimension: time,
      projectIds: [projectId]
    }
  })
}

export async function getMileageData(time) {
  const projectId = store.state[StateTypes.PORTAL].id;
  return request({
    url: '/vehicle-dispatch/vd/stats/homePage/mileageRanking',
    method: 'post',
    data:  {
      dimension: time,
      projectIds: [projectId]
    }
  })
}

export async function getUseCarFee(time) {
  const projectId = store.state[StateTypes.PORTAL].id;
  return request({
    url: '/vehicle-dispatch/vd/stats/homePage/usingCarFee',
    method: 'post',
    data:  {
      dimension: time,
      projectIds: [projectId]
    }
  })
}

export async function getFuelConsumption(time) {
  const projectId = store.state[StateTypes.PORTAL].id;
  return request({
    url: '/vehicle-dispatch/vd/stats/homePage/usingOilPer100Km',
    method: 'post',
    data:  {
      dimension: time,
      projectIds: [projectId]
    }
  })
}

export async function getCarData() {
  const projectId = store.state[StateTypes.PORTAL].id;
  return request({
    url: '/vehicle-dispatch/vd/stats/homePage/carDetailInfo',
    method: 'post',
    data:  {
      projectIds: [projectId]
    }
  })
}

export async function getDriverData() {
  const projectId = store.state[StateTypes.PORTAL].id;
  return request({
    url: '/vehicle-dispatch/vd/stats/homePage/driverDetailInfo',
    method: 'post',
    data:  {
      projectIds: [projectId]
    }
  })
}
