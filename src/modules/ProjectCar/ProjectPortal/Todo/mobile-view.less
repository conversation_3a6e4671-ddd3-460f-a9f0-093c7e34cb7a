@import "../../../CarApply/components/index.less";
.task-container {
  height: 100%;
  overflow: hidden;
  background: #f1f2f3;
}
/deep/ .fm-pull-refresh {
  overflow: visible;
}

.card-container {
  // height: calc(100% - 420px);
  height: calc(100% - 200px);
  overflow: auto;
  padding-left: 30px;
  padding-right: 30px;
}
.v-task-item {
  background-color: #fff;
  border-radius: 16px;
  margin-top: 30px;
  &__box {
    display: flex;
    flex-direction: column;

    &-thumbnail {
      width: 180px;
      height: 180px;
      margin-right: 30px;

      image {
        width: 100%;
        height: 100%;
      }
    }

    &-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
      overflow: hidden;

      .box-info__bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }

  .v-task-item__box + .v-task-item__box {
    margin-top: 30px;
    padding-top: 30px;
  }
  //修改滑块背景颜色
  /deep/.u-swipe-content {
    background-color: #fff;
    border-radius: 16px;
  }
}

// 卡片标题栏
.box-info__top {
  padding-left: 30px;
  border-bottom: solid 1px #f2f2f3;
  padding-bottom: 32px;
  display: flex;
  align-items: start;
  justify-content: space-between;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;

  .status-bar {
    padding: 6px 20px;
    background: linear-gradient(225deg, #DDDDDD 0%, #979DA0 100%);
    border-radius: 0 14px 0 14px;
    font-size: 24px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 52px;
    letter-spacing: 1px;
  }
}

// 标题
.box-info__title {
  font-weight: bold;
  font-size: 32px;
  /*display: block;*/
  padding-top: 32px;
  display: flex;
  align-items: center;
  // 指示块
  &-bar {
    width: 6px;
    height: 30px;
    background: #4545d1;
    border-radius: 4px;
    display: inline-block;
    vertical-align: middle;
  }
  // 标题内容
  &-text {
    margin-left: 16px;
    color: #333333;
    display: inline-block;
    vertical-align: middle;
    flex: 1;
  }
}

.box-info__attr {
  color: #999;
  font-size: 20px;
  margin-top: 20px;
}

// 卡片任务内容栏
.box-info__task-info {
  padding-top: 32px;
  padding-left: 48px;
  padding-right: 48px;
  padding-bottom: 32px;
}

// 任务名称
.box-info__task-name {
  font-size: 28px;
  color: #999999;
  display: flex;
}

.box-info__task-starter {
  font-size: 28px;
  color: #999999;
  display: flex;
  padding-top: 28px;

  .starter-text {
    margin-left: 64px;
  }
}

// 任务环节
.box-info__task-node {
  font-size: 28px;
  padding-top: 10px;
  color: #999999;
  display: flex;
}

.task-text {
  color: #333333;
  font-size: 28px;
  font-weight: 400;
  margin-left: 36px;
  flex: 1;
  white-space: nowrap; //不换行
  overflow: hidden; //超出隐藏
  text-overflow: ellipsis; // 超出部分省略表示
  padding-right: 20px;
}

/*.time-text {
  margin-bottom: 30px;
}*/
.state-text {
  margin-left: 92px;
}

.stop-text {
  color: #f32111;
}

// 任务时间
.box-info__task-time {
  padding-top: 28px;
  color: #999999;
  font-size: 28px;
  display: flex;
}
.box-info__task-state {
  /*padding-top: 10px;*/
  position: absolute;
  right: 32px;
  color: #999999;
  font-size: 28px;
  display: flex;
  display: inline-block;
  vertical-align: middle;
}
.state-text-finish {
  width: 86px;
  height: 30px;
  text-align: center;
  padding: 8px 12px;
  border-radius: 26px;
  background-color: #03BE8A;
  border: 1px solid #03BE8A;
  color: #fff;
}
.state-text-run {
  width: 86px;
  height: 30px;
  text-align: center;
  padding: 8px 12px;
  border-radius: 26px;
  background-color: #3C83FF;
  border: 1px solid #3C83FF;
  color: #fff;
}
.state-text-ts {
  width: 86px;
  height: 30px;
  text-align: center;
  padding: 8px 12px;
  border-radius: 26px;
  background-color: #FFA418 ;
  border: 1px solid #FFA418 ;
  color: #fff;
}
.state-text-waste {
  width: 86px;
  height: 30px;
  text-align: center;
  padding: 8px 12px;
  border-radius: 26px;
  background-color: #9BA0A3;
  border: 1px solid #9BA0A3;
  color: #fff;
}
.box-info__task-time {
  padding-bottom: 10px;
}

.box-info__item-label {
  width: 140px;
}

.box-info-warn {
  display: inline-block;
  vertical-align: middle;
  float: right;
  color: #ff4b4b;
  font-size: 24px;
  background-color: #ffeded;
  // padding: 3px 10px;
}

.task-btn-group {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f2f2f2;
  .btn {
    border-radius: 0;
    border: none;
    height: 88px;
    width: 50%;
    white-space: nowrap;
    line-height: 88px;
    text-align: center;
    vertical-align: middle;
    padding: 0px;
    // color: #fff;
    &::after {
      display: none;
    }
  }
  .enter {
    color: #fdac42;
    background-color: #fff;
    border-right: 1px solid #f2f2f2;
    font-size: 32px;
    // background-color: #FDAC42;
  }
  .entrust {
    color: #0097d8;
    background-color: #fff;
    font-size: 32px;
    // background-color: #0097D8;
  }
  .active {
    &:hover {
      background-color: #f0f0f0;
    }
  }
}
/deep/ .fm-search {
  //padding: 0 32px;
}

/deep/ .fm-search__content {
  //height: 74px;
  border-radius: 16px !important;
}
.box__task-time {
  padding-top: 16px;
  color: #999999;
  font-size: 28px;
  display: flex;
}
.icon-time {
  width: 48px;
  height: 48px;
}
.line_sty {
  border-top: 1px solid #f2f2f2;
  margin-top: 22px;
}
