import { mapGetters, mapMutations, mapState } from 'vuex'
import * as GetterTypes from "@store/Getter/getterTypes";
import { Toast } from 'fawkes-mobile-lib'

import EmptyData from '@/components/EmptyData/index.vue';
import DateTimePicker from '@/modules/FormCenter/components/DateTimePicker/index.vue';
import { circulationLists, relTaskLists, userTaskLists } from '../../../Todo/api'
export default {
  components: {
    EmptyData,
    DateTimePicker
  },
  data() {
    return {
      value: '',
      // tabs: ['任务待办', '流程跟踪', '表单查询'],
      tabs: ['任务待办', '流程跟踪'],
      displayList: [],
      taskList: [],
      flowList: [],
      formList: [],
      taskPage: 1,
      flowPage: 1,
      formPage: 1,
      pageSize: 20,
      finished: false,
      refreshing: false,
      loading: false,
      loadingFinished: [false, false, false],
      searchValue: '',
      currentTab: '任务待办',
      isBack: true,
      isMain: true
    }
  },
  computed: {
    ...mapGetters([GetterTypes.IS_NOT_PC]),
    ...mapState('CarApply', ['enums']),
  },
  created() {
    this.isMain = this.$storage.get('isMainTab') === 'true';
    this.isBack = this.$storage.get('isMainTab') === 'false' && !this[GetterTypes.IS_NOT_PC];
  },
  mounted() {
    this.isMobile && Toast.loading({
      message: '加载中...',
      forbidClick: true,
    })
    this.getList()
  },
  activated() {
    // 页面激活时移除默认的返回上一级路由处理事件，设置退出应用事件
    document.removeEventListener('backbutton', this.$back, false)
    document.addEventListener('backbutton', eventBackButton, false)
    this.getList()
  },
  deactivated() {
    // 页面隐藏时移除退出应用事件，恢复默认的返回上一级路由的处理事件
    document.removeEventListener('backbutton', eventBackButton, false)
    document.removeEventListener('backbutton', exitApp, false)
    document.addEventListener('backbutton', this.$back, false)
    this.pageReset()
  },
  methods: {
    ...mapMutations('CarApply', ['SET_LAST_ROUTE']),
    getReturnState(state) {
      const item = this.enums.ApplyFormApprovalReturnStateEnums.find(item => {
        return item.key === state
      })
      return item ? item.value : '';
    },
    getBackground(state) {
      switch (true) {
        case [4,5,6,10,13].findIndex(item => item === state) > -1:
          // 红色
          return 'linear-gradient(225deg, #ecc7ca 0%, #FF3F4C 100%)'
        case [7,8,9].findIndex(item => item === state) > -1:
          // 黄色
          return 'linear-gradient(225deg, #FCE15E 0%, #FF7F3C 100%)'
        case [0,11,12].findIndex(item => item === state) > -1:
          // 蓝色
          return 'linear-gradient(225deg, #6FCFFF 0%, #3C83FF 100%)'
        default:
          // 灰色
          return 'linear-gradient(225deg, #DDDDDD 0%, #979DA0 100%)'
      }
    },
    borderType(taskState) {
      switch (taskState) {
        case '0':
          return 'border: 2px solid; border-image: linear-gradient(180deg, rgba(60, 131, 255, 0.3), rgba(255, 255, 255, 1)) 2 2; clip-path: inset(0 round 5px);'
        case '1':
          return 'border: 2px solid; border-image: linear-gradient(180deg, rgba(3, 190, 138, 0.3), rgba(255, 255, 255, 1)) 2 2; clip-path: inset(0 round 5px);'
        case '2':
          return 'border: 2px solid; border-image: linear-gradient(180deg, rgba(155, 160, 163, 0.4), rgba(255, 255, 255, 1)) 2 2; clip-path: inset(0 round 5px);'
        default:
          return 'border: 2px solid; border-image: linear-gradient(180deg, rgba(255, 164, 24, 0.3), rgba(255, 255, 255, 1)) 2 2; clip-path: inset(0 round 5px);'
      }
    },
    backgroundType(taskState) {
      switch (taskState) {
        case '0':
          return 'background: linear-gradient(to right, rgba(60, 131, 255, 0.08), #FFF);'
        case '1':
          return 'background: linear-gradient(to right, rgba(3, 190, 138, 0.08), #FFF);'
        case '2':
          return 'background: linear-gradient(to right, rgba(155, 160, 163, 0.2), #FFF);'
        default:
          return 'background: linear-gradient(to right, rgba(255, 164, 24, 0.08), #FFF);'
      }
    },
    imgType(taskState) {
      switch (taskState) {
        case '0':
          return require('@/assets/img/car/icon_daiban.png')
        case '1':
          return require('@/assets/img/car/icon_car_green.png')
        case '2':
          return require('@/assets/img/car/icon_car_gray.png')
        default:
          return require('@/assets/img/car/icon_car_orange.png')
      }
    },
    setCurPage() {
      this.SET_LAST_ROUTE(this.$route.path);
    },
    // 重置列表page,list值
    pageReset() {
      this.taskPage = 1
      this.flowPage = 1
      this.formPage = 1
      this.taskList = []
      this.flowList = []
      this.formList = []
    },
    changeType(name, title) {
      this.isMobile && Toast.loading({
        message: '加载中...',
        forbidClick: true,
      })
      this.currentTab = title
      // this.$store.commit('SET_CURRENT_TAB', this.currentTab)
      this.loading = false
      var index = this.search(this.tabs, name)
      this.finished = this.loadingFinished[index]
      this.pageReset()
      this.getList()
      if (this.currentTab == title) {
        return false
      }
    },
    search(arr, dst) {
      var i = arr.length
      while (i--) {
        if (arr[i] == dst) {
          return i
        }
      }
      return false
    },
    onRefresh() {
      this.pageReset()
      this.loading = false
      this.finished = false
      this.getList()
    },
    onLoad() {
      console.log('load')
      this.loading = true
      this.getList()
    },
    getList() {
      switch (this.currentTab) {
        case '任务待办':
          userTaskLists({
            page: this.taskPage++,
            size: this.pageSize,
            taskSubject: '',
            formName: this.searchValue,
            columnName: 'createDate',
            sort: 'desc',
            deleteFlag: 0,
          })
            .then((res) => {
              if (res.status && res.data.total === 0) {
                this.loadingFinished[0] = true
                this.finished = this.loadingFinished[0]
                this.refreshing = false
              }
              if (res.status) {
                this.refreshing = false
                if (this.taskList.length === res.data.total) {
                  this.loadingFinished[0] = true
                  this.finished = this.loadingFinished[0]
                } else {
                  this.taskList = [...this.taskList, ...res.data.list]
                }
                this.displayList = this.taskList
              }
            })
            .catch(() => {
              this.finished = true
            })
            .finally(() => {
              Toast.clear()
              this.loading = false
            })
          break
        case '流程跟踪':
          relTaskLists({
            page: this.flowPage++,
            size: this.pageSize,
            taskSubject: '',
            formName: this.searchValue,
            cloumn: '',
            order: '',
          })
            .then((res) => {
              if (res.status && res.data.total == 0) {
                this.loadingFinished[1] = true
                this.finished = this.loadingFinished[1]
                this.refreshing = false
              }
              if (res.status) {
                this.refreshing = false
                if (this.flowList.length === res.data.total) {
                  this.loadingFinished[1] = true
                  this.finished = this.loadingFinished[1]
                } else {
                  this.flowList = [...this.flowList, ...res.data.list]
                }
                this.displayList = this.flowList
              }
            })
            .catch(() => {
              this.finished = true
            })
            .finally(() => {
              Toast.clear()
              this.loading = false
            })
          break
        case '表单查询':
          circulationLists({
            asignee: '',
            creator: '',
            startDate: '',
            endDate: '',
            taskState: '',
            taskSubject: '',
            formName: this.searchValue,
            page: this.formPage++,
            size: this.pageSize,
            order: '',
            column: '',
          })
            .then((res) => {
              if (res.status && res.data.total == 0) {
                this.loadingFinished[2] = true
                this.finished = this.loadingFinished[2]
                this.refreshing = false
              }
              if (res.status) {
                this.refreshing = false
                if (this.formList.length === res.data.total) {
                  this.loadingFinished[2] = true
                  this.finished = this.loadingFinished[2]
                } else {
                  this.formList = [...this.formList, ...res.data.list]
                }
                this.displayList = this.formList
                if (!this.searchValue) {
                  this.displayList = this.displayList.filter((item) => {
                    return item.taskState != 2
                  })
                }
              }
            })
            .catch(() => {
              this.finished = true
            })
            .finally(() => {
              Toast.clear()
              this.loading = false
            })
          break
      }
    },
    onSearch(val) {
      this.searchValue = val
      this.onRefresh()
    },
    onClear() {
      this.searchValue = ''
      this.onRefresh()
    },
    // 判断是否支持审批
    checkTask(item) {
      return true
    },
    // 打开任务详情页面
    handleContentClick(result) {
      // const {userTask: item} = result;
      const item = result;
      if (this.checkTask(item)) {
        this.$store.commit('SET_CURRENT_ROW', item)
        if (this.currentTab === '任务待办') {
          this.setCurPage();
          this.$router.push({
            name: 'Application',
            params: {
              type: 'execute',
              formKey: item.formKey,
              taskKey: item.taskKey,
              taskId: item.taskId,
              bizId: item.formBizId,
              processInstanceId: item.processInstanceId,
              formName: item.formName
            }
          }).then(() => {});
        } else if (this.currentTab === '流程跟踪') {
          // 流程跟踪
          this.setCurPage();
          this.$router.push({
            name: 'formView',
            params: {
              type: 'view',
              formKey: item.formKey,
              taskKey: item.taskKey,
              taskId: item.taskId,
              bizId: item.formBizId,
              processInstanceId: item.processInstanceId,
              formName: item.formName
            },
          }).then(() => {});
        } else {
          // 表单查询
          this.setCurPage();
          this.$router.push({
            name: 'formView',
            params: {
              type: 'view',
              formKey: item.formKey,
              taskKey: item.taskKey,
              taskId: item.taskId,
              bizId: item.formBizId,
              processInstanceId: item.processInstanceId,
              taskName: item.taskName
            }
          }).then(() => {});
        }
      } else {
        Toast('该阶段暂不支持移动端审批', 'middle')
      }
    },
  },
}
