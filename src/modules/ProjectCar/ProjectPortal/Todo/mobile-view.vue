
<template>
  <div class="task-container" :class="{'bg-white': displayList.length === 0} ">
    <!--    <fm-nav-bar title="表单中心" left-text="" right-text="" :border="false" :left-arrow="isBack" />-->
    <fm-search
      v-model="searchValue"
      placeholder="请输入表单名称"
      maxlength="50"
      @clear="onClear"
      @search="onSearch"
    />
    <div class="card-container" v-if="displayList.length !== 0">
      <fm-pull-refresh
        ref="taskScroller"
        refresh-layer-color="#4b8bf4"
        v-model="refreshing"
        success-text="刷新成功"
        @refresh="onRefresh()"
      >
        <fm-list
          v-model="loading"
          :finished="finished"
          finished-text="已经到底啦~"
          :immediate-check="false"
          @load="onLoad"
        >
          <div class="v-task-item" v-for="(item, index) in displayList" :key="index" :style="currentTab !== '任务待办' && borderType(item.taskState)">
            <div class="v-task-item__box">
              <div class="v-task-item__box-info" @click="handleContentClick(item)">
                <div class="box-info__top" :style="currentTab !== '任务待办' ? backgroundType(item.userTask.taskState): 'background: linear-gradient(to right, rgba(60, 131, 255, 0.08), #FFF)'">
                  <!-- 标题和时间 -->
                  <div class="box-info__title">
                    <img :src="currentTab !== '任务待办' ? imgType(item.userTask.taskState) : require('@/assets/img/car/icon_daiban.png')" class="address-text m-r-10">
                    <div class="box-info__title-text">
                      <div>{{ item.taskName || 'mobile流程' }}</div>
                    </div>
                    <div v-if="currentTab !== '任务待办'" class="box-info__task-state">
                      <div class="task-text state-text-run" v-if="item.userTask.taskState === 0">流转中</div>
                      <div class="task-text state-text-finish" v-else-if="item.userTask.taskState === 1">已完成</div>
                      <div class="task-text state-text-waste" v-else-if="item.userTask.taskState === 2">废弃</div>
                      <div class="task-text state-text-ts" v-else>暂存</div>
                    </div>
                  </div>
                  <div
                    v-if="Number.isInteger(item.applyFormApprovalReturnState)"
                    class="status-bar"
                    :style="{background: getBackground(item.applyFormApprovalReturnState)}"
                  >{{getReturnState(item.applyFormApprovalReturnState)}}</div>
                </div>
                <div class="box-info__task-info">
                  <div class="box-info__task-name">
                    <div>节点名称</div>
                    <div class="task-text sub-text text-right">
                      {{ item.taskSubject }}
                    </div>
                  </div>
                  <div class="box-info__task-starter">
                    <div>发起人</div>
                    <div class="task-text starter-text text-right">
                      {{ item.taskCreatorName }}
                    </div>
                  </div>
                  <div v-if="currentTab === '任务待办'" class="box-info__task-time">
                    <div>开始时间</div>
                    <div class="task-text time-text text-right">
                      {{ item.processCreateDate }}
                    </div>
                  </div>
                  <div v-else>
                    <div class="line_sty"></div>
                    <div class="box__task-time col-center">
                      <img src="@/assets/img/car/icon_time.png" class="icon-time m-r-10">
                      <div>
                        {{ item.processCreateDate }}
                      </div>
                    </div>
                  </div>
                  <div class="box-info__task-stop" v-if="!checkTask(item)">
                    <div class="task-text stop-text">该阶段暂不支持移动端审批</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </fm-list>
      </fm-pull-refresh>
    </div>
    <empty-data v-else></empty-data>
  </div>
</template>

<script>

import { Image, Search, NavBar, Tab, Tabs, PullRefresh, List, Toast } from 'fawkes-mobile-lib'

import mixins from './mixins';
export default {
  name: 'ToDoMobile',
  mixins: [mixins],
  components: {
    [Image.name]: Image,
    [Search.name]: Search,
    [NavBar.name]: NavBar,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [PullRefresh.name]: PullRefresh,
    [List.name]: List,
    [Toast.name]: Toast,
  },
  mounted() {
    Toast.loading({
      message: '加载中...',
      forbidClick: true,
    })
  },
  beforeRouteLeave(to, from, next) {
    // 导航离开该组件的对应路由时调用
    // 可以访问组件实例 `this`
    const el = document.querySelector('.apply-container')
    if (el) {
      const shouldMemo = to.name === 'formView' || to.name === 'tripDetail'
      this.SET_SCROLL_TOP(shouldMemo ? el.scrollTop : 0)
    }
    next();
  },
}
</script>


<style scoped lang='less'>
@import "./mobile-view";
</style>
