<template>
  <div>
    <TitleKit>
      <template #left>
        <div style="font-size: 16px; font-weight: 600;">
          司机列表
        </div>
      </template>
    </TitleKit>
    <TempTable
      :data-list="tableData"
      :table-config.sync="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      @add-item="handleAdd"
      @delete-item="handleDelete"
      @batch-add="handleBatchAdd"
      @export-data="handleExport"
      @searchData="searchData"
      @clickRow="handleClickRow"
      :showButtons="showButtons"
      :current-page.sync="pageNo"
    >
      <template v-slot:column-driverStatus="{scope}">
        <card-tag v-if="scope.row.driverStatus"
                  class="p-24 p-t-8 p-b-8"
                  :tag="{
                    color: getTagType(scope.row.driverStatus, 'driverStatus'),
                    text: getTagText(scope.row.driverStatus, 'driverStatus')
        }" />
        <!--        <fks-tag v-if="scope.row.driverStatus" :type="getTagType(scope.row.driverStatus, 'driverStatus')">{{ getTagText(scope.row.driverStatus, 'driverStatus') }}</fks-tag>-->
      </template>
      <template v-slot:column-attachment1="{ scope }">
        <TextListUpload v-if="!loading" :key="scope.row.attachment1" v-model="scope.row.attachment1"
                        :disabled="true" />
      </template>
      <template v-slot:column-attachment2="{ scope }">
        <TextListUpload v-if="!loading" v-model="scope.row.attachment2" :disabled="true" />
      </template>
    </TempTable>

    <fks-drawer
      :before-close="beforeClose"
      :visible.sync="visible"
      :wrapperClosable="true"
      :modal="false"
      class="dialog"
      direction="rtl"
      size="660px"
      @open="onDrawerOpen"
    >
      <header class="card-header drawer-header" slot="title">
        <div class="flex col-center m-b-24">
          <div class="card-title" style="color: #333333" :key="currentData.driverFullName">
            {{ currentData.driverFullName }}
          </div>
          <div class="m-l-20">
            <card-tag v-for="(tag, index) in currentData.tags" :key="index" :tag="tag" />
            <card-tag class="m-l-10" :tag="{ color: '#3C83FF', text: currentData.projectName }" />
          </div>
        </div>
      </header>
      <driver-form
        :type.sync="type"
        :current-data="currentData"
        :show-header="false"
        @refresh="clear"
        ref="detailForm"
        />
    </fks-drawer>
    <add-driver ref="addDriver" type="plus" @updateTable="reSearchData" />
    <batch-add-driver ref="batchAddDriver" @updateTable="reSearchData"  @openDialog="openDialog"/>
    <UserForm ref="userForm" @refresh="reSearchData" />
    <fks-dialog
      title="关闭"
      :visible.sync="tipVisible"
      size="small"
    >
      <div style="padding: 16px 20px">
        <span>表单内容发生改变是否继续关闭?</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="handelCancle">取 消</fks-button>
        <fks-button type="primary" @click="onConfirmTip">确 定</fks-button>
      </span>
    </fks-dialog>
  </div>
</template>

<script>
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import UserForm from '@modules/ProjectCar/CompanyPortal/UserManagement/components/UserForm.vue'

import AddDriver from '@modules/ProjectCar/ProjectPortal/DriverManage/components/Add.vue'
import BatchAddDriver from '@modules/ProjectCar/ProjectPortal/DriverManage/components/BatchAdd.vue'
import {
  batchDeleteDriver, exportDriverInfo,
  getCar,
  getDriverListByPost, getDriverSearchParam
} from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import { mapGetters, mapState } from 'vuex'
import DriverForm from '@modules/ProjectCar/ProjectPortal/DriverManage/components/DriverForm.vue'
import TextListUpload from '@modules/FormCenter/components/FormUpload/TextListUpload.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import { exportFile, fileReader } from '@utils/exportFile'
import * as StateTypes from "@store/State/stateTypes";
import globalSearchMixin from "@/mixins/globalSearchMixin";
import * as GetterTypes from '@store/Getter/getterTypes'
export default {
  name: 'DriverManage',
  mixins: [globalSearchMixin],
  components: {
    CardTag,
    TextListUpload,
    DriverForm,
    AddDriver,
    BatchAddDriver,
    TempTable,
    UserForm,
    TitleKit
  },
  data() {
    return {
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['add', 'batchAdd', 'delete', 'export', 'filter', 'filedConfig'],
      queryParams: {
        projectId: null,
        conditions: [],
      },
      visible: false,
      tableData: [],
      currentData: {},
      type: 'view',
      selectedFields: [],
      tableConfig: [
        {
          label: '项目名称',
          prop: 'projectName',
          group: '项目信息',
          width: '200px',
        },
        {
          label: '司机姓名',
          prop: 'driverFullName',
          group: '司机信息',
          width: '150px',
          clickable: true,
        },
        // {
        //   label: '用车类型',
        //   prop: 'projectUseCarType',
        //   group: '项目属性',
        //   width: '150px',
        //   enums: this.projectCarType
        // },
        {
          label: '司机联系电话',
          prop: 'driverPhone',
          group: '司机信息',
          width: '180px',
        },
        {
          label: '司机来源',
          prop: 'driverResource2',
          group: '司机信息',
          width: '150px',
          enums: this.driverResourceOption
        },
        {
          label: '年龄',
          prop: 'driverAge2',
          group: '司机信息',
          width: '80px',
        },
        // {
        //   label: '司机身份证号码',
        //   prop: 'driverIdNumber',
        //   group: '司机信息',
        //   width: '180px',
        // },
        {
          label: '驾龄',
          prop: 'driverAge',
          group: '司机信息',
          width: '80px',
        },
        {
          label: '车牌号',
          prop: 'carNum',
          group: '车辆信息',
          width: '160px',
          default: "暂未绑定"
        },
        {
          label: '出租方名称',
          prop: 'carCompName',
          group: '车辆信息',
          width: '180px',
        },
        {
          label: '品牌型号',
          prop: 'carType',
          group: '车辆信息',
          width: '150px',
        },
        {
          label: '座位数',
          prop: 'carSeatNum',
          group: '车辆信息',
          width: '100px',
        },
        {
          label: '司机状态',
          prop: 'driverStatus',
          group: '司机信息',
          width: '150px',
          customer: true
        },
        // {
        //   label: '月度租赁费用',
        //   prop: 'driverZlFy1',
        //   group: '司机信息',
        //   width: '120px',
        // },
        // {
        //   label: '司机性别',
        //   prop: 'driverSex',
        //   group: '司机信息',
        //   width: '100px',
        // },
        // {
        //   label: '入职日期',
        //   prop: 'driverRzTime',
        //   group: '司机信息',
        //   width: '180px',
        //   formatDate: true,
        // },
        // {
        //   label: '离职日期',
        //   prop: 'driverLzTime',
        //   group: '司机信息',
        //   width: '180px',
        //   formatDate: true,
        // },
        // {
        //   label: '驾驶证到期日期',
        //   prop: 'driverJszdqTime',
        //   group: '司机信息',
        //   width: '180px',
        //   formatDate: true,
        // },
        // // {
        // //   label: '驾驶证',
        // //   prop: 'attachment1',
        // //   group: '附件信息',
        // //   width: '250px',
        // //   customer: true,
        // // },
        // // {
        // //   label: '其他资料',
        // //   prop: 'attachment2',
        // //   group: '附件信息',
        // //   width: '250px',
        // //   customer: true,
        // // },
        // {
        //   label: '备注',
        //   prop: 'remark1',
        //   group: '备注信息',
        //   width: '180px',
        // },
      ],

      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      tipVisible: false,
      type1: ''
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    driverStatusOption() {
      return this.enums.DriverStatusEnums
    },
    driverResourceOption() {
      return this.enums.DriverResourceEnums;
    },
    projectCarType() {
      return this.enums.ProjectUseCarTypeEnums
    },
  },
  created() {
    this.selectedFields = this.tableConfig.map(item => item.prop)
    this.tableConfig.forEach((item) => {
      if (item.prop === 'driverStatus') {
        item.enums = this.driverStatusOption
      }
      if (item.prop === 'driverResource2') {
        item.enums = this.driverResourceOption
      }
      if (item.prop === 'projectUseCarType') {
        item.enums = this.projectCarType
      }
    })
  },
  mounted() {
    this.queryParams.projectId = this.portal.id
    this.getData(this.queryParams)
    getDriverSearchParam().then(res => {
      this.searchConfigs = res.data;
    })
  },
  methods: {
    openDialog() {
      this.tipVisible = true
      this.type1 = 'batchAdd'
    },
    handelCancle() {
      this.tipVisible = false
      this.type1 = 'add'
    },
    onConfirmTip() {
      this.visible = false
      this.$refs.batchAddDriver.visible = false
      this.tipVisible = false
      if (this.type1 === 'batchAdd') {
        this.$refs.batchAddDriver.cleanData()
      }
    },
    globalSearch() {
      this.tableData = [];
      this.reSearchData();
    },
    getTagText(value, prop) {
      if (prop === 'driverStatus') {
        return this.driverStatusOption.find(item => item.key == value).value;
      }
      return value;
    },
    getTagType(value, prop) {
      let status = {};
      if (prop === 'driverStatus') {
        status = {
          100: '#40bb5a',
          400: '#ff4d4f',
          // 100: 'success',
          // 200: 'primary',
          // 300: 'danger',
          // 400: 'danger',
        }
      }
      return status[value];
    },
    reSearchData(refresh) {
      if (!refresh || refresh == false) {
        this.pageNo = 1
      }
      this.getData(this.queryParams)
    },
    async handleDelete(rows) {
      if (rows && rows.length > 0) {
        let ids = rows.map(item => {
          return {
            driverId: item.id,
            carId: item.carId
          }
        })
        let res = await batchDeleteDriver(ids);
        if (res.status) {
          this.$message.success('删除成功');
          this.reSearchData(true)
        }
      }
    },
    handleExport(list) {
      let data = {
        pageGetParam: { ...this.queryParams },
        fields: list
      }
      exportDriverInfo(data).then(res => {
        try {
          fileReader(res)
        } catch (error) {
          console.error("导出失败:", error);
          alert("导出失败，请稍后重试。");
        }
      })
    },
    handleBatchAdd() {
      this.$refs.batchAddDriver.open()
    },
    handleAdd() {
      this.$refs.addDriver.open({}, 'plus')
    },
    handleClickRow(row, prop) {
      this.visible = true
      this.type = 'view'
      this.currentData = { ...row }
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    async getData(query) {
      this.loading = true
      this.queryParams = { ...this.queryParams, ...query }
      try {
        const params = {
          ...this.queryParams,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
        }
        this.loading = true
        const res = await getDriverListByPost(params, this[GetterTypes.GET_GLOBAL_STATE])
        if (!res.status) {
          return false
        }
        this.total = res.data.total
        this.finished = res.data.isLastPage
        let tableData = res.data.list

        if (tableData.length > 0) {
          const newData = await this.addCarToTable(tableData)
          tableData = [...newData]
        }
        this.$set(this.$data, 'tableData', tableData)
        this.loading = false
      } catch (e) {
        this.loading = false
      }
    },
    async addCarToTable(tableData) {
      const resCar = await getCar({ driverIds: tableData.map((item) => item.id).join(',') })
      this.carInfo = resCar.data || {}
      return tableData.map((item) => {
        if (this.carInfo[item.id]) {
          return {
            ...item,
            carNum: this.carInfo[item.id].carNum,
            carNumber: this.carInfo[item.id].carNum,
            carName: this.carInfo[item.id].carName,
            carType: this.carInfo[item.id].carType,
            carSeatNum: this.carInfo[item.id].carSeatNum,
            carCompName: this.carInfo[item.id].carCompName,
            carId: this.carInfo[item.id].id,
          }
        } else {
          return item
        }
      })
    },
    onDrawerOpen() {
    },
    beforeClose(done) {
      // this.visible = false
      // done()
      if (this.type === 'view') {
        this.visible = false
        done()
      }
      if (this.type === 'edit') {
        // 基本信息 - 保险信息 - 年检信息 - 通行证信息 - 违章信息
        const formData = this.$refs.detailForm.$refs.addFormRef.formData
        const backFromData = this.$refs.detailForm.$refs.addFormRef.backFromData
        const flag1 = !this.isEqual(formData,backFromData)
        if (flag1) { // 内容发送变化
          this.tipVisible = true
        } else {
          this.visible = false
          done()
        }
      }
    },
    isEqual(data1,data2) {
      let flag = true
      for (let key in data1) {
        if (key in data2 && data2[key] === data1[key]) continue
        flag = false
      }
      return flag
    },
    clear() {
      this.visible = false
      this.currentData = {}
      this.pageNo = 1
      this.getData(this.queryParams)
    },
  },
}
</script>

<style scoped lang="less">
@import '~@/styles/headers';
@import "~@components/PageTitle/exclude/TitleKit.css";

.drawer-header {
  padding: 24px 0 0 24px;
}
</style>
