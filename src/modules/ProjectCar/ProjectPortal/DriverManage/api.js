import request from '@/utils/request'

// 新增司机
export function addDriver(data) {
  return request({
    url: '/vehicle-dispatch/vd/driver/add',
    method: 'post',
    data
  })
}

export function batchAddDriver(data) {
  return request({
    url: '/vehicle-dispatch/vd/driver/template/batchAddOrUpdate',
    method: 'post',
    data
  })
}

// 删除司机
export function deleteDriver(id) {
  return request({
    url: '/vehicle-dispatch/vd/driver/delete',
    method: 'post',
    params: { id }
  })
}

// 删除司机
export function batchDeleteDriver(data) {
  return request({
    url: '/vehicle-dispatch/vd/driver/multiDelete',
    method: 'post',
    data
  })
}

// 解除司机，车辆绑定
export function unbindCarWithDriver(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/unbind/driver',
    method: 'post',
    params
  })
}

// 司机列表
export function getDriverList(params) {
  return request({
    url: '/vehicle-dispatch/vd/driver/page',
    method: 'get',
    params
  })
}


// 获取司机参数列表
export function getDriverSearchParam(params) {
  return request({
    url: '/vehicle-dispatch/vd/driver/page/getParam',
    params
  })
}

// post形式获取司机列表
export function getDriverListByPost(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/driver/page',
    method: 'post',
    data,
    params
  })
}

// 导出司机列表
export function exportDriverInfo(params) {
  return request({
    url: '/vehicle-dispatch/vd/driver/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 修改司机
export function updateDriver(data) {
  return request({
    url: '/vehicle-dispatch/vd/driver/update',
    method: 'post',
    data
  })
}


// 查询车辆
export function getCar(params) {
  return request({
    url: '/vehicle-dispatch/vd/driver/car',
    method: 'get',
    params
  })
}

// 根据电话查询司机
export function getDrierByPhone(params) {
  return request({
    url: '/vehicle-dispatch/vd/driver/phone',
    method: 'get',
    params
  })
}


// 根据电话查询司机
export function getDrierByPhoneList(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/driver/byPhone',
    method: 'post',
    data: params
  })
}

// 查询车牌号
export function getCarNum(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/carNum',
    method: 'get',
    params
  })
}

// 查询车牌号
export function bindDiver(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/bind/driver',
    method: 'post',
    params
  })
}

// 到期提醒列表
export function getReminder(params) {
  return request({
    url: '/vehicle-dispatch/vd/expire/page',
    method: 'get',
    params
  })
}


// 身份证校验
export function idCardVerify(params) {
  return request({
    url: '/vehicle-dispatch/vd/idCard/verify',
    method: 'get',
    params
  })
}
