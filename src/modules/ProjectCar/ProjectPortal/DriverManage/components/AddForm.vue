<template>
  <div>
    <div class="notify-content  m-b-24" v-if="type === 'plus' && notifyTitle" v-html="notifyTitle"/>
    <fks-form
      ref="form"
      :model="formData"
      :rules="rules"
      :class="{newTheme: layoutSingleLine}"
    >

      <fks-form-item
        :rules="[{required: true, message: '请选择项目名称', trigger: 'blur'}]"
        :span="span"
        label="项目名称"
        prop="projectName"
        required
        v-if="isCompanyPortal"
      >
        <fks-select
          v-model="formData.projectId"
          :disabled="isView"
          class="full-width"
          @change="handleProjectChange"
          filterable
        >
            <fks-option
              v-for="portal in authPortalList"
              :key="portal.id"
              :label="portal.projectName"
              :value="portal.portalId"
            />
        </fks-select>
      </fks-form-item>
      <fks-form-item :span="span" label="姓名" prop="driverFullName" required>
        <fks-input
          v-model="formData.driverFullName"
          :disabled="isView"
          placeholder="请输入姓名"
        />
      </fks-form-item>

      <fks-form-item :span="span" label="性别" prop="driverSex">
        <fks-select
          v-model="formData.driverSex"
          :disabled="isView"
          :placeholder="isView ? '' : '请选择性别'"
          class="full-width"
        >
          <fks-option :label="'男'" :value="'男'"/>
          <fks-option :label="'女'" :value="'女'"/>
        </fks-select>
      </fks-form-item>

<!--      <fks-form-item :span="span" label="用车类型" prop="projectUseCarType" required>-->
<!--        <fks-select-->
<!--          v-model="formData.projectUseCarType"-->
<!--          :disabled="isView"-->
<!--          :placeholder="isView ? '' : '请选择用车类型'"-->
<!--          class="full-width"-->
<!--        >-->
<!--          <fks-option-->
<!--            v-for="item in projectCarType"-->
<!--            :key="item.code"-->
<!--            :label="item.value"-->
<!--            :value="item.key"-->
<!--          />-->
<!--        </fks-select>-->
<!--      </fks-form-item>-->

      <fks-form-item :span="span" label="驾龄" prop="driverAge">
        <fks-input
          v-model="formData.driverAge"
          :disabled="isView"
          placeholder="请输入驾龄"
          @input="val => {
            formData.driverAge = inputNumberLimit(val)
          }"
        />
      </fks-form-item>

      <fks-form-item :span="span" label="年龄" prop="driverAge2">
        <fks-input
          v-model="formData.driverAge2"
          disabled
          placeholder="请输入司机年龄"
          @input="val => {
            formData.driverAge2 = inputNumberLimit(val)
          }"
        />
      </fks-form-item>

      <fks-form-item :span="span" label="驾驶证号" prop="driverIdNumber" required>
        <fks-input
          :disabled="isView"
          v-model="formData.driverIdNumber"
          @blur="handleIdNumberBlur"
          placeholder="请输入驾驶证号"
        />
      </fks-form-item>
      <fks-form-item :span="span" label="联系电话" prop="driverPhone" required>
        <fks-input
          :disabled="isView"
          v-model="formData.driverPhone"
          placeholder="请输入联系电话"
        />
      </fks-form-item>
      <fks-form-item :span="span" label="绑定车辆" prop="carNumber">
        <fks-input
          v-if="isView"
          v-model="formData.carNumber"
          :disabled="isView"
          :placeholder="isView ? '' : '请输入车辆名'"
        />
        <fks-select
          v-else
          v-model="formData.carNumber"
          filterable
          :disabled="isView"
          :loading="loadingCarNum"
          :placeholder="isView ? '' : '请选择车牌号'"
          class="full-width"
          clearable
          @change="changeCarNum"
        >
          <fks-option
            v-for="(item, index) in carNumList"
            :key="index"
            :label="item.carNum + '/' + item.carName"
            :value="item.carNum"
          >
          </fks-option>
        </fks-select>
      </fks-form-item>
      <fks-form-item :span="span" label="品牌型号" prop="carType">
        <fks-input
          :disabled="isView"
          :placeholder="isView ? '' : '自动带入，无需填写'" :value="formData.carType"
          readonly/>
      </fks-form-item>
      <fks-form-item :span="span" label="车辆名称" prop="carName">
        <fks-input
          :disabled="isView"
          :placeholder="isView ? '' : '自动带入，无需填写'" :value="formData.carName"
          readonly/>
      </fks-form-item>
      <fks-form-item :span="span" label="座位数" prop="carSeatNum">
        <fks-input :disabled="isView"
                   :placeholder="isView ? '' : '自动带入，无需填写'" :value="formData.carSeatNum"
                   readonly/>
      </fks-form-item>


      <fks-form-item :span="span" label="司机来源" prop="driverResource2">
        <fks-select
          v-model="formData.driverResource2"
          filterable
          :disabled="isView"
          :placeholder="isView ? '' : '请选择司机来源'"
          class="full-width"
        >
          <fks-option
            v-for="item in driverResourceOption"
            :key="item.code"
            :label="item.value"
            :value="item.key"
          />
        </fks-select>
      </fks-form-item>

      <fks-form-item
        v-if="isRentDriver" :span="span" label="月度租赁价格" prop="driverZlFy1">
        <fks-input-number
          :disabled="isView"
          :placeholder="isView ? '' : ''"
          size="small"
          v-model="formData.driverZlFy1" />
      </fks-form-item>


      <fks-form-item :span="span" label="初始工作年限" prop="driverJsgznxCs">
        <fks-input-number
          :disabled="isView"
          :min="0"
          :placeholder="isView ? '' : ''"
          size="small"
          v-model="formData.driverJsgznxCs" />
      </fks-form-item>

      <fks-form-item :span="span" label="累计工作年限" prop="driverJsgznxLj">
        <fks-input-number
          :disabled="isView"
          :min="0"
          :placeholder="isView ? '' : ''"
          size="small"
          v-model="formData.driverJsgznxLj" />
      </fks-form-item>
      <fks-form-item :span="span" label="状态" prop="driverStatus">
        <fks-select
          v-model="formData.driverStatus"
          :disabled="flowConfig.driverStatus === 'readonly'"
          :placeholder="isView ? '' : '请选择状态'"
          class="full-width"
        >
          <fks-option v-for="item in driverStatusOption" :key="item.code" :label="item.value"
                      :value="item.key"/>
        </fks-select>
      </fks-form-item>

      <fks-form-item :span="span" label="入职日期" prop="driverRzTime">
        <fks-date-picker
          v-model="formData.driverRzTime"
          :disabled="isView"
          :placeholder="isView ? '' : '请选择入职日期'"
          type="date"
          class="full-width"
        >
        </fks-date-picker>
      </fks-form-item>
      <fks-form-item :span="span" label="离职日期" prop="driverLzTime">
        <fks-date-picker
          v-model="formData.driverLzTime"
          :disabled="flowConfig.driverLzTime === 'readonly'"
          :placeholder="isView ? '' : '请选择离职日期'"
          type="date"
          class="full-width"
        >
        </fks-date-picker>
      </fks-form-item>

      <fks-form-item :span="span" label="驾驶证到期日期" prop="driverJszdqTime">
        <fks-date-picker
          v-model="formData.driverJszdqTime"
          :disabled="isView"
          :placeholder="isView ? '' : '请选择驾驶证到期日期'"
          type="date"
          class="full-width"
        >
        </fks-date-picker>
      </fks-form-item>

      <fks-form-item label="驾驶证" prop="attachment1">
        <input-attachment
          :disabled="isView"
          :form-data="formData"
          attachment-name="attachment1"
          @getFileInfo="formData.files1 = $event.length"
        />
      </fks-form-item>
      <fks-form-item label="其他资料" prop="attachment2">
        <input-attachment
          :disabled="isView"
          :form-data="formData"
          attachment-name="attachment2"
          @getFileInfo="formData.files2 = $event.length"
        />
      </fks-form-item>
      <fks-form-item
        label="备注"
        prop="remark1"
        class="m-t-20"
      >
        <fks-input
          v-model="formData.remark1"
          :disabled="isView"
          :placeholder="isView ? '' : '请输入备注'"
          :rows="2"
          maxlength="100"
          show-word-limit
          style="width: 100%"
          type="textarea"
        />
      </fks-form-item>
    </fks-form>

  </div>
</template>

<script>
import InputAttachment from '@components/InputAttachment/pc-view.vue'
import validator from '@/mixins/validator.js';
import {getCarListByPost} from "@modules/ProjectCar/ProjectPortal/CarManage/api";
import {mapState} from "vuex";
import { queryTips, tipsMap } from '@utils/notifyContentUtils'
import { idCardVerify } from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import * as StateTypes from "@store/State/stateTypes";

export default {
  name: "AddForm",
  mixins: [validator],
  components: {InputAttachment},
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      carInfo: {}, // 选择的车辆信息
      loadingCarNum: false,
      carNumList: [], // 车辆列表
      formData: {
        driverFullName: '', // 姓名
        driverAge: '', // 司机驾龄
        driverPhone: '', // 联系电话
        carNumber: '', // 绑定车辆
        carType: '', // 品牌型号
        carName: '', // 车辆名称
        carSeatNum: '', // 座位数
        driverStatus: '', // 状态
        driverRzTime: '', // 入职日期
        driverLzTime: '', // 离职日期
        driverJszdqTime: '', // 驾驶证到期日期
        driverZlFy1: '', // 月度租赁费用
        files1: 0,
        attachment1: '',
        files2: 0,
        attachment2: '',
        remark1: '', // 备注
        projectName: '',
        projectId: ''
      },
      backFromData: {},
      notifyTitle: ""
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapState([StateTypes.AUTH_PORTALS]),
    isCompanyPortal() {
      return this.portal.name === '数据舱'
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter(item => item.projectStatus === 100); // 100激活，200关闭
    },
    isRentDriver() {
      return this.formData.driverResource2 === 200;
    },
    span() {
      return this.layoutSingleLine ? 24 : 12;
    },
    isEdit() {
      return this.type === 'edit';
    },
    isView() {
      return this.type === 'view';
    },
    isAdd() {
      return this.type === 'plus';
    },
    driverStatusOption() {
      return this.enums.DriverStatusEnums;
    },
    driverResourceOption() {
      return this.enums.DriverResourceEnums;
    },
    projectCarType() {
      return this.enums.ProjectUseCarTypeEnums
    },
    flowConfig() {
      if (this.type === 'view') {
        return Object.keys(this.formData).reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      if (this.isEdit) {
        return ['carNum', 'carType', 'carSeatNum'].reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      return {}
    },
    rules() {
      return {
        driverFullName: [
          {required: true, message: '请输入姓名', trigger: 'change'}
        ],
        driverPhone: [
          {required: true, message: '请输入联系电话', trigger: 'change'},
          {required: true, validator: this.phoneValidator, trigger: 'blur'}
        ],
        driverStatus: [
          {required: true, message: '请选择状态', trigger: 'change'}
        ],
        driverRzTime: [
          {required: true, message: '请选择入职日期', trigger: 'change'}
        ],
        driverSex: [
          {required: true, message: '请选择司机性别', trigger: 'change'}
        ],
        driverJsgznxCs: [
          {required: true, message: '请选择司机初始工作年限', trigger: 'change'}
        ],
        driverResource2: [
          {required: true, message: '请选择司机来源', trigger: 'change'}
        ],
        driverZlFy1: [
          {required: true, message: '请输入月度租赁费用', trigger: 'change'}
        ],
        driverIdNumber: [
          { required: true, message: '请输入驾驶证号', trigger: 'change' },
          {
            required: true,

            validator: async function(rule, value, callback) {
              const idCardRegex = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;
              idCardVerify(value).then(res => {

              })
              if (!value) {
                callback(new Error('驾驶证号不能为空'));
              }
              try {
                const res = await idCardVerify({idCard: value}); // 调用接口验证
                if (!res.data) {
                  callback(new Error('驾驶证号验证失败，请检查输入'));
                } else {
                  callback(); // 验证通过
                }
              } catch (error) {
                callback(new Error('验证服务出错，请稍后重试'));
              }
            },
            trigger: 'blur'
          }
        ]

      }
    },
  },
  watch: {
    'formData.driverRzTime': 'calculateTotalExperience',
    'formData.driverJsgznxCs': 'calculateTotalExperience',
    'formData.projectId': {
      immediate: true,
      handler(val) {
        if (val && this.isCompanyPortal) {
          this.formData.projectName = this.authPortalList.find(item => item.portalId === val).projectName
        }
        val && this.getCarNumberList()
      }
    }
  },
  methods: {
    handleProjectChange(val) {
      const item = this.authPortalList.find(item => item.portalId === val);
      if (item && this.isCompanyPortal) {
        this.formData.projectName = item.projectName;
      }
      // 当项目切换时，绑定车辆，品牌型号，车辆名称，座位数需要清空
      this.formData.carNumber = '';
      this.formData.carType = '';
      this.formData.carName = '';
      this.formData.carSeatNum = '';
    },
    handleIdNumberBlur() {
      const idNumber = this.formData.driverIdNumber;
      if (idNumber.length === 18) {
        // 提取出生日期
        const year = parseInt(idNumber.substring(6, 10));
        const month = parseInt(idNumber.substring(10, 12)) - 1; // 月份从 0 开始
        const day = parseInt(idNumber.substring(12, 14));
        const birthDate = new Date(year, month, day);

        // 计算年龄
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const m = today.getMonth() - birthDate.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
          age--; // 如果还未到生日，年龄减一
        }

        // 更新年龄
        this.$set(this.formData, 'driverAge2', age);
      } else {
        // 如果身份证号不合法，清空年龄
        this.formData.driverAge2 = null;
      }
    },
    calculateTotalExperience() {
      const { driverRzTime, driverJsgznxCs } = this.formData;
      if (driverRzTime && driverJsgznxCs !== null) {
        const startYear = new Date(driverRzTime).getFullYear();
        const currentYear = new Date().getFullYear();
        const additionalYears = currentYear - startYear;

        // 计算累计工作年限
        this.formData.driverJsgznxLj = driverJsgznxCs + (additionalYears > 0 ? additionalYears : 0);
      }
    },
    getCarNumberList() {
      this.loadingCarNum = true;
      let projectId = this.formData.projectId || this.portal.id;
      getCarListByPost({pageNo: 1, pageSize: 50, projectId}).then(res => {
        this.loadingCarNum = false;
        this.carNumList = res.data.list || [];
      }).catch(() => {
        this.loadingCarNum = false;
      })
    },
    changeCarNum(val) {
      const carInfo = this.carNumList.find(item => item.carNum === val);
      this.formData.carType = carInfo?.carType;
      this.formData.carName = carInfo?.carName;
      this.formData.carSeatNum = carInfo?.carSeatNum || 5;
      this.carInfo = carInfo;
    },
    doEdit() {
      this.backFromData = JSON.parse(JSON.stringify(this.formData));
    },
    editCancel() {
      this.formData = JSON.parse(JSON.stringify(this.backFromData));
    },
    queryTipsContent(projectId) {
      queryTips(projectId, tipsMap.addDriver).then(res => {
        if (res.data.tipContent) {
          this.notifyTitle = res.data.tipContent
        }
      })
    }
  },
  created() {
    // 项目门户需要手动获取车辆列表
    if (!this.isCompanyPortal) {
      this.getCarNumberList();
    }
  }
}
</script>

<style scoped lang="less">
@import "~@/styles/disabled";
@import "~@/styles/tabs";
@import "~@/styles/input";
</style>
