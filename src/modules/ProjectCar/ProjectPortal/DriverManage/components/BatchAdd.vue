<template>
  <fks-drawer
    :before-close="beforeClose"
    :modal="false"
    :visible.sync="visible"
    :wrapperClosable="true"
    class="dialog"
    direction="rtl"
    size="80vw"
  >
    <template slot="title">
      <div class="dialog-title flex col-center">
        <i :class="'fks-icon-' + type" :style="{ color: theme }" class="m-r-10"/>
        <span>{{ title }}司机</span>
      </div>
    </template>
    <div class="table-container" id="table-container">

      <header id="header" class="flex row-between">
        <common-title title="司机信息" />
        <div class="buttons">
          <fks-popconfirm
            title="确认删除？"
            :disabled="disabledConfirm"
            @onConfirm="handleDelete"
          >
            <fks-button
              slot="reference"
              dangerText
              icon="fks-icon-delete"
              @click="handlePreCheck"
            >
              删除
            </fks-button>
          </fks-popconfirm>
          <fks-button style="margin-left: 15px;" text icon="fks-icon-circle-plus-outline" @click="handleAdd">新增行</fks-button>
          <fks-checkbox style="margin-left: 15px"  v-model="checked">关联车辆<span style="color:#FF4143">（该操作会解除原有关联）</span></fks-checkbox>
        </div>
      </header>
      <batch-add-form
        ref="addFormRef"
        :line-car="checked"
        type="plus"
        layout-single-line
        @disabledPopConfirm="disabledConfirm = $event"
      />
      <common-title title="填写信息"/>
      <fks-form
        id="user-info"
        ref="formRef"
        :model="formData"
        class="form newTheme"
        label-position="left"
        label-width="210px"
      >
        <fks-form-item label="填写人">
          <fks-input :value="userFullName" disabled readonly/>
        </fks-form-item>
        <fks-form-item label="填写时间">
          <fks-input :value="createTime" disabled readonly/>
        </fks-form-item>
        <fks-form-item label="二级单位">
          <fks-input :value="vdUserInfo.userDepName" disabled readonly/>
        </fks-form-item>
        <fks-form-item label="三级部门">
          <fks-input :value="vdUserInfo.userDepName2" disabled readonly/>
        </fks-form-item>
      </fks-form>
      <footer>

        <div>
          <fks-divider/>
          <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                      @click="submit">
            提交
          </fks-button>
        </div>
      </footer>
    </div>
  </fks-drawer>
</template>
<script>
import FormUpload from '@/modules/FormCenter/components/FormUpload';
import emmiter from 'fawkes-lib/lib/mixins/emitter';
import {mapState} from 'vuex';
import * as types from '@/store/Getter/getterTypes.js';
import validator from '@/mixins/validator.js';
import BatchAddForm from './BatchAddForm.vue';
import dayjs from "dayjs";
import {
  addDriver,
  batchAddDriver,
  bindDiver,
  updateDriver
} from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import { getVdUserInfo } from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import { deleteDraft, DRIVER } from '@utils/draftUtil'

export default {
  mixins: [emmiter, validator],
  components: {
    FormUpload,
    BatchAddForm
  },
  data() {
    return {
      formData: {},
      vdUserInfo: {},
      createTime: null,
      loading: false,
      visible: false,
      driverInfo: {},
      checked: false,
      disabledConfirm: true
    }
  },
  props: {
    type: {
      type: String,
      default: 'plus'
    }
  },
  computed: {
    ...mapState(['portal']),
    isCompanyPortal() {
      return this.portal.name === '数据舱'
    },
    theme() {
      return this.$store.getters[types.THEME];
      // return '#2F54EB'
    },
    title() {
      const typeObj = {
        plus: '批量新增'
      }
      return typeObj[this.type]
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    username() {
      return this.$storage.get('username')
    },
    userFullName() {
      return this.$storage.get('userFullname')
    },
  },
  mounted() {
    this.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    getVdUserInfo(this.userInfo.id).then(res => {
      if (res.status) {
        this.vdUserInfo = res.data || {};
      }
    })
  },
  methods: {
    beforeClose(done) {
      // this.visible = false;
      // // 保存草稿
      // this.$refs.addFormRef.cleanTableData();
      // this.loading = false;
      // done()
      // tableData
      const tableData = this.$refs.addFormRef.tableData;
      console.log(tableData,'==tableData====')
      let flag = tableData.some(item => {
        const excludeKeys = ['searchCar','searchPhone']
        const isOtherKeyHasVal = Object.keys(item).some(key => {
          return !excludeKeys.includes(key) && item[key];
        });
        return isOtherKeyHasVal
      })
      if (flag) { // 发生变化
        this.$emit('openDialog')
      } else {
        this.visible = false;
        // 保存草稿
        this.$refs.addFormRef.cleanTableData();
        this.loading = false;
        done()
      }
    },
    cleanData() {
      this.$refs.addFormRef.cleanTableData();
      this.loading = false;
    },
    handlePreCheck(e) {
      // 检查是否有选中项
      if (this.disabledConfirm) {
        this.$message.warning('请选择要删除的行');
        e.preventDefault();
        e.stopPropagation();
      }
    },
    handleDelete() {
      const selectedRows = this.$refs.addFormRef.selectedRows;
      if (!selectedRows.length) {
        this.$message.warning('请选择要删除的行');
        return;
      }
      this.$refs.addFormRef.delSelectedRows();
    },
    handleAdd() {
      this.$refs.addFormRef.addRows();
    },
    async submit() {
      try {
        await this.$refs.addFormRef.validateTable();
      } catch (e) {
        return false;
      }
      this.loading = true;
      const tableData = this.$refs.addFormRef.tableData;
      // 提交数据
      const payload = JSON.parse(JSON.stringify(tableData));

      payload.forEach(item => {
        if (!this.isCompanyPortal) {
           item.projectId = this.portal.id;
           item.projectName = this.portal.name;
        }
        // item.projectId = this.portal.id;
        // item.projectName = this.portal.name;
        item.driverRzTime ? item.driverRzTime = dayjs(item.driverRzTime).format('YYYY-MM-DD HH:mm:ss') : ''
        item.driverLzTime ? item.driverLzTime = dayjs(item.driverLzTime).format('YYYY-MM-DD HH:mm:ss') : ''
        item.driverJszdqTime ? item.driverJszdqTime = dayjs(item.driverJszdqTime).format('YYYY-MM-DD HH:mm:ss') : ''
      })

      const res = await batchAddDriver(payload);
      if (!res.status) {
        this.loading = false;
        this.$message.error(res.message || `新增失败！`);
        return false;
      }

      this.loading = false;
      this.$refs.addFormRef.cleanTableData();
      this.broadcast('FormUpload', 'formSubmit', true);
      this.$message.success(`批量新增成功`);
      this.$emit('updateTable');
      deleteDraft(this.userInfo.id, DRIVER)
      this.$nextTick(() => {
        this.beforeClose(() => {
        });
      });
    },
    open() {
      this.visible = true;
      // 调用接口查询是否有草稿数据
      this.$nextTick(() => {
        this.$refs.addFormRef.loadData();
      })
    },
  }
}
</script>

<style lang='less' scoped>
@import "~@/styles/disabled";
@import "~@/styles/drawer";

/deep/ .fks-date-editor.fks-input {
  width: 100%;
}

.dialog {
  font-size: 32px;
}

.table-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: space-between;
  height: 100%;
}

.sub-btn {
  padding: 5px 10px;
  color: #333;
  transition: all 0.3s ease; /* 平滑过渡效果 */
}

.sub-btn:hover {
  color: #333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.sub-btn:active {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
  transform: translateY(5px); /* 模拟点击下沉效果 */
}


@import '~@/styles/input';
@import "~@/styles/button";
</style>
