<template>
  <div>
    <fks-drawer
    :before-close="beforeClose"
    :modal="false"
    :visible.sync="visible"
    :wrapperClosable="true"
    class="dialog"
    direction="rtl"
    size="660px"
  >
    <template slot="title">
      <div class="dialog-title flex col-center">
        <i :class="'fks-icon-' + type" :style="{ color: theme }" class="m-r-10"/>
        <span>{{ title }}司机</span>
      </div>
    </template>
    <div class="drawer-container">
<!--      add-form 组件需要在提交完成后销毁-->
      <add-form
        v-if="visible"
        ref="addFormRef"
        type="plus"
        layout-single-line
        class="flex-grow-1 overflow-y-auto"
      />
      <div style="border-top: 1px solid #F1F1F0;padding-top: 4px">
        <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                    @click="submit">
          提交
        </fks-button>
      </div>
    </div>
    </fks-drawer>
    <fks-dialog
      title="关闭"
      :visible.sync="tipVisible"
      size="small"
    >
      <div style="padding: 16px 20px">
        <span>表单内容发生改变是否继续关闭?</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="tipVisible = false">取 消</fks-button>
        <fks-button type="primary" @click="onConfirmTip">确 定</fks-button>
      </span>
    </fks-dialog>
  </div>

</template>
<script>
import FormUpload from '@/modules/FormCenter/components/FormUpload';
import emmiter from 'fawkes-lib/lib/mixins/emitter';
import {mapState} from 'vuex';
import * as types from '@/store/Getter/getterTypes.js';
import validator from '@/mixins/validator.js';
import AddForm from './AddForm';
import dayjs from "dayjs";
import {
  addDriver,
  bindDiver,
  updateDriver
} from "@modules/ProjectCar/ProjectPortal/DriverManage/api";

export default {
  mixins: [emmiter, validator],
  components: {
    FormUpload,
    AddForm
  },
  props: {
    type: {
      type: String,
      default: 'view'
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      driverInfo: {},
      tipVisible: false
    }
  },
  computed: {
    ...mapState(['portal']),
    isCompanyPortal() {
      return this.portal.name === '数据舱'
    },
    theme() {
      return this.$store.getters[types.THEME];
      // return '#2F54EB'
    },
    title() {
      const typeObj = {
        plus: '新增',
        edit: '编辑',
        view: '查看'
      }
      return typeObj[this.type]
    }
  },
  methods: {
    onConfirmTip() {
      this.handleClose()
      this.tipVisible = false
    },
    beforeClose(done) {
      // this.$refs.addFormRef.$refs.form.resetFields();
      // this.$refs.addFormRef.$refs.form.clearValidate();
      // this.visible = false;
      // done()
      const formData = this.$refs.addFormRef.formData
      const files1 = this.$refs.addFormRef.files1
      const files2 = this.$refs.addFormRef.files2
      const excluedeKeys = ['attachment1','attachment2']
      const otherKeysHasVal = Object.keys(formData).some(key => {
        return !excluedeKeys.includes(key) && formData[key]
      })

      if ((done instanceof Function) && (otherKeysHasVal || files1 || files2)) {
        this.tipVisible = true
      } else {
        this.$refs.addFormRef.$refs.form.resetFields();
        this.$refs.addFormRef.$refs.form.clearValidate();
        this.visible = false;
        (done instanceof Function) && done()
      }
    },
    handleClose() {
      this.$refs.addFormRef.$refs.form.resetFields();
      this.$refs.addFormRef.$refs.form.clearValidate();
      this.visible = false;
    },
    async submit() {
      try {
        await this.$refs.addFormRef.$refs.form.validate();
      } catch (e) {
        return false;
      }
      this.loading = true;
      const formData = this.$refs.addFormRef.formData;
      const payload = Object.assign({}, formData);
      if (!this.isCompanyPortal) {
         payload.projectId = this.portal.id;
         payload.projectName = this.portal.name;
      }
      // payload.projectId = this.portal.id;
      // payload.projectName = this.portal.name;
      payload.driverRzTime ? payload.driverRzTime = dayjs(payload.driverRzTime).format('YYYY-MM-DD HH:mm:ss') : ''
      payload.driverLzTime ? payload.driverLzTime = dayjs(payload.driverLzTime).format('YYYY-MM-DD HH:mm:ss') : ''
      payload.driverJszdqTime ? payload.driverJszdqTime = dayjs(payload.driverJszdqTime).format('YYYY-MM-DD HH:mm:ss') : ''
      if (this.driverInfo.id) {
        payload.id = this.driverInfo.id;
      }
      const res = this.isEdit || this.driverInfo.id ? await updateDriver(payload) : await addDriver(payload);
      if (!res.status) {
        this.loading = false;
        return false;
      }
      const carInfo = this.$refs.addFormRef.carInfo;
      if (carInfo && carInfo.id) {
        const resDiver = await bindDiver({carId: carInfo.id, driverId: res.data.id});
        if (!resDiver.status) {
          this.loading = false;
          this.driverInfo = res.data;
          this.$message.error(resDiver.message || `绑定司机失败！`);
          this.$emit('updateTable');
          return false;
        }
      }
      this.loading = false;
      this.broadcast('FormUpload', 'formSubmit', true);
      this.$message.success(`${this.isEdit ? '编辑' : '新增'}成功`);
      this.$emit('updateTable');
      this.$nextTick(() => {
        this.beforeClose();
      });
    },
    open() {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.addFormRef.queryTipsContent(this.portal.id);
      })
    },
  }
}
</script>

<style lang='less' scoped>
@import "~@/styles/disabled";
@import "~@/styles/drawer";

/deep/ .fks-date-editor.fks-input {
  width: 100%;
}

.drawer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dialog {
  font-size: 32px;
}

.sub-btn {
  padding: 5px 10px;
  color: #333;
  transition: all 0.3s ease; /* 平滑过渡效果 */
}
</style>
