<template>
  <fks-table
    ref="table"
    :data="tableData"
    :header-cell-style="{
      background: 'transparent',
      color: '#333333aa !important',
      fontWeight: 'unset !important',
    }"
    :cell-style="{ color: '#333333 !important' }"
    style="overflow-y: auto;"
    :height="tableHeight"
    class="full-width"
    @selection-change="handleSelectionChange"
    @paste.native="handlePaste($event)"
    :sticky="true"
  >
    <!-- 选择框 -->
    <fks-table-column type="selection" width="50" />
    <!-- 序号 -->
    <fks-table-column type="index" label="序号" width="50" />

    <fks-table-column width="250" prop="driverPhone">
      <template slot="header">
        <red-star title="联系电话" />
      </template>
      <template slot-scope="scope">
        <fks-row style="display: flex; justify-content: center">
          <fks-col :span="16">
            <fks-input
              v-model="scope.row.driverPhone"
              :disabled="isView"
              placeholder="请输入联系电话"
              autocomplete="off"
              maxlength="11"
              @click.native="selectColumn(scope.row, scope.column, scope.$index)"
            />
          </fks-col>
          <fks-col :offset="1" :span="7" v-if="scope.row.originDriver != null" style="display: flex; justify-content: flex-start; align-items: center">
            <div
              style="
                display: inline-block;
                border-radius: 11px;
                color: #3c83ff;
                background: rgba(60, 131, 255, 0.2);
                padding: 4px 8px;
                font-size: 11px;
              "
            >
              {{ scope.row.originDriver }}
            </div>
            <fks-tooltip
              v-if="scope.row.originDriver && scope.row.originDriver !== '新增'"
              class="item" effect="dark" content="已存在，当前操作修改原有信息" placement="right">
              <i style=" margin-left:3px; color:rgba(255, 164, 24, 1)" class="fks-icon-warning"></i>
            </fks-tooltip>
          </fks-col>
        </fks-row>
      </template>
    </fks-table-column>

    <fks-table-column width="150" prop="driverFullName">
      <template slot="header">
        <red-star title="姓名" />
      </template>
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.driverFullName"
          :disabled="isView"
          autocomplete="off"
          placeholder="请输入姓名"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

<!--    <fks-table-column label="用车类型" width="180" prop="projectUseCarType">-->
<!--      <template slot-scope="scope">-->
<!--        <fks-select-->
<!--          v-model="scope.row.projectUseCarType"-->
<!--          placeholder="请选择用车类型"-->
<!--          class="full-width"-->
<!--          @click.native="selectColumn(scope.row, scope.column, scope.$index)"-->
<!--        >-->
<!--          <fks-option-->
<!--            v-for="item in projectCarType"-->
<!--            :key="item.code"-->
<!--            :label="item.value"-->
<!--            :value="item.key"-->
<!--          />-->
<!--        </fks-select>-->
<!--      </template>-->
<!--    </fks-table-column>-->
    <fks-table-column label="项目名称" width="180" prop="projectName" v-if="isCompanyPortal">
      <template slot="header">
        <red-star title="项目名称" />
      </template>
      <template slot-scope="scope">
        <fks-select
          v-model="scope.row.projectId"
          placeholder="请选择项目名称"
          class="full-width"
          @change="(val) => handleChangePro(val, scope.row)"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          filterable
        >
        <fks-option
          v-for="portal in authPortalList"
          :key="portal.id"
          :label="portal.projectName"
          :value="portal.portalId"
        />
        </fks-select>
      </template>
    </fks-table-column>
    <fks-table-column width="130" prop="driverSex">
      <template slot="header">
        <red-star title="性别" />
      </template>
      <template slot-scope="scope">
        <fks-select

          v-model="scope.row.driverSex"
          :disabled="isView"
          :placeholder="isView ? '' : '选择性别'"
          class="full-width"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        >
          <fks-option :label="'男'" :value="'男'"/>
          <fks-option :label="'女'" :value="'女'"/>
        </fks-select>
      </template>
    </fks-table-column>

    <fks-table-column label="驾龄" width="150" prop="driverAge">
      <template slot-scope="scope">
        <fks-input-number
          v-model="scope.row.driverAge"
          placeholder="请输入驾龄"
          :min="0"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <fks-table-column label="年龄" width="150" prop="driverAge2">
      <template slot-scope="scope">
        <fks-input-number
          v-model="scope.row.driverAge2"
          disabled
          placeholder="请输入年龄"
          :min="0"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <fks-table-column label="驾驶证号" width="200" prop="driverIdNumber">
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.driverIdNumber"
          placeholder="请输入驾驶证号"
          controls-position="right"
          @blur="handleIdNumberBlur(scope.row)"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>


    <fks-table-column width="200" prop="driverResource2">
      <template slot="header">
        <red-star title="司机来源" />
      </template>
      <template slot-scope="scope">
        <fks-select
          v-model="scope.row.driverResource2"
          filterable
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          :disabled="isView"
          :placeholder="isView ? '' : '请选择司机来源'"
          class="full-width"
        >
          <fks-option
            v-for="item in driverResourceOption"
            :key="item.code"
            :label="item.value"
            :value="item.key"
          />
        </fks-select>
      </template>
    </fks-table-column>

    <fks-table-column label="月度租赁价格" width="220" prop="driverZlFy1">
      <template slot-scope="scope">
        <fks-input-number
          :disabled="isView"
          :placeholder="isView ? '' : ''"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          v-model="scope.row.driverZlFy1" />
      </template>
    </fks-table-column>


    <fks-table-column width="220" prop="driverJsgznxCs">
      <template slot="header">
        <red-star title="初始工作年限" />
      </template>
      <template slot-scope="scope">
        <fks-input-number
          :min="0"
          :disabled="isView"
          :placeholder="isView ? '' : ''"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          v-model="scope.row.driverJsgznxCs" />
      </template>
    </fks-table-column>


    <fks-table-column label="累计工作年限" width="220" prop="driverJsgznxLj">
      <template slot-scope="scope">
        <fks-input-number
          :disabled="isView"
          :min="0"
          :placeholder="isView ? '' : ''"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          v-model="scope.row.driverJsgznxLj" />
      </template>
    </fks-table-column>

    <fks-table-column width="150" prop="driverStatus">
      <template slot="header">
        <red-star title="状态" />
      </template>
      <template slot-scope="scope">
        <fks-select
          v-model="scope.row.driverStatus"
          :placeholder="isView ? '' : '请选择状态'"
          class="full-width"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        >
          <fks-option
            v-for="item in driverStatusOption"
            :key="item.code"
            :label="item.value"
            :value="item.key"
          />
        </fks-select>
      </template>
    </fks-table-column>

    <!-- 入职日期 -->
    <fks-table-column width="200" prop="driverRzTime">
      <template slot="header">
        <red-star title="入职日期" />
      </template>
      <template slot-scope="scope">
        <fks-date-picker
          v-model="scope.row.driverRzTime"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          :disabled="isView"
          :placeholder="isView ? '' : '请选择入职日期'"
          type="date"
          class="full-width"
        />
      </template>
    </fks-table-column>

    <!-- 离职日期 -->
    <fks-table-column label="离职日期" width="200" prop="driverLzTime">
      <template slot-scope="scope">
        <fks-date-picker
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          v-model="scope.row.driverLzTime"
          :placeholder="isView ? '' : '请选择离职日期'"
          type="date"
          class="full-width"
        />
      </template>
    </fks-table-column>

    <!-- 驾驶证到期日期 -->
    <fks-table-column label="驾驶证到期日期" width="220" prop="driverJszdqTime">
      <template slot-scope="scope">
        <fks-date-picker
          v-model="scope.row.driverJszdqTime"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          :disabled="isView"
          :placeholder="isView ? '' : '请选择驾驶证到期日期'"
          type="date"
          class="full-width"
        />
      </template>
    </fks-table-column>

    <!-- 驾驶证 -->
    <fks-table-column label="驾驶证" width="200">
      <template slot-scope="scope">
        <input-attachment
          v-if="scope.row.driverPhone"
          :form-data="scope.row"
          :disabled="isView"
          attachment-name="attachment1"
        />
      </template>
    </fks-table-column>

    <!-- 其他资料 -->
    <fks-table-column label="其他资料" width="200">
      <template slot-scope="scope">
        <input-attachment
          v-if="scope.row.driverPhone"
          :form-data="scope.row"
          :disabled="isView"
          attachment-name="attachment2"
        />
      </template>
    </fks-table-column>

    <fks-table-column label="备注" width="180" prop="remark1">
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.remark1"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          :disabled="isView"
          autocomplete="off"
          placeholder="请输入备注"
        />
      </template>
    </fks-table-column>

    <fks-table-column label="绑定车辆" width="220" v-if="lineCar" prop="carNum">
      <template slot-scope="scope">
        <fks-select
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          v-model="scope.row.carNum"
          :disabled="isView || isEdit"
          :loading="loadingCarNum"
          :placeholder="isView ? '' : '请选择车牌号'"
          class="full-width"
          clearable
          maxlength="8"
          @change="(val) => changeCarNum(val, scope.row)"
        >
          <fks-option
            v-for="item in getCarNumList(scope.row)"
            :key="item.carNum"
            :label="item.carNum"
            :value="item.carNum"
          >
          </fks-option>
        </fks-select>
      </template>
    </fks-table-column>

    <fks-table-column label="车辆信息" width="180" v-if="lineCar">
      <template slot-scope="scope">
        <div
          v-if="scope.row.carNum"
          style="
            display: inline-block;
            border-radius: 11px;
            color: #3c83ff;
            background: rgba(60, 131, 255, 0.2);
            padding: 4px 8px;
            font-size: 11px;
          "
        >
          {{ scope.row.carNum }}
        </div>
      </template>
    </fks-table-column>
    <fks-table-column label="已关联司机" width="180" v-if="lineCar">
      <template slot-scope="scope">
        <div
          v-if="scope.row.carDriver"
          style="
            display: inline-block;
            border-radius: 11px;
            color: #3c83ff;
            background: rgba(60, 131, 255, 0.2);
            padding: 4px 8px;
            font-size: 11px;
          "
        >
          {{ scope.row.carDriver }}
        </div>
        <span v-else> / </span>
      </template>
    </fks-table-column>
  </fks-table>
</template>

<script>
import validator from '@/mixins/validator.js';
import {
  getCarList,
  getCarListByPost,
  getDriverInfoByCar
} from "@modules/ProjectCar/ProjectPortal/CarManage/api";
import { getCar, getDrierByPhone } from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import {mapState} from "vuex";
import { saveDraftDebounced, getDraft, DRIVER, deleteDraft, CAR } from '@utils/draftUtil'
import fi from 'fawkes-lib/src/locale/lang/fi'
import { debounce } from '@utils/util'
import RedStar from "@components/red-star.vue";
import * as StateTypes from "@store/State/stateTypes";
import {PROJECT_PORTAL} from "@utils/constants";
import * as types from '@store/Action/actionTypes'
import multiSelection from "@/mixins/multiSelection";
import InputAttachment from '@components/InputAttachment/pc-view.vue'

export default {
  name: "BatchAddDriverForm",
  mixins: [validator, multiSelection],
  components: {RedStar, InputAttachment},
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    // 是否关联车辆
    lineCar: {
      type: Boolean,
      default: false
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    },
    tableHeight: {
      type: Number,
      default: 500
    }
  },
  data() {
    return {
      carInfo: {}, // 选择的车辆信息
      loadingCarNum: false,
      tableData: [],
      saveDraft: false,
      draftList: [],
      formData: {
        driverPhone: '', // 联系电话
        driverFullName: '', // 姓名
        driverAge: '', // 司机驾龄
        driverAge2: '', // 司机年龄
        driverIdNumber: '', // 身份证号
        driverStatus: '', // 状态
        driverRzTime: '', // 入职日期
        driverLzTime: '', // 离职日期
        driverJszdqTime: '', // 驾驶证到期日期
        files1: 0,
        attachment1: '',
        files2: 0,
        attachment2: '',
        remark1: '', // 备注
        carNum: '', // 绑定车辆
        carType: '', // 品牌型号
        carName: '', // 车辆名称
        carSeatNum: '', // 座位数
        originDriver: null,
        carDriver: '',
        driverJsgznxCs: '',
        driverJsgznxLj: '',
        driverSex: ''
      },
      backFromData: {},
      selectedRows: [],
      selectTableColumn: "",
      selectTableIndex: 0,
      projectHashTable: {}
    }
  },
  mounted() {
    this.addRows()
    this.$store.dispatch(types.GET_AUTH_PORTAL)
  },
  watch: {
    lineCar: {
      handler(val) {
        this.tableData.forEach(item => {
          item.carNum = val ? item.carNumBack : null;
        })
        this.$nextTick(() => {
          // 2. 调用 doLayout 来重新计算表格布局
          if (this.$refs.table && typeof this.$refs.table.doLayout === 'function') {
            this.$refs.table.doLayout();
          }
        });
      },
      deep: true, // 深度监听，以监听到对象内部的变化
    },
    // 监听 tableData 的变化
    tableData: {
      handler(newVal) {
        if (this.saveDraft) {
          if (newVal && newVal.length > 0) {
            const hasData = newVal.some(item => {
              return this.hasData(item);
            });
            if (hasData) {
              this.saveDrafts();
            }
          }
        }

        newVal.forEach(item => {
          if (item.driverPhone !== item.previousDriverPhone) {
            this.debouncePhoneChange(item)
            item.previousDriverPhone = item.driverPhone;
          }

          if (item.carNum !== item.prevcarNum) {
            this.debounceCarChange(item)
            item.prevcarNum = item.carNum;
          }
        })
      },
      deep: true, // 深度监听，以监听到对象内部的变化
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['applyResource', 'userInfo', 'openId', 'portal']),
    ...mapState([StateTypes.AUTH_PORTALS]),
    isCompanyPortal() {
      return this.portal.name === '数据舱'
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter(item => item.projectStatus === 100); // 100激活，200关闭
    },
    span() {
      return this.layoutSingleLine ? 24 : 12;
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    isEdit() {
      return this.type === 'edit';
    },
    isView() {
      return this.type === 'view';
    },
    isAdd() {
      return this.type === 'plus';
    },
    driverStatusOption() {
      return this.enums.DriverStatusEnums;
    },
    driverResourceOption() {
      return this.enums.DriverResourceEnums;
    },
    projectCarType() {
      return this.enums.ProjectUseCarTypeEnums
    },
    flowConfig() {
      if (this.type === 'view') {
        return Object.keys(this.formData).reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      if (this.isEdit) {
        return ['carNum', 'carType', 'carSeatNum'].reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      return {}
    },
    rules() {
      return {
        driverFullName: [
          {required: true, message: '请输入姓名', trigger: 'change'}
        ],
        driverPhone: [
          {required: true, message: '请输入联系电话', trigger: 'change'},
          {required: true, validator: this.phoneValidator, trigger: 'blur'}
        ],
        driverStatus: [
          {required: true, message: '请选择状态', trigger: 'change'}
        ],
        driverRzTime: [
          {required: true, message: '请选择入职日期', trigger: 'change'}
        ]
      }
    },
  },
  methods: {
    getCarNumList(row) {
      if (row.projectId) {
        return this.projectHashTable[row.projectId] || [];
      }
      return [];
    },
    handleChangePro(val,row) {
      const name = this.authPortalList.find(item => item.portalId === val).projectName
      this.$set(row,'projectName',name)
      // 检查项目-车牌号哈希表里是否已有当前项目id的缓存列表，没有则调用接口获取
      if(!this.projectHashTable[val]) {
        this.getCarNumData(val)
      }
    },
    handleIdNumberBlur(row) {
      const idNumber = row.driverIdNumber;
      if (idNumber.length === 18) {
        // 提取出生日期
        const year = parseInt(idNumber.substring(6, 10));
        const month = parseInt(idNumber.substring(10, 12)) - 1; // 月份从 0 开始
        const day = parseInt(idNumber.substring(12, 14));
        const birthDate = new Date(year, month, day);

        // 计算年龄
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const m = today.getMonth() - birthDate.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
          age--; // 如果还未到生日，年龄减一
        }

        // 更新年龄
        this.$set(row, 'driverAge2', age);
      } else {
        // 如果身份证号不合法，清空年龄
        row.driverAge2 = '';
      }
    },
    hasData(item) {
      const formData = item;
      return formData.driverFullName || formData.driverAge || formData.driverPhone || formData.carNumber ||
        formData.carType || formData.carName || formData.carSeatNum || formData.driverStatus ||
        formData.driverRzTime || formData.driverLzTime || formData.driverJszdqTime ||
        formData.files1 > 0 || formData.attachment1 || formData.files2 > 0 || formData.attachment2 ||
        formData.remark1 || formData.projectId
;
    },
    loadData() {
      getDraft(this.userInfo.id, DRIVER).then(res => {
        if (res.data) {
          this.draftList = JSON.parse(res.data);
          if (this.draftList && this.draftList.length > 0) {
            this.openHTML();
          }
        }
      })
    },
    loadDraftData() {
      this.saveDraft = false;
      // 模拟从草稿加载数据
      this.tableData = [...this.draftList];
      this.$nextTick(() => {
        this.$message.success('草稿已恢复')
      });
    },
    // 保存草稿
    saveDrafts() {
      let context = JSON.stringify(this.tableData);
      saveDraftDebounced(this.userInfo.id, DRIVER, context)
        .then(() => {
          // this.$message.success('草稿保存成功');
        })
        .catch((error) => {
          console.error('保存草稿失败:', error);
        });
    },
    getCarNumData(projectId) {
      this.loadingCarNum = true;
      getCarListByPost({projectId, pageSize: 50, pageNo: 1}).then(res => {
        this.loadingCarNum = false;
        this.$set(this.projectHashTable, projectId, res.data.list || [])
      }).catch(() => {
        this.loadingCarNum = false;
      })
    },
    debouncePhoneChange(row) {
      if (!row.searchPhone) {
        row.searchPhone = debounce(this.phoneBlur, 500, false)
      }
      row.searchPhone(row)
    },
    debounceCarChange(row) {
      if (!row.searchCar) {
        row.searchCar = debounce(this.changeCarNum, 500, false)
      }
      row.searchCar(row.carNum, row)
    },
    phoneBlur(item) {
      let projectId = this.$route.params.projectId || this.portal.id;
      let phone = item.driverPhone;
      getDrierByPhone({
        phone, projectId
      }).then((res) => {
        let driver = res.data;
        if (driver) {
          Object.keys(driver).forEach(key => {
            this.$set(item, key, driver[key]);
          });
          this.$set(item, 'originDriver', driver.driverFullName);

          getCar({driverIds: driver.id}).then(res => {
            let carData = res.data;
            if (carData && carData[driver.id]) {
              this.$set(item, 'carNumBack', carData[driver.id].carNum);
              if (this.lineCar) {
                this.$set(item, 'carNum', carData[driver.id].carNum);
              }
            }
          })
        } else {
          if (this.hasData(item)) {
            if (item.id) {
              const keepFields = ['carNum', 'previousDriverPhone', 'carNumBack'];
              // 遍历 item 的所有键
              Object.keys(item).forEach((key) => {
                // 如果不在保留列表里，就置空
                if (!keepFields.includes(key)) {
                  this.$set(item, key, null);
                }
              });
            } else {
              this.$set(item, 'id', null);
            }
            this.$set(item, 'originDriver', item.driverPhone ? '新增' : null);
          }
        }
      })
    },
    changeCarNum(val, row) {
      if (this.projectHashTable[row.projectId]) {
        const carInfo = this.projectHashTable[row.projectId].find(item => item.carNum === val);
        // 更新行数据
        row.carName = carInfo?.carName;
        row.carId = carInfo?.id;
        if (carInfo) {
          getDriverInfoByCar(carInfo.id).then((res) => {
            if (res.data) {
              this.$set(row, "carDriver", res.data[carInfo.id].driverFullName);
            } else {
              this.$set(row, "carDriver", null);
            }
          })
        }
      }

    },
    validateTable() {
      const validationErrors = [];

      // 字段名称映射，用于显示友好的错误信息
      const fieldLabels = {
        driverFullName: '姓名',
        driverPhone: '联系电话',
        driverSex: '性别',
        driverStatus: '状态',
        driverRzTime: '入职日期',
        driverJsgznxCs: '初始工作年限',
        driverResource2: '司机来源',
        driverZlFy1: '月度租赁价格',
        projectId: '项目名称'
      };

      this.tableData.forEach((data, index) => {
        const rowErrors = [];
        const rowNumber = index + 1;

        // 检查基础必填字段
        const requiredFields = ['driverFullName', 'driverPhone', 'driverSex', 'driverStatus', 'driverRzTime', 'driverJsgznxCs', 'driverResource2'];

        requiredFields.forEach(field => {
          if (!data[field]) {
            rowErrors.push(fieldLabels[field]);
          }
        });

        // 检查手机号格式
        if (data.driverPhone && !/^[1-9]\d{10}$/.test(data.driverPhone)) {
          rowErrors.push('联系电话格式不正确（应为11位手机号）');
        }

        // 检查司机来源为租赁时的额外字段
        if (data.driverResource2 === 200 && !data.driverZlFy1) {
          rowErrors.push(fieldLabels.driverZlFy1);
        }

        // 检查公司门户下的项目选择
        if (this.isCompanyPortal && !data.projectId) {
          rowErrors.push(fieldLabels.projectId);
        }

        // 如果当前行有错误，添加到总错误列表
        if (rowErrors.length > 0) {
          validationErrors.push(`第${rowNumber}行缺少：${rowErrors.join('、')}`);
        }
      });

      if (validationErrors.length > 0) {
        // 显示详细的验证错误信息
        const errorMessage = validationErrors.length > 3
          ? `${validationErrors.slice(0, 3).join('；')}；等${validationErrors.length}项错误，请检查表格信息！`
          : `${validationErrors.join('；')}，请完善表格信息！`;

        this.$message.warning(errorMessage);
        return Promise.reject(false);
      }

      // 如果表格信息完整，返回 Promise.resolve 表示验证通过
      return Promise.resolve(true);
    },
    addRows() {
      this.$nextTick(() => {
        this.saveDraft = false;
        const tableElement = this.$refs.table.$el;  // 获取表格 DOM 元素
        tableElement.scrollTop = tableElement.scrollHeight;  // 滚动到最底部
        let emptyData = JSON.parse(JSON.stringify(this.formData));

        let list = this.tableData
        list.push(emptyData);
        this.$set(this.$data, "tableData", list);
      })
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    // 删除选中的行
    delSelectedRows() {
      this.tableData = this.tableData.filter(row => !this.selectedRows.includes(row));
      this.$refs.table.clearSelection();  // 清除表格选中的状态
      this.saveDrafts();
    },
    cleanTableData() {
      this.saveDraft = false;
      let emptyData = JSON.parse(JSON.stringify(this.formData));
      this.tableData = [emptyData]
    },
    some(collection, predicate) {
      // 如果 predicate 是函数，直接使用它，否则使用默认检查值是否存在的函数
      const check = typeof predicate === 'function' ? predicate : (val) => !!val;
      // 遍历集合，遇到符合条件的元素返回 true
      for (let item of collection) {
        if (check(item)) {
          return true;
        }
      }
      return false;
    },

    removeDraft() {
      deleteDraft(this.userInfo.id, DRIVER)
      this.$nextTick(() => {
        this.$message.success('草稿删除成功')
      });
    },
    openHTML() {
      // 保存当前 Vue 实例的引用
      const self = this;

      // 定义点击恢复的事件处理函数
      const restoreDraft = () => {
        self.loadDraftData(); // 调用你在 methods 里定义的 loadDraftData 函数
      };

      // 定义点击恢复的事件处理函数
      const removeDraft = () => {
        self.removeDraft(); // 调用你在 methods 里定义的 loadDraftData 函数
      };
      // 使用 Vue 的 VNode 创建可点击的 HTML 元素
      this.$message({
        dangerouslyUseHTMLString: true,
        type: "warning",
        message: `
          <span>存在草稿，是否恢复？</span>
          <a href="javascript:void(0);" style="color: #027AFF;" id="restoreDraft">恢复</a>
          <a href="javascript:void(0);" style="color: #FF4D4F;" id="removeDraft">删除</a>
        `,
        onClose: () => {
          const restoreLink = document.getElementById('restoreDraft');
          if (restoreLink) {
            restoreLink.removeEventListener('click', restoreDraft);
          }
          const removeLink = document.getElementById('removeDraft');
          if (removeLink) {
            removeLink.removeEventListener('click', removeDraft);
          }
        }
      });

      // 手动绑定事件到生成的链接
      this.$nextTick(() => {
        const restoreLink = document.getElementById('restoreDraft');
        if (restoreLink) {
          restoreLink.addEventListener('click', restoreDraft);
        }
        // 销毁弹窗时的回调
        const removeLink = document.getElementById('removeDraft');
        if (removeLink) {
          removeLink.addEventListener('click', removeDraft);
        }
      });
    },
    handlePaste(event) {
      event.preventDefault();
      // 获取剪贴板中的文本内容
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedData = clipboardData.getData('Text');  // 获取纯文本格式的数据

      let rows = pastedData
        .trim() // 去除首尾换行，避免空行
        .split('\n') // 按行拆分
        .map(row => row.split('\t').map(cell => cell === '' ? null : cell))
        .filter(row => row.some(cell => cell !== ''));  // 过滤掉完全为空的行

      let len = rows[0].length;

      const field = this.selectTableColumn;
      let columns = this.$refs.table.columns.map(column => column.property);
      let startIndex = columns.indexOf(field)
      const props = columns.slice(startIndex, startIndex + len);

      if (rows.length > 0) {
        const startRow = this.selectTableIndex;

        // 确保 tableData 的长度足够，至少有 startRow + rows.length 行
        while (this.tableData.length < startRow + rows.length) {
          this.tableData.push({});  // 添加空对象作为新行
        }

        rows.forEach((cell, rowIndex) => {
          const targetRow = startRow + rowIndex;

          // 遍历 props，从 props[0] 开始逐一处理
          props.forEach((prop, columnIndex) => {
            let value = cell ? cell[columnIndex] : '';
            if (value) {
              value = value.replace(/[\s\r]/g, '');
            }
            if (['driverStatus', 'driverResource2', 'projectUseCarType'].includes(prop)) {
              const mapObject = {
                driverStatus: this.driverStatusOption,
                driverResource2: this.driverResourceOption,
                projectUseCarType: this.projectCarType,
              };

              const map = mapObject[prop]; // 通过字段名获取对应的选项数组

              if (map && Array.isArray(map)) {
                const foundEntry = map.find(entry => entry.value === value);
                value = foundEntry ? foundEntry.key : '';
              }
            }
            // 将值写入对应的列
            this.$set(this.tableData[targetRow], prop, value);
          });
        });
      }
    },
    selectColumn(row, column, index) {
      // 设置当前选择的行
      this.saveDraft = true;
      const propName = column.property; // 获取当前列的字段名（prop）
      this.selectTableColumn = propName;
      this.selectTableIndex = index;
    },
  }
}
</script>

<style scoped lang="less">
@import '~@/styles/disabled';
@import '~@/styles/scrollbar';
</style>
