<template>
  <div class="flex flex-column full-height">
    <header class="card-header" v-if="showHeader">
      <div class="flex col-center m-b-24">
        <div class="card-title" :key="currentData.driverFullName">{{ currentData.driverFullName }}</div>
        <div class="m-l-20">
          <card-tag v-for="(tag, index) in currentData.tags" :key="index" :tag="tag"/>
          <card-tag class="m-l-10" :tag="{color: '#3C83FF', text: currentData.projectName}" />
        </div>
      </div>
    </header>
    <main class="flex-grow-1 flex flex-column overflow-y-auto overflow-x-hidden" style="max-width: 900px">
      <div class="flex-grow-1 overflow-y-auto form full-width overflow-x-hidden">
        <common-title title="司机信息" class="m-b-16" />
        <add-form ref="addFormRef" :type="type" layout-single-line />
      </div>
      <div class="footer-buttons" v-if="showButtons">
        <fks-button
          v-if="getAuth('edit')"
          slot="reference"
          @click="doEdit"
          :loading="buttonLoading"
          icon="fks-icon-edit"
          text
        >
          修改
        </fks-button>
        <fks-popconfirm title="确认删除？" @onConfirm="handleDelete" class="m-l-10">
          <fks-button
            v-if="getAuth('delete')"
            slot="reference"
            :loading="buttonLoading"
            icon="fks-icon-delete"
            dangerText
          >
            删除
          </fks-button>
        </fks-popconfirm>
      </div>
      <div class="footer-buttons" v-if="type === 'edit'">
        <fks-popconfirm title="确认提交？" @onConfirm="handleConfirm">
          <fks-button
            v-if="getAuth('edit')"
            slot="reference"
            :loading="buttonLoading"
            icon="fks-icon-check"
            text
          >
            提交
          </fks-button>
        </fks-popconfirm>

        <fks-button
          v-if="getAuth('edit')"
          @click="editCancel"
          slot="reference"
          :loading="buttonLoading"
          icon="fks-icon-close"
          text
        >
          取消
        </fks-button>
      </div>
    </main>
  </div>
</template>

<script>
import CardTag from "@components/CardFlow/components/tag.vue";
import AddForm from "./AddForm.vue"
import dayjs from "dayjs";
import {mapState} from "vuex";
import {
  bindDiver,
  deleteDriver,
  unbindCarWithDriver,
  updateDriver
} from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import EventBus from "@utils/eventBus";
import * as StateTypes from "@store/State/stateTypes";
import { getAuth } from '@utils/buttonAuth'

export default {
  name: "DriverForm" ,
  components: {CardTag, AddForm},
  props: {
    type: {
      type: String,
      default: 'view'
    },
    currentData: {
      type: Object,
    },
    showHeader: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      buttonLoading: false,
    }
  },
  computed: {
    ...mapState(['portal']),
    ...mapState([StateTypes.IS_PROJECT_CLOSED]),
    disableMutation() {
      return this[StateTypes.IS_PROJECT_CLOSED];
    },
    showButtons() {
      return this.type === 'view' && !this.disableMutation;
    }
  },
  methods: {
    getAuth,
    handleConfirm() {
      const formRef = this.$refs["addFormRef"].$refs.form;
      formRef.validate(async (valid) => {
        if (valid) {
          this.buttonLoading = true;
          const payload = Object.assign({}, this.$refs.addFormRef.formData);
          payload.driverRzTime ? payload.driverRzTime = dayjs(payload.driverRzTime).format('YYYY-MM-DD HH:mm:ss') : ''
          payload.driverLzTime ? payload.driverLzTime = dayjs(payload.driverLzTime).format('YYYY-MM-DD HH:mm:ss') : ''
          payload.driverJszdqTime ? payload.driverJszdqTime = dayjs(payload.driverJszdqTime).format('YYYY-MM-DD HH:mm:ss') : ''
          const res = await updateDriver(payload);
          this.buttonLoading = false;
          if (res.status) {
            this.$message.success('编辑成功');
            // 编辑成功后，通知CardFlow组件更新数据, 如果涉及到删除数据则传true，编辑数据则传false
            this.$emit('refresh', false)
            if (this.type === 'edit') {
              this.$emit('update:type', 'view')
            }
          }

          const carId = this.currentData.carId;

          const carInfo = this.$refs.addFormRef.carInfo;

          if (carId && ((!carInfo || !carInfo.id) || carId !== carInfo.id)) {
            const res = await unbindCarWithDriver({carId, driverId : payload.id})
          }

          if (carInfo && carInfo.id) {
            const resDiver = await bindDiver({carId: carInfo.id, driverId: res.data.id});
            if (!resDiver.status) {
              this.loading = false;
              this.driverInfo = res.data;
              this.$message.error(resDiver.message || `绑定司机失败！`);
              return false;
            }
          }
          this.$emit('refresh');

        }
      })
    },
    deleteUser() {
      EventBus.$emit('scroll', false);
      deleteDriver(this.currentData.id).then((res) => {
        EventBus.$emit('scroll', true);
        if (!res.status) {
          return false;
        }
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.$emit('refresh', true)
      })
    },
    async handleDelete() {
      if (this.currentData.id) {
        // 检查是否绑定了车辆，如果有需要先解绑
        const carId = this.currentData.carId;
        if (carId) {
          const driverId = this.currentData.id;
          const res = await unbindCarWithDriver({carId, driverId})
          if (res.status) {
            // 解绑成功后再删除司机
            this.deleteUser();
          }
        } else {
          this.deleteUser();
        }
      }
    },
    doEdit() {
      this.$refs.addFormRef.doEdit();
      this.$emit('update:type', 'edit')
    },
    editCancel() {
      this.$refs.addFormRef.editCancel();
      this.$emit('update:type', 'view')
    }
  },
  watch: {
    currentData: {
      immediate: true,
      handler(newVal) {
        this.$nextTick(() => {
          this.$refs.addFormRef.formData = JSON.parse(JSON.stringify(newVal))
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import "~@/styles/headers";
@import "~@/styles/button";
</style>
