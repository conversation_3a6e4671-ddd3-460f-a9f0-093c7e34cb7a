<template>
  <div>
    <TitleKit>
      <template #left>
        <div style="font-size: 16px; font-weight: 600">到期提醒</div>
      </template>
    </TitleKit>
    <TempTable
      ref="table"
      :data-list="tableData"
      :table-config.sync="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      :showButtons="showButtons"
      :current-page.sync="pageNo"
    >
      <template v-slot:column-dqType="{ scope }">
        {{ getType(scope.row.dqType) }}
      </template>

      <template v-slot:column-dqTime="{ scope }">
        {{ scope.row.dqTime ? $dayjs(scope.row.dqTime).format('YYYY-MM-DD') : '' }}
      </template>

      <template v-slot:column-expireStatus="{ scope }">
        <fks-tag :type="statusType[scope.row.expireStatus]" size="small">
          {{ getStatusData(scope.row.expireStatus, scope.row.syTime) }}
        </fks-tag>
      </template>

    </TempTable>

  </div>
</template>

<script>
import { getReminder } from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import { mapState } from 'vuex'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import TitleKit from '@components/PageTitle/TitleKit.vue'

export default {
  name: 'Reminder',
  components: { TitleKit, TempTable },
  data() {
    return {
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: [ 'filedConfig' ],
      queryParams: {
        projectId: null,
        conditions: [],
      },
      carInfo: null,
      visible: false,
      tableData: [],
      currentData: {},
      type: 'view',
      selectedFields: [],
      tableConfig: [
        {
          label: '项目名称',
          prop: 'projectName',
          group: '基础信息',
        },
        {
          label: '到期类型',
          prop: 'dqType',
          group: '基础信息',
          customer: true, // 使用了 slot 渲染
        },
        {
          label: '到期时间',
          prop: 'dqTime',
          group: '基础信息',
          customer: true, // 使用了格式化渲染
        },
        {
          label: '责任人',
          prop: 'zrrFullName',
          group: '基础信息',
        },
        {
          label: '到期状态',
          prop: 'expireStatus',
          group: '基础信息',
          customer: true, // 使用了 tag 和函数渲染
        },
      ],

      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      statusType: {
        ZC: 'success',
        DQ: 'danger',
        KDQ: 'Warning',
        YQ: 'danger',
      },
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ExpireStatusEnumsOption() {
      return this.enums.ExpireStatusEnums
    },
    ExpireTypeEnumsOption() {
      return this.enums.ExpireTypeEnums
    },
  },
  created() {
    this.getData()
  },
  methods: {
    // 到期状体处理、根据syTime(剩余时间)判断。正常（不显示时间），快到期（3天），到期（不显示时间），逾期（3天）逾期这里时间是负数，需特殊处理
    getStatusData(status, syTime) {
      if (status === 'KDQ') {
        return this.getStatus(status) + '（' + syTime + '天）'
      } else if (status === 'YQ') {
        let time = syTime ? syTime.toString().substring(1) : 0
        return this.getStatus(status) + '（' + time + '天）'
      } else {
        return this.getStatus(status)
      }
    },
    getStatus(val) {
      return this.ExpireStatusEnumsOption.find((item) => item.key === val)?.value
    },
    getType(val) {
      return this.ExpireTypeEnumsOption.find((item) => item.key === val)?.value
    },
    async getData() {
      try {
        const params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          userName: this.currUser.userName || this.$storage.get('username'),
        }
        this.loading = true
        const res = await getReminder(params)
        this.loading = false
        if (!res.status) {
          return false
        }
        const { total, list } = res.data
        this.total = total
        this.tableData = list
      } catch (e) {
        this.loading = false
      }
    },
    clear() {
      this.updateTable()
    },
    updateTable() {
      this.pageNo = 1
      this.getData()
    },
  },
}
</script>

<style scoped lang="less">
.query-table {
  padding: 0 10px 20px;
}
/deep/ .fks-query-pagebox {
  margin: 15px;
  bottom: 0 !important;
  right: 0 !important;
}
</style>
