.container {
  height: 100%;

  .content {
    height: calc(100% - 100px);

    .user-profile {
      background-image: url("~@/assets/img/my/profile-bg.png");
      background-size: cover;
      padding-left: 30px;
      padding-top: 24px;
      padding-bottom: 24px;

      .avatar {
        width: 164px;
        height: 164px;
        border: 2px solid #fff;
        border-radius: 50%;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.12);
      }

      .avatar-text {
        line-height: 164px;
        text-align: center;
        font-size: 82px;
        color: #fff;
        background-color: #4545d1;
      }

      header {
        display: flex;
        align-items: center;

        .information {
          margin-left: 30px;
          font-size: 32px;
          font-weight: bold;
        }
      }
    }

    .basic-infos {
      margin: 36px 60px;

      .title {
        font-size: 32px;
        font-weight: bold;
        color: #191919;
        margin-bottom: 30px;
      }

      .info {
        margin-bottom: 24px;
        display: grid;
        grid-template-columns: 40% 60%;

        .info-item {
          display: flex;
          align-items: center;

          span {
            display: inline-block;
            margin-left: 10px;
            letter-spacing: 2px;
            font-size: 28px;
            color: rgba(25, 25, 25, 0.6);
          }
        }

        .info-value {
          font-size: 28px;
          color: #191919;
          letter-spacing: 2px;
          text-align: right;
        }
      }
    }
  }
}


.versions_sty {
  text-align: center;
  color: #fff;
  margin-top: 20px;
  font-size: 20px;
  padding: 10px 15px;
  background: linear-gradient(129deg, #F0CFA0 0%, #C39156 100%);
  border-radius: 200px 200px 200px 0;
}
