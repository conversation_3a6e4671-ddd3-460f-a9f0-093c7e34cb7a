<template>
  <div class="container">
    <div class="content">
      <div class="user-profile">
        <header>
          <fm-image
            v-if="avatarUrl"
            :src="avatarUrl"
            fit="cover"
            round
            height="80"
            width="80"
          />
          <div v-else class="avatar avatar-text">
            {{ userFullName | getFamilyName }}
          </div>
          <div class="information">
            <div class="person_sty">
              <span>{{ userFullName }}</span>
            </div>
            <div class="versions_sty">V{{verison}}</div>
          </div>
        </header>
      </div>
      <div class="basic-infos">
        <div class="title">基本信息</div>
        <div
          v-for="(item, index) in infosList"
          :key="index"
          class="info"
        >
          <div class="info-item">
            <img :src="require(`@/assets/img/car/${item.icon}.png`)" class="">
            <span>{{ item.name }}：</span>
          </div>
          <div class="info-value">{{ getValue(item.key, item.key1) }}</div>
        </div>
        <fm-divider/>
        <!-- <div class="title">我的二维码</div>
        <qrcode :size="130" :value="qrCodeText"/> -->
        <!--        <div class="logout">-->
        <!--          <fm-button type="primary" block color="#ffffff" size="large" @click="logout">退出登录</fm-button>-->
        <!--        </div>-->
      </div>
      <portal-switch />
    </div>
    <main-tabbar v-if="isMain"></main-tabbar>
    <tabbar v-else></tabbar>
  </div>
</template>
<script>
import {Button, Cell, CellGroup, Divider, Image, NavBar, Overlay, Toast} from 'fawkes-mobile-lib'
import * as StateTypes from '@/store/State/stateTypes'
import MainTabbar from '@/modules/Tabbar/index'
import tabbar from '@/modules/CarApply/components/TabBar.vue'
import {mapGetters, mapState} from "vuex";
import * as GetterTypes from "@store/Getter/getterTypes";
import PortalSwitch from "@components/HomeHeader/components/portal-switch.vue";
import avatarMixin from "@/mixins/avatarMixin";
import storage from "@utils/storage";

const infosList = [
  {name: '手机', icon: 'icon_phone', key: 'userPhone', key1: 'phone'},
  {name: '二级单位', icon: 'icon_corp', key: 'userDepName'},
  {name: '三级部门', icon: 'icon_dep', key: 'userDepName2'}
]
export default {
  name: 'PersonalCenter',
  mixins: [avatarMixin],
  components: {
    PortalSwitch,
    tabbar,
    MainTabbar,
    [CellGroup.name]: CellGroup,
    [Divider.name]: Divider,
    [Cell.name]: Cell,
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Overlay.name]: Overlay,
    [Image.name]: Image,
    [Toast.name]: Toast,
  },
  data() {
    return {
      infosList,
      disableFinger: true,
      userDept: '',
      userComp: '',
      phone: '',
      show: false,
      showPic: false,
      email: '',
      imageUrl: '',
      loading: false,
      isBack: false,
      isMain: true,
      verison: ''
    }
  },
  computed: {
    ...mapGetters([GetterTypes.IS_NOT_PC]),
    ...mapState([StateTypes.USER_INFO, StateTypes.APPLY_RESOURCE]),
  },
  created() {
    this.isMain = this.$storage.get('isMainTab') === 'true'
    this.isBack = this.$storage.get('isMainTab') === 'false' && !this[GetterTypes.IS_NOT_PC];
  },
  mounted() {
    this.verison = localStorage.getItem('version');
  },
  methods: {
    getValue(key, key1) {
      const placeholder = key === 'userPhone' ? '暂无联系方式' : '暂无归属'
      const user = storage.getObject("user");
      return user[key] || (key1 ? user[key1] : '') || placeholder;
    }
  },
}
</script>
<style lang="less" scoped>
@import "./mobile-view";
</style>
