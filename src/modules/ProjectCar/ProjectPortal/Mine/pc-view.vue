<template>
<!--  <fks-query-page-->
<!--    v-loading="loading"-->
<!--    :current-page.sync="pageNo"-->
<!--    :data="data"-->
<!--    :page-size.sync="pageSize"-->
<!--    :page-sizes="[15, 20, 50, 100]"-->
<!--    :table-name="'记录列表'"-->
<!--    :total="total"-->
<!--    class="record-table"-->
<!--    highlight-current-row-->
<!--    layout="total, sizes, prev, pager, next, jumper"-->
<!--    @clear="onClear"-->
<!--    @query="query"-->
<!--    @sort-change="sortHandler"-->
<!--  >-->
<!--    <template slot="button">-->
<!--      <fks-button @click="handleClick">去往详情页</fks-button>-->
<!--    </template>-->
<!--  </fks-query-page>-->
  <div>
    <fks-query-page
      v-loading="loading"
      :page-sizes="[15, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :current-page.sync="form.pageNo"
      :page-size.sync="form.pageSize"
      :data="formList"
      highlight-current-row
      :total="form.total"
      :table-name="'记录列表'"
      class="record-table"
      @query="query"
      @clear="onClear"
      @sort-change="sortHandler"
    >
      <template slot="button">
        <div class="flex row-end">
          <fks-button
            type="primary"
            icon="fks-icon-export"
            :loading="exportLoading"
            @click="handleExport"
          >
            {{`导出列表(${form.total}条)`}}
          </fks-button>
          <compacted-search-bar
            class="m-l-20"
            id="car-apply"
            :configs="configs"
            :optionConfigs="optionConfigs"
            @query="query"
            @change="handleQueryChange"
            @clear="onClear"
          />
          <fks-button
            class="m-l-20"
            icon="fks-icon-search"
            @click="query"
          >
            搜索
          </fks-button>
        </div>
      </template>
      <template>
        <fks-table-column type="index" align="center" label="#" width="60">
          <template slot-scope="scope">
            {{ scope.$index + (form.pageNo - 1) * form.pageSize + 1 }}
          </template>
        </fks-table-column>
        <fks-table-column prop="applyFullName" label="申请人" />
        <fks-table-column
          prop="startAddress"
          min-width="240"
          label="出发地"
          align="center"
        >
          <template slot-scope="scope">
            {{ getStartCity(scope.row) }}{{ getStartAddress(scope.row) }}
          </template>
        </fks-table-column>
        <fks-table-column
          prop="endAddress"
          min-width="220"
          label="目的地"
          align="center"
        />
        <fks-table-column
          prop="startTime"
          min-width="150"
          label="出车时间"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.startTime ? $dayjs(scope.row.startTime).format('YYYY-MM-DD HH:mm') : '' }}
          </template>
        </fks-table-column>
        <fks-table-column
          prop="predictEndTime"
          min-width="150"
          label="预计返回时间"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.predictEndTime ? $dayjs(scope.row.predictEndTime).format('YYYY-MM-DD HH:mm') : '' }}
          </template>
        </fks-table-column>
        <fks-table-column
          prop="applyUserTime"
          min-width="150"
          label="申请时间"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.applyUserTime ? $dayjs(scope.row.applyUserTime).format('YYYY-MM-DD HH:mm') : '' }}
          </template>
        </fks-table-column>
        <fks-table-column label="所属部门" align="center" min-width="200">
          <template slot-scope="{row}">
            <div class="flex col-center row-center">
              <span>{{row.applyTopDepartment}}</span>
              <span style="display: inline-block;margin-left: 10px">{{row.applyDepartment}}</span>
            </div>
          </template>
        </fks-table-column>
        <fks-table-column prop="carResourceType" label="车辆来源">
          <template slot-scope="{row}">
            {{getResourceType(row.carResourceType)}}
          </template>
        </fks-table-column>
        <fks-table-column
          prop="useCarPersonConfirmEndTime"
          label="行程完成时间"
          min-width="150"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.useCarPersonConfirmEndTime ? $dayjs(scope.row.useCarPersonConfirmEndTime).format('YYYY-MM-DD HH:mm') : '' }}
          </template>
        </fks-table-column>
        <fks-table-column
          min-width="130"
          prop="taskState"
          label="状态"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <fks-tag style="width: 90px" :type="tagTypeList[+scope.row.applyFormUserState]">
              {{ scope.row.applyFormUserState | transferEnums('ApplyFormUserStateEnums') }}
            </fks-tag>
          </template>
        </fks-table-column>
        <fks-table-column label="操作" align="center" fixed="right" width="150px">
          <template slot-scope="scope">
            <div class="flex col-center row-center">
              <template v-for="(it, i) in scope.row.buttonsBig">
                <fks-button
                  class="m-r-20"
                  :style="{'color': it.buttonValue === '处理' ? 'rgb(253, 77, 89)' : `rgb(${it.buttonColor})`}"
                  @click.stop="handleOpt(it,  scope.row)"
                  :key="scope.row.id + i + it.buttonValue"
                  text
                >
                  {{ it.buttonValue }}
                </fks-button>
              </template>
              <fks-popover
                v-if="scope.row.buttonsSmall.length > 1"
                width="100"
                placement="bottom"
                trigger="click">
                <div class="flex column col-center row-start">
                  <template v-for="it in [...scope.row.buttonsSmall, ...scope.row.buttonsSmallMore]">
                    <!--                  <template v-if="it.buttonKey === 'REVOCATION'">-->
                    <!--                    <fks-popconfirm-->
                    <!--                      :key="scope.row.id + it.buttonValue"-->
                    <!--                      confirmButtonText="确定"-->
                    <!--                      cancelButtonText="取消"-->
                    <!--                      icon="fks-icon-info"-->
                    <!--                      iconColor="red"-->
                    <!--                      @onConfirm="handleOpt(it,  scope.row)"-->
                    <!--                      @onCancel="scope.row.showPopover = false"-->
                    <!--                      title="是否要执行撤销操作？"-->
                    <!--                    >-->
                    <!--                      <fks-button slot="reference" text :style="{'color': `rgb(${it.buttonColor})`}">撤销</fks-button>-->
                    <!--                    </fks-popconfirm>-->
                    <!--                  </template>-->
                    <fks-button
                      class="full-width m-l-20"
                      :class="{'more-btn': it.buttonKey !== 'EVALUATE', 'evaluate-btn': it.buttonKey === 'EVALUATE' }"
                      :style="{'color': `rgb(${it.buttonColor})`, 'text-align': 'left'}"
                      @click.stop="handleOpt(it,  scope.row)"
                      :key="scope.row.id + it.buttonValue"
                      text
                    >
                      {{ it.buttonValue }}
                    </fks-button>
                  </template>
                </div>
                <fks-button slot="reference" text style="color: rgb(60,131,255);"  @click="scope.row.showPopover = !scope.row.showPopover">更多</fks-button>
              </fks-popover>
              <template v-else>
                <template v-for="it in scope.row.buttonsSmall">
                  <fks-button
                    :style="{'color': `rgb(${it.buttonColor})`}"
                    @click.stop="handleOpt(it,  scope.row)"
                    :key="scope.row.id + it.buttonValue"
                    text
                  >
                    {{ it.buttonValue }}
                  </fks-button>
                </template>
              </template>

              <!--            <template v-if="showBtn(scope.row)">-->
              <!--              <fks-button-->
              <!--                v-if="scope.row.applyFormUserState === 4"-->
              <!--                dangerText-->
              <!--                @click.stop="handleView(scope.row, 2)"-->
              <!--              >-->
              <!--                重新提交-->
              <!--              </fks-button>-->
              <!--              <fks-button-->
              <!--                v-else-if="showDeal(scope.row)"-->
              <!--                dangerText-->
              <!--                @click.stop="handleView(scope.row, 2)"-->
              <!--              >-->
              <!--                行程结束-->
              <!--                处理-->
              <!--              </fks-button>-->
              <!--              <fks-button-->
              <!--                v-else-->
              <!--                dangerText-->
              <!--                @click.stop="handleView(scope.row, 2)"-->
              <!--              >-->
              <!--                处理-->
              <!--                {{-->
              <!--                  ['', '', '', '', '重新提交', '', '', '', '确认行程开始', '行程结束', '提交', '确认费用'][scope.row.applyFormState]-->
              <!--                }}-->
              <!--              </fks-button>-->
              <!--            </template>-->
              <!--            <fks-button-->
              <!--              v-else-if="showDeal(scope.row)"-->
              <!--              dangerText-->
              <!--              @click.stop="handleView(scope.row, 2)"-->
              <!--            >-->
              <!--                :class="['', '', '', '', 'danger', '', '', '', 'primary', 'danger', 'primary', 'primary'][item.applyFormState]"-->
              <!--              处理-->
              <!--                {{-->
              <!--                  ['', '', '', '', '重新提交', '', '', '', '确认行程开始', '行程结束', '提交', '确认费用'][item.applyFormState]-->
              <!--                }}-->
              <!--            </fks-button>-->
              <!--            <fks-button-->
              <!--              v-else-if="scope.row.applyFormUserState  === 3 && currUser.userName === vdApprovalUser.userName"-->
              <!--              dangerText-->
              <!--              @click.stop="handleView(scope.row, 2)"-->
              <!--            >-->
              <!--              处理-->
              <!--              提交-->
              <!--            </fks-button>-->

              <!--            <fks-button-->
              <!--              v-if="scope.row.flagAllowEvaluate"-->
              <!--              dangerText-->
              <!--              @click.stop="openRate(scope.row, 1)"-->
              <!--            >-->
              <!--              评价-->
              <!--            </fks-button>-->
              <!--            <fks-button style="margin-left: 0;" text @click.stop="handleView(scope.row,  showTripDetail(scope.row.applyFormUserState) ? 3 : 1)">查看</fks-button>-->
            </div>
          </template>
        </fks-table-column>
      </template>
    </fks-query-page>
    <rate-popup
      ref="ratePopup"
      :cur-car.sync="curCar"
      :is-cur="true"
      :rate-type="rateType"
      @refreshList="onRefresh"
    ></rate-popup>
    <rate-drawer
      ref="rateDrawer"
      :cur-car.sync="curCar"
      :is-cur="true"
      :rate-type="rateType"
      @refreshList="onRefresh"
    ></rate-drawer>
    <revocation-drawer
      ref="revocationDrawer"
      :cur-car.sync="curCar"
      :is-cur="true"
      @refreshList="onRefresh"
    ></revocation-drawer>
  </div>
</template>

<script>
import CompactedSearchBar from '../../../../components/CompactedSearchBar/index.vue'
import mixins from './mixins';
export default {
  name: "CarRecords",
  mixins: [mixins],
  components: { CompactedSearchBar },
  data() {
    return {
      loading: false,
      data: [],
      pageNo: 1,
      pageSize: 15,
      total: 0
    }
  },
  methods: {
    query() {
    },
    onClear() {
    },
    sortHandler() {
    },
    handleClick() {
      this.$router.push({name: 'Details'})
    }
  }
}
</script>

<style lang="scss" scoped>
.record-table.fks-query-page {
  padding: 0 0 32px 0 !important;
}
</style>
