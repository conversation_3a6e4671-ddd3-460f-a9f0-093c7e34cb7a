<template>
  <div id="travelTask" :class="{'bg-white': formList.length === 0}" class="main">
    <!--    <fm-nav-bar-->
    <!--      title="出行任务"-->
    <!--      :left-arrow="!isNotPC"-->
    <!--      @click-left="$router.go(-1)"-->
    <!--      :border="false"-->
    <!--    >-->
    <!--    </fm-nav-bar>-->
    <div class="flex row-between col-center full-width bg-white search-apply">
      <fm-search
        v-model="form.searchValue"
        clearable
        maxlength="50"
        placeholder="请输入联系人、联系人电话、地址、时间"
        @clear="onClear"
        @search="onSearch"
      >
        <!--        todo 暂时隐藏搜索图标-->
        <!--        <template #left-icon>-->
        <!--          <i class="fm-icon fm-icon-search" @click="onSearch"></i>-->
        <!--        </template>-->
      </fm-search>
      <!--      <img src="@/assets/img/car/filter.png" class="filter-img" @click="showFilter = true;">-->
    </div>
    <fm-popup :append-to-body="false"
              :style="{ height: 'calc(50% + 20px)', top: '52px', 'overflow-y': 'auto' }"
              :visible.sync="showFilter"
              class="filter-popup"
              get-container="#carApply" position="top">
      <div class="font-32 font-bold m-32">按申请时间选择</div>
      <div class="flex flex-wrap col-center row-between m-32">
        <div
          v-for="item in timeList"
          :key="item.value"
          :class="{'primary': timeApplyChoose.includes(item.value)}"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          @click="timeApplyChoose = [item.value]; form.startApplyTime = ''; form.endApplyTime = '';"
        >
          {{ item.text }}
        </div>

        <div
          :class="{'primary': !!form.startApplyTime, 'no-time': !form.startApplyTime }"
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          @click="showStartApplyTime = true"
        >
          {{ form.startApplyTime ? form.startApplyTime : '开始时间' }}
        </div>
        <div class="time-line m-b-32"></div>
        <div
          :class="{'primary': !!form.endApplyTime, 'no-time': !form.endApplyTime }"
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          @click="showEndApplyTime = true"
        >
          {{ form.endApplyTime ? form.endApplyTime : '结束时间' }}
        </div>
        <date-time-picker
          :show.sync="showStartApplyTime"
          :time.sync="form.startApplyTime"
          title="开始时间"
          type="date"
          @confirm="timeApplyChoose = [];"
        ></date-time-picker>
        <date-time-picker
          :show.sync="showEndApplyTime"
          :time.sync="form.endApplyTime"
          title="结束时间"
          type="date"
          @confirm="timeApplyChoose = [];"
        ></date-time-picker>
      </div>
      <div class="font-32 font-bold m-32">按出发时间选择</div>
      <div class="flex flex-wrap col-center row-between m-32">
        <div
          v-for="item in timeList"
          :key="item.value"
          :class="{'primary': timeChoose.includes(item.value)}"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          @click="timeChoose = [item.value]; form.startTime = ''; form.endTime = '';"
        >
          {{ item.text }}
        </div>

        <div
          :class="{'primary': !!form.startTime, 'no-time': !form.startTime }"
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          @click="showStartTime = true"
        >
          {{ form.startTime ? form.startTime : '开始时间' }}
        </div>
        <div class="time-line m-b-32"></div>
        <div
          :class="{'primary': !!form.endTime, 'no-time': !form.endTime }"
          class="obt-btn time-tag time-input text-center m-b-32 m-r-16"
          @click="showEndTime = true"
        >
          {{ form.endTime ? form.endTime : '结束时间' }}
        </div>
        <date-time-picker
          :show.sync="showStartTime"
          :time.sync="form.startTime"
          title="开始时间"
          type="date"
          @confirm="timeChoose = [];"
        ></date-time-picker>
        <date-time-picker
          :show.sync="showEndTime"
          :time.sync="form.endTime"
          title="结束时间"
          type="date"
          @confirm="timeChoose = [];"
        ></date-time-picker>
      </div>
      <div class="font-32 font-bold m-32">按状态选择</div>
      <div class="flex flex-wrap col-center m-32">
        <div
          v-for="item in applyFormState"
          :key="item.code"
          :class="{'primary': statusChoose.includes(item.key)}"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          @click="statusChoose = [item.key]"
        >
          {{ item.value }}
        </div>
      </div>
      <div class="font-32 font-bold m-32">按用车类型选择</div>
      <div class="flex flex-wrap col-center m-32 p-b-100">
        <div
          v-for="item in useCarType"
          :key="item.code"
          :class="{'primary': useCarTypeChoose.includes(item.key)}"
          class="obt-btn time-tag text-center m-b-32 m-r-16"
          @click="useCarTypeChoose = [item.key]"
        >
          {{ item.value }}
        </div>
      </div>
      <div class="foot foot-filter">
        <fm-button
          :disabled="loading"
          :loading="loading"
          class="flow-btn btn-50 m-r-30"
          plain
          type="primary"
          @click="clearFilter"
        >重置
        </fm-button>
        <fm-button
          :disabled="loading"
          :loading="loading"
          class="flow-btn btn-50"
          type="primary"
          @click="onSearchFilter"
        >确定
        </fm-button>
      </div>
    </fm-popup>
    <empty-data v-if="formList.length === 0 && loading === false"></empty-data>
    <div v-else class="apply-container">
      <!-- 下拉刷新 -->
      <fm-pull-refresh
        v-model="isRefresh"
        refresh-layer-color="#4b8bf4"
        success-text="刷新成功"
        @refresh="onRefresh()"
      >
        <!-- 上拉加载 -->
        <fm-list
          v-model="loading"
          :finished="finished"
          :immediate-check="false"
          finished-text="已经到底啦~"
          @load="onLoad"
        >
          <!-- 滑动 -->
          <fm-swipe-cell v-for="(item, i) in formList" :key="item.id">
            <!-- 卡片 -->
            <fm-cell-group
              :class="['driving', 'end', 'danger', 'start'][+item.processState]"
              inset
              @click="handleView(item,  showTripDetail(item.applyFormUserState) ? 3 : 1)"
            >
              <div
                :class="['status-active', '', 'status-danger', 'status-start'][+item.processState]"
                class="status flex col-center row-center">
                <span v-if="item.applyFormUserState !== null">
                  {{ item.applyFormUserState | transferEnums('ApplyFormUserStateEnums') }}
                </span>
              </div>
              <div class="car-cell p-r-32">
                <div class="flex col-center row-between full-width">
                  <div class="flex col-center">
                    <img class="address-text m-r-10" src="@/assets/img/car/icon_start.png">
                    <span class="address-city">
                      {{ getCity(item, 'startAddress') }}
                    </span>
                  </div>
                  <div class="address text-right m-l-40">
                    {{ getAddress(item, 'startAddress') }}
                  </div>
                </div>
                <div class="flex col-center">
                  <div
                    :class="['address-line-driving', 'address-line-end', 'address-line-end', 'address-line-driving'][+item.processState]"
                    class="address-line m-l-16"></div>
                  <img
                    v-if="['0', '3'].includes(item.processState) && ![1, 9].includes(item.applyFormDriverState)"
                    class="round-trip-img m-l-10 m-r-10"
                    src="@/assets/img/car/icon_round_trip_active.png">
                  <img v-else class="round-trip-img m-l-10 m-r-10"
                       src="@/assets/img/car/icon_round_trip.png">
                </div>
                <div class="flex col-center row-between full-width">
                  <div class="flex col-center">
                    <img class="address-text m-r-10" src="@/assets/img/car/icon_end.png">
                    <span class="address-city d-inline-block">
                      {{ getCity(item, 'endAddress') }}
                    </span>
                  </div>
                  <div class="address text-right m-l-40">
                    {{ getAddress(item, 'endAddress') }}
                  </div>
                </div>
              </div>
              <div class="car-line m-l-32 m-r-32 m-t-32"></div>
              <div class="flex col-center row-between car-cell">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_time.png">
                  <span class="time">申请时间：</span>
                </div>
                <div class="time time-info">{{
                    item.applyUserTime ? $dayjs(item.applyUserTime).format('YYYY-MM-DD HH:mm') : ''
                  }}
                </div>
              </div>
              <div class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_time.png">
                  <span class="time">出发时间：</span>
                </div>
                <div class="time time-info">{{
                    getTime(item, 'start') ? $dayjs(getTime(item, 'start')).format('YYYY-MM-DD HH:mm') : ''
                  }}
                </div>
              </div>
              <div v-if="item.vdCarInfo"
                   class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_car_info.png">
                  <span class="time">车辆信息：</span>
                </div>
                <div class="time time-info">
                  <span>{{ item.vdCarInfo.carType }}</span>
                  <span class="m-l-16">{{ item.vdCarInfo.carNum }}</span>
                </div>
              </div>
              <div :class="{'m-b-24': !item.remark2}"
                   class="flex col-center row-between car-cell p-t-24">
                <div class="flex col-center">
                  <img class="address-text m-r-10" src="@/assets/img/car/icon_driver.png">
                  <span class="time">联系人：</span>
                </div>
                <div class="time time-info">
                  <a :href="`tel:${item.contactsPhone}`" @click.stop="">
                    <img :src="require('@/assets/img/detail/dianhua.png')" class="m-l-16"
                         height="14px"
                         width="14px"/>
                    <span class="m-l-16">{{ item.contactsPhone }}</span>
                    <span>{{ item.contacts }}</span>
                  </a>
                </div>
              </div>
              <div class="m-t-24"></div>
              <div class="m-l-32 m-r-32">
                <template v-for="(it, i) in item.buttonsBig">
                  <div
                    v-if="it.buttonValue !== '查看'"
                    :key="item.id + i + it.buttonValue"
                    :style="{'background-color': `rgb(${it.buttonColor})`}"
                    class="obt-btn full-width primary m-b-32 text-center"
                    @click.stop="handleOpt(it, item)"
                  >
                    {{ it.buttonValue }}
                  </div>
                </template>
              </div>
              <div v-if="item.buttonsSmallMore.length > 0" class="flex row-between col-center">
                <div :id="item.id" @click.stop="">
                  <fm-popover
                    :actions="item.buttonsSmallMore"
                    :visible.sync="item.showPopover"
                    class="m-l-32 m-b-32"
                    get-container="'#carApply'"
                    placement="bottom-start"
                    trigger="click"
                    @select="it => handleOpt(it, item)"
                  >
                    <template #reference>
                      <fm-button size="small" text>更多<i class="fm-icon fm-icon-arrow"></i>
                      </fm-button>
                    </template>
                  </fm-popover>
                </div>
                <div class="flex row-end m-l-32 m-r-32">
                  <template v-for="(it, i) in item.buttonsSmall">
                    <div
                      v-if="it.buttonSizeType === 'small'"
                      :key="it.buttonKey + item.id"
                      :class="{'m-r-10': i + 1 < item.buttonsSmall.length}"
                      :style="{'border-color': `rgb(${it.buttonColor})`, color: `rgb(${it.buttonColor})`}"
                      class="obt-btn other-btn text-center m-b-32"
                      @click.stop="handleOpt(it, item)"
                    >
                      {{ it.buttonValue }}
                    </div>
                  </template>
                </div>
              </div>
              <div v-else class="flex row-end m-l-32 m-r-32">
                <template v-for="(it, i) in item.buttonsSmall">
                  <div
                    v-if="it.buttonSizeType === 'small'"
                    :key="it.buttonKey + item.id"
                    :class="{'m-r-10': i + 1 < item.buttonsSmall.length}"
                    :style="{'border-color': `rgb(${it.buttonColor})`, color: `rgb(${it.buttonColor})`}"
                    class="obt-btn other-btn text-center m-b-32"
                    @click.stop="handleOpt(it, item)"
                  >
                    {{ it.buttonValue }}
                  </div>
                </template>
              </div>
            </fm-cell-group>
          </fm-swipe-cell>
        </fm-list>
      </fm-pull-refresh>
    </div>
    <tabbar></tabbar>
  </div>
</template>

<script>
import {Button, Cell, CellGroup, Popup, Rate, Toast} from 'fawkes-mobile-lib';
import {getApprovalList, getDriverTask} from '@/modules/CarApply/components/api.js';
import tabbar from '@/modules/CarApply/components/TabBar.vue'
import RatePopup from '@/modules/CarApply/components/RatePopup.vue';
import {mapActions, mapGetters, mapMutations, mapState} from 'vuex'
import * as GetterTypes from "@/store/Getter/getterTypes";
import * as StateTypes from "@/store/State/stateTypes";
import NavigateMap from '@/components/NavigateMap/index.vue';
import EmptyData from '@/components/EmptyData/index.vue';
import DateTimePicker from '@/modules/FormCenter/components/DateTimePicker/index.vue';
import {getCarInfo, getFlowButton1} from "@/api/carApply";
import mobileCarMixin from "@/mixins/mobileCarMixin";


export default {
  name: 'ReimburseTestList',
  mixins: [mobileCarMixin],
  components: {
    [Button.name]: Button,
    [Rate.name]: Rate,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    Toast,
    NavigateMap,
    RatePopup,
    tabbar,
    EmptyData,
    Popup,
    DateTimePicker
  },
  data() {
    return {
      showFilter: false,
      evaluateScore: 0,
      evaluate: 1,
      activeKey: 0,
      value: '', //搜索文字
      formList: [],
      timeApplyChoose: [],
      timeChoose: [],
      statusChoose: [],
      useCarTypeChoose: [],
      timeList: [
        {
          value: 1,
          text: '近1个月',
          start: `${this.$dayjs(new Date().getTime() - 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`,
          end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss')
        },
        {
          value: 3,
          text: '近3个月',
          start: `${this.$dayjs(new Date().getTime() - 3 * 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`,
          end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss')
        },
        {
          value: 6,
          text: '近6个月',
          start: `${this.$dayjs(new Date().getTime() - 6 * 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`,
          end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss')
        },
        {
          value: this.$dayjs().format('YYYY'),
          text: '今年',
          start: this.$dayjs().format('YYYY') + '-01-01 00:00:00',
          end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss')
        },
        {
          value: this.$dayjs().format('YYYY') - 1,
          text: this.$dayjs().format('YYYY') - 1,
          start: `${this.$dayjs().format('YYYY') - 1}-01-01 00:00:00`,
          end: `${this.$dayjs().format('YYYY') - 1}-12-31 23:59:59`
        },
        {
          value: this.$dayjs().format('YYYY') - 2,
          text: this.$dayjs().format('YYYY') - 2,
          start: `${this.$dayjs().format('YYYY') - 2}-01-01 00:00:00`,
          end: `${this.$dayjs().format('YYYY') - 2}-12-31 23:59:59`
        }
      ],
      showStartApplyTime: false,
      showEndApplyTime: false,
      showStartTime: false,
      showEndTime: false,
      form: {
        pageNo: 1,
        pageSize: 20,
        sort: 'desc', // 排序规则,示例值(desc)
        searchValue: '' // 搜索条件（联系人、联系人电话、地址、时间）
      },
      isRefresh: false,
      finished: false,
      loading: false, // 下拉刷新时禁止无限加载
      bannerShow: true,
      curCar: {},
      rateType: 1,// 1、评价，2、未评价
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState([StateTypes.USER_INFO]),
    ...mapGetters([GetterTypes.IS_NOT_PC]),
    applyFormState() {
      return this.enums?.ApplyFormStateEnums ?? [];
    },
    // 用车类型
    useCarType() {
      return this.enums?.UseCarTypeEnums ?? [];
    },
  },
  created() {
    if (this.$storage.get('commonOneFormNotice')) {
      this.bannerShow = false
    }
    if (!this.currUser.phone || !this.currUser.userName) {
      this.getCurrentUser(this.$storage.get('username')).then(res => {
      });
    }
  },
  mounted() {
    Toast.loading({
      message: '加载中...',
      forbidClick: true,
    })
    if (this.$route.query.refresh === 'true') {
      setTimeout(() => {
        this.onRefresh();
      }, 3000);
    } else {
      this.onRefresh()
    }
  },
  methods: {
    ...mapActions('CarApply', [
      'getCurrentUser'
    ]),
    ...mapMutations('CarApply', ['SET_LAST_ROUTE']),
    getTime(item, type) {
      if (type === 'start') {
        return item.startTime2 || ''
      } else {
        return item.endTime2 || ''
      }
    },
    openRate(item, type) {
      this.curCar = item;
      this.rateType = +type
      this.$nextTick(() => {
        this.$refs.ratePopup.open();
      })
    },
    clearFilter() {
      this.timeApplyChoose = [];
      this.timeChoose = [];
      this.statusChoose = [];
      this.useCarTypeChoose = [];
      this.form.startApplyTime = '';
      this.form.endApplyTime = '';
      this.form.startTime = '';
      this.form.endTime = '';
    },
    onSearchFilter() {
      // const flag = this.timeApplyChoose.length === 0 && !this.form.startApplyTime && !this.form.endApplyTime &&
      //   this.timeChoose.length === 0 && !this.form.startTime && !this.form.endTime &&
      //   this.statusChoose.length === 0 && this.useCarTypeChoose.length === 0;
      // if (flag) {
      //   Toast({
      //     message: '您还没有选择筛选项！',
      //     duration: 3000
      //   });
      //   return false;
      // }
      this.onRefresh(true);
    },
    onClear() {
      this.form.searchValue = ''
      // 复用刷新接口
      this.onRefresh()
    },
    // 搜索
    onSearch(val) {
      // 复用刷新接口
      this.onRefresh()
    },
    onRefresh(flag = false) {
      this.finished = false
      this.loading = true
      this.form.pageNo = 1
      getDriverTask(this.getParams())
        .then(async (res) => {
          if (!res.status) {
            Toast({
              message: res.message || '请求失败',
              duration: 3000
            });
            return false;
          }
          // if (flag && res.data.list.length === 0) {
          //   this.isMobile ? Toast({
          //     message: '未找到匹配的纪录\n请修改筛选条件试试',
          //     duration: 3000
          //   }) : '';
          //   return false;
          // }
          if (flag) {
            this.showFilter = false;
          }
          this.formList = await this.getData(res);
          this.isRefresh = false
          if (res.data.isLastPage) {
            this.finished = true
            this.form.pageNo = 1
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {
          Toast.clear()
          this.loading = false
        })
    },
    async getData(res) {
      const buttonIds = res.data.list.map(item => item.id).join(',')
      const response = await getFlowButton1(buttonIds)
      const carIds = res.data.list.filter(item => item.carId).map(item => item.carId).join(',')
      const {data: carRes} = await getCarInfo(carIds)

      return res.data.list.map(item => {
        item.showPopover = false;
        item.buttonsBig = [];
        item.buttonsSmall = [];
        item.buttonsSmallMore = [];
        item.vdCarInfo = carRes ? carRes[item.carId] : null;
        item.buttonsBig.push({
          buttonColor: '60,131,255',
          buttonDesc: '用户查看按钮',
          buttonKey: 'VIEW',
          buttonSizeType: 'big',
          buttonValue: '查看'
        });
        if (response.data[item.id].length) {
          response.data[item.id].forEach(button => {
            if (button.buttonSizeType === 'big') {
              item.buttonsBig.push(button)
            } else {
              item.buttonsSmall.push(button)
            }
          })
        }
        return item;
      });
    },
    getParams() {
      const userName = this.currUser.userName || this.$storage.get('username') || this[StateTypes.USER_INFO].userName;
      const form = JSON.parse(JSON.stringify(this.form));
      if (form.startTime) {
        form.startTime = form.startTime + ' 00:00:00'
      }
      if (form.startApplyTime) {
        form.startApplyTime = this.form.startApplyTime + ' 00:00:00'
      }
      if (form.endTime) {
        form.endTime = form.endTime + ' 23:59:59'
      }
      if (form.endApplyTime) {
        form.endApplyTime = form.endApplyTime + ' 23:59:59'
      }
      if (this.timeApplyChoose.length > 0) {
        const time = this.timeList.find(it => it.value === this.timeApplyChoose[0]);
        form.startApplyTime = time.start;
        form.endApplyTime = time.end;
      }
      if (this.timeChoose.length > 0) {
        const time = this.timeList.find(it => it.value === this.timeChoose[0]);
        form.startTime = time.start;
        form.endTime = time.end;
      }
      if (this.statusChoose.length > 0) {
        form.status = this.statusChoose[0];
      }
      if (this.useCarTypeChoose.length > 0) {
        form.useCarType = this.useCarTypeChoose[0];
      }
      form.userName = userName;
      return form;
    },
    onLoad() {
      this.loading = true
      this.form.pageNo++
      getDriverTask(this.getParams())
        .then(async (res) => {
          if (res.status) {
            this.finished = true
            return false;
          }
          const list = this.getData(res);
          this.formList = this.isPC ? list : [...this.formList, ...list];
          if (res.data.isLastPage) {
            this.finished = true
            this.form.pageNo = 1
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {
          this.loading = false
        })
    }
  },
}
</script>

<style lang="less" scoped>
@import "~@/modules/CarApply/components/index";
</style>
