import request from '@/utils/request'

// 车辆列表
export function getCarList(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/page',
    method: 'get',
    params
  })
}

// 车辆列表
export function getCarListByPost(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/car/page',
    method: 'post',
    data,
    params
  })
}

// 导出车辆列表
export function exportCarInfo(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 新增车辆
export function addCar(data) {
  return request({
    url: '/vehicle-dispatch/vd/car/add',
    method: 'post',
    data
  })
}

// 批量新增车辆
export function batchAddCar(data) {
  return request({
    url: '/vehicle-dispatch/vd/car/template/batchAddOrUpdate',
    method: 'post',
    data
  })
}

// 编辑车辆
export function editCar(data) {
  return request({
    url: '/vehicle-dispatch/vd/car/update',
    method: 'post',
    data
  })
}

// 车辆绑定司机
export function carBindToDriver(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/bind/driver',
    method: 'post',
    params
  })
}

// 根据车辆查询司机信息
export function getDriverInfoByCar(carIds) {
  return request({
    url: '/vehicle-dispatch/vd/car/driver',
    params: {carIds}
  })
}


// 查询保险列表
export function getInsuranceList(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/bx/page',
    params
  })
}

// 新增保险信息
export function addInsuranceItem(data) {
  return request({
    url: '/vehicle-dispatch/vd/car/bx/add',
    method: 'POST',
    data
  })
}

// 根据车牌号获得车辆
export function getCarByNum(data) {
  return request({
    url: '/vehicle-dispatch/vd/driver/car/byNum',
    method: 'POST',
    data
  })
}


// 删除保险信息
export function deleteCar(id) {
  return request({
    url: '/vehicle-dispatch/vd/car/delete',
    method: 'POST',
    params: {
      id
    }
  })
}

// 删除保险信息
export function batchDeleteCar(data) {
  return request({
    url: '/vehicle-dispatch/vd/car/multiDelete',
    method: 'post',
    data
  })
}




// 删除保险信息
export function deleteInsuranceItem(ids) {
  return request({
    url: '/vehicle-dispatch/vd/car/bx/delete',
    method: 'POST',
    params: {ids}
  })
}


// 查询年检列表
export function getAnnualInspectionList(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/nj/page',
    params
  })
}

// 新增年检信息
export function addAnnualInspectionItem(data) {
  return request({
    url: '/vehicle-dispatch/vd/car/nj/add',
    method: 'POST',
    data
  })
}

// 删除年检信息
export function deleteAnnualInspectionItem(ids) {
  return request({
    url: '/vehicle-dispatch/vd/car/nj/delete',
    method: 'POST',
    params: {ids}
  })
}

// 查询维修保养信息
export function getMaintenanceList(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/wxby/page',
    params
  })
}

// 新增维修保养信息
export function addMaintenanceItem(data) {
  return request({
    url: '/vehicle-dispatch/vd/car/wxby/add',
    method: 'POST',
    data
  })
}

// 删除维修保养信息
export function deleteMaintenanceItem(ids) {
  return request({
    url: '/vehicle-dispatch/vd/car/wxby/delete',
    method: 'POST',
    params: {ids}
  })
}

// 获取通行证信息
export function getPassportInfos(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/txz/page',
    params
  })
}

// 新增通行证信息
export function addPassportItem(data) {
  return request({
    url: '/vehicle-dispatch/vd/car/txz/add',
    method: 'POST',
    data
  })
}

// 删除通行证信息
export function deletePassportItem(ids) {
  return request({
    url: '/vehicle-dispatch/vd/car/txz/delete',
    method: 'POST',
    params: {ids}
  })
}

// 获取违章信息
export function getViolationInfos(params) {
  return request({
    url: '/vehicle-dispatch/vd/car/wz/page',
    params
  })
}

// 新增违章信息
export function addViolationItem(data) {
  return request({
    url: '/vehicle-dispatch/vd/car/wz/add',
    method: 'POST',
    data
  })
}

// 删除违章信息
export function deleteViolationItem(ids) {
  return request({
    url: '/vehicle-dispatch/vd/car/wz/delete',
    method: 'POST',
    params: {ids}
  })
}

// 车辆总数统计
export function getCarTotal() {
  return request({
    url: '/vehicle-dispatch/vd/stats/carNum'
  })
}

// 车辆状态统计
export function getCarStatus() {
  return request({
    url: '/vehicle-dispatch/vd/stats/carStatus'
  })
}

// 统计图表
export function getStatistics(params, type) {
  const url = ['carYh', 'carXcLc', 'carCcCsFz', 'carCcCsFz', 'carFy'][type];
  return request({
    url: `/vehicle-dispatch/vd/stats/${url}`,
    params
  })
}
// 统计图表
export function getCarSearchParam() {
  return request({
    url: `/vehicle-dispatch/vd/car/page/getParam`,
  })
}
