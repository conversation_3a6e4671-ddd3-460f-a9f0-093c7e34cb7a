<template>
  <div class="flex flex-column full-height">
    <header class="card-header" v-if="showHeader">
      <div class="flex col-center m-b-24">
        <div class="card-title" :key="currentData.carNum">{{ currentData.carNum }}</div>
        <div class="m-l-20">
          <card-tag v-for="(tag, index) in currentData.tags" :key="index" :tag="tag"/>
          <card-tag class="m-l-10" :tag="{color: '#3C83FF', text: currentData.projectName}" />
        </div>
      </div>
    </header>
    <main class="flex-grow-1 flex flex-column overflow-y-auto overflow-x-hidden" style="max-width: 900px">
      <div class="flex flex-column flex-grow-1 overflow-hidden">
        <fks-tabs v-model="activeName" @tab-click="handleTabClick">
          <fks-tab-pane
            v-for="item in tabs"
            :key="item.name"
            :name="item.name"
          >
        <span slot="label" class="flex col-center">
          <fks-icon :icon-class="`${item.icon}${activeName === item.name ? '-active' : ''}`"
                    class="svg" style="margin-right: 5px"/>
          <span>{{ item.label }}</span>
        </span>
          </fks-tab-pane>
        </fks-tabs>
        <div class="flex-grow-1 overflow-y-auto form full-width overflow-x-hidden">
          <base-info
            ref="CarManageBaseInfo"
            layout-single-line
            @formChange="formChange = $event"
            :type="type"
            @unbindDriver="$emit('refreshItem')"
          />
          <insurance-info ref="InsuranceInfo" class="m-t-20" :car-id="currentData.id" :type="type"/>
          <annual-inspection-info ref="AnnualInspectionInfo" class="m-t-20" :car-id="currentData.id" :type="type"/>
          <maintenance-info ref="MaintenanceInfo" class="m-t-20" :car-id="currentData.id" :type="type"/>
          <passport-info ref="PassportInfo" class="m-t-20" :car-id="currentData.id" :type="type"/>
          <violation-and-accident ref="ViolationAndAccident" class="m-t-20" :car-id="currentData.id"  :type="type"/>
        </div>
      </div>

      <div class="footer-buttons" v-if="showButtons">
        <fks-button
          v-if="getAuth('edit')"
          slot="reference"
          @click="doEdit"
          :loading="buttonLoading"
          icon="fks-icon-edit"
          text
        >
          修改
        </fks-button>

        <fks-popconfirm title="确认删除？" @onConfirm="handleDelete" class="m-l-10">
          <fks-button
            v-if="getAuth('delete')"
            slot="reference"
            :loading="buttonLoading"
            icon="fks-icon-delete"
            dangerText
          >
            删除
          </fks-button>
        </fks-popconfirm>
      </div>
      <div class="footer-buttons" v-if="type === 'edit'">
        <fks-popconfirm title="确认提交？" @onConfirm="handleConfirm">
          <fks-button
            v-if="getAuth('edit')"
            slot="reference"
            :loading="buttonLoading"
            icon="fks-icon-check"
            text
          >
            提交
          </fks-button>
        </fks-popconfirm>

        <fks-button
          v-if="getAuth('edit')"
          @click="editCancel"
          slot="reference"
          :loading="buttonLoading"
          icon="fks-icon-close"
          text
        >
          取消
        </fks-button>
      </div>
    </main>
  </div>
</template>

<script>
import CardTag from "@components/CardFlow/components/tag.vue";
import BaseInfo from "@modules/ProjectCar/ProjectPortal/CarManage/components/BaseInfo.vue";
import InsuranceInfo from "./InsuranceInfo.vue";
import AnnualInspectionInfo from "./AnnualInspectionInfo.vue";
import MaintenanceInfo from "./MaintenanceInfo.vue";
import PassportInfo from "./PassportInfo.vue";
import ViolationAndAccident from "./ViolationAndAccident.vue";

import {carManagementTabs} from "@utils/constants";
import {
  carBindToDriver,
  deleteCar,
  editCar
} from '@modules/ProjectCar/ProjectPortal/CarManage/api'
import {
  deleteDriver,
  unbindCarWithDriver
} from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import { delProject } from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import EventBus from "@utils/eventBus";
import {mapState} from "vuex";
import * as StateTypes from "@store/State/stateTypes";
import { getAuth } from '@utils/buttonAuth'

export default {
  name: "DetailForm",
  components: {
    CardTag,
    BaseInfo,
    InsuranceInfo,
    AnnualInspectionInfo,
    MaintenanceInfo,
    PassportInfo,
    ViolationAndAccident
  },
  props: {
    currentData: {
      type: Object,
    },
    showHeader: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      activeName: 'CarManageBaseInfo',
      buttonLoading: false,
      tabs: carManagementTabs,
      formChange: false,
      type: 'view',
    }
  },
  computed: {
    ...mapState([StateTypes.IS_PROJECT_CLOSED]),
    disableMutation() {
      return this[StateTypes.IS_PROJECT_CLOSED];
    },
    showButtons() {
      return this.type === 'view' && !this.disableMutation;
    }
  },
  methods: {
    getAuth,
    handleTabClick(tab) {
      // 滚动到点击实例的位置
      this.$nextTick(() => {
        if (this.$refs[tab.name]) {
          const el = this.$refs[tab.name].$el;
          el.scrollIntoView({behavior: 'smooth'});
        }
      })
    },
    async handleDelete() {
      if (this.currentData.id) {
        EventBus.$emit('scroll', false);
        let res = await deleteCar(this.currentData.id);
        EventBus.$emit('scroll', true);
        if (res.status) {
          this.$message.success('删除成功');
          // 编辑成功后，通知CardFlow组件更新数据, 如果涉及到删除数据则传true，编辑数据则传false
          this.$emit('refresh', true)
        }
      }
    },

    handleConfirm() {
      this.$refs.CarManageBaseInfo.$refs.form.validate(async (valid) => {
        if (valid) {
          this.$emit('formSubmit', true);
          const formData = this.$refs.CarManageBaseInfo.formData;
          const {driver, driverPhone, driverChange, driverFullName, ...rest} = formData;
          if (this.formChange) {
            this.buttonLoading = true;
            const res = await editCar(rest);
            if (res.status) {
              this.$message.success('编辑成功')
              this.buttonLoading = false;
              this.formChange = false;
            }
          }
          if (driverChange) {
            this.buttonLoading = true;
            carBindToDriver({
              carId: formData.id,
              driverId: driver
            }).then((res) => {
              if (res.status) {
                this.$message.success('绑定司机成功')
              } else {
                // 绑定失败
                this.$refs.CarManageBaseInfo.formData.driver = ''
                this.$refs.CarManageBaseInfo.formData.driverPhone = ''
                this.$refs.CarManageBaseInfo.formData.driverFullName = ''
                this.$refs.CarManageBaseInfo.formData.driverChange = false
              }
            }).finally(() => {
              this.buttonLoading = false;
            })
          }
          // 数据更改后，重新刷新该条更改的数据
          this.$emit('refreshItem')
          if (this.type === 'edit') this.type = "view"
        }
      })
    },
    doEdit() {
      this.$refs.CarManageBaseInfo.doEdit();
      this.type = 'edit';
    },
    editCancel() {
      this.$refs.CarManageBaseInfo.editCancel();
      this.type = 'view';
    }
  },
  watch: {
    currentData: {
      immediate: true,
      handler(newVal) {
        this.$nextTick(()=>{
          this.$refs.CarManageBaseInfo.formData = JSON.parse(JSON.stringify(newVal));
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import "~@/styles/headers";
@import "~@/styles/button";
</style>

