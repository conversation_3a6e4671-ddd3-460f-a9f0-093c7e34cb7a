<template>
  <fks-table
    ref="table"
    :cell-style="{ color: '#333333 !important' }"
    :data="tableData"
    :header-cell-style="{
      background: 'transparent',
      color: '#333333aa !important',
      fontWeight: 'unset !important',
    }"
    :height="tableHeight"
    :sticky="true"
    class="full-width"
    style="overflow-y: auto;"
    @selection-change="handleSelectionChange"
    @paste.native="handlePaste($event)"
  >
    <!-- 选择框 -->
    <fks-table-column type="selection" width="50"/>
    <!-- 序号 -->
    <fks-table-column label="序号" type="index" width="50"/>

    <fks-table-column prop="carNum" width="250">
      <template slot="header">
        <red-star title="车牌号"/>
      </template>
      <template slot-scope="scope">
        <fks-row style="display: flex; justify-content: center">
          <fks-col :span="16">
            <fks-autocomplete
              v-model="scope.row.carNum"
              :disabled="isView || isEdit"
              :fetch-suggestions="querySearchCarNum"
              :loading="loadingCarNum"
              :placeholder="isView? '' : '请输入车牌号'"
              @select="val => changeCarNum(val.carNum, scope.row)"
              @click.native="selectColumn(scope.row, scope.column, scope.$index)"
            >
              <template slot-scope="{item}">
                <div>{{ item.carNum }}</div>
              </template>
            </fks-autocomplete>
          </fks-col>
          <fks-col :offset="1" :span="6"
                   style="display: flex; justify-content: flex-start; align-items: center">
            <div
              v-if="scope.row.originCar"
              style="
                display: inline-block;
                border-radius: 11px;
                color: #3c83ff;
                background: rgba(60, 131, 255, 0.2);
                margin-right: 3px;
                padding: 4px 8px;
                font-size: 11px;
                white-space: nowrap;
              "
            >
              {{ scope.row.originCar }}
            </div>
            <fks-tooltip
              v-if="scope.row.originCar && scope.row.originCar !== '新增'"
              class="item" content="已存在，当前操作修改原有信息" effect="dark" placement="right">
              <i class="fks-icon-warning" style="color:rgba(255, 164, 24, 1)"></i>
            </fks-tooltip>
          </fks-col>
        </fks-row>
      </template>
    </fks-table-column>

    <!--    <fks-table-column label="用车类型" width="180" prop="projectUseCarType">-->
    <!--      <template slot-scope="scope">-->
    <!--        <fks-select-->
    <!--          v-model="scope.row.projectUseCarType"-->
    <!--          :placeholder="getPlaceholder('请选择用车类型')"-->
    <!--          class="full-width"-->
    <!--          @change="formChange"-->
    <!--          @click.native="selectColumn(scope.row, scope.column, scope.$index)"-->
    <!--        >-->
    <!--          <fks-option-->
    <!--            v-for="item in projectCarType"-->
    <!--            :key="item.code"-->
    <!--            :label="item.value"-->
    <!--            :value="item.key"-->
    <!--          />-->
    <!--        </fks-select>-->
    <!--      </template>-->
    <!--    </fks-table-column>-->
    <fks-table-column v-if="isCompanyPortal" label="项目名称" prop="projectName" width="180">
      <template slot="header">
        <red-star title="项目名称"/>
      </template>
      <template slot-scope="scope">
        <fks-select
          v-model="scope.row.projectId"
          :placeholder="getPlaceholder('请选择项目名称')"
          class="full-width"
          @change="(val) => formChange(val,'projectName',scope.row)"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
          filterable
        >
          <fks-option
            v-for="portal in authPortalList"
            :key="portal.id"
            :label="portal.projectName"
            :value="portal.portalId"
          />
        </fks-select>
      </template>
    </fks-table-column>

    <!-- 车辆名称 -->
    <fks-table-column prop="carName" width="150">
      <template slot="header">
        <red-star title="车辆名称"/>
      </template>
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.carName"
          :placeholder="getPlaceholder('请输入车辆名称')"
          autocomplete="off"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <fks-table-column prop="carResource" width="180">
      <template slot="header">
        <red-star title="车辆来源"/>
      </template>
      <template slot-scope="scope">
        <fks-select
          v-model="scope.row.carResource"
          filterable
          :placeholder="getPlaceholder('请选择来源')"
          class="full-width"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        >
          <fks-option
            v-for="item in carResource"
            :key="item.code"
            :label="item.value"
            :value="item.key"
          />
        </fks-select>
      </template>
    </fks-table-column>

    <!-- 车辆名称 -->
    <fks-table-column prop="carCompName" width="170">
      <template slot="header">
        <red-star title="出租方名称"/>
      </template>
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.carCompName"
          :disabled="!isRentCar(scope.row)"
          :placeholder="getPlaceholder('请输入出租方名称', isRentCar(scope.row))"
          autocomplete="off"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <fks-table-column label="车辆状态" prop="carStatus" width="120">
      <template slot-scope="scope">
        <fks-select v-model="scope.row.carStatus" placeholder="" @change="formChange"
                    @click.native="selectColumn(scope.row, scope.column, scope.$index)">
          <fks-option
            v-for="item in carStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          >
            {{ item.value }}
          </fks-option>
        </fks-select>
      </template>
    </fks-table-column>

    <!-- 品牌型号 -->
    <fks-table-column prop="carType" width="180">
      <template slot="header">
        <red-star title="品牌型号"/>
      </template>
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.carType"
          :placeholder="getPlaceholder('请输入品牌型号')"
          autocomplete="off"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <!-- 座位数 -->
    <fks-table-column prop="carSeatNum" width="130">
      <template slot="header">
        <red-star title="座位数"/>
      </template>
      <template slot-scope="scope">
        <fks-input-number
          v-model="scope.row.carSeatNum"
          :min="1"
          autocomplete="off"
          controls-position="right"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <fks-table-column prop="carZlFy1" width="180">
      <template slot="header">
        <red-star title="月度租赁价格"/>
      </template>
      <template slot-scope="scope">
        <fks-input-number
          v-model="scope.row.carZlFy1"
          :disabled="!isRentCar(scope.row)"
          :placeholder="getPlaceholder('请输入价格', isRentCar(scope.row))"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        >
          <template slot="append">元</template>
        </fks-input-number>
      </template>
    </fks-table-column>

    <!-- 座位数 -->
    <fks-table-column prop="carAge" width="130">
      <template slot="header">
        <red-star title="车龄"/>
      </template>
      <template slot-scope="scope">
        <fks-input-number
          v-model="scope.row.carAge"
          :min="0"
          autocomplete="off"
          controls-position="right"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <fks-table-column prop="carZlTime1" width="190">
      <template slot="header">
        <red-star title="租赁开始时间"/>
      </template>
      <template slot-scope="scope">
        <fks-date-picker
          v-model="scope.row.carZlTime1"
          :disabled="!isRentCar(scope.row)"
          :placeholder="getPlaceholder('请选择租赁开始时间', isRentCar(scope.row))"
          class="full-width"
          clearable
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <fks-table-column prop="carZlTime2" width="180">
      <template slot="header">
        <red-star title="租赁截止时间"/>
      </template>
      <template slot-scope="scope">
        <fks-date-picker
          v-model="scope.row.carZlTime2"
          :disabled="!isRentCar(scope.row)"
          :placeholder="getPlaceholder('请选择租赁截止时间', isRentCar(scope.row))"
          class="full-width"
          clearable
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <!-- 购置时间 -->
    <fks-table-column prop="carBuyTime" width="180">
      <template slot="header">
        <red-star title="购置时间"/>
      </template>
      <template slot-scope="scope">
        <fks-date-picker
          v-model="scope.row.carBuyTime"
          :placeholder="getPlaceholder('')"
          class="full-width"
          clearable
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>
    <!-- 入场日期 -->
    <fks-table-column prop="carRcTime1" width="180">
      <template slot="header">
        <red-star title="入场日期"/>
      </template>
      <template slot-scope="scope">
        <fks-date-picker
          v-model="scope.row.carRcTime1"
          :placeholder="getPlaceholder('')"
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <!-- 里程补贴标准 -->
    <fks-table-column label="里程补贴标准（元/公里）" prop="carLcbtbz" width="200">
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.carLcbtbz"
          :placeholder="getPlaceholder('')"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <!-- 里程补贴标准 -->
    <fks-table-column prop="carXslcCs" width="180">
      <template slot="header">
        <red-star title="初始行驶里程"/>
      </template>
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.carXslcCs"
          :placeholder="getPlaceholder('')"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <!-- 里程补贴标准 -->
    <fks-table-column label="累计行驶里程" prop="carXslcLj" width="180">
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.carXslcLj"
          :placeholder="getPlaceholder('')"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>


    <!-- 车架号 -->
    <fks-table-column label="车架号" prop="carCjh" width="140">
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.carCjh"
          :placeholder="getPlaceholder('')"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <!-- 发动机号 -->
    <fks-table-column label="发动机号" prop="carFdjh" width="140">
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.carFdjh"
          :placeholder="getPlaceholder('')"
          @change="formChange"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <!-- 其他资料 -->
    <fks-table-column label="附件（车辆照片、行驶证等）" width="320">
      <template slot-scope="scope">
        <input-attachment
          v-if="scope.row.carNum"
          :form-data="scope.row"
          attachment-name="attachment1"
          :disabled="isView"
        />
      </template>
    </fks-table-column>

    <fks-table-column label="备注" prop="remark1" width="180">
      <template slot-scope="scope">
        <fks-input v-model="scope.row.remark1" :disabled="isView" placeholder="请输入备注"
                   @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <fks-table-column v-if="lineCar" :key="`driverPhone`" label="司机联系电话" prop="driverPhone"
                      width="180">
      <template slot-scope="scope">
        <fks-input
          v-model="scope.row.driverPhone"
          :disabled="isView"
          autocomplete="off"
          maxlength="11"
          placeholder="请输入联系电话"
          @blur="phoneBlur(scope.row)"
          @click.native="selectColumn(scope.row, scope.column, scope.$index)"
        />
      </template>
    </fks-table-column>

    <fks-table-column v-if="lineCar" :key="`originDriver`" label="司机信息"
                      width="120">
      <template slot-scope="scope">
        <div
          v-if="scope.row.originDriver"
          :style="{
              display: 'inline-block',
              borderRadius: '11px',
              color: scope.row.originDriver === '无' ? '#ff4d4f' : '#3c83ff',
              background: scope.row.originDriver === '无' ? 'rgba(255, 77, 79, 0.2)' : 'rgba(60, 131, 255, 0.2)',
              padding: '4px 8px',
              fontSize: '11px'
            }"
        >
          {{ scope.row.originDriver }}
        </div>

        <span v-else> / </span>
      </template>
    </fks-table-column>

    <fks-table-column v-if="lineCar" :key="`bindingCar`" label="司机关联车辆"
                      width="130">
      <template slot-scope="scope">
        <div
          v-if="scope.row.bindingCar"
          style="
            display: inline-block;
            border-radius: 11px;
            color: #3c83ff;
            background: rgba(60, 131, 255, 0.2);
            padding: 4px 8px;
            font-size: 11px;
            white-space: nowrap;
          "
        >
          {{ scope.row.bindingCar }}
        </div>
        <span v-else> / </span>
      </template>
    </fks-table-column>
  </fks-table>
</template>

<script>
import validator from '@/mixins/validator.js';
import {getCarList, getDriverInfoByCar} from "@modules/ProjectCar/ProjectPortal/CarManage/api";
import {
  getCar,
  getDrierByPhone,
  getDriverList
} from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import {mapState} from "vuex";
import {CAR, deleteDraft, getDraft, saveDraftDebounced} from '@utils/draftUtil'
import * as types from '@store/Getter/getterTypes'
import * as actionTypes from '@store/Action/actionTypes'
import {debounce} from '@utils/util'
import RedStar from "@components/red-star.vue";
import * as StateTypes from "@store/State/stateTypes";
import multiSelection from "@/mixins/multiSelection";
import InputAttachment from '@components/InputAttachment/pc-view.vue'

export default {
  name: "BatchAddCarForm",
  mixins: [validator, multiSelection],
  components: {RedStar, InputAttachment},
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    // 是否关联司机
    lineCar: {
      type: Boolean,
      default: false
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    },
    tableHeight: {
      type: Number,
      default: 500
    }
  },
  data() {
    return {
      carInfo: {}, // 选择的车辆信息
      loadingCarNum: false,
      carNumList: [], // 车辆列表
      tableData: [],
      saveDraft: false,
      draftList: [],
      selectTableColumn: null,
      selectTableIndex: 0,
      formData: {
        // 车牌号、车辆来源、出租方名称、品牌型号、座位数、购置时间、租赁开始时间、租赁截止时间、月租金由公司门户统一导入配置，不可编辑
        carNum: '', // 车牌号
        carResource: '', // 车辆来源
        carCompName: '', // 出租方名
        carName: '', // 车辆名称
        carType: '', // 品牌型号
        carSeatNum: 5, // 座位数
        carZlFy1: '', // 月度租赁价格（车辆月租赁费用）
        carBuyTime: '', // 购置时间（车辆购买时间）
        carZlTime1: '', // 租赁开始时间
        carZlTime2: '', // 租赁结束时间
        carCjh: '', // 车架号
        carFdjh: '', // 发动机号
        affiliatedUnit: '', // 所属单位
        affiliatedUnitId: '', // 所属单位id
        carRcTime1: '', // 入场日期
        carLcbtbz: '', // 里程补贴标准（元/公里）
        driver: '', // 司机
        driverPhone: '', // 联系电话
        attachment1: '',
        attachmentOther: '',
        carStatus: 100, // 车辆状态
        carTcTime1: '', // 退场日期
        carTcSy: '', // 退场事由
        remark1: '', // 备注
      },
      backFromData: {},
      selectedRows: [],
    }
  },
  watch: {
    lineCar: {
      handler(val) {
        this.tableData.forEach(item => {
          item.driverPhone = val ? item.driverPhoneBack : null;
        })
        this.$nextTick(() => {
          // 2. 调用 doLayout 来重新计算表格布局
          if (this.$refs.table && typeof this.$refs.table.doLayout === 'function') {
            this.$refs.table.doLayout();
          }
        });
      },
      deep: true, // 深度监听，以监听到对象内部的变化
    },
    // 监听 tableData 的变化
    tableData: {
      handler(newVal) {
        if (this.saveDraft) {
          if (newVal && newVal.length > 0) {
            const hasData = newVal.some(item => {
              const formData = item;
              return formData.carNum || formData.carResource || formData.carCompName || formData.carName ||
                formData.carType || formData.carSeatNum || formData.carCjh || formData.carBuyTime ||
                formData.carFdjh || formData.carRcTime1 || formData.carLcbtbz || formData.projectId
            });
            if (hasData) {
              this.saveDrafts();
            }
          }
        }
        newVal.forEach(item => {
          if (item.driverPhone === null) {
            item.driverId = null;
          } else if (item.driverPhone !== item.previousDriverPhone) {
            this.debouncePhoneChange(item)
            item.previousDriverPhone = item.driverPhone;
          }

          if (item.carNum !== item.prevcarNum) {
            this.debounceCarChange(item)
            item.prevcarNum = item.carNum;
          }
        })
      },
      deep: true, // 深度监听，以监听到对象内部的变化
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState(['applyResource', 'userInfo', 'openId', 'portal']),
    ...mapState(['portal']),
    ...mapState([StateTypes.AUTH_PORTALS]),
    isCompanyPortal() {
      return this.portal.name === '数据舱'
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter(item => item.projectStatus === 100); // 100激活，200关闭
    },
    showDriverSelector() {
      const isAdd = this.type === 'plus';
      const noDriverSelected = Boolean(this.formData.driver) === false // 还未选择任何司
      return isAdd || noDriverSelected || this.formData.driverChange;
    },
    carResource() {
      return this.enums.CarResourceEnums;
    },
    carStatus() {
      return this.enums.CarStatusEnums;
    },
    projectCarType() {
      return this.enums.ProjectUseCarTypeEnums
    },
    span() {
      return this.layoutSingleLine ? 24 : 12;
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    isEdit() {
      return this.type === 'edit';
    },
    isView() {
      return this.type === 'view';
    },
    isAdd() {
      return this.type === 'plus';
    },
    driverStatusOption() {
      return this.enums.DriverStatusEnums;
    },
    flowConfig() {
      const type = this.type === 'view' ? 'readonly' : ''
      return Object.keys(this.formData).reduce((acc, cur) => {
        return Object.assign(acc, {[cur]: type})
      }, {driver: type})
    },
    theme() {
      return this.$store.getters[types.THEME];
      // return '#2F54EB'
    },
    rules() {
      return {
        driverFullName: [
          {required: true, message: '请输入姓名', trigger: 'change'}
        ],
        driverPhone: [
          {required: true, message: '请输入联系电话', trigger: 'change'},
          {required: true, validator: this.phoneValidator, trigger: 'blur'}
        ],
        driverStatus: [
          {required: true, message: '请选择状态', trigger: 'change'}
        ],
        driverRzTime: [
          {required: true, message: '请选择入职日期', trigger: 'change'}
        ]
      }
    },
  },
  created() {
    getDriverList({pageNo: 1, pageSize: 100}).then(res => {
      this.driverList = res.data.list;
    });
    this.$store.dispatch(actionTypes.GET_AUTH_PORTAL)
  },
  mounted() {
    this.addRows()
    this.remoteMethod();
  },
  methods: {
    createFilter(query) {
      return (option) => {
        return option.carNum.toLowerCase().indexOf(query.toLowerCase()) >= 0;
      };
    },
    querySearchCarNum(query, cb) {
      const results = query ? this.carNumList.filter(this.createFilter(query)) : this.carNumList;
      cb(results);
    },
    getPlaceholder(text, isRent) {
      if (this.type === 'view') {
        return ''
      }
      if (isRent === false) {
        // 非租赁车辆占位符
        return '自有车辆无需填写'
      }
      return text
    },
    formChange(value, prop, row) {
      if (prop === 'projectName') {
        const name = this.authPortalList.find(item => item.portalId === value).projectName
        this.$set(row, 'projectName', name)
      }
      this.$emit('formChange', true)
    },
    loadData() {
      getDraft(this.userInfo.id, CAR).then(res => {
        this.draftList = JSON.parse(res.data);
        if (this.draftList && this.draftList.length > 0) {
          this.openHTML();
        }
      })
    },
    loadDraftData() {
      this.saveDraft = false;
      // 模拟从草稿加载数据
      this.tableData = [...this.draftList];
      this.$nextTick(() => {
        this.$message.success('草稿已恢复')
      });
    },
    // 保存草稿
    saveDrafts() {
      let context = JSON.stringify(this.tableData);
      saveDraftDebounced(this.userInfo.id, CAR, context)
        .then(() => {
          // this.$message.success('草稿保存成功');
        })
        .catch((error) => {
          console.error('保存草稿失败:', error);
        });
    },
    remoteMethod() {
      this.loadingCarNum = true;
      let projectId = this.portal.id
      getCarList({projectId}).then(res => {
        this.carNumList = res.data.list || [];
      }).catch(() => {
      }).finally(() => {
        this.loadingCarNum = false;
      })
    },
    debouncePhoneChange(row) {
      if (!row.searchPhone) {
        row.searchPhone = debounce(this.phoneBlur, 500, false)
      }
      row.searchPhone(row)
    },
    debounceCarChange(row) {
      if (!row.searchCar) {
        row.searchCar = debounce(this.changeCarNum, 500, false)
      }
      row.searchCar(row.carNum, row)
    },
    phoneBlur(item) {
      let phone = item.driverPhone;
      if (item.projectId) {
        let projectId = item.projectId;
        getDrierByPhone({
          phone, projectId
        }).then((res) => {
          let driver = res.data;
          if (driver) {
            this.$set(item, 'originDriver', driver.driverFullName);
            this.$set(item, 'driverId', driver.id);
            getCar({driverIds: driver.id}).then(res => {
              let carData = res.data;
              if (carData) {
                this.$set(item, 'bindingCar', carData[driver.id].carNum);
              }
            })
          } else {
            this.$set(item, 'driverId', null);
            this.$set(item, 'originDriver', "无");
            this.$set(item, 'bindingCar', null);
          }
        })
      }

    },
    changeCarNum(val, row) {
      const carInfo = this.carNumList.find(item => item.carNum === val);
      if (carInfo) {
        Object.keys(carInfo).forEach(key => {
          this.$set(row, key, carInfo[key]);
        });
        this.$set(row, 'originCar', '修改');
        getDriverInfoByCar(carInfo.id).then((res) => {
          let list = res.data
          if (list && list[carInfo.id]) {
            this.$set(row, 'driverPhoneBack', list[carInfo.id].driverPhone)
            if (this.lineCar) {
              this.$set(row, 'driverPhone', list[carInfo.id].driverPhone)
            }
          }
        })
      } else {

        if (row.id) {
          const keepFields = ['carNum', 'prevcarNum', 'driverPhoneBack'];
          // 遍历 item 的所有键
          Object.keys(row).forEach((key) => {
            // 如果不在保留列表里，就置空
            if (!keepFields.includes(key)) {
              this.$set(row, key, null);
            }
          });
        } else {
          this.$set(row, 'id', null);
        }
        let str = row.carNum ? '新增' : null;
        this.$set(row, 'originCar', str);
      }
    },
    isRentCar(formData) {
      return formData.carResource === 200 || formData.carResource === 300;
    },
    validateTable() {
      const isTableComplete = this.some(this.tableData, (formData) => {
        // 基础字段的检查
        const basicFieldsIncomplete =
          !formData.carNum ||
          !formData.carResource ||
          !formData.carName ||
          !formData.carType ||
          !formData.carSeatNum ||
          !formData.carBuyTime ||
          !formData.carRcTime1 ||
          !formData.carAge ||
          !formData.carXslcCs ||
          (this.isCompanyPortal && !formData.projectId);

        // 如果是租赁车，需要检查额外字段
        let extraFieldsIncomplete = false;
        if (this.isRentCar(formData)) {
          extraFieldsIncomplete = (!formData.carCompName ||
            !formData.carZlFy1 ||
            !formData.carZlTime1 ||
            !formData.carZlTime2);
        }

        // 只要基础字段不完整，或者在特定情况下额外字段不完整，返回 true
        return basicFieldsIncomplete || extraFieldsIncomplete;
      });

      if (isTableComplete) {
        this.$message.warning('请完善表格信息！');
        return Promise.reject(false); // 返回一个被拒绝的 Promise，以中断提交流程
      }
    },
    addRows() {
      this.$nextTick(() => {
        const tableElement = this.$refs.table.$el;  // 获取表格 DOM 元素
        tableElement.scrollTop = tableElement.scrollHeight;  // 滚动到最底部
        let emptyData = JSON.parse(JSON.stringify(this.formData));
        let list = this.tableData
        list.push(emptyData);
        this.$set(this.$data, "tableData", list);
      })
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    // 删除选中的行
    delSelectedRows() {
      this.tableData = this.tableData.filter(row => !this.selectedRows.includes(row));
      this.$refs.table.clearSelection();  // 清除表格选中的状态
    },
    cleanTableData() {
      this.saveDraft = false;
      let emptyData = JSON.parse(JSON.stringify(this.formData));
      this.tableData = [emptyData]
    },
    some(collection, predicate) {
      // 如果 predicate 是函数，直接使用它，否则使用默认检查值是否存在的函数
      const check = typeof predicate === 'function' ? predicate : (val) => !!val;
      // 遍历集合，遇到符合条件的元素返回 true
      for (let item of collection) {
        if (check(item)) {
          return true;
        }
      }
      return false;
    },
    removeDraft() {
      deleteDraft(this.userInfo.id, CAR)
      this.$nextTick(() => {
        this.$message.success('草稿删除成功')
      });
    },
    openHTML() {
      // 保存当前 Vue 实例的引用
      const self = this;

      // 定义点击恢复的事件处理函数
      const restoreDraft = () => {
        self.loadDraftData(); // 调用你在 methods 里定义的 loadDraftData 函数
      };

      // 定义点击恢复的事件处理函数
      const removeDraft = () => {
        self.removeDraft(); // 调用你在 methods 里定义的 loadDraftData 函数
      };
      // 使用 Vue 的 VNode 创建可点击的 HTML 元素
      this.$message({
        dangerouslyUseHTMLString: true,
        type: "warning",
        message: `
          <span>存在草稿，是否恢复？</span>
          <a href="javascript:void(0);" style="color: #027AFF;" id="restoreDraft">恢复</a>
          <a href="javascript:void(0);" style="color: #FF4D4F;" id="removeDraft">删除</a>
        `,
        onClose: () => {
          const restoreLink = document.getElementById('restoreDraft');
          if (restoreLink) {
            restoreLink.removeEventListener('click', restoreDraft);
          }
          const removeLink = document.getElementById('removeDraft');
          if (removeLink) {
            removeLink.removeEventListener('click', removeDraft);
          }
        }
      });

      // 手动绑定事件到生成的链接
      this.$nextTick(() => {
        const restoreLink = document.getElementById('restoreDraft');
        if (restoreLink) {
          restoreLink.addEventListener('click', restoreDraft);
        }
        // 销毁弹窗时的回调
        const removeLink = document.getElementById('removeDraft');
        if (removeLink) {
          removeLink.addEventListener('click', removeDraft);
        }
      });
    },
    handlePaste(event) {
      event.preventDefault();
      // 获取剪贴板中的文本内容
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedData = clipboardData.getData('Text');  // 获取纯文本格式的数据

      let rows = pastedData
        .trim() // 去除首尾换行，避免空行
        .split('\n') // 按行拆分
        .map(row => row.split('\t').map(cell => cell === '' ? null : cell))
        .filter(row => row.some(cell => cell !== ''));  // 过滤掉完全为空的行

      let len = rows[0].length;

      const field = this.selectTableColumn;
      let columns = this.$refs.table.columns.map(column => column.property);
      let startIndex = columns.indexOf(field)
      const props = columns.slice(startIndex, startIndex + len);

      if (rows.length > 0) {
        const startRow = this.selectTableIndex;

        // 确保 tableData 的长度足够，至少有 startRow + rows.length 行
        while (this.tableData.length < startRow + rows.length) {
          this.tableData.push({});  // 添加空对象作为新行
        }

        rows.forEach((cell, rowIndex) => {
          const targetRow = startRow + rowIndex;

          // 遍历 props，从 props[0] 开始逐一处理
          props.forEach((prop, columnIndex) => {
            let value = cell ? cell[columnIndex] : '';
            if (value) {
              value = value.replace(/[\s\r]/g, '');
            }

            if (['carResource', 'carStatus', 'projectUseCarType'].includes(prop)) {
              const mapObject = {
                carResource: this.carResource,
                carStatus: this.carStatus,
                projectUseCarType: this.projectCarType,
              };

              const map = mapObject[prop]; // 通过字段名获取对应的选项数组

              if (map && Array.isArray(map)) {
                const foundEntry = map.find(entry => entry.value === value);
                value = foundEntry ? foundEntry.key : '';
              }
            }
            // 将值写入对应的列
            this.$set(this.tableData[targetRow], prop, value);
          });
        });
      }
    },
    selectColumn(row, column, index) {
      // 设置当前选择的行
      this.saveDraft = true;
      const propName = column.property; // 获取当前列的字段名（prop）
      this.selectTableColumn = propName;
      this.selectTableIndex = index;
    },
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/disabled';
@import '~@/styles/scrollbar';
</style>
