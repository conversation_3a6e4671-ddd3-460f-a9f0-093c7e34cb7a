<script src="../../../../../components/index.js"></script>
<template>
  <div style="overflow: hidden;">
    <common-title v-if="isEdit" title="基本信息"/>
    <div v-if="type === 'plus' && notifyTitle" class="notify-content "
         v-html="notifyTitle"/>
    <fks-form
      ref="form"
      :class="{newTheme: layoutSingleLine}"
      :disabled="isView"
      :flow-config="flowConfig"
      :model="formData"
      class="m-t-32"
    >
      <fks-form-item
        v-if="isCompanyPortal"
        :rules="[{required: true, message: '请选择项目名称', trigger: 'blur'}]"
        :span="span"
        label="项目名称"
        prop="projectName"
        required
      >
        <fks-select
          v-model="formData.projectId"
          :disabled="isView"
          class="full-width"
          @change="handleProjectChange"
          filterable
        >
          <fks-option
            v-for="portal in authPortalList"
            :key="portal.id"
            :label="portal.projectName"
            :value="portal.portalId"
          />
        </fks-select>
      </fks-form-item>
      <fks-form-item
        :rules="[{required: true, message: '请输入正确格式的车牌号', validator: this.carNumValidator, trigger: 'blur'}]"
        :span="span"
        label="车牌号"
        lead
        prop="carNum"
        required
      >
        <fks-input v-model="formData.carNum"
                   :disabled="isView || isEdit"
                   :placeholder="getPlaceholder('请输入车牌号')"
                   @change="formChange"/>
      </fks-form-item>
      <!--      <fks-form-item-->
      <!--        :rules="[{required: true, message: '请选择用车类型', trigger: 'blur'}]"-->
      <!--        :span="span"-->
      <!--        label="用车类型"-->
      <!--        prop="projectUseCarType"-->
      <!--        required-->
      <!--      >-->
      <!--        <fks-select v-model="formData.projectUseCarType" :placeholder="getPlaceholder('请选择用车类型')"-->
      <!--                    class="full-width"-->
      <!--                    @change="formChange">-->
      <!--          <fks-option-->
      <!--            v-for="item in projectCarType"-->
      <!--            :key="item.code"-->
      <!--            :label="item.value"-->
      <!--            :value="item.key"-->
      <!--          />-->
      <!--        </fks-select>-->
      <!--      </fks-form-item>-->
      <fks-form-item
        :rules="[{required: true, message: '请选择车辆来源', trigger: 'blur'}]"
        :span="span"
        label="车辆来源"
        prop="carResource"
        required
      >
        <fks-select v-model="formData.carResource" filterable :placeholder="getPlaceholder('请选择车辆来源')"
                    class="full-width"
                    @change="formChange">
          <fks-option
            v-for="item in carResource"
            :key="item.code"
            :label="item.value"
            :value="item.key"
          />
        </fks-select>
      </fks-form-item>
      <fks-form-item
        v-if="isRentCar"
        :rules="[{required: true, message: '请输入出租方名称', trigger: 'blur'}]"
        :span="span"
        label="出租方名称"
        prop="carCompName"
        required
      >
        <fks-input v-model="formData.carCompName" :placeholder="getPlaceholder('请输入出租方名称')"
                   @change="formChange"/>
      </fks-form-item>
      <fks-form-item
        :rules="[{required: true, message: '请输入车辆名称', trigger: 'blur'}]"
        :span="span"
        label="车辆名称"
        prop="carName"
        required
      >
        <fks-input v-model="formData.carName" :placeholder="getPlaceholder('请输入车辆名称')"
                   @change="formChange"/>
      </fks-form-item>
      <fks-form-item
        :rules="[{required: true, message: '请输入品牌型号', trigger: 'blur'}]"
        :span="span"
        label="品牌型号"
        prop="carType"
        required
      >
        <fks-input v-model="formData.carType" :placeholder="getPlaceholder('请输入品牌型号')"
                   @change="formChange"/>
      </fks-form-item>
      <fks-form-item
        :rules="[{required: true, message: '请输入座位数', trigger: 'blur'}]"
        :span="span"
        label="座位数"
        prop="carSeatNum"
        required
      >
        <fks-input v-if="isView" :value="formData.carSeatNum"/>
        <fks-input-number
          v-else
          size="small"
          v-model="formData.carSeatNum"
          :min="5"
          controls-position="right"
          @change="formChange"
        />
      </fks-form-item>
      <fks-form-item
        v-if="isRentCar"
        :rules="[{required: true, validator: numberValidator, message: '请输入月度租赁价格', trigger: 'blur'}]"
        :span="span"
        label="月度租赁价格"
        prop="carZlFy1"
        required
      >
        <fks-input v-if="isView" :value="formData.carZlFy1">
          <template slot="append">元</template>
        </fks-input>
        <fks-input-number
          v-else
          v-model="formData.carZlFy1"
          :placeholder="getPlaceholder('请输入月度租赁价格')"
          size="small"
          @change="formChange"
        />
      </fks-form-item>
      <fks-form-item
        :rules="[{required: true, message: '请选择购置时间', trigger: 'blur'}]"
        :span="span"
        label="购置时间"
        prop="carBuyTime"
        required
      >
        <fks-date-picker
          v-model="formData.carBuyTime"
          :placeholder="getPlaceholder('请选择购置时间')"
          class="full-width"
          clearable
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="formChange"
        />
      </fks-form-item>

      <fks-form-item
        :rules="[
          { required: true, message: '请填写车龄', trigger: 'blur' },
          { validator: (rule, value, callback) => {
              if (!isValidNumber(value)) {
                callback('车龄必须是非负数字');
              } else {
                callback();
              }
            }, trigger: 'blur' },
        ]"
        :span="span" label="车龄" prop="carAge">
        <fks-input
          v-model="formData.carAge"
          :placeholder="getPlaceholder('请填写车龄')"
          @change="formChange"/>
        <div v-if="formData.carAge > 7 && type === 'plus' && carAgeText" class=""
             v-html="carAgeText"></div>
      </fks-form-item>

      <fks-form-item
        v-if="isRentCar"
        :rules="[{required: true, message: '请选择租赁开始时间', trigger: 'blur'}]"
        :span="span"
        label="租赁开始时间"
        prop="carZlTime1"
        required
      >
        <fks-date-picker
          v-model="formData.carZlTime1"
          :placeholder="getPlaceholder('请选择租赁开始时间')"
          class="full-width"
          clearable
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="formChange"
        />
      </fks-form-item>
      <fks-form-item
        v-if="isRentCar"
        :rules="[{required: true, message: '请选择租赁截止时间', trigger: 'blur'}]"
        :span="span"
        label="租赁截止时间"
        prop="carZlTime2"
        required
      >
        <fks-date-picker
          v-model="formData.carZlTime2"
          :placeholder="getPlaceholder('请选择租赁截止时间')"
          class="full-width"
          clearable
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="formChange"
        />
      </fks-form-item>
      <fks-form-item
        :rules="[{required: true, message: '请选择入场日期', trigger: 'blur'}]"
        :span="span"
        label="入场日期"
        prop="carRcTime1"
        required
      >
        <fks-date-picker
          v-model="formData.carRcTime1"
          :placeholder="getPlaceholder('选择入场日期')"
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="formChange"
        >
        </fks-date-picker>
      </fks-form-item>

      <fks-form-item
        :rules="[
          {required: true, message: '请填写初始行驶里程', trigger: 'blur'},
          { validator: (rule, value, callback) => {
              if (!isValidNumber(value)) {
                callback('初始行驶里程必须是非负数字');
              } else {
                callback();
              }
            }, trigger: 'blur' },
        ]"
        :span="span" label="初始行驶里程" prop="carXslcCs">
        <fks-input
          v-model="formData.carXslcCs"
          :placeholder="getPlaceholder('请填写初始行驶里程')"
          @change="formChange">
          <template slot="append">公里</template>
        </fks-input>
        <div v-if="formData.carXslcCs > 200000 && type === 'plus' && carLCText" class=""
             v-html="carLCText"/>
      </fks-form-item>

      <fks-form-item

        :rules="[
          { validator: (rule, value, callback) => {
              if (!isValidNumber(value)) {
                callback('累计行驶里程必须是非负数字');
              } else {
                callback();
              }
            }, trigger: 'blur' },
        ]"
        :span="span" label="累计行驶里程" prop="carXslcLj">
        <fks-input
          v-model="formData.carXslcLj"
          :placeholder="getPlaceholder('请填写累计行驶里程')"
          @change="formChange">
          <template slot="append">公里</template>
        </fks-input>
      </fks-form-item>

      <fks-form-item :span="span"
                     :rules="[
                        { validator: (rule, value, callback) => {
                            if (!isValidNumber(value)) {
                              callback('里程补贴标准必须是非负数字，且最多两位小数');
                            } else {
                              callback();
                            }
                          }, trigger: 'blur' }
                      ]"
                     label="里程补贴标准" prop="carLcbtbz">
        <fks-input
          v-model="formData.carLcbtbz"
          :placeholder="getPlaceholder('请填写里程补贴')"
          @change="formChange"
        >
          <template slot="append">元/公里</template>
        </fks-input>
      </fks-form-item>
      <fks-form-item :span="span" label="车架号" prop="carCjh">
        <fks-input
          v-model="formData.carCjh"
          :placeholder="getPlaceholder('请填写车架号')"
          @change="formChange"
        />
      </fks-form-item>
      <fks-form-item :span="span" label="发动机号" prop="carFdjh">
        <fks-input
          v-model="formData.carFdjh"
          :placeholder="getPlaceholder('请填写发动机号')"
          @change="formChange"
        />
      </fks-form-item>
      <fks-form-item :span="span" label="司机" prop="driver">
        <fks-select
          v-if="showDriverSelector"
          filterable
          v-model="formData.driver"
          :placeholder="type === 'view' ? '' : '请选择司机'"
          class="full-width"
          @change="changeDriver"
        >
          <fks-option
            v-for="{driverFullName, id} in driverList"
            :key="id"
            :label="driverFullName"
            :value="id"
          />
        </fks-select>
        <fks-input
          v-else
          :value="formData.driverFullName"
          readonly
        >
          <fks-popconfirm
            slot="suffix"
            title="确认解除绑定？"
            @onConfirm="onConfirm"
          >
            <fks-button
              slot="reference"
              class=""
              size="mini" text>
              <i
                class="fks-icon-circle-close"
                style="color: #ccc;cursor: pointer;font-size: 16px;margin-right: 5px"
              />
            </fks-button>
          </fks-popconfirm>
        </fks-input>
      </fks-form-item>
      <fks-form-item
        :span="span"
        label="司机联系电话"
        prop="driverPhone"
      >
        <fks-input
          :placeholder="getPlaceholder('选择司机后，联系电话自动带入')"
          :value="formData.driverPhone"
          disabled
          readonly
        />
      </fks-form-item>
      <fks-form-item
        :rules="[{required: true, message: '请选择车辆状态', trigger: 'blur'}]"
        :span="span"
        label="车辆状态"
        prop="carStatus"
        required
      >
        <fks-radio-group v-model="formData.carStatus" @change="formChange">
          <fks-radio
            v-for="item in carStatus"
            :key="item.key"
            :label="item.key"
          >
            {{ item.value }}
          </fks-radio>
        </fks-radio-group>
      </fks-form-item>
      <fks-form-item
        v-if="showExit"
        :rules="[{required: showExit, message: '请选择退场日期', trigger: 'blur'}]"
        :span="span"
        label="退场日期"
        prop="carTcTime1"
        required
      >
        <fks-date-picker
          v-model="formData.carTcTime1"
          :placeholder="getPlaceholder('请选择退场日期')"
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="formChange">
        </fks-date-picker>
      </fks-form-item>
      <fks-form-item v-if="showExit" :span="24" label="退场事由" prop="carTcSy">
        <fks-input v-model="formData.carTcSy" :autosize="{ minRows: 4, maxRows: 6 }"
                   :disabled="isView"
                   :placeholder="isView ? '' : '请输入退场事由'"
                   clearable maxlength="200" show-word-limit
                   type="textarea"
                   @change="formChange"/>
      </fks-form-item>
      <fks-form-item
        :span="24"
        label="附件（车辆照片、行驶证等）"
        prop="attachment1"
      >
        <input-attachment
          :formData="formData"
          :disabled="isView"
          :attachmentName="'attachment1'"
        />
      </fks-form-item>
      <fks-form-item :span="24" class="mt-2" label="备注" prop="remark1">
        <fks-input v-model="formData.remark1" :autosize="{ minRows: 4, maxRows: 6 }"
                   :disabled="isView"
                   :placeholder="isView ? '' : '请输入备注'"
                   clearable maxlength="200" show-word-limit type="textarea"
                   @change="formChange"/>
      </fks-form-item>
    </fks-form>
  </div>
</template>
<script>
import * as types from '@/store/Getter/getterTypes.js';
import validator from '@/mixins/validator.js';
import SearchSelector from '@components/PersonSelector/select.vue';
import {
  getDriverListByPost,
  unbindCarWithDriver
} from "@modules/ProjectCar/ProjectPortal/DriverManage/api";
import {mapState} from "vuex";
import {queryTips, tipsMap} from '@utils/notifyContentUtils'
import * as StateTypes from "@store/State/stateTypes";
import InputAttachment from '@components/InputAttachment/pc-view.vue'

export default {
  name: 'CarManageBaseInfo',
  mixins: [validator],
  components: {
    SearchSelector,
    InputAttachment
  },
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    layoutSingleLine: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      refresh: true,
      formData: {
        // 车牌号、车辆来源、出租方名称、品牌型号、座位数、购置时间、租赁开始时间、租赁截止时间、月租金由公司门户统一导入配置，不可编辑
        carNum: '', // 车牌号
        carResource: '', // 车辆来源
        carCompName: '', // 出租方名
        carName: '', // 车辆名称
        carType: '', // 品牌型号
        carSeatNum: 5, // 座位数
        carZlFy1: '', // 月度租赁价格（车辆月租赁费用）
        carBuyTime: '', // 购置时间（车辆购买时间）
        carZlTime1: '', // 租赁开始时间
        carZlTime2: '', // 租赁结束时间
        carCjh: '', // 车架号
        carFdjh: '', // 发动机号
        affiliatedUnit: '', // 所属单位
        affiliatedUnitId: '', // 所属单位id
        carRcTime1: '', // 入场日期
        carLcbtbz: '', // 里程补贴标准（元/公里）
        driver: '', // 司机
        driverPhone: '', // 联系电话
        attachment1: '',
        attachmentOther: '',
        carStatus: 100, // 车辆状态
        carTcTime1: '', // 退场日期
        carTcSy: '', // 退场事由
        remark1: '', // 备注
        carAge: '',
        carXslcCs: '',
        carXslcLj: '',
        projectId: '', // 项目id
        projectName: '' // 项目名称
      },
      files: 0,
      backFromData: {},
      vehicleStatusOption: [
        {name: '正常', value: '0'},
        {name: '维修', value: '1'},
        {name: '退场', value: '2'}
      ],
      notifyTitle: "",
      carAgeText: "",
      carLCText: "",
      driverList: []
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState(['portal']),
    ...mapState([StateTypes.AUTH_PORTALS]),
    isCompanyPortal() {
      return this.portal.name === '数据舱'
    },
    params() {
      return {
        projectId: this.portal.id,
        projectName: this.portal.name
      }
    },
    span() {
      return this.layoutSingleLine ? 24 : 12;
    },
    projectCarType() {
      return this.enums.ProjectUseCarTypeEnums
    },
    showDriverSelector() {
      const isAdd = this.type === 'plus';
      const noDriverSelected = Boolean(this.formData.driver) === false // 还未选择任何司
      return isAdd || noDriverSelected || this.formData.driverChange;
    },
    // 车辆来源为租赁
    isRentCar() {
      return this.formData.carResource === 200 || this.formData.carResource === 300;
    },
    carResource() {
      return this.enums.CarResourceEnums;
    },
    carStatus() {
      return this.enums.CarStatusEnums;
    },
    showExit() {
      return this.formData.carStatus === 300
    },
    isView() {
      return this.type === 'view';
    },
    isEdit() {
      return this.type === 'edit';
    },
    flowConfig() {
      const type = this.type === 'view' ? 'readonly' : ''
      return Object.keys(this.formData).reduce((acc, cur) => {
        return Object.assign(acc, {[cur]: type})
      }, {driver: type})
    },
    theme() {
      return this.$store.getters[types.THEME];
      // return '#2F54EB'
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter(item => item.projectStatus === 100); // 100激活，200关闭
    }
  },
  methods: {
    isValidNumber(val) {
      let res = true; // 默认为合法
      try {
        const num = Number(val);
        if (isNaN(num)) {
          res = false;
        } else if (num < 0) {
          res = false;
        } else {
          // 检查最多两位小数
          const parts = String(num).split('.');
          if (parts[1] && parts[1].length > 2) {
            res = false;
          }
        }
      } catch (e) {
        res = false;
      }
      return res;
    },
    getPlaceholder(text) {
      if (this.type === 'view') {
        return ''
      }
      return text
    },
    handleProjectChange() {
      // 当项目变化时，司机表单项需要清空
      this.formData.driverPhone = '';
      this.formData.driver = '';
      this.formData.driverChange = false;
      this.formChange();
    },
    formChange() {
      this.$emit('formChange', true)
    },
    onConfirm() {
      unbindCarWithDriver({
        carId: this.formData.id,
        driverId: this.formData.driver
      })
        .then(res => {
          if (res.status) {
            this.$message.success('解绑成功')
            this.$emit('unbindDriver')
            this.formData.driver = ''
            this.formData.driverPhone = ''
          }
        })
    },
    changeDriver(val) {
      const item = this.driverList.find(item => item.id === val);
      this.formData.driverPhone = item.driverPhone;
      this.formData.driverFullName = item.driverFullName;
      this.formData.driverChange = true;
      this.$nextTick(() => {
        this.$forceUpdate();
      })
    },
    doEdit() {
      this.backFromData = JSON.parse(JSON.stringify(this.formData));
    },
    editCancel() {
      this.formData = JSON.parse(JSON.stringify(this.backFromData));
    },
    queryTipsContent(projectId) {
      queryTips(projectId, tipsMap.addCar).then(res => {
        if (res.data.tipContent) {
          this.notifyTitle = res.data.tipContent
        }
      })
      queryTips(projectId, tipsMap.carLc).then(res => {
        if (res.data.tipContent) {
          this.carLCText = res.data.tipContent
        }
      })
      queryTips(projectId, tipsMap.carAge).then(res => {
        if (res.data.tipContent) {
          this.carAgeText = res.data.tipContent
        }
      })
    }
  },
  watch: {
    'formData.attachment1': {
      handler() {
        // 强制刷新文件上传组件
        this.refresh = false;
        this.$nextTick(() => {
          this.refresh = true;
        })
        this.files = 0;
      }
    },
    'formData.projectId': {
      immediate: true,
      handler(val) {
        if (val && this.isCompanyPortal) {
          this.formData.projectName = this.authPortalList.find(item => item.portalId === val).projectName
        }
        if (this.showDriverSelector && val) {
          // 当项目切换时, 调用接口获取司机列表
          getDriverListByPost({pageNo: 1, pageSize: 50, projectId: val || ''}).then(res => {
            if (res.status) {
              this.driverList = res.data.list;
            }
          })
        }
      }
    }
  },
  created() {
    if (!this.isCompanyPortal) {
      // 项目门户手动获取司机列表
      getDriverListByPost({pageNo: 1, pageSize: 50, projectId: this.portal.id}).then(res => {
        if (res.status) {
          this.driverList = res.data.list;
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
@import "~@/styles/disabled";

/deep/ .fks-date-editor.fks-input {
  width: 100%;
}

@import "~@/styles/tabs";
@import "~@/styles/input";
</style>
