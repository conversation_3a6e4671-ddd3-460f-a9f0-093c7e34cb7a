<template>
  <div>
    <header class="flex row-between">
      <common-title title="保险记录列表"/>
      <div class="buttons">
        <fks-popconfirm
          title="确认删除？"
          @onConfirm="handleDelete"
        >
          <fks-button slot="reference" :disabled="isView" dangerText icon="fks-icon-delete">删除
          </fks-button>
        </fks-popconfirm>
        <fks-button :style="{color: isView ? '#ccc !important' : '#333 !important'}" :disabled="isView" icon="fks-icon-circle-plus" text @click="handleAdd">新增
        </fks-button>
      </div>
    </header>
    <fks-table
      ref="SendingDetails"
      v-loading="tableLoading"
      :cell-style="{color: '#333333 !important'}"
      :data="tableData"
      :header-cell-style="{background: 'transparent', color: '#333333aa !important', fontWeight: 'unset !important'}"
      @selection-change="handleSelectionChange"
    >
      <fks-table-column v-if="!isView" fixed="left" type="selection" width="25"/>
      <fks-table-column align="left" header-align="left" label="投保日期" prop="bxTime1"
                        width="110">
        <template slot-scope="{row}">
          {{ $dayjs(row.bxTime1).format('YYYY-MM-DD') }}
        </template>
      </fks-table-column>
      <fks-table-column align="left" header-align="left" label="保险截止日期" prop="bxTime2"
                        width="110">
        <template slot-scope="{row}">
          {{ $dayjs(row.bxTime2).format('YYYY-MM-DD') }}
        </template>
      </fks-table-column>
      <fks-table-column align="left" header-align="left" label="保险公司" min-width="150px"
                        prop="bxGs">
        <template slot-scope="{row}">
          <overflow-tooltip :text="row.bxGs"/>
        </template>
      </fks-table-column>
      <fks-table-column align="left" header-align="left" label="保险价格（元）" prop="bxJg"
                        width="120"/>
      <fks-table-column align="left" header-align="left" label="附件（保单等）" min-width="200px"
                        prop="attachment1">
        <template slot-scope="{row}">
          <input-attachment disabled :form-data="row" attachment-name="attachment1" />
        </template>
      </fks-table-column>
      <fks-table-column align="left" header-align="left" label="备注" min-width="150px"
                        prop="remark1">
        <template slot-scope="{row}">
          <overflow-tooltip :text="row.remark1"/>
        </template>
      </fks-table-column>
    </fks-table>
    <fks-drawer
      v-if="visible"
      :before-close="beforeClose"
      :modal="false"
      :visible.sync="visible"
      :wrapperClosable="false"
      class="dialog"
      direction="rtl"
      size="660px"
    >
      <template slot="title">
        <div class="dialog-title flex col-center">
          <i class="fks-icon-plus m-r-10"/>
          <span>新增保险信息</span>
        </div>
      </template>
      <fks-form ref="formRef" :model="form" class=" newTheme">
        <fks-row class="">
          <fks-form-item
            :rules="[{required: true, message: '请输入保险公司', trigger: 'change'}]"
            label="保险公司"
            prop="bxGs"
            required
          >
            <fks-input
              v-model="form.bxGs"
              placeholder="请输入保险公司"/>
          </fks-form-item>
          <fks-form-item
            :rules="[{required: true, trigger: 'change', validator: numberValidator}]"
            label="保险价格"
            prop="bxJg"
            required
          >
            <fks-input
              v-model="form.bxJg"
              placeholder="请输入保险价格">
              <template slot="append">元</template>
            </fks-input>
          </fks-form-item>
          <fks-form-item
            :rules="[{required: true, trigger: 'change', message: '请选择投保日期'}]"
            label="投保日期"
            prop="bxTime1"
            required
          >
            <fks-date-picker
              v-model="form.bxTime1"
              class="full-width"
              clearable
              placeholder="请选择投保日期"
              type="date"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </fks-form-item>
          <fks-form-item
            :rules="[{required: true, trigger: 'change', message: '请选择保险截止日期'}]"
            label="保险截止日期"
            prop="bxTime2"
            required
          >
            <fks-date-picker
              v-model="form.bxTime2"
              class="full-width"
              clearable
              placeholder="请选择保险截止日期"
              type="date"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </fks-form-item>
          <fks-form-item label="附件（保单）" prop="attachment1">
            <input-attachment
              :form-data="form"
              :disabled="false"
              :attachment-name="'attachment1'"
            />
          </fks-form-item>
          <fks-form-item label="备注" prop="remark1">
            <fks-input
              v-model="form.remark1"
              :maxlength="300"
              show-word-limit
              type="textarea"
            />
          </fks-form-item>
        </fks-row>
      </fks-form>

      <fks-row>
        <fks-divider/>
        <fks-button :loading="loading" class="sub-btn" icon="fks-icon-check" text
                    @click="handleSubmit">
          提交
        </fks-button>
      </fks-row>
    </fks-drawer>
  </div>
</template>

<script>
import FormUpload from '@modules/FormCenter/components/FormUpload/index.vue'
import {
  addInsuranceItem,
  deleteInsuranceItem,
  getInsuranceList
} from "@modules/ProjectCar/ProjectPortal/CarManage/api";
import validator from "@/mixins/validator";
import OverflowTooltip from "@components/OverflowTooltip/index.vue";
import InputAttachment from '@components/InputAttachment/pc-view.vue'

export default {
  name: 'InsuranceInfo',
  components: {OverflowTooltip, FormUpload, InputAttachment},
  mixins: [validator],
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    carId: {
      type: String
    }
  },
  data() {
    return {
      reminderList: [],
      loading: false,
      tableLoading: false,
      visible: false,
      form: {
        bxGs: '', // 保险公司
        bxJg: '', // 保险价格
        bxTime1: '', // 投保日期
        bxTime2: '', // 保险截止日期
        remark1: '', // 备注
        attachment1: '' // 附件（保单）
      },
      formData: {
        date: '',
        reminder: '',
        smsReminder: ''
      },
      tableData: [],
      pageNo: 1,
      pageSize: 100,
      total: 0,
      selections: [],
      isChange: false
    }
  },
  computed: {
    rules() {
      return {
        date: [{required: true, message: '请输入'}],
        reminder: [{required: true, message: '请选择提醒人'}],
        smsReminder: [{required: true, message: '请选择'}],
      }
    },
    isClickSms() {
      return !!this.formData.date && !!this.formData.reminder;
    },
    isView() {
      return this.type === 'view';
    },
    isEdit() {
      return this.type === 'edit';
    },
    flowConfig() {
      if (this.type === 'view') {
        return Object.keys(this.formData).reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      return {}
    },
  },
  methods: {
    beforeClose(done) {
      this.formData = {
        bxGs: '', // 保险公司
        bxJg: '', // 保险价格
        bxTime1: '', // 投保日期
        bxTime2: '', // 保险截止日期
        remark1: '', // 备注
        attachment1: '' // 附件（保单）
      };
      done();
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.loading = true;
          addInsuranceItem({...this.form, carId: this.carId})
            .then(res => {
              if (res.status) {
                this.$message.success('新增成功')
                this.visible = false;
                this.isChange = true
                // 新增成功后重新查询列表
                this.getData()
                // 清空表单数据
                this.formData = {
                  bxGs: '', // 保险公司
                  bxJg: '', // 保险价格
                  bxTime1: '', // 投保日期
                  bxTime2: '', // 保险截止日期
                  remark1: '', // 备注
                  attachment1: '' // 附件（保单）
                }
              }
            }).finally(() => {
            this.loading = false
            this.form = {};
          })
        }
      })
    },
    getData(pageNo) {
      this.tableLoading = true;
      this.carId && getInsuranceList({
        carId: this.carId,
        pageNo: pageNo || this.pageNo,
        pageSize: this.pageSize
      }).then(res => {
        if (res.status) {
          this.total = res.data.total;
          this.tableData = res.data.list;
        }
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    handleAdd() {
      this.visible = true;
    },
    handleDelete() {
      if (this.selections.length) {
        const deleteIds = this.selections.map(item => item.id).join(',')
        deleteInsuranceItem(deleteIds).then(res => {
          if (res.status) {
            this.$message.success('删除成功')
            this.isChange = true
            this.getData();
          }
        })
      } else {
        this.$message.warning('请选择删除项')
      }
    },
    handleSelectionChange(selection) {
      this.selections = selection;
    },
    /** 校验内容 */
    validateAssetValue(value) {
      let val = value?.replace(/,/g, "")
      let valueNum = Number(val)
      return value && isNaN(valueNum) ? 0 : val
    },
    /** 输入框 */
    inputHandle(value, row) {
      row.value = this.validateAssetValue(value)
    },
    /** 失去焦点 */
    blurHandle(event, row) {
      const value = event.target.value
      row.value = this.validateAssetValue(value)
      // this.tableCurrent = null
    },
    /** 在组件中使用千分符 */
    toFormatMoney(value) {
      // 网上有很多教程，自行查阅
    },
  },
  watch: {
    visible(newVal) {
      // 关闭弹窗后清空数据
      if (!newVal) {
        this.$refs.formRef.resetFields();
        this.$refs.formRef.clearValidate();
      }
    },
    carId() {
      this.getData()
    }
  },
  created() {
    this.getData();
  }
}
</script>


<style lang='less' scoped>
@import "./insurance";
@import "~@/styles/disabled";

@import "~@/styles/input";

/deep/ .fks-drawer__body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.dialog-title {
  font-size: 32px;
}

/deep/ .fks-query-pagebox {
  bottom: 0 !important;
  right: 0 !important;
}

header {
  padding-bottom: 24px;
  border-bottom: 1px solid #cccccc;
}

.fks-form-item {
  margin: 12px 0;
}
</style>
