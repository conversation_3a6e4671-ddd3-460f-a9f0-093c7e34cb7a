<template>
  <div>
    <fks-drawer
    :before-close="beforeClose"
    :visible.sync="visible"
    :wrapperClosable="true"
    :modal="false"
    class="dialog"
    direction="rtl"
    size="660px"
    @open="onDrawerOpen"
  >
    <template slot="title">
      <div class="dialog-title flex col-center">
        <i :class="'fks-icon-' + type" :style="{ color: theme }" class="m-r-10"/>
        <span>{{ title }}车辆信息</span>
      </div>
    </template>
    <div class="add-container">
      <base-info
        v-if="visible"
        style="overflow-y: auto"
        ref="CarManageBaseInfo"
        :type="type"
        @formChange="formChange = $event"
        layout-single-line
      />
      <div style="border-top: 1px solid #F1F1F0;padding-top: 4px">
        <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                    @click="submit">
          提交
        </fks-button>
      </div>
    </div>
    </fks-drawer>
    <fks-dialog
      title="关闭"
      :visible.sync="tipVisible"
      size="small"
    >
      <div style="padding: 16px 20px">
        <span>表单内容发生改变是否继续关闭?</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <fks-button @click="tipVisible = false">取 消</fks-button>
        <fks-button type="primary" @click="onConfirmTip">确 定</fks-button>
      </span>
    </fks-dialog>
  </div>

</template>
<script>
import {mapState} from 'vuex';
import * as types from '@/store/Getter/getterTypes.js';
import BaseInfo from './BaseInfo.vue';
import InsuranceInfo from './InsuranceInfo.vue';
import AnnualInspectionInfo from './AnnualInspectionInfo.vue';
import MaintenanceInfo from './MaintenanceInfo.vue';
import PassportInfo from './PassportInfo.vue';
import ViolationAndAccident from './ViolationAndAccident.vue';
import {addCar, carBindToDriver, editCar} from "@modules/ProjectCar/ProjectPortal/CarManage/api";
import * as StateTypes from "@store/State/stateTypes";
import {carManagementTabs} from "@utils/constants";

export default {
  name: 'CarManageAdd',
  components: {
    BaseInfo,
    InsuranceInfo,
    AnnualInspectionInfo,
    MaintenanceInfo,
    PassportInfo,
    ViolationAndAccident
  },
  data() {
    return {
      type: 'view',
      loading: false,
      visible: false,
      tabList: carManagementTabs,
      carId: null,
      formChange: false,
      tipVisible: false
    }
  },
  computed: {
    ...mapState([StateTypes.PORTAL]),
    isCompanyPortal() {
      return this[StateTypes.PORTAL].name === '数据舱'
    },
    tabs() {
      if (this.isAdd) {
        return this.tabList.filter(item => item.name === '1')
      } else {
        return this.tabList
      }
    },
    theme() {
      return this.$store.getters[types.THEME];
      // return '#2F54EB'
    },
    isAdd() {
      return this.type === 'plus'
    },
    isView() {
      return this.type === 'view';
    },
    isEdit() {
      return this.type === 'edit';
    },
    title() {
      const typeObj = {
        plus: '新增',
        edit: '编辑',
        view: '查看'
      }
      return typeObj[this.type]
    }
  },
  methods: {
    // ...mapActions('SafetyAssessment/testQuestionBank', [
    //   'addExamTree',
    //   'updateExamTree'
    // ]),
    onConfirmTip() {
      this.handleClose()
      this.tipVisible = false
    },
    onDrawerOpen() {
      this.$refs.CarManageBaseInfo && this.$refs.CarManageBaseInfo.$refs.form.clearValidate();
    },
    handleTabs(tab, event) {
      console.log(tab, event);
    },
    beforeClose(done) {
      const formData = this.$refs.CarManageBaseInfo.formData
      const files = this.$refs.CarManageBaseInfo.files
      const excluedeKeys = ['attachment1','carSeatNum','carStatus']
      const otherKeysHasVal = Object.keys(formData).some(key => {
        return !excluedeKeys.includes(key) && formData[key]
      })
      const flag = !otherKeysHasVal && formData.carSeatNum == 5 && formData.carStatus == 100
      if (!flag || files) {
        this.tipVisible = true
      } else {
        this.$refs.CarManageBaseInfo.$refs.form.resetFields();
        this.$refs.CarManageBaseInfo.$refs.form.clearValidate();
        this.visible = false;
        done()
      }
    },
    handleClose() {
      this.$refs.CarManageBaseInfo.$refs.form.resetFields();
      this.$refs.CarManageBaseInfo.$refs.form.clearValidate();
      this.visible = false;
    },
    open(data, type) {
      this.visible = true;
      this.type = type;
      if (this.isEdit || this.isView) {
        this.carId = data.id;
        this.$nextTick(() => {
          this.$refs.CarManageBaseInfo.formData = Object.assign({}, data);
        })
      }
      this.$nextTick(() => {
        const portal = this[StateTypes.PORTAL];
        this.$refs.CarManageBaseInfo.queryTipsContent(portal.id);
      })
    },
    afterSuccess() {
      this.$emit('mutate')
      this.$emit('refresh', false)
      this.formChange = false;
      this.$refs.CarManageBaseInfo.$refs.form.resetFields()
      this.$refs.CarManageBaseInfo.$refs.form.clearValidate()
      this.visible = false;
    },
    async submit() {
        this.$refs.CarManageBaseInfo.$refs.form.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            const formData = this.$refs.CarManageBaseInfo.formData;
            const portal = this[StateTypes.PORTAL];
            const {driver, driverPhone, driverChange, driverFullName, ...rest} = formData;
            let payload = {};
            // 准备参数
            if (this.isEdit) {
              payload = rest
            } else {
              const projectInfo = {projectId: portal.id, projectName: portal.name}
              // payload = {...rest, ...projectInfo}
              if (this.isCompanyPortal) {
                payload = {...rest}
              } else {
                payload = {...rest, ...projectInfo}
              }

            }
            // 开始发起请求
            if (this.isEdit) {
              if (this.formChange) {
                const res = await editCar(payload);
                if (res.status) {
                  this.$message.success('编辑成功')
                  this.formChange = false;
                  if (!driverChange) {
                    this.afterSuccess()
                  }
                }
              }
              this.bindDriver(driverChange, driver, formData.id)
              if (this.type === 'edit') this.type = "view"
            } else {
              const res = await addCar(payload);
              if (res.status) {
                this.$message.success('新增成功')
                if (!driverChange) {
                  this.afterSuccess()
                }
                this.bindDriver(driverChange, driver, res.data.id)
              }
            }
            this.loading = false;
          }
        })
    },
    bindDriver(driverChange, driverId, carId) {
      // 拿到车辆id和司机信息绑定
      if (driverChange && driverId) {
        carBindToDriver({
          carId: carId,
          driverId: driverId
        }).then((res) => {
          if (res.status) {
            this.$message.success('绑定司机成功')
            this.afterSuccess()
          }
        }).finally(() => {
          this.loading = false;
        })
      }
    }
  }
}
</script>


<style lang='less' scoped>
/deep/ .fks-divider.fks-divider--horizontal {
  margin-top: 0;
}
.dialog {
  font-size: 32px;
}
.add-container {
  display: grid;
  height: 100%;
  grid-template-rows: 1fr auto;
  grid-row-gap: 20px;
  overflow-x: hidden;

  .tab-item {
    width: 100%;
    overflow-x: auto;
  }
}
.sub-btn {
  padding: 5px 10px;
  color: #333;
  transition: all 0.3s ease; /* 平滑过渡效果 */
}
</style>
