<template>
  <div>
    <header class="flex row-between">
      <common-title title="维修保养记录列表" />
      <div class="buttons">
        <fks-popconfirm
          title="确认删除？"
          @onConfirm="handleDelete"
        >
          <fks-button :disabled="isView" slot="reference" dangerText icon="fks-icon-delete">删除</fks-button>
        </fks-popconfirm>
        <fks-button :style="{color: isView ? '#ccc !important' : '#333 !important'}" :disabled="isView" text icon="fks-icon-circle-plus" @click="handleAdd">新增</fks-button>
      </div>
    </header>
    <fks-table
      ref="SendingDetails"
      v-loading="tableLoading"
      :data="tableData"
      :header-cell-style="{background: 'transparent', color: '#333333aa !important', fontWeight: 'unset !important'}"
      :cell-style="{color: '#333333 !important'}"
      @selection-change="handleSelectionChange"
    >
      <fks-table-column v-if="!isView" fixed="left" type="selection" width="40"/>
      <fks-table-column header-align="left" align="left"  label="日期" prop="wxbyTime1" width="110">
        <template slot-scope="{row}">
          {{ $dayjs(row.bxTime1).format('YYYY-MM-DD') }}
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left"  label="项目" min-width="150px" prop="wxbyXm">
        <template slot-scope="{row}">
          <overflow-tooltip :text="row.wxbyXm"/>
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left"  label="费用（元）" prop="wxbyFy" width="120"/>
      <fks-table-column header-align="left" align="left"  label="附件" min-width="200px" prop="attachment">
        <template slot-scope="{row}">
          <input-attachment disabled :form-data="row" attachment-name="attachment1" />
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left"  label="备注" min-width="150px" prop="remark1">
        <template slot-scope="{row}">
          <overflow-tooltip :text="row.remark1"/>
        </template>
      </fks-table-column>
    </fks-table>
    <fks-drawer
      v-if="visible"
      :visible.sync="visible"
      :modal="false"
      :before-close="beforeClose"
      :wrapperClosable="false"
      class="dialog"
      direction="rtl"
      size="660px"
    >
      <template slot="title">
        <div class="dialog-title flex col-center">
          <i class="fks-icon-plus m-r-10"/>
          <span>新增维修保养信息</span>
        </div>
      </template>
      <fks-form ref="formRef" :model="form" class="newTheme">
        <fks-row>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{required: true, trigger: 'change', message: '请选择日期'}]"
              label="日期"
              prop="wxbyTime1"
              required
            >
              <fks-date-picker
                v-model="form.wxbyTime1"
                class="full-width"
                placeholder="请选择维修养护日期"
                clearable
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{required: true, message: '请输入维修保养项目', trigger: 'change'}]"
              label="维修保养项目"
              prop="wxbyXm"
              required
            >
              <fks-input
                placeholder="请输入维修保养项目" v-model="form.wxbyXm"/>
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{required: true, trigger: 'change', validator: numberValidator}]"
              label="费用"
              prop="wxbyFy"
              required
            >
              <fks-input
                placeholder="请输入维修保养费用" v-model="form.wxbyFy">
                <template slot="append">元</template>
              </fks-input>
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="附件" prop="attachment1">
              <input-attachment :form-data="form" attachment-name="attachment1" />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="备注" prop="remark1">
              <fks-input
                v-model="form.remark1"
                :maxlength="300"
                show-word-limit
                type="textarea"
              />
            </fks-form-item>
          </fks-col>
        </fks-row>
      </fks-form>
      <fks-row>
        <fks-divider/>
        <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                    @click="handleSubmit">
          提交
        </fks-button>
      </fks-row>
    </fks-drawer>

  </div>

</template>

<script>
import validator from "@/mixins/validator";
import {
  addMaintenanceItem,
  deleteMaintenanceItem,
  getMaintenanceList
} from "@modules/ProjectCar/ProjectPortal/CarManage/api";
import OverflowTooltip from "@components/OverflowTooltip/index.vue";
import InputAttachment from '@components/InputAttachment/pc-view.vue'

export default {
  name: 'MaintenanceInfo',
  mixins: [validator],
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    carId: {
      type: String
    }
  },
  components: {OverflowTooltip, InputAttachment},
  data() {
    return {
      loading: false,
      visible: false,
      form: {
        wxbyTime1: '',
        wxbyXm: '',
        wxbyFy: '',
        attachment1: '',
        remark1: ''
      },
      pageNo: 1,
      pageSize: 100,
      total: 0,
      tableData: [],
      tableLoading: false,
      selections: [],
      isChange: false
    }
  },
  computed: {
    isView() {
      return this.type === 'view';
    },
    isEdit() {
      return this.type === 'edit';
    },
    flowConfig() {
      if (this.type === 'view') {
        return Object.keys(this.form).reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      return {}
    },
  },
  methods: {
    beforeClose(done) {
      this.form = {
        wxbyTime1: '',
        wxbyXm: '',
        wxbyFy: '',
        attachment1: '',
        remark1: ''
      };
      done();
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.loading = true;
          addMaintenanceItem({...this.form, carId: this.carId})
            .then(res => {
              if (res.status) {
                this.$message.success('新增成功')
                this.visible = false;
                this.isChange = true
                // 新增成功后重新查询列表
                this.getData()
                // 清空表单数据
                this.form = {
                  wxbyTime1: '',
                  wxbyXm: '',
                  wxbyFy: '',
                  attachment1: '',
                  remark1: ''
                }
              }
            }).finally(() => {
              this.loading = false
              this.form = {};
            })
        }
      })
    },
    getData(pageNo) {
      this.tableLoading = true;
      this.carId && getMaintenanceList({
        pageNo: this.pageNo,
        pageSize: pageNo || this.pageSize,
        carId: this.carId
      }).then(res => {
        if (res.status) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleAdd() {
      this.visible = true;

    },
    handleDelete() {
      if (this.selections.length) {
        const deleteIds = this.selections.map(item => item.id).join(',')
        deleteMaintenanceItem(deleteIds).then(res => {
          if (res.status) {
            this.$message.success('删除成功')
            this.isChange = true
            this.getData();
          }
        })
      } else {
        this.$message.warning('请选择删除项')
      }
    },
    handleSelectionChange(selection) {
      this.selections = selection
    }
  },
  watch: {
    visible(newVal) {
      // 关闭弹窗后清空数据
      if (!newVal) {
        this.$refs.formRef.resetFields();
        this.$refs.formRef.clearValidate();
      }
    },
    carId() {
      this.getData()
    }
  },
  created() {
    this.getData();
  }
}
</script>


<style lang='less' scoped>
@import "~@/styles/disabled";

@import "~@/styles/tabs";
@import "~@/styles/input";
/deep/ .fks-drawer__body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.dialog-title {
  font-size: 32px;
}

/deep/ .fks-query-pagebox {
  bottom: 0 !important;
  right: 0 !important;
}
header {
  padding-bottom: 24px;
  border-bottom: 1px solid #cccccc;
}
.fks-form-item{
  margin: 12px 0;
}
</style>
