<template>
  <div>
    <header class="flex row-between">
      <common-title title="通行证记录列表" />
      <div class="buttons">
        <fks-popconfirm
          title="确认删除？"
          @onConfirm="handleDelete"
        >
          <fks-button :disabled="isView" slot="reference" dangerText icon="fks-icon-delete">删除</fks-button>
        </fks-popconfirm>
        <fks-button :style="{color: isView ? '#ccc !important' : '#333 !important'}"  :disabled="isView" text icon="fks-icon-circle-plus" @click="handleAdd">新增</fks-button>
      </div>
    </header>
    <fks-table
      ref="SendingDetails"
      v-loading="tableLoading"
      :data="tableData"
      :header-cell-style="{background: 'transparent', color: '#333333aa !important', fontWeight: 'unset !important'}"
      :cell-style="{color: '#333333 !important'}"
      @selection-change="handleSelectionChange"
    >
      <fks-table-column v-if="!isView" fixed="left" type="selection" width="40"/>
      <fks-table-column header-align="left" align="left" label="通行证申请日期" prop="txzTime1" width="150">
        <template slot-scope="{row}">
          {{ $dayjs(row.txzTime1).format('YYYY-MM-DD') }}
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left" label="通行证到期日期" prop="txzTime2" width="150">
        <template slot-scope="{row}">
          {{ $dayjs(row.txzTime2).format('YYYY-MM-DD') }}
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left" label="附件" prop="attachment1" min-width="150px">
        <template slot-scope="{row}">
          <input-attachment disabled :form-data="row" attachment-name="attachment1" />
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left" label="备注" prop="remark1" min-width="150px">
        <template slot-scope="{row}">
          <overflow-tooltip :text="row.remark1"/>
        </template>
      </fks-table-column>
    </fks-table>
    <fks-drawer
      v-if="visible"
      :visible.sync="visible"
      :modal="false"
      :before-close="beforeClose"
      :wrapperClosable="false"
      class="dialog"
      direction="rtl"
      size="660px"
    >
      <template slot="title">
        <div class="dialog-title flex col-center">
          <i class="fks-icon-plus m-r-10"/>
          <span>新增通行证信息</span>
        </div>
      </template>
      <fks-form ref="formRef" :model="form" label-width="160px" class="newTheme">
        <fks-row>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{required: true, trigger: 'change', message: '请选择通行证申请日期'}]"
              label="通行证申请日期"
              prop="txzTime1"
              required
            >
              <fks-date-picker
                v-model="form.txzTime1"
                class="full-width"
                placeholder="请选择通行证申请日期"
                clearable
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{required: true, trigger: 'change', message: '请选择通行证到期日期'}]"
              label="通行证到期日期"
              prop="txzTime2"
              required
            >
              <fks-date-picker
                v-model="form.txzTime2"
                class="full-width"
                placeholder="请选择通行证到期日期"
                clearable
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="附件" prop="attachment1">
              <input-attachment :form-data="form" attachment-name="attachment1" />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="备注" prop="remark1">
              <fks-input
                v-model="form.remark1"
                :maxlength="300"
                show-word-limit
                type="textarea"
              />
            </fks-form-item>
          </fks-col>
        </fks-row>
      </fks-form>
      <fks-row>
        <fks-divider/>
        <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                    @click="handleSubmit">
          提交
        </fks-button>
      </fks-row>
    </fks-drawer>

  </div>
</template>

<script>
import InputAttachment from '@components/InputAttachment/pc-view.vue'
import OverflowTooltip from "@components/OverflowTooltip/index.vue";
import {
  addPassportItem,
  deletePassportItem,
  getPassportInfos
} from "@modules/ProjectCar/ProjectPortal/CarManage/api";

export default {
  name: 'PassportInfo',
  components: {InputAttachment, OverflowTooltip},
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    carId: {
      type: String
    }
  },
  data() {
    return {
      visible: false,
      reminderList: [],
      loading: false,
      tableLoading: false,
      formData: {
        date: '',
        reminder: '',
        smsReminder: ''
      },
      form: {
        txzTime1: '',
        txzTime2: '',
        attachment1: '',
        remark1: ''
      },
      tableData: [],
      pageNo: 1,
      pageSize: 100,
      total: 0,
      selections: [],
      isChange: false
    }
  },
  computed: {
    rules() {
      return {
        date: [{required: true, message: '请输入'}],
        reminder: [{required: true, message: '请选择提醒人'}],
        smsReminder: [{required: true, message: '请选择'}],
      }
    },
    isClickSms() {
      return !!this.formData.date && !!this.formData.reminder;
    },
    isView() {
      return this.type === 'view';
    },
    isEdit() {
      return this.type === 'edit';
    },
    flowConfig() {
      if (this.type === 'view') {
        return Object.keys(this.formData).reduce((acc, cur) => {
          return Object.assign(acc, {[cur]: 'readonly'})
        }, {})
      }
      return {}
    },
  },
  methods: {
    beforeClose(done) {
      this.formData = {
        date: '',
        reminder: '',
        smsReminder: ''
      };
      done();
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.loading = true;
          addPassportItem({...this.form, carId: this.carId})
            .then(res => {
              if (res.status) {
                this.$message.success('新增成功')
                this.isChange = true
                this.visible = false;
                // 新增成功后重新查询列表
                this.getData()
                // 清空表单数据
                this.form = {
                  txzTime1: '',
                  txzTime2: '',
                  attachment1: '',
                  remark1: ''
                };
              }
            }).finally(() => {
            this.loading = false
            this.form = {};
          })
        }
      })
    },
     getData(pageNo) {
      this.tableLoading = true;
      this.carId && getPassportInfos({
        carId: this.carId,
        pageNo: pageNo || this.pageNo,
        pageSize: this.pageSize
      }).then(res => {
        if (res.status) {
          this.total = res.data.total;
          this.tableData = res.data.list;
        }
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    changeReminder(val) {
    },
    handleAdd() {
      this.visible = true
    },
    handleDelete() {
      if (this.selections.length) {
        const deleteIds = this.selections.map(item => item.id).join(',')
        deletePassportItem(deleteIds).then(res => {
          if (res.status) {
            this.$message.success('删除成功')
            this.isChange = true
            this.getData();
          }
        })
      } else {
        this.$message.warning('请选择删除项')
      }
    },
    handleSelectionChange(selection) {
      this.selections = selection;
    }
  },
  watch: {
    visible(newVal) {
      // 关闭弹窗后清空数据
      if (!newVal) {
        this.$refs.formRef.resetFields();
        this.$refs.formRef.clearValidate();
      }
    },
    carId() {
      this.getData()
    }
  },
  created() {
    this.getData();
  }
}
</script>


<style lang='less' scoped>
@import "./insurance";
@import "~@/styles/disabled";
@import "~@/styles/input";
/deep/ .fks-drawer__body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
header {
  padding-bottom: 24px;
  border-bottom: 1px solid #cccccc;
}
.fks-form-item{
  margin: 12px 0;
}
</style>
