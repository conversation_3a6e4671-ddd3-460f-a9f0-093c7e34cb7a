<template>
  <div>
    <header class="flex row-between">
      <common-title title="年检信息列表" />
      <div class="buttons">
        <fks-popconfirm title="确认删除？" @onConfirm="handleDelete">
          <fks-button :disabled="isView" slot="reference" dangerText icon="fks-icon-delete"
            >删除</fks-button
          >
        </fks-popconfirm>
        <fks-button :style="{color: isView ? '#ccc !important' : '#333 !important'}"  :disabled="isView" text icon="fks-icon-circle-plus" @click="handleAdd"
          >新增</fks-button
        >
      </div>
    </header>
    <fks-table
      v-loading="tableLoading"
      :data="tableData"
      :header-cell-style="{
        background: 'transparent',
        color: '#333333aa !important',
        fontWeight: 'unset !important',
      }"
      :cell-style="{ color: '#333333 !important' }"
      @selection-change="handleSelectionChange"
    >
      <fks-table-column v-if="!isView" fixed="left" type="selection" width="40" />

      <fks-table-column
        header-align="left"
        align="left"
        label="年检日期"
        prop="njTime1"
        width="110"
      >
        <template slot-scope="{ row }">
          {{ $dayjs(row.njTime1).format('YYYY-MM-DD') }}
        </template>
      </fks-table-column>

      <fks-table-column
        header-align="left"
        align="left"
        label="年检到期日期"
        prop="njTime2"
        width="110"
      >
        <template slot-scope="{ row }">
          {{ $dayjs(row.njTime2).format('YYYY-MM-DD') }}
        </template>
      </fks-table-column>
      <!--      <fks-table-column header-align="left" align="left" prop="insuranceCompany" label="关联表单">-->
      <!--        <template slot-scope="scope">-->
      <!--          <fks-button text> 查看</fks-button>-->
      <!--        </template>-->
      <!--      </fks-table-column>-->
      <fks-table-column
        header-align="left"
        align="left"
        prop="attachment1"
        label="附件"
        min-width="150px"
      >
        <template slot-scope="scope">
          <input-attachment
            :formData="scope.row"
            :disabled="isView || isEdit"
            :attachmentName="'attachment1'"
          />
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left" label="备注" min-width="150px">
        <template slot-scope="{ row }">
          <overflow-tooltip :text="row.remark1" />
        </template>
      </fks-table-column>
    </fks-table>

    <fks-drawer
      v-if="visible"
      :visible.sync="visible"
      :modal="false"
      :before-close="beforeClose"
      :wrapperClosable="false"
      class="dialog"
      direction="rtl"
      size="660px"
    >
      <template slot="title">
        <div class="dialog-title flex col-center">
          <i class="fks-icon-plus m-r-10" />
          <span>新增年检信息</span>
        </div>
      </template>
      <fks-form class="newTheme" ref="formRef" :model="formData">
        <fks-row>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{ required: true, trigger: 'change', message: '请选择日期' }]"
              label="年检日期"
              prop="njTime1"
              required
            >
              <fks-date-picker
                v-model="formData.njTime1"
                class="full-width"
                clearable
                placeholder="请输入年检日期"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{ required: true, trigger: 'change', message: '请选择日期' }]"
              label="年检到期日期"
              prop="njTime2"
              required
            >
              <fks-date-picker
                v-model="formData.njTime2"
                class="full-width"
                placeholder="请选择年检到期日期"
                clearable
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="附件" prop="attachment1">
              <input-attachment
                :formData="formData"
                :disabled="false"
                :attachmentName="'attachment1'"
              />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="备注" prop="remark1">
              <fks-input
                v-model="formData.remark1"
                :maxlength="300"
                show-word-limit
                type="textarea"
              />
            </fks-form-item>
          </fks-col>
        </fks-row>
      </fks-form>
      <fks-row>
        <fks-divider/>
        <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                    @click="handleSubmit">
          提交
        </fks-button>
      </fks-row>
    </fks-drawer>
  </div>
</template>

<script>
import {
  addAnnualInspectionItem,
  deleteAnnualInspectionItem,
  getAnnualInspectionList,
} from '@modules/ProjectCar/ProjectPortal/CarManage/api'
import OverflowTooltip from '@components/OverflowTooltip/index.vue'
import InputAttachment from '@components/InputAttachment/pc-view.vue'

export default {
  name: 'AnnualInspectionInfo',
  components: { OverflowTooltip, InputAttachment },
  props: {
    type: {
      type: String,
      default: 'view',
    },
    carId: {
      type: String,
    },
  },
  data() {
    return {
      reminderList: [],
      visible: false,
      loading: false,
      formData: {
        date: '',
        reminder: '',
        smsReminder: '',
      },
      tableData: [],
      selections: [],
      pageNo: 1,
      pageSize: 100,
      tableLoading: false,
      isChange: false
    }
  },
  computed: {
    rules() {
      return {
        date: [{ required: true, message: '请输入' }],
        reminder: [{ required: true, message: '请选择提醒人' }],
        smsReminder: [{ required: true, message: '请选择' }],
      }
    },
    isClickSms() {
      return !!this.formData.date && !!this.formData.reminder
    },
    isView() {
      return this.type === 'view'
    },
    isEdit() {
      return this.type === 'edit'
    },
    flowConfig() {
      if (this.type === 'view') {
        return Object.keys(this.formData).reduce((acc, cur) => {
          return Object.assign(acc, { [cur]: 'readonly' })
        }, {})
      }
      return {}
    },
  },
  methods: {
    changeReminder(val) {
      // console.log(val)
    },
    beforeClose(done) {
      this.formData = {
        date: '',
        reminder: '',
        smsReminder: '',
      };
      console.info('🚀🚀', 'this.formData -->', this.formData, `<-- AnnualInspectionInfo.vue/beforeClose`)
      done();
    },
    handleSelectionChange(selection) {
      this.selections = selection
    },
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true
          addAnnualInspectionItem({ ...this.formData, carId: this.carId })
            .then((res) => {
              if (res.status) {
                this.$message.success('新增成功')
                this.visible = false
                this.isChange = true
                // 清空表单数据
                this.formData = {
                  date: '',
                  reminder: '',
                  smsReminder: '',
                }
                // 新增成功后重新查询列表
                this.getData()
              }
            })
            .finally(() => {
              this.loading = false
              this.form = {};
            })
        }
      })
    },
    handleAdd() {
      this.visible = true
    },
    handleDelete() {
      if (this.selections.length) {
        const deleteIds = this.selections.map((item) => item.id).join(',')
        deleteAnnualInspectionItem(deleteIds).then((res) => {
          if (res.status) {
            this.$message.success('删除成功')
            this.isChange = true
            this.getData()
          }
        })
      } else {
        this.$message.warning('请选择删除项')
      }
    },
    getData() {
      this.carId &&
        getAnnualInspectionList({ pageNo: this.pageNo, pageSize: this.pageSize, carId: this.carId })
          .then((res) => {
            if (res.status) {
              this.tableData = res.data.list
              this.total = res.data.total
            }
          })
          .finally(() => {
            this.tableLoading = false
          })
    },
  },
  watch: {
    visible(newVal) {
      // 关闭弹窗后清空数据
      if (!newVal) {
        this.$refs.formRef.resetFields()
        this.$refs.formRef.clearValidate()
      }
    },
    carId() {
      this.getData()
    },
  },
  created() {
    this.getData()
  },
}
</script>

<style scoped lang="less">
@import './insurance';
@import '~@/styles/disabled';
@import "~@/styles/input";
/deep/ .fks-drawer__body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
header {
  padding-bottom: 24px;
  border-bottom: 1px solid #cccccc;
}
.fks-form-item{
  margin: 12px 0;
}
</style>
