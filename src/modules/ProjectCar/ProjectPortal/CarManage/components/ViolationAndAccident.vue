<template>
  <div>
    <header class="flex row-between">
      <common-title title="违章/事故列表" />
      <div class="buttons">
        <fks-popconfirm
          title="确认删除？"
          @onConfirm="handleDelete"
        >
          <fks-button :disabled="isView" slot="reference" dangerText icon="fks-icon-delete">删除</fks-button>
        </fks-popconfirm>
        <fks-button :style="{color: isView ? '#ccc !important' : '#333 !important'}"  :disabled="isView" text icon="fks-icon-circle-plus" @click="handleAdd">新增</fks-button>
      </div>
    </header>
    <fks-table
      v-loading="tableLoading"
      :data="tableData"
      :header-cell-style="{background: 'transparent', color: '#333333aa !important', fontWeight: 'unset !important'}"
      :cell-style="{color: '#333333 !important'}"
      @selection-change="handleSelectionChange"
    >
      <fks-table-column fixed="left" type="selection" width="40"/>
      <fks-table-column header-align="left" align="left" label="类型" prop="wzLx">
        <template slot-scope="{row}">
          {{ getTypeText(row) }}
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left" label="发生日期" prop="wzTime" width="110">
        <template slot-scope="{row}">
          {{ $dayjs(row.wzTime).format('YYYY-MM-DD') }}
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left" label="司机" prop="wzDriverFullName"/>
      <fks-table-column header-align="left" align="left" label="事件描述" min-width="150px" prop="wzSzms">
        <template slot-scope="{row}">
          <overflow-tooltip :text="row.wzSzms"/>
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left" label="责任划分" prop="wzZrhf" width="90"/>
      <fks-table-column header-align="left" align="left" label="罚金（元）" prop="wzFj" width="90"/>
      <fks-table-column header-align="left" align="left" label="扣分（分）" prop="wzKf" width="90"/>
      <fks-table-column header-align="left" align="left" label="附件" min-width="200px" prop="attachment1">
        <template slot-scope="{row}">
          <input-attachment disabled :form-data="row" attachment-name="attachment1" />
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left" label="备注" min-width="150px" prop="remark1">
        <template slot-scope="{row}">
          <overflow-tooltip :text="row.remark1"/>
        </template>
      </fks-table-column>
    </fks-table>
    <fks-drawer
      v-if="visible"
      :visible.sync="visible"
      :modal="false"
      :before-close="beforeClose"
      :wrapperClosable="false"
      class="dialog"
      direction="rtl"
      size="660px"
    >
      <template slot="title">
        <div class="dialog-title flex col-center">
          <i class="fks-icon-plus m-r-10"/>
          <span>新增违章信息</span>
        </div>
      </template>
      <fks-form ref="formRef" :model="form" :disabled="isView" class="newTheme">
        <fks-row>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{required: true, message: '请选择类型', trigger: 'change'}]"
              label="类型"
              prop="wzLx"
              required
            >
              <fks-select v-model="form.wzLx" class="full-width">
                <fks-option
                  v-for="item in wzEnums"
                  :key="item.code"
                  :label="item.value"
                  :value="item.key"
                />
              </fks-select>
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{required: true, trigger: 'change', message: '请选择发生日期'}]"
              label="发生日期"
              prop="wzTime"
              required
            >
              <fks-date-picker
                v-model="form.wzTime"
                class="full-width"
                placeholder="请选择发生日期"
                clearable
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="司机" prop="wzDriverName" required>
              <fks-select
                v-model="form.wzDriverName"
                filterable
                placeholder="请选择司机"
                class="full-width"
                @change="handleDriverChange"
              >
                <fks-option
                  v-for="item in driverList"
                  :key="item.id"
                  :label="item.driverFullName"
                  :value="item.id"
                >
                </fks-option>
              </fks-select>
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{required: true, trigger: 'change', message: '请输入事件描述'}]"
              label="事件描述"
              prop="wzSzms"
              required
            >
              <fks-input
                v-model="form.wzSzms"
                :rows="5"
                placeholder="请输入事件描述"
                type="textarea"
              >
              </fks-input>
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item
              :required="form.wzLx === 200"
              :rules="[{required: form.wzLx === 200, trigger: 'change', message: '请输入责任划分'}]"
              label="责任划分"
              prop="wzZrhf"
            >
              <fks-input v-model="form.wzZrhf" placeholder="请输入责任划分"/>
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="罚金" prop="wzFj">
              <fks-input v-model="form.wzFj">
                <template slot="append">元</template>
              </fks-input>
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="扣分" prop="wzKf">
              <fks-input v-model="form.wzKf">
                <template slot="append">分</template>
              </fks-input>
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item
              :rules="[{required: true, trigger: 'change', validator: fileValidator}]"
              label="附件"
              prop="attachment1"
              required
            >
              <input-attachment :form-data="form" attachment-name="attachment1" @getFileInfo="fileCount = $event.length" />
            </fks-form-item>
          </fks-col>
          <fks-col :span="24">
            <fks-form-item label="备注" prop="remark1">
              <fks-input
                v-model="form.remark1"
                :maxlength="300"
                show-word-limit
                type="textarea"
              />
            </fks-form-item>
          </fks-col>
        </fks-row>
      </fks-form>
      <fks-row>
        <fks-divider/>
        <fks-button class="sub-btn" :loading="loading" icon="fks-icon-check" text
                    @click="handleSubmit">
          提交
        </fks-button>
      </fks-row>
    </fks-drawer>

  </div>
</template>

<script>
import {
  addViolationItem,
  deleteViolationItem,
  getViolationInfos
} from "@modules/ProjectCar/ProjectPortal/CarManage/api";
import {mapState} from "vuex";
import {getDriverList} from "@modules/ProjectCar/ProjectPortal/DriverManage/api";
import validator from "@/mixins/validator";
import OverflowTooltip from "@components/OverflowTooltip/index.vue";
import InputAttachment from '@components/InputAttachment/pc-view.vue'


export default {
  name: 'ViolationAndAccident',
  components: {OverflowTooltip, InputAttachment},
  mixins: [validator],
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    carId: {
      type: String
    }
  },
  data() {
    return {
      loading: false,
      tableLoading: false,
      visible: false,
      form: {
        wzLx: '', // 类型
        wzTime: '', // 发生日期
        wzDriverName: '', // 司机
        wzDriverFullName: '', // 司机全名
        wzDriverPhone: '', // 司机联系电话
        wzSzms: '', // 事件描述
        wzZrhf: '', // 责任划分
        wzFj: '', // 罚金
        wzKf: '', // 扣分
      },
      tableData: [],
      pageNo: 1,
      pageSize: 5,
      total: 0,
      selections: [],
      driverList: [],
      fileCount: 0,
      isChange: false
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    wzEnums() {
      return this.enums.CarWzEnums;
    },
    isView() {
      return this.type === 'view';
    },
    isEdit() {
      return this.type === 'edit';
    }
  },
  methods: {
    beforeClose(done) {
      this.form = {
        wzLx: '', // 类型
        wzTime: '', // 发生日期
        wzDriverName: '', // 司机
        wzDriverFullName: '', // 司机全名
        wzDriverPhone: '', // 司机联系电话
        wzSzms: '', // 事件描述
        wzZrhf: '', // 责任划分
        wzFj: '', // 罚金
        wzKf: '', // 扣分
      }
      done();
    },
    getTypeText({wzLx}) {
      if (wzLx) {
        const item = this.wzEnums.find(item => item.key.toString() === wzLx);
        return item.value;
      }
      return ''
    },
    fileValidator(val, value, callback) {
      if (this.fileCount === 0) {
        callback(new Error('请选择附件'))
      } else {
        callback()
      }
    },
    handleDriverChange(val) {
      const item = this.driverList.find(item => item.id === val);
      this.form.wzDriverFullName = item.driverFullName;
      this.form.wzDriverPhone = item.driverPhone;
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.loading = true;
          addViolationItem({...this.form, carId: this.carId})
            .then(res => {
              if (res.status) {
                this.$message.success('新增成功')
                this.isChange = true
                this.visible = false;
                // 新增成功后重新查询列表
                this.getData();
                // 清空表单数据
                this.form = {
                  wzLx: '', // 类型
                  wzTime: '', // 发生日期
                  wzDriverName: '', // 司机
                  wzDriverFullName: '', // 司机全名
                  wzDriverPhone: '', // 司机联系电话
                  wzSzms: '', // 事件描述
                  wzZrhf: '', // 责任划分
                  wzFj: '', // 罚金
                  wzKf: '', // 扣分
                }
              }
            }).finally(() => {
            this.loading = false
            this.form = {};
          })
        }
      })
    },
    getData(pageNo) {
      this.tableLoading = true;
      this.carId && getViolationInfos({
        carId: this.carId,
        pageNo: pageNo || this.pageNo,
        pageSize: this.pageSize
      }).then(res => {
        if (res.status) {
          this.total = res.data.total;
          this.tableData = res.data.list;
        }
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    handleAdd() {
      this.visible = true;
    },
    handleDelete() {
      if (this.selections.length) {
        const deleteIds = this.selections.map(item => item.id).join(',')
        deleteViolationItem(deleteIds).then(res => {
          if (res.status) {
            this.$message.success('删除成功')
            this.isChange = true
            this.getData();
          }
        })
      } else {
        this.$message.warning('请选择删除项')
      }
    },
    handleSelectionChange(selection) {
      this.selections = selection;
    },
    /** 校验内容 */
    validateAssetValue(value) {
      let val = value?.replace(/,/g, "")
      let valueNum = Number(val)
      return value && isNaN(valueNum) ? 0 : val
    },
    /** 输入框 */
    inputHandle(value, row) {
      row.value = this.validateAssetValue(value)
    },
    /** 失去焦点 */
    blurHandle(event, row) {
      const value = event.target.value
      row.value = this.validateAssetValue(value)
      // this.tableCurrent = null
    },
    /** 在组件中使用千分符 */
    toFormatMoney(value) {
      // 网上有很多教程，自行查阅
    },
  },
  watch: {
    visible(newVal) {
      // 关闭弹窗后清空数据
      if (!newVal) {
        this.$refs.formRef.resetFields();
        this.$refs.formRef.clearValidate();
      }
    },
    carId() {
      this.getData()
    }
  },
  created() {
    this.getData();
    getDriverList({pageNo: 1, pageSize: 100}).then(res => {
      this.driverList = res.data.list;
    });
  }
}
</script>


<style lang='less' scoped>
@import "./insurance";
@import "~@/styles/disabled";
header {
  padding-bottom: 24px;
  border-bottom: 1px solid #cccccc;
}
/deep/ .fks-drawer__body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.fks-form-item{
  margin: 12px 0;
}
</style>
