<template class="bg-white">
  <div v-if="!blankPage">
    <PcView v-if="isPC && params" v-bind="params"/>
    <MobileView v-else-if="isMobile && params" v-bind="params"/>
  </div>
  <fks-empty v-else :description="text"/>
</template>

<script>
import FormCenter from "@modules/FormCenter/index.vue";
import {applicationDefaultParams} from "@/mixins/menuRouteMixin";
import platform from "@/mixins/platform";
import PcView from '@/modules/FormCenter/vehicleDispatch/pc-view.vue';
import MobileView from '@/modules/FormCenter/vehicleDispatch/mobile-view.vue';
import { mapState } from 'vuex'
import { IS_PHONE_VERIFY } from '@/store/State/stateTypes'
import { FORM_VIEW_PARAMS } from '@store/modules/FormController/formFieldConfig'

export default {
  name: "Details",
  mixins: [platform],
  components: {FormCenter, PcView, MobileView},
  data() {
    return {
      params: null,
      blankPage: false,
      text: '验证失败，无法访问'
    }
  },
  computed: {
    ...mapState([IS_PHONE_VERIFY]),
  },
  created() {
    if(this[IS_PHONE_VERIFY]) {
      if (this.$route.query.type) {
        this.params = {...FORM_VIEW_PARAMS, ...this.$route.query, noAuth: true}
      } else {
        this.params = {...FORM_VIEW_PARAMS, ...(this.$route.params.type ? this.$route.params : applicationDefaultParams), noAuth: true};
      }
    }else {
      this.blankPage = true
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
