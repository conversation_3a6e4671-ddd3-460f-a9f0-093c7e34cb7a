<template>
  <div class="flex flex-column" v-if="isPC">
    <HomeHeader />
    <MainContainer class="flex-grow-1" />
  </div>
  <MainContainer v-else />
</template>

<script>
import platform from "@/mixins/platform";
import HomeHeader from "@components/HomeHeader/index.vue";
import MainContainer from "@components/Main/index.vue";

export default {
  name: "HomePage",
  components: { MainContainer, HomeHeader},
  mixins: [platform],
  mounted() {
    //生成一个上下文模块,包含目录下所有模块的引用(参数：查询的目录，是否查询子孙目录，文件后缀)
    const req = require.context('@/assets/svg/menu', true, /\.svg$/)
    const requireAll = (requireContext) =>
      requireContext.keys()
        .map((key) => {
          return {
            code: key.replace(/(.*\/)*([^.]+).*/gi, '$2')
          }
        })
    //获取SVG图标数组
    requireAll(req)
  }
}
</script>
