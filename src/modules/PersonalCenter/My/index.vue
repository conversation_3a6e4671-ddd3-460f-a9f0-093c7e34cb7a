<template>
  <div class="container">
    <div class="content">
      <div class="user-profile">
        <header>
          <fm-image
            v-if="imageUrl"
            :src="imageUrl ? imageUrl : require('@/assets/img/avatar-person.png')"
            fit="cover"
            round
            height="80"
            width="80"
          />
          <div v-else class="avatar avatar-text">
            {{ userFullName | getFamilyName }}
          </div>
          <div class="information">
            <div class="person_sty" @click="goToSettings">
              <span>{{ userFullName }}</span>
              <i style="margin-left: 10px" class="fm-icon fm-icon-edit"></i>
            </div>
            <div class="versions_sty">V{{verison}}</div>
          </div>
        </header>
      </div>
      <div class="infos">
        <div class="title">基本信息</div>
        <div
          v-for="(item, index) in infosList"
          :key="index"
          class="info"
        >
          <div class="info-item">
            <img :src="require(`@/assets/img/car/${item.icon}.png`)" class="">
            <span>{{ item.name }}：</span>
          </div>
          <div class="info-value">{{ getValue(item.key) }}</div>
        </div>
        <fm-divider/>
        <!-- <div class="title">我的二维码</div>
        <qrcode :size="130" :value="qrCodeText"/> -->
        <!--        <div class="logout">-->
        <!--          <fm-button type="primary" block color="#ffffff" size="large" @click="logout">退出登录</fm-button>-->
        <!--        </div>-->
      </div>
    </div>
    <main-tabbar v-if="isMain"></main-tabbar>
    <tabbar v-else></tabbar>
  </div>
</template>
<script>
import {
  Button,
  Cell,
  CellGroup,
  Dialog,
  Divider,
  Image,
  NavBar,
  Overlay,
  Toast
} from 'fawkes-mobile-lib'
import * as StateTypes from '@/store/State/stateTypes'
import {STATUS_BAR_HEIGHT} from '@/store/State/stateTypes'
import {loginOut} from '@/api/app'
import {eventBackButton, exitApp} from '@/utils/app'
import {IS_LOGIN} from '@/store/Mutation/mutationTypes'
import MainTabbar from '@/modules/Tabbar/index'
import tabbar from '@/modules/CarApply/components/TabBar.vue'
import {mapGetters, mapState} from "vuex";
import * as GetterTypes from "@store/Getter/getterTypes";

const infosList = [
  {name: '手机', icon: 'icon_phone', key: 'phone'},
  // {name: '邮箱', icon: 'icon_emil', key: 'email'},
  {name: '公司', icon: 'icon_corp', key: 'userComp'},
  {name: '部门', icon: 'icon_dep', key: 'userDept'}
]
export default {
  name: 'PersonalCenter',
  components: {
    tabbar,
    MainTabbar,
    [CellGroup.name]: CellGroup,
    [Divider.name]: Divider,
    [Cell.name]: Cell,
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Overlay.name]: Overlay,
    [Image.name]: Image,
    [Toast.name]: Toast,
  },
  data() {
    return {
      infosList,
      disableFinger: true,
      enableFinger: false,
      userFullName: '',
      userDept: '',
      userComp: '',
      phone: '',
      show: false,
      showPic: false,
      email: '',
      imageUrl: '',
      // qrcode: "https://fawkes.cybereng.com/",
      loading: false,
      isBack: false,
      isMain: true,
      verison: ''
    }
  },
  filters: {
    getFamilyName(value) {
      return value?.substr(0, 1)
    },

  },
  computed: {
    ...mapGetters([GetterTypes.IS_NOT_PC]),
    ...mapState('CarApply', ['currUser']),
    ...mapState([StateTypes.USER_INFO, StateTypes.APPLY_RESOURCE]),
    statusBarHeight() {
      return this.$store.state[STATUS_BAR_HEIGHT]
    },
    // 可微信扫描查看具体展示的信息
    qrCodeText() {
      let QRText = `BEGIN:VCARD"
      FN: ${this.currUser.userFullName}
      TEL: ${this.currUser.phone}
      ADR;WORK:浙江省杭州市余杭区高教路201号
      EMAIL;INTERNET,HOME: ${this.currUser.email}
      ORG: ${this.currUser.tenatName};
      END:VCARD`
      return QRText
    },
  },
  watch: {
    show(newVal) {
      // 处理overlay响应物理返回键
      if (newVal) {
        document.removeEventListener('backbutton', eventBackButton, false)
        document.removeEventListener('backbutton', exitApp, false)
        document.addEventListener('backbutton', this.closeOverlay, false)
      } else {
        document.removeEventListener('backbutton', this.closeOverlay, false)
        document.addEventListener('backbutton', eventBackButton, false)
      }
    },
  },
  beforeMount() {
  },
  created() {
    this.isMain = this.$storage.get('isMainTab') === 'true'
    this.isBack = this.$storage.get('isMainTab') === 'false' && !this[GetterTypes.IS_NOT_PC];
  },
  mounted() {
    // 页面激活时移除默认的返回上一级路由处理事件，设置退出应用事件
    document.removeEventListener('backbutton', this.$back, false)
    document.addEventListener('backbutton', eventBackButton, false)
    this.getUserInfo()
    this.fingerPermission()
    this.enableFinger = this.$storage.get('enableFinger') === 'true' ? true : false;
    const localVersion = localStorage.getItem('version')
    this.verison = localVersion;
  },
  deactivated() {
    // 页面隐藏时移除退出应用事件，恢复默认的返回上一级路由的处理事件
    document.removeEventListener('backbutton', eventBackButton, false)
    document.removeEventListener('backbutton', exitApp, false)
    document.addEventListener('backbutton', this.$back, false)
  },
  methods: {
    goToSettings() {
      this.$router.push({name: 'editUserInfo', params: {name: this.userFullName}})
    },
    getValue(key) {
      return this[key]
    },
    logout() {
      document.removeEventListener('backbutton', eventBackButton, false)
      document.removeEventListener('backbutton', exitApp, false)
      Dialog.alert({
        title: '确认退出登录？',
        theme: 'default',
        confirmButtonColor: '#4545D1',
        cancelButtonColor: '#353535',
        showCancelButton: true,
      })
        .then(() => {
          loginOut()
            .then((res) => {
              if (res.status) {
                this.userFullName = ''
                this.phone = ''
                this.email = ''
                this.userDept = ''
                this.userComp = ''
                // 清除keep-alive缓存
                // this.removeKeepAliveCache()
                this.$store.commit(IS_LOGIN, false)
                this.$storage.remove('access_token')
                this.$storage.remove('langList')
                this.$router.push('/login')
              }
            })
            .catch(() => {
            })
        })
        .catch(() => {
          // 移除dialog绑定的回退事件
          document.removeEventListener('backbutton', this.$back, false)
          document.addEventListener('backbutton', eventBackButton, false)
        })
    },
    saveFingerState(val) {
      if (!val) {
        this.$storage.set('enableFinger', val)
      } else {
        window.Fingerprint && Fingerprint.show(
          {
            clientId: 'fawkes', //Android: Used for encryption. iOS: used for dialogue if no `localizedReason` is given.
            clientSecret: 'fawkes_secret', //Necessary for Android encrpytion of keys. Use random secret key.
            title: '指纹登录验证',
            disableBackup: true,
            cancelButtonTitle: '取消',
          },
          () => {
            this.$storage.set('enableFinger', val)
          },
          (err) => {
            this.enableFinger = false
          }
        )
      }
    },
    fingerPermission() {
      window.Fingerprint && Fingerprint.isAvailable(
        (result) => {
          this.disableFinger = false
        },
        (message) => {
          this.disableFinger = true
        }
      )
    },
    getUserInfo() {
      this.userFullName = this.currUser.userFullName;
      this.phone = this.currUser.phone
      this.email = this.currUser.email || '未绑定'
      this.userDept = this.currUser.deptName || '暂无归属'
      this.userComp = this.currUser.tenantName || '暂无归属'
      if (this[StateTypes.APPLY_RESOURCE] === 1) {
        this.imageUrl = this.currUser.avatarOrigin
      } else {
        this.imageUrl = this[StateTypes.USER_INFO].sysThirdParty ? (this[StateTypes.USER_INFO].sysThirdParty.avatar || this.currUser.avatarOrigin) : this.currUser.avatarOrigin
      }
    },
    gotoChangePass() {
      this.$router.push('/change')
    },
    closeOverlay() {
      this.show = false
    },
    removeKeepAliveCache() {
      if (this.$vnode.parent) {
        let keys = this.$vnode.parent.componentInstance.keys
        let l = keys.length
        for (let i = 0; i < l; i++) {
          this.removeKeepAliveCacheForVueInstance(keys[0], keys)
        }
      }
    },
    removeKeepAliveCacheForVueInstance(item, keys) {
      let key = item
      if (this.$vnode.parent) {
        let cache = this.$vnode.parent.componentInstance.cache
        if (cache[key]) {
          cache[key].componentInstance.$destroy()
          delete cache[key]
          let index = keys.indexOf(key)
          if (index > -1) {
            keys.splice(index, 1)
          }
        }
      }
    },
    showAvatar() {
      if (this.imageUrl) {
        this.showPic = true
      } else {
        this.$toast('未上传头像，无法查看！')
      }
    },
  },
}
</script>
<style lang="less" scoped>
.container {
  height: 100%;

  .content {
    height: calc(100% - 100px);

    .user-profile {
      background-image: url("~@/assets/img/my/profile-bg.png");
      background-size: cover;
      padding-left: 30px;
      padding-top: 24px;
      padding-bottom: 24px;

      .avatar {
        width: 164px;
        height: 164px;
        border: 2px solid #fff;
        border-radius: 50%;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.12);
      }

      .avatar-text {
        line-height: 164px;
        text-align: center;
        font-size: 82px;
        color: #fff;
        background-color: #4545d1;
      }

      header {
        display: flex;
        align-items: center;

        .information {
          margin-left: 30px;
          font-size: 32px;
          font-weight: bold;
        }
      }
    }

    .infos {
      margin: 36px 60px;

      .title {
        font-size: 32px;
        font-weight: bold;
        color: #191919;
        margin-bottom: 30px;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        .info-item {
          display: flex;
          align-items: center;

          span {
            display: inline-block;
            margin-left: 10px;
            letter-spacing: 2px;
            font-size: 28px;
            color: rgba(25, 25, 25, 0.6);
          }
        }

        .info-value {
          font-size: 28px;
          color: #191919;
          letter-spacing: 2px;
        }
      }
    }
  }
}


.versions_sty {
  text-align: center;
  color: #fff;
  margin-top: 20px;
  font-size: 20px;
  padding: 10px 15px;
  background: linear-gradient(129deg, #F0CFA0 0%, #C39156 100%);
  border-radius: 200px 200px 200px 0;
}
</style>
