<template>
  <fks-drawer
    :visible.sync="drawerVisible"
    :modal="false"
    direction="rtl"
    size="700px"
    @close="$emit('close', refresh)"
  >
    <template slot="title">
      <common-title :title="formName"/>
    </template>
    <fks-tabs v-model="activeName">
        <fks-tab-pane label="表单信息" name="form">
          <component
            class="drawer-form"
            :is="dynamicForm"
            ref="formTemp"
            :bizId="bizId"
            :taskKey="taskKey"
            :type="type"
            :currentButtonKey="currentButtonKey"
            v-bind="$attrs"
            viewType="drawer"
            @setEntityName="setEntityName"
            @setFormName="setFormName"
            @setModelKey="setModelKey"
            @setPreSaveValidateProps="setPreSaveValidateProps"
            @setService="setService"
            @setSubmitText="setSubmitText"
          />
          <approve-buttons
            class="drawer-buttons"
            v-if="this.type !== 'view'"
            :button-loading="buttonLoading"
            :taskKey="taskKey"
            :type="type"
            :bizId="bizId"
            :submit-text="submitText"
            :currentButtonKey="currentButtonKey"
            viewType="drawer"
            @onAction="onAction"
          />
        </fks-tab-pane>
        <fks-tab-pane label="流程信息" name="flow">
          <fks-flow-comments
            class="drawer-flow"
            v-if="!noAuthPage"
            :data="comments"
            :data-serise="commentSeries"
            :flow-active="type !== 'add'"
            :flow-info="flowInfo"
          />
        </fks-tab-pane>
      </fks-tabs>
  </fks-drawer>
</template>

<script>
import mixins from './mixins';
import {UserTask_0} from "@utils/constants";
import ApproveButtons from "@modules/FormCenter/components/ApproveButtons/index.vue";

export default {
  name: 'FormCenterDrawerView',
  components: {ApproveButtons},
  mixins: [mixins],
  data() {
    return {
      drawerVisible: false,
      activeName: 'form',
      refresh: false // 表单审批后需要重新刷新表格，如果只是查看则不刷新
    }
  },
  computed: {
    isApply() {
      return this.taskKey === UserTask_0;
    }
  }
}
</script>


<style lang='less' scoped>
::v-deep .no-flow-chart {
  *[aria-controls*='pane-flow'] {
    display: none;
  }
}

/deep/ .fks-tabs.fks-tabs--top {
  display: flex;
  flex-direction: column;
  height: 100%;

  .fks-tab-pane {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;

    .drawer-form {
      //flex-grow: 1;
      //overflow-y: auto;
    }

    .drawer-flow {
      padding-top: 0;
    }
  }
}
</style>
