<template>
  <pc-view :notify="notify" v-if="isPC" :params.sync="params"/>
  <mobile-view v-else :params.sync="params"/>
</template>

<script>
import platform from "@/mixins/platform";
import PcView from "./pc-view.vue";
import MobileView from "./mobile-view.vue";

export default {
  name: "FormCenterIndex",
  mixins: [platform],
  components: {PcView, MobileView},
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    notify: {
      type: Boolean,
      default: false
    }
  }
}
</script>
