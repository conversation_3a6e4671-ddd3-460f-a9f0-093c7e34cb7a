/*
 * @Author: gao_m3
 * @Date: 2022-07-14 16:51:18
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-07-28 14:23:21
 * @Descripttion:
 */
import request from '@/utils/request'

// 表单提交通用接口
export function submitForm (url, data) {
  return request({
    url: url,
    method: 'post',
    data,
  })
}

// 表单初始化通用接口
export function getFormData (url, data, method) {
  return method === 'post' ? request({
    url: url,
    method: 'post',
    data: data
  }) : request({
    url: url,
    method: 'get',
    params: data
  })
}
export function getApprovalList (params) {
  return request({
    url: 'sys-bpm/process/history',
    method: 'get',
    params,
  })
}

/**
 * @description: 查询任务待办详情
 */
export function getUserTaskDetail(taskId) {
  return request({
    url: '/sys-bpm/userTask',
    method: 'get',
    params: {
      taskId: taskId
    }
  })
}

/**
 * @description: 获取流程评论
 * @param {data}
 */
export function getProcessHistory(params) {
  return request({
    method: 'get',
    url: '/sys-bpm/process/history',
    params: params
  })
}

/**
 * @description: 获取流程进行时的流程信息，用于展示流程图
 */
export function getHistoryNodes(params) {
  return request({
    method: 'get',
    url: '/sys-bpm/process/history/instance',
    params: params
  })
}

/**
 * @description: 获取流程图xml
 */
export function getModelXML(data) {
  return request({
    method: 'get',
    url: '/sys-bpm/model',
    params: {
      id: data.id,
      modelKey: data.modelKey
    }
  })
}

export function processAbandon (data) {
  return request({
    method: 'delete',
    url: '/sys-bpm/process/',
    params: {
      taskId: data.taskId,
      comment: data.comment || '',
      fileToken: data.fileToken,
    },
  })
}

// 根据文件token和grouptoken获取文件信息,data中传至少传g9s数组和f8s数组中的一个
export function getFile(data) {
  return request({
    url: '/sys-storage/file',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
}

// 根据文件token下载文件
export function downloadFile(f8s) {
  return request({
    url: '/sys-storage/download',
    method: 'get',
    params: {
      f8s
    },
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded'
    // },
    responseType: 'blob'
  });
}


// 根据文件token和grouptoken删除文件，token删除单文件，grouptoken删除文件组
export function deleteFile(data) {
  return request({
    url: '/sys-storage/file',
    method: 'delete',
    data
  });
}
