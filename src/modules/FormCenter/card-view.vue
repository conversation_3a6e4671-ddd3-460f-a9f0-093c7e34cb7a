<template>
<div class="flex flex-column" style="overflow: hidden;">
  <component
    style="overflow-x: hidden"
    class="flex-grow-1 overflow-y-auto"
    :is="dynamicForm"
    ref="formTemp"
    :bizId="bizId"
    :taskKey="taskKey"
    :type="type"
    :currentButtonKey="currentButtonKey"
    v-bind="$attrs"
    viewType="drawer"
    @setEntityName="setEntityName"
    @setFormName="setFormName"
    @setModelKey="setModelKey"
    @setPreSaveValidateProps="setPreSaveValidateProps"
    @setService="setService"
    @setSubmitText="setSubmitText"
    @setFormData="submitFormData"
    @setReturnNodeList="setReturnNodeList"
    @init-success="showApproveBtn = true"
    :isCrossNodeReturn="isCrossNodeReturn"
  />
  <approve-buttons
    v-if="this.type !== 'view' && showApproveBtn"
    :button-loading="buttonLoading"
    :taskKey="taskKey"
    :type="type"
    :bizId="bizId"
    :submit-text="submitText"
    :currentButtonKey="currentButtonKey"
    :disable-submit="disabledSubmit"
    :returnNodeList="returnNodeList"
    viewType="drawer"
    @onAction="onAction"
    @revocation="$emit('revocation')"
    @refresh="$emit('refresh', $event)"
    @modify="$emit('modify', $event)"
    @getButtonList="getButtonList"
  />
</div>
</template>

<script>
import mixins from './mixins';
import {UserTask_300} from "@utils/constants";
import ApproveButtons from "@modules/FormCenter/components/ApproveButtons/index.vue";
import FlowTable from "@components/FlowTable/index.vue";
export default {
  name: "card-view",
  components: {FlowTable, ApproveButtons},
  mixins: [mixins],
  provide() {
    return {
      appendButtons: this.appendButtons
    }
  },
  data() {
    return {
      disabledSubmit: false,
      // 当前表单能返回的流程节点列表
      returnNodeList: [],
    }
  },
  computed: {
    appendButtons() {
      return this.params.buttonsSmall || [];
    },
  },
  methods: {
    getButtonList(data) {
      this.formBtnList = data;
    },
    setReturnNodeList(list) {
      this.returnNodeList = list;
    },
    submitFormData(payload) {
      this.$emit('setFormData', payload)
      if (this.$refs.formTemp) {
        if (this.taskKey === UserTask_300 && this.type === 'execute') {
          // 当处于司机确认流程中的时候，如果提交人和司机不是同一个人，则不能显示提交按钮（确认行程开始）
          // this.disabledSubmit = this.$refs.formTemp.formData.task0UserName !== this.$refs.formTemp.vdDriverInfo.driverUserName;
        }
      }
    },
  }
}
</script>
