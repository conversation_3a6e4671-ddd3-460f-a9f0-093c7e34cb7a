<template>
  <div class="flex column full-height" :class="{'bg-white': !isApply && !noAuthPage}">
    <header v-if="!isApply && !noAuthPage" class="card-header application-buttons">
      <div class="flex col-center" style="padding-top: 10px">
        <div class="card-title">{{ formName }}</div>
      </div>
    </header>
    <div class="flex-grow-1 bg-white" style="overflow: hidden">
      <component
        class="full-height form-component"
        :is="dynamicForm"
        ref="formTemp"
        :bizId="bizId"
        :taskKey="taskKey"
        :type="type"
        v-bind="$attrs"
        @setEntityName="setEntityName"
        @setFormName="setFormName"
        @setModelKey="setModelKey"
        @setPreSaveValidateProps="setPreSaveValidateProps"
        @setService="setService"
        @setSubmitText="setSubmitText"
        @setReturnNodeList="setReturnNodeList"
        @init-success="showApproveBtn = true"
        :isCrossNodeReturn="isCrossNodeReturn"
      />
    </div>
    <div class="bg-white full-width">
      <approve-buttons
        class="application-buttons"
        v-if="this.type !== 'view' && showApproveBtn"
        :button-loading="buttonLoading"
        :taskKey="taskKey"
        :type="type"
        :bizId="bizId"
        :submit-text="submitText"
        :returnNodeList="returnNodeList"
        @onAction="onAction"
        @getButtonList="getButtonList"
      />
    </div>
  </div>
</template>
<script>
import mixins from './mixins'
import { UserTask_0 } from '@utils/constants'
import ApproveButtons from '@modules/FormCenter/components/ApproveButtons/index.vue'
import FlowTable from '@components/FlowTable/index.vue'
import CardTag from '@components/CardFlow/components/tag.vue'

export default {
  name: 'FormCenterPcView',
  components: { CardTag, FlowTable, ApproveButtons },
  mixins: [mixins],
  data() {
    return {
      returnNodeList: [],
    }
  },
  computed: {
    isApply() {
      return this.taskKey === UserTask_0
    },
  },
  methods: {
    getButtonList(data) {
      this.formBtnList = data
    },
    setReturnNodeList(list) {
      this.returnNodeList = list
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@/styles/headers';

::v-deep .no-flow-chart {
  *[aria-controls*='pane-flow'] {
    display: none;
  }
}

.form-component {
  max-width: 1400px;
  margin: 0 auto;
}

.application-buttons {
  min-width: 1400px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 10px 0;
}
</style>
