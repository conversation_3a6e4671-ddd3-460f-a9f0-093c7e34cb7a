<template>
  <fm-popup
    :close-on-click-modal="false"
    get-container="#app"
    position="bottom"
    @open="selectProjectOpenHandler"
    :visible.sync="show">
    <fm-search v-if="isShowSearch" v-model="searchValue"  placeholder="请输入关键词"  @search="onSearch" class="select-search" />
    <fm-picker
      title=""
      show-toolbar
      confirm-button-text="完成"
      :columns="columns"
      :value-key="valueKey"
      @confirm="confirm"
      @cancel="cancel"
    />
  </fm-popup>
</template>

<script>
import { mapActions, mapState, mapMutations } from 'vuex';
import {
  Popup,
  Picker,
  Search
} from 'fawkes-mobile-lib';
export default {
  name: 'SelectPicker',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    name: {
      type: String,
      default: 'UseCarTypeEnums'
    },
    code: {
      type: [String, Number, Boolean],
      default: ''
    },
    text: {
      type: String,
      default: ''
    },
    valueCode: {
      type: String,
      default: 'code'
    },
    isEnum: {
      type: Boolean,
      default: true
    },
    column: {
      type: Array,
      default() {
        return []
      }
    },
    isShowSearch: {
      type: Boolean,
      default: false
    }
  },
  components: {
    [Popup.name]: Popup,
    [Picker.name]: Picker
  },
  data() {
    return {
      searchValue: ''
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    columns() {
      return this.isEnum ? this.enums[this.name]??[] : this.column;
    }
  },
  watch: {
    searchValue(newValue) {
      this.$emit('update:search', newValue);
    },
    show(newVal) {
      if (newVal) {
        window.addEventListener('popstate', this.preventBack);
      } else {
        window.removeEventListener('popstate', this.preventBack);
      }
    },
  },
  methods: {
    preventBack(e) {
      if (this.show) {
        e.preventDefault(); // 阻止返回
        this.$emit('update:show', false); // 触发父组件更新
      }
    },
    // popup打开
    selectProjectOpenHandler () {
      window.history.pushState(null, null, location.href)
    },
    confirm(val) {
      this.$emit('update:text', this.isEnum ? val.value : val[this.valueKey]);
      this.$emit('update:code', this.isEnum ?  val.key : val[this.valueCode]);
      this.$emit('confirm',this.isEnum ?  val.key : val[this.valueCode]);
      this.$emit('choose', val);
      this.cancel();
    },
    cancel() {
      this.$emit('update:show', false)
      this.$emit('cancel');
    },
    onSearch() {
      this.$emit('update:search', this.searchValue);
    }
  },
  beforeDestroy() {
    window.removeEventListener('popstate', this.preventBack);
  },
}
</script>

<style scoped lang="less">
.select-search {
  position: absolute;
  width: 100%;
  top: 70px;
  z-index: 9999;
  .fm-search__content {
    border-radius: 12px;
    height: 65px;
    border: 1px solid #DDDDDD;
    background: #FFFFFF;
  }
  .fm-cell {
    display: flex;
    flex-direction: row; /* 默认从左到右排列 */
    flex-direction: row-reverse; /* 反转排列 */
  }
}
/deep/ .fm-field__clear {
  margin-top: 20px;
}
/deep/ .fm-icon-search {
  width: 42px;
  height: 42px;
}
/deep/ .fm-search .fm-icon {
  margin-right: 5px;
}
</style>
