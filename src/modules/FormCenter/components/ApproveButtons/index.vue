<template>
  <component
    :is="getComponent"
    :button-loading.sync="buttonLoading"
    :task-key.sync="taskKey"
    :type.sync="type"
    :bizId="bizId"
    :submit-text="submitText"
    :getComment="getComment"
    :getReturnNode="getReturnNode"
    :returnNodeList="returnNodeList"
    :viewType="viewType"
    :currentButtonKey="currentButtonKey"
    :disableSubmit="disableSubmit"
    @onAction="$emit('onAction', $event)"
    @getButtonList="$emit('getButtonList', $event)"
    @refresh="$emit('refresh')"
    @revocation="$emit('revocation')"
    @modify="$emit('modify', $event)"
  />
</template>

<script>
import platform from "@/mixins/platform";
import PcView from "./pc-view.vue";
import MobileView from "./mobile-view.vue";

export default {
  name: "ApproveButtons",
  mixins: [platform],
  components: {PcView, MobileView},
  props: ['buttonLoading', 'taskKey', 'type', 'submitText', 'getComment', 'getReturnNode', 'returnNodeList', 'bizId', 'viewType', 'currentButtonKey', 'disableSubmit'],
  computed: {
    getComponent() {
      return this.isPC ? PcView : MobileView
    }
  }
}
</script>
