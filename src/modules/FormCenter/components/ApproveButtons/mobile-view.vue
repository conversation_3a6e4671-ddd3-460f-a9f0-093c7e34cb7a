<template>
  <div class="buttons bg-white" :style="leftPosition" style="align-items: center">
    <fm-button
      v-if="isFeeApprove && buttonList.length > 0"
      :loading="buttonLoading"
      :disabled="buttonLoading"
      type="primary"
      plain
      style="width: calc(50% - 10px);margin-right: 10px;"
      @click="handleFeeSaveClick"
    >
      暂存
    </fm-button>
    <div v-for="(item, i) in buttonList" :key="item.type" class="button">
      <fm-button
        :class="[item.type, i > 0 ? 'm-l-30' : '']"
        :loading="buttonLoading"
        :disabled="buttonLoading"
        block
        native-type="submit"
        :type="buttonType(item)"
        @click="$emit('onAction', {...item, comment: getComment(), returnNode: getReturnNode()})"
      >
        {{ item.code === 'SUBMIT' ? !!$route.query.buttonKey ? item.text : (submitText || item.text)  : item.text }}
      </fm-button >
    </div>
  </div>
</template>
<script>
import mixins from './mixins';
import ApproveButtons from '@modules/FormCenter/components/ApproveButtons/index.vue'
export default {
  name: 'ApproveButtonsMobileView',
  components: { ApproveButtons },
  mixins: [mixins],
  computed: {
    leftPosition() {
      return {left: 0, right: 0, position: 'fixed'};
    }
  },
  methods: {
    buttonType(item) {
      switch (item.type) {
        case 'submit':
          return 'primary'
        case 'abandon':
          return 'danger'
        case 'reject':
          return 'danger'
        default:
          return 'default'
      }
    },
  }
}
</script>


<style scoped lang='less'>
@import "./mobile-view";
</style>
