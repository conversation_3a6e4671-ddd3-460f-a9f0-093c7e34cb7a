<template>
  <div class="footer-buttons flex col-center row-between">
    <div class="button flex">
      <fks-popconfirm
        v-if="
          appendButtons.findIndex((btn) => btn.buttonKey === 'REVOCATION') > -1 && !currentButtonKey
        "
        style="margin-right: 10px"
        title="确认撤销？"
        @onConfirm="$emit('revocation')"
      >
        <fks-button
          slot="reference"
          :disabled="btnDisabled"
          :loading="buttonLoading"
          icon="fks-icon-close"
          plain
          style="color: #ff3b30 !important"
          text
          class="dangerButton"
        >
          撤销
        </fks-button>
      </fks-popconfirm>
      <template v-for="item in buttonList">
        <fks-popover
          placement="top"
          width="400"
          trigger="click"
          popper-class="reject-popover"
          v-model="rejectPopVisible"
          v-if="item.type === 'reject' && !currentButtonKey"
        >
          <div style="max-width: 400px;">
            <fks-form :model="form" :rules="rules" ref="rejectForm" label-width="72px" :label-position="'top'">
              <!-- 退回给 -->
              <fks-form-item label="退回节点" prop="returnNode">
                <div class="form-tip" style="font-size: 12px; color: #909399; margin-bottom: 4px; padding-left: 8px; line-height: 18px">
                  被勾选人处理后，将会提交给你继续审批
                </div>
                <fks-select
                  v-model="form.returnNode"
                  placeholder="请选择"
                  class="full-width"
                  popper-class="node-select-dropdown"
                >
                  <fks-option
                    v-for="item in returnNodeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                    <div class="node-option">
                      <div class="node-title">{{ item.label }}</div>
                      <div class="node-sub">{{ item.userName }}</div>
                    </div>
                  </fks-option>
                </fks-select>
              </fks-form-item>

              <!-- 退回理由 -->
              <fks-form-item label="退回理由" prop="reason">
                <fks-input
                  type="textarea"
                  v-model="form.reason"
                  :maxlength="200"
                  :rows="4"
                  placeholder="请输入您的退回原因"
                  show-word-limit
                />
              </fks-form-item>
            </fks-form>

            <!-- 按钮组 -->
            <div class="flex row-end" style="margin-top: 12px;" v-if="rejectPopVisible">
              <fks-button class="reject-btn btn-cancel" @click="rejectPopVisible = false" >取消</fks-button>
              <fks-button class="reject-btn btn-confirm" type="primary" @click="submitReject(item)">
                退回
              </fks-button>
            </div>
          </div>

          <!-- 触发器插槽（可自定义） -->
          <slot name="reference">
            <fks-button
              slot="reference"
              :disabled="btnDisabled"
              :icon="item.icon"
              :loading="buttonLoading"
              plain
              style="color: #ff3b30 !important"
              text
              class="dangerButton"
            >
              {{ item.text }}
            </fks-button>
          </slot>
        </fks-popover>
        <fks-popconfirm
          v-else-if="!currentButtonKey"
          :key="item.type"
          style="margin-right: 10px"
          title="是否确认？"
          @onConfirm="$emit('onAction', { ...item, comment })"
        >
          <fks-button
            text
            class="second-text"
            slot="reference"
            :key="item.type"
            :class="{ dangerButton: item.type === 'abandon', [item.type]: true }"
            :disabled="btnDisabled"
            :icon="item.icon"
            :loading="buttonLoading"
            :type="item.type === 'submit' ? 'primary' : 'plain'"
            :style="{ color: item.type === 'abandon' ? '#ff3b30 !important' : '#333' }"
          >
            <!--      提交按钮文字自定义展示-->
            {{
              item.code === 'SUBMIT'
                ? !!currentButtonKey
                  ? item.text
                  : submitText || item.text
                : item.text
            }}
            <!--          {{ item.code === "SUBMIT" ? submitText || item.text : item.text }}-->
          </fks-button>
        </fks-popconfirm>
      </template>
      <fks-popconfirm v-if="currentButtonKey" title="确认取消？" @onConfirm="handleCancel">
        <fks-button
          slot="reference"
          text
          :disabled="btnDisabled"
          :loading="buttonLoading"
          icon="fks-icon-close"
          >取消
        </fks-button>
      </fks-popconfirm>
      <fks-button
        v-if="currentButtonKey"
        icon="fks-icon-check"
        :disabled="btnDisabled"
        :loading="buttonLoading"
        text
        @click="handleConfirm"
        >确认
      </fks-button>
      <fks-button
        v-if="isFeeApprove"
        :loading="buttonLoading"
        :disabled="btnDisabled"
        icon="fks-icon-folder"
        text
        class="second-text"
        @click="handleFeeSaveClick"
      >
        暂存
      </fks-button>
      <template v-for="btn in appendButtons.filter((item) => item.buttonKey !== 'REVOCATION')">
        <fks-button
          class="second-text"
          v-if="!currentButtonKey"
          :key="btn.buttonKey"
          :icon="btn.buttonKey === 'XC_FORM_MODIFY' ? 'fks-icon-browse' : 'fks-icon-user'"
          text
          :disabled="btnDisabled"
          :loading="buttonLoading"
          @click="handleBtnClick(btn)"
        >
          {{ btn.buttonValue }}
        </fks-button>
      </template>
    </div>
    <i
      v-if="currentButtonKey"
      class="fks-icon-info-outline cursor-pointer"
      style="font-size: 20px;color: #999999;padding-right: 10px"
      @click="handleTipClick"
    />
  </div>
</template>
<script>
import mixins from './mixins'
import { changeTips, UserTask_0 } from '@utils/constants'
import { mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'

export default {
  name: 'ApproveButtonsPcView',
  inject: ['appendButtons'],
  mixins: [mixins],
  data() {
    return {
      form: {
        returnNode: '',
        reason: ''
      },
      rejectPopVisible: false,
      rules: {
        returnNode: [
          { required: true, message: '请选择退回节点', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请选择退回原因', trigger: 'change' },
          { max: 200, message: '最多200字', trigger: 'blur' }
        ]
      },
    }
  },
  watch: {
    returnNodeList(newList) {
      if (Array.isArray(newList) && newList.length > 0) {
        this.form.returnNode = newList[newList.length - 1].value;
      } else {
        this.form.returnNode = '';
      }
    }
  },
  methods: {
    submitReject(item) {
      const form = Array.isArray(this.$refs.rejectForm)
        ? this.$refs.rejectForm[0]
        : this.$refs.rejectForm;

      form?.validate?.().then(() => {
        // 校验通过后提交
        this.$emit('onAction', {
          ...item,
          comment: this.form.reason,
          returnNode: this.form.returnNode
        });
      });
    },
    handleTipClick() {
      const content = changeTips[this.currentButtonKey];
      this.$alert(content, '提示');
    },
    handleBtnClick(btn) {
      this.$emit('modify', btn.buttonKey);
    },
    handleCancel() {
      this.$emit('modify')
    },
    handleConfirm() {
      const item = this.buttonList.find((item) => item.type === 'submit')
      this.$emit('onAction', item)
    },
  },
  computed: {
    ...mapState([StateTypes.IS_PROJECT_CLOSED]),
    btnDisabled() {
      return this.buttonLoading || this.disableSubmit || this[StateTypes.IS_PROJECT_CLOSED]
    },
    // 选择行程信息变更
    isXcModify() {
      return this.$route.query && this.currentButtonKey === 'XC_FORM_MODIFY'
    },
    // 选择车辆/司机变更
    isXcDriverCarModify() {
      return this.currentButtonKey === 'XC_DRIVER_CAR_MODIFY'
    },
    // 租车公司变更
    isCarModify() {
      return this.currentButtonKey === 'XC_CAR_COMP_MODIFY'
    },
    // 修改
    isModifyAll() {
      return this.currentButtonKey === 'FORM_MODIFY_ADMIN'
    },
    isModify() {
      return this.isXcModify || this.isXcDriverCarModify || this.isCarModify || this.isModifyAll
    },
    disableApproveInput() {
      return this.isModify || this.taskKey === UserTask_0
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@/styles/button';

.btn-cancel:hover {
  background: #FFFFFF !important;
}

.btn-confirm:hover {
  background: #3C83FF !important;
}
</style>

<style lang="less">
@import "./exclude/pc-view.css";
</style>
