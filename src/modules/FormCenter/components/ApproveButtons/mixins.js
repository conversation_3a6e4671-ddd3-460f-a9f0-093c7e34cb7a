import {UserTask_400, UserTask_300} from "@utils/constants";
import {getApprovalList, getButtonInfo} from "@modules/FormCenter/components/FlowApproval/api";

export default {
  props: ['buttonLoading', 'taskKey', 'type', 'submitText', 'getComment', 'getReturnNode', 'returnNodeList', 'bizId', 'viewType', 'currentButtonKey', 'disableSubmit'],
  data() {
    return {
      comment: '', // 审批意见
      buttonList: []
    }
  },
  computed: {
    approvalAdvice() {
      // 如果在司机确认流程
      return this.taskKey === UserTask_300 ? '请输入审批意见，如需退回请在此输入退回原因' : '请输入审批意见'
    },
    // 当前表单是否在抽屉框里打开
    isDrawer() {
      return this.viewType === 'drawer'
    },
    // 行车日志填报流程
    isFeeApprove() {
      return this.taskKey === UserTask_400 && this.type === 'execute' && !this.currentButtonKey;
    }
  },
  methods: {
    handleFeeSaveClick() {
      this.$emit('onAction', {type: 'feeSave'});
    },
    sortIcon(item) {
      switch (item.type) {
        case 'submit':
          return 1
        case 'saveDraft':
          return 2
        case 'reject':
          return 3
        case 'abandon':
          return 4
        case 'addCountersignee':
          return 5
        case 'edit':
          return 6
        case 'circulate':
          return 7
        default:
          return 8
      }
    },
    buttonIcon(item) {
      switch (item.customCode) {
        case 'SAVEDRAFT':
          return 'fks-icon-hold'
        case 'SUBMIT':
          return 'fks-icon-check'
        case 'ABANDON':
          return 'fks-icon-reject'
        case 'REJECT':
          return 'fks-icon-reject'
        default:
          return ''
      }
    },
  },
  async created() {
    if (!!this.currentButtonKey && this.$route.params.formKey === 'vehicleDispatch') {
      this.buttonList = [
        {
          type: 'submit',
          text: this.submitText,
          code: 'SUBMIT',
          customCode: 'SUBMIT',
          icon: 'fks-icon-check'
        }
      ];
      this.$emit('getButtonList', this.buttonList);
      return false;
    }
    if (this.type === 'add') {
      this.buttonList = [
        // {
        //   type: 'saveDraft',
        //   text: '暂存',
        //   code: 'SAVEDRAFT',
        //   customCode: 'SAVEDRAFT',
        //   icon: 'fks-icon-hold'
        // },
        {
          type: 'submit',
          text: '提交',
          code: 'SUBMIT',
          customCode: 'SUBMIT',
          icon: 'fks-icon-check'
        },
      ]
      this.$emit('getButtonList', this.buttonList);
      return false;
    }
    if (this.type === 'execute') {
      const res = await getApprovalList({bizId: this.$route.params.bizId || this.bizId})
      const length = res.data ? res.data.length : -1;
      const row = {
        formKey: 'vehicleDispatch',
        taskKey: length > 0 ? res.data[length - 1].taskKey : '',
        taskId: length > 0 ? res.data[length - 1].taskId : '',
        bizId: length > 0 ? res.data[length - 1].formBizId : '',
        processInstanceId: length > 0 ? res.data[length - 1].processInstanceId : '',
      }
      const buttonRes = await getButtonInfo({taskId: row.taskId})
      const buttons = JSON.parse(buttonRes.data.buttonList).taskButtonList;
      this.buttonList = buttons
        .filter(item => item.code !== 'SAVEDRAFT')
        .sort((a, b) => this.sortIcon(b) - this.sortIcon(a))
        .map(item => ({...item, icon: this.buttonIcon(item)}))
      this.$emit('getButtonList', this.buttonList);
    }
  },
  // watch: {
  //   disableSubmit(newVal){
  //     if (newVal) {
  //       const index = this.buttonList.findIndex(btn => btn.type === 'submit');
  //       if (index > -1) {
  //         this.buttonList.splice(index, 1);
  //       }
  //     }
  //   }
  // }
}
