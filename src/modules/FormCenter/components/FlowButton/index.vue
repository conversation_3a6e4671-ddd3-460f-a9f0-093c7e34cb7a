<template>
  <div class="foot">
    <fm-button
      v-if="type === 'add'"
      class="flow-btn submit-btn"
      type="primary"
      :disabled="loading"
      :loading="loading"
      block
      @click="clickHandler('submit', submitText)"
    >{{ submitText }}</fm-button>
  </div>
</template>

<script>
export default {
  name: 'index',
  props: {
    type: {
      default: 'add', // add 添加 execute审核 edit 编辑  view查看
      type: String
    },
    submitText: {
      default: '提交', // add 添加 execute审核 edit 编辑  view查看
      type: String
    },
    submitCharacter: {
      default: '提交',
      type: String
    },
    onSubmit: {
      // 提交钩子
      type: Function
    },
  },
  data() {
    return {
      loading: false,
    }
  },
  methods: {
    clickHandler(type, text)  {
      if (this.onSubmit) {
        this.onSubmit();
        return false;
      };
    }
  }
}
</script>

<style lang="less" scoped>
@import './index';
</style>
