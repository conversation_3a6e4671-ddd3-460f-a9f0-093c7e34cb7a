<template>
  <div class="approve-textarea" v-if="isMobile" :style="{'margin-bottom': isShow ? '80px' : 0}">
    <fm-cell-group :gutter="0" label-width="100px">
      <fm-cell
        v-if="isShow"
        class="process-cell"
        title="当前环节"
        value-align="right"
      >
        <div class="black">{{ row.taskName }}</div>
      </fm-cell>

      <fm-cell
        title="退回节点"
        is-link
        :value="returnedNodeStr || '请选择退回节点'"
        value-class="cell-right-value"
        @click.native="showPickerDutyDirector = true"
      />

      <select-picker
        :code.sync="returnedNode"
        :column="localColumns"
        :is-enum="false"
        :show.sync="showPickerDutyDirector"
        :text.sync="returnedNodeStr"
        :value-code="'value'"
        :value-key="'label'"
        :is-show-search="true"
        @update:search="handleSearchUpdateMasterList"
      />
      <div v-if="isShow" class="approve-title" style="font-size: 14px; line-height: 18px">
        审批意见
      </div>
      <fm-field
        v-if="isShow"
        v-model="comment"
        :maxlength="300"
        class="approve-textarea-entrust"
        :placeholder="approvalAdvice"
        show-word-limit
        rows="3"
        type="textarea"
      ></fm-field>
      <fm-dialog
          :visible.sync="show"
          show-cancel-button
          title="选择退回节点"
          @confirm="getNode()"
      >
        <div
            v-for="(item, index) in rejectList"
            :key="index"
            class="reject-node-item"
        >
          <div
              :class="[
              rejectTaskKey === item.taskKey ? 'hover' : '',
              'reject-node',
            ]"
              @click="getRejectNode(item)"
          >
            {{ item.taskName }}
          </div>
        </div>
      </fm-dialog>
      <select-picker
          :code.sync="returnReasonCode"
          :column="column"
          :is-enum="false"
          :show.sync="showPickerReturnReason"
          :text.sync="returnReasonText"
          :value-code="'rejectCauseCode'"
          :value-key="'rejectCause'"
          @confirm="confirmReturnReason"
      ></select-picker>
    </fm-cell-group>
    <div v-if="showBtn" :style="{marginTop: isMobile ? '35%' : 0, ...leftPosition}" class="buttons bg-white">
      <template v-for="(item, index) in buttonList">
        <fm-button
          v-if="(submitText !== '提交' && item.text !== '暂存') || (submitText === '提交' ? (['UserTask_5'].includes(taskKey) && row.formKey === 'vehicleDispatch' ?  item.text !== '暂存' : true) : ['UserTask_6'].includes(taskKey) && row.formKey === 'vehicleDispatch' ? item.text !== '暂存' : false)"
          :key="index"
          :disabled="isDisable"
          :loading="isDisable"
          :plain="item.type === 'abandon'"
          :type="buttonType(item)"
          class="btn"
          @click="clickHandler(item)"
        >
          {{ item.text === '提交' ? !!$route.query.buttonKey ? item.text : submitText : item.text }}
        </fm-button>
      </template>

    </div>
  </div>
  <div v-else>
    <fks-dialog :visible.sync="showPickerReturnReasonPC">
      <template slot="title">
        <div class="title">
          <i class="fks-icon-edit" />
          <span>选择退回原因</span>
        </div>
      </template>
      <fks-form>
        <fks-form-item label="原因">
          <fks-select style="margin: 0 auto" v-model="returnReasonCode" @change="returnReasonChange">
            <fks-option
                v-for="item in column"
                :key="item.rejectCauseCode"
                :label="item.rejectCause"
                :value="item.rejectCauseCode"
            />
          </fks-select>
        </fks-form-item>
        <fks-form-item>
          <fks-button type="primary" @click="confirmReturnReasonPC" icon="fks-icon-check">确定</fks-button>
        </fks-form-item>
      </fks-form>
    </fks-dialog>
    <fks-form style="padding: 0 22px" label-width="130px" label-position="right">
      <fks-form-item v-if="isShow" label="审批意见">
        <fks-input
            v-model="comment"
            :maxlength="300"
            :rows="5"
            :placeholder="approvalAdvice"
            show-word-limit
            type="textarea"
        />
      </fks-form-item>
      <fks-form-item class="special">
        <div class="flex row-center col-center">
          <template v-for="(item, index) in buttonList">
            <fks-popconfirm v-if="item.text === '退回'" title="确认回退？" @onConfirm="clickHandler(item)" style="margin-right: 10px">
              <fks-button plain slot="reference" :icon="item.icon" :loading="isDisable" :disabled="isDisable">{{item.text}}</fks-button>
            </fks-popconfirm>
            <fks-button
                v-else-if="(submitText !== '提交' && item.text !== '暂存') || (submitText === '提交' ? (['UserTask_5'].includes(taskKey) && row.formKey === 'vehicleDispatch' ?  item.text !== '暂存' : true) : ['UserTask_6'].includes(taskKey) && row.formKey === 'vehicleDispatch' ? item.text !== '暂存' : false)"
                :icon="item.icon"
                :key="index"
                :loading="isDisable"
                :disabled="isDisable"
                :plain="item.type === 'abandon'"
                :type="buttonType(item)"
                @click="clickHandler(item)"
            >
              {{ item.text === '提交' ? !!$route.query.buttonKey ? item.text : submitText : item.text }}
            </fks-button>
          </template>
        </div>
      </fks-form-item>
    </fks-form>
  </div>
</template>
<script>
import {CURRENT_ROW, REJECT_TASK_KEY} from '@/store/State/stateTypes'
import {SET_REJECT_TASK_KEY} from '@/store/Mutation/mutationTypes'
import {Button, Cell, CellGroup, Dialog, Field, Toast,} from 'fawkes-mobile-lib'
import SelectPicker from '@/modules/FormCenter/components/SelectPicker/index.vue';
import Blank from '@/components/Blank/index.vue';
import {backDlg, comfirm, getApprovalList, getButtonInfo, getTaskKey, processAbandon,} from './api'
import {mapActions, mapState} from 'vuex'
import platform from "@/mixins/platform";
import EventBus from "@utils/eventBus";

export default {
  name: 'FlowApproval',
  mixins: [platform],
  components: {
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Toast.name]: Toast,
    [Field.name]: Field,
    [Dialog.Component.name]: Dialog.Component,
    [Button.name]: Button,
    SelectPicker,
    Blank
  },
  props: {
    isShow: {
      type: Boolean,
      default: true
    },
    from: {
      type: Object,
      default() {
        return {}
      }
    },
    formName: {
      type: String,
      default: '',
    },
    submitURL: {
      type: String,
      default: '',
    },
    entityName: {
      type: String,
      default: '',
    },
    urlAndEntity: {
      type: Object,
      default() {
      },
    },
    formData: {
      type: Object,
      default() {
      },
    },
    detailParamList: {
      type: Array,
      default() {
        return []
      },
    },
    modelKey: {
      type: String,
      default() {
        return ''
      },
    },
    taskKey: {
      type: String,
      default: ''
    },
    bizId: {
      type: String,
      default: ''
    },
    beforeSubmit: {
      type: Function,
      default: function () {
      },
    },
    // 校验规则，默认不检查
    checkRules: {
      type: Function,
      default: function () {
        return true
      },
    },
    afterSubmit: {
      type: Function,
      default: function () {
      },
    },
    aftereReject: {
      type: Function,
      default: function () {
      },
    },
    isBeforeSubmit: {
      type: Boolean,
      default: false,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    getEntityParam: {
      type: Function,
      default: function () {
      },
    },
    submitText: {
      type: String,
      default: '提交'
    },
    showBtn: {
      type: Boolean,
      default: true
    },
    returnNodeList: {
      type: Array,
      default: [],
    }
  },
  data() {
    return {
      comment: '', // 审批意见
      returnedNode: "",
      returnedNodeStr: "",
      showPickerDutyDirector: false, // 是否显示往期流程节点
      localColumns: [],
      isDisable: false,
      buttonList: [],
      show: false,
      rejectList: [], //流程退回的节点列表
      currentNode: '',
      currentKey: '',
      stageFlag: 0,
      column: [
        {rejectCauseCode: '1', rejectCause: '无车辆'},
        {rejectCauseCode: '2', rejectCause: '无驾驶员'}
      ],
      showPickerReturnReason: false,
      showPickerReturnReasonPC: false,
      returnReasonText: '',
      returnReasonCode: '',
      type: this.$route.params.type,
      item: {},
      isSelectReason: false,
      row: {}
    }
  },
  computed: {
    // row() {
    //   // 页面中使用row来指代当前记录
    //   return this.$store.state[CURRENT_ROW]
    // },
    approvalAdvice(){
      const tasks = ['UserTask_0', 'UserTask_1', 'UserTask_2', 'UserTask_3']
      const canDrawback = tasks.findIndex(task => this.taskKey === task) > -1;
      return canDrawback ? '请输入审批意见，如需退回请在此输入退回原因' : '请输入审批意见'
    },
    rejectTaskKey() {
      return this.$store.state[REJECT_TASK_KEY]
    },
    leftPosition() {
      return this.isMobile ? {left: 0, right: 0, position: 'fixed'} : {margin: '0 30%'}
    },
    ...mapState('CarApply', ['lastRoute', 'currUser'])
  },
  watch: {
    returnNodeList: {
      immediate: true,
      handler(newVal) {
        this.localColumns = newVal.map(item => ({
          ...item,
          label: `${item.label}（${item.userName || ''}）`
        }));
      }
    }
  },
  async mounted() {
    if (this.$store.state[CURRENT_ROW]) {
      this.row = this.$store.state[CURRENT_ROW]
    } else {
      const res = await getApprovalList({bizId: this.$route.params.bizId || this.bizId})
      const length = res.data ? res.data.length : -1;
      if (res.status) {
        this.row = {
          formKey: 'vehicleDispatch',
          taskKey: length > 0 ? res.data[length - 1].taskKey : '',
          taskId: length > 0 ? res.data[length - 1].taskId : '',
          bizId: length > 0 ? res.data[length - 1].formBizId : '',
          processInstanceId: length > 0 ? res.data[length - 1].processInstanceId : '',
        }
      }
    }
    if (this?.row?.taskId) {
      this.getButtonInfo()
    }
    let params = {
      taskId: this.row.taskId,
      bizId: this.row.formBizId,
    }
    if (Object.keys(params).length > 0) {
      getApprovalList(params).then((res) => {
        if (res.data?.at(-1).approveState == 'stage') {
          this.comment = res.data.at(-1).comment
        }
      })
    }
    this.$store.commit(SET_REJECT_TASK_KEY, '')
    // this.getRejectCause().then(res => {
    //   this.column = res.data;
    // })
  },
  methods: {
    ...mapActions('CarApply', ['getRejectCause', 'getFlowButton', 'getFlowChange']),
    setDefaultReturnNode(item) {
      if (item) {
        this.returnedNodeStr = `${item.label}（${item.userName || ''}）`
        this.returnedNode = item.value;
      }
    },
    handleSearchUpdateMasterList(searchVal) {
      const list = this.initPrevNodeList || [];
      this.prevNodeList = searchVal
        ? list.filter(item => item.userFullname.includes(searchVal))
        : [...list];
    },
    returnReasonChange(val) {
      const item = this.column.find(el => el.rejectCauseCode === val)
      this.returnReasonText = item.rejectCause
    },
    // 获取流程设计中的按钮信息
    async getButtonInfo() {
      if (!!this.$route.query.buttonKey && this.$route.params.formKey === 'vehicleDispatch') {
        // const response = await this.getFlowButton({userName: this.$storage.get('username'), id: this.$route.params.bizId});
        //
        // data = response.data || [];
        // data = data.map(it => {
        //   if (['TH_RESUBMIT', 'COMMIT', 'XC_CONFIRM', 'XC_START'].includes(it.buttonKey)) {
        //     return {
        //       type: 'saveDraft',
        //       text:  it.buttonValue,
        //       code: 'SAVEDRAFT',
        //       customCode: 'SAVEDRAFT',
        //       icon: 'fks-icon-hold'
        //     }
        //   }
        // });
        this.buttonList = [
          {
            type: 'submit',
            text: this.submitText,
            code: 'SUBMIT',
            customCode: 'SUBMIT',
            icon: 'fks-icon-check'
          }
        ];
        return false;
      }
      getButtonInfo({
        taskId: this.row.taskId,
      }).then(async (res) => {
        if (!res.data) {
          return false;
        }
        let buttonInfo = JSON.parse(res.data.buttonList)
        if (buttonInfo?.taskButtonList?.length) {
          this.buttonList = buttonInfo.taskButtonList.sort(
            (a, b) => this.sortIcon(b) - this.sortIcon(a)
          ).map(item => ({...item, icon: this.buttonIcon(item)}))
        } else {
          this.buttonList = buttonInfo?.sort(
            (a, b) => this.sortIcon(b) - this.sortIcon(a)
          ).map(item => ({...item, icon: this.buttonIcon(item)}))
        }
      })
    },
    buttonIcon(item) {
      switch (item.customCode) {
        case 'SAVEDRAFT':
          return 'fks-icon-hold'
        case 'SUBMIT':
          return 'fks-icon-check'
        case 'ABANDON':
          return 'fks-icon-reject'
        case 'REJECT':
          return 'fks-icon-reject'
        default:
          return ''
      }
    },
    buttonType(item) {
      switch (item.type) {
        case 'submit':
          return 'primary'
        case 'abandon':
          return 'danger'
        case 'reject':
          return 'danger'
        default:
          return 'default'
      }
    },
    sortIcon(item) {
      switch (item.type) {
        case 'submit':
          return 1
        case 'saveDraft':
          return 2
        case 'reject':
          return 3
        case 'abandon':
          return 4
        case 'addCountersignee':
          return 5
        case 'edit':
          return 6
        case 'circulate':
          return 7
        default:
          return 8
      }
    },
    clickHandler(item) {
      if (item.type == 'submit') {
        this.confirmDlg()
      } else if (item.type == 'reject') {
        this.backDlg(item)
      } else if (item.type == 'abandon') {
        this.abandonTask()
      } else {
        this.draftDlg()
      }
    },
    async normalCheck() {
      if (this.checkRules()) {
        await this.beforeSubmit()
        this.confirm()
      }
    },
    // 流程的提交
    async confirmDlg() {
      this.stageFlag = 0
      // 表单提交前校验
      // 费用填报采用的是新的表单，所以需要单独进行校验
      if (this.taskKey === 'UserTask_5') {
        const originalRef = this.$parent.$refs.formTemp;
        const vdAfUcCompleteFormList = originalRef.formData.vdAfUcCompleteFormList;
        let count = 0;
        for (let i = 0; i < vdAfUcCompleteFormList.length; i++) {
          if (originalRef && originalRef.$refs[`carCost${i}`][0].$refs.form) {
            originalRef.$refs[`carCost${i}`][0].$refs.form.validate().then(async () => {
              count++;
              if (count === vdAfUcCompleteFormList.length) {
                await this.normalCheck()
              }
            }).catch(()=>{
              this.locateToErr()
            })
          }
        }
        return false;
      }
      const originalRef = this.$parent.$refs.formTemp.$refs.form
      if (originalRef) {
        originalRef.validate().then(async () => {
          await this.normalCheck()
        }).catch(()=>{
          this.locateToErr()
        })
      } else {
        await this.normalCheck()
      }
      // const originalRef = this.taskKey === 'UserTask_5' ?
      //   this.$parent.$refs.formTemp.$refs.carCost.$refs.form :
      //   this.$parent.$refs.formTemp.$refs.form
      // if (originalRef) {
      //   originalRef.validate().then(async () => {
      //     await this.normalCheck()
      //   }).catch(()=>{
      //     this.locateToErr()
      //   })
      // } else {
      //   await this.normalCheck()
      // }
    },
    // 流程变更
    flowChange(data) {
      const { vdApplyForm, modifycomment }  = data;
      const params = {
        flowButtonKey: this.$route.query.buttonKey,
        currentUserName: this.$storage.get('username'), // 当前登录人用户名
        vdAfFlowChangeRecord: {
          vdApplyFormId: vdApplyForm.id,
          changeCause: modifycomment,
          changeFullName: this.currUser.userFullName,
          changeUserName: this.currUser.userName,
          changeUserDept: this.currUser.deptName,
          changeTypeKey: this.$route.query.buttonKey
        }
      }
      // 租车公司变更
      if (params.flowButtonKey === 'XC_CAR_COMP_MODIFY') {
        params.vdApplyForm = {
          id: vdApplyForm.id,
          carCompanyName: vdApplyForm.carCompanyName,
          carCompanyApproverFullName: vdApplyForm.carCompanyApproverFullName,
          carCompanyApproverUserName: vdApplyForm.carCompanyApproverUserName,
          vdCarCompanyInfoId: vdApplyForm.vdCarCompanyInfoId
        }
      }
      // 修改司机/车辆信息
      if (params.flowButtonKey === 'XC_DRIVER_CAR_MODIFY') {
        const { carNum, carType, carAge } = vdApplyForm.carResource === 1 ? vdApplyForm : data.inputVdCarInfo;
        const { driverFullName, driverPhone, driverAge } = vdApplyForm.driverResource === 1 ? vdApplyForm : data.inputVdDriverInfo;
        params.vdApplyForm = {
          id: vdApplyForm.id
        }
        params.inputVdDriverInfo = {
          driverFullName,
          driverPhone,
          driverAge
        }
        params.inputVdCarInfo = {
          carNum,
          carType,
          carAge
        }
      }
      // 行程信息变更
      if (params.flowButtonKey === 'XC_FORM_MODIFY') {
        params.vdApplyForm = {
          id: vdApplyForm.id,
          startAddress: vdApplyForm.startAddress,
          startAddressDetail: vdApplyForm.startAddressDetail,
          endAddress: vdApplyForm.endAddress,
          startTime: this.$dayjs(vdApplyForm.startTime).format('YYYY-MM-DD HH:mm') + ':00',
          predictEndTime: this.$dayjs(vdApplyForm.predictEndTime).format('YYYY-MM-DD HH:mm') + ':00',
          useCarTripType: vdApplyForm.useCarTripType,
          useCarJgTimeHour: vdApplyForm.useCarJgTimeHour,
          useCarJgTimeMinute: vdApplyForm.useCarJgTimeMinute
        }
      }

      this.getFlowChange(params).then(res => {
        if (!res.status) {
          this.isDisable = false;
          return false;
        }
        if (this.isMobile) {
          Toast(this.formName + '成功', 'middle')
        } else {
          this.$message.success(this.formName + '成功')
        }
        setTimeout(() => {
          Toast.clear()
          this.back();
        }, 1000)
      })
    },
    // 表单提交方法
    confirm() {
      try {
        this.isDisable = true
        let url = this.submitURL
        let formProcessParam = {
          taskId: this.row.taskId,
          bizId: this.row.formBizId,
          approval: '',
          comment: this.comment,
          stageFlag: this.stageFlag,
          formKey: this.row.formKey,
          modelKey: this.modelKey,
          processInstanceId: this.row.processInstanceId,
          variable: this.formData,
        }
        let temp = JSON.parse(JSON.stringify(this.formData))
        temp.createDate = this.$dayjs(temp.createDate).format(
            'YYYY-MM-DD 00:00:00'
        )
        let data = this.isDefault ? {
          detailParamList: this.detailParamList,
          entityName: this.entityName,
          entityObject: temp,
          formProcessParam: formProcessParam,
        } : this.getEntityParam(formProcessParam)
        if (!!this.$route.query.buttonKey && this.$route.params.formKey === 'vehicleDispatch') {
          this.flowChange(data);
          return false;
        }
        console.info('🚀🚀', 'comfirm data -->', data, `<-- index.vue/confirm`)
        if (!data) {
          return false;
        }
        comfirm(url, data).then((res) => {
          if (res.status) {
            if (this.stageFlag === 0) {
              this.afterSubmit()
              if (this.isMobile) {
                Toast('审批成功', 'middle')
              } else {
                this.$message.success('审批成功')
              }
            } else {
              if (this.isMobile) {
                Toast('暂存成功', 'middle')
              } else {
                this.$message.success('暂存成功')
              }
            }
            setTimeout(() => {
              Toast.clear()
              this.back();
            }, 1000)
          } else {
            this.isDisable = false
            this.isMobile && Toast(res.message ||  '审批失败', 'middle')
            this.isPC && this.$message.warning(res.message ||  '审批失败')
          }
        }).catch((e) => {
          this.isDisable = false
        })
      } catch (e) {
        this.isDisable = false
      }

    },
    drawBack(item){
      try {
        // 派车申请 租车公司确认 退回
        // 当没有填写退回内容时，弹出退回理由对话框
        if (this.taskKey === 'UserTask_2' && this.type === 'execute' && !this.comment) {
          this.isMobile && (this.showPickerReturnReason = true)
          this.isPC && (this.showPickerReturnReasonPC = true)
          this.item = item
        } else {
          this.returnBack(item)
        }
      } catch (e) {
        console.log(e, this.type, 'ee')
      }
    },
    // 流程退回
    backDlg(item) {
      if (!this.comment && !(item?.targetNode?.length == 1 && this.taskKey === 'UserTask_2' && this.type === 'execute')) {
        this.isMobile && Toast('请填写退回原因')
        this.isPC && this.$message.warning('请填写退回原因')
        this.isMobile && this.$parent.tabClick(2)
        return false;
      }
      // 只有一个退回节点时，直接退回
      if (item?.targetNode?.length == 1) {
        if (this.isSelectReason) {
          if (this.comment) {
            this.returnBack(item);
          } else {
            this.isMobile && Toast('请填写退回原因');
          }
          return false;
        }
        this.isMobile && Dialog.confirm({
          // 组件除show外的属性
          title: '提示',
          message: '是否要执行退回操作?',
        })
            .then(() => {
              this.drawBack(item)
            })
            .catch(() => {
            })
        this.isPC && this.drawBack(item);
      } else if (item?.targetNode?.length > 1) {
        this.showRejectNodeList(item.targetNode)
        // 多个
        // this.show = true
      }
    },
    // 确认退回原因
    confirmReturnReason() {
      if (+this.returnReasonCode !== 3) {
        this.comment = this.returnReasonText
      }
      if (+this.returnReasonCode === 3 && !this.comment) {
        this.isMobile && Toast('请填写退回原因');
        this.isSelectReason = true;
        // 跳转到审批页填写退回原因
        this.$parent.tabClick(2)
        return false;
      }
      this.returnBack(this.item, true);
    },
    // pc端确认退回原因
    confirmReturnReasonPC() {
      this.comment = this.returnReasonText
      if (+this.returnReasonCode === 1 || +this.returnReasonCode === 2) {
        // 选择原因后，直接返回
        this.returnBack(this.item, true)
      } else {
        this.showPickerReturnReasonPC = false
      }
    },
    returnBack(item, flag = false) {
      console.log('执行return back 逻辑', this.comment)
      this.isDisable = true
      let params = {
        taskId: this.row.taskId,
        bizId: '',
        targetKey: item.targetNode.toString(), //目前的流程没有做回退任意节点，回退数组中只取第一个人
        comment: this.comment || '退回',
      }
      if (flag && this.returnReasonCode !== '3') {
        params.comment = this.returnReasonText
      }
      backDlg(params).then((res) => {
        if (res.status) {
          this.aftereReject(item.targetNode)
          this.isMobile && Toast('退回成功', 'middle')
          this.isPC && this.$message.success('退回成功')
          setTimeout(() => {
            Toast.clear()
            this.isSelectReason = false;
            this.back();
          }, 1000)
        } else {
          this.isMobile && Toast.fail(res.message)
          this.isPC && this.$message.error(res.message)
        }
      })
    },
    // 表单暂存
    draftDlg() {
      if (this.comment.replace(/\s+/g, '').length == 0) {
        this.comment = '暂存'
      }
      this.stageFlag = 1
      this.beforeSubmit()
      this.confirm()
    },
    abandonTask() {
      Dialog.confirm({
        // 组件除show外的属性
        title: '提示',
        message: '是否要执行废弃操作?',
      })
          .then(() => {
            const flowData = {
              taskId: this.row.taskId,
              comment: this.comment,
            }
            processAbandon(flowData)
                .then((res) => {
                  this.isMobile && Toast('废弃成功', 'middle')
                  this.isPC && this.$message.success('废弃成功')
                  setTimeout(() => {
                    Toast.clear()
                    this.back();
                  }, 1000)
                })
                .catch((err) => {
                  console.error(err)
                })
          })
          .catch(() => {
          })
    },
    showRejectNodeList(rejectNode) {
      getTaskKey({
        taskId: this.row.taskId,
      })
          .then((res) => {
            if (res.status) {
              this.rejectList = res.data.filter((task) =>
                  rejectNode.includes(task.taskKey)
              )
            }
          })
          .finally(() => {
            this.show = true
          })
    },
    getRejectNode(item) {
      this.currentKey = item.taskKey
      this.$store.commit(SET_REJECT_TASK_KEY, this.currentKey)
      this.currentNode = item.taskKey
    },
    getNode() {
      this.isDisable = true
      backDlg({
        taskId: this.row.taskId,
        bizId: '',
        targetKey: this.currentNode,
        comment: this.comment || '退回',
      }).then((res) => {
        if (res.status) {
          this.aftereReject(this.currentNode)
          this.isMobile && Toast('退回成功', 'middle')
          this.isPC && this.$message.success('退回成功')
          setTimeout(() => {
            Toast.clear()
            this.back();
          }, 1000)
        } else {
          this.isMobile && Toast.fail(res.message)
          this.isPC && this.$message.error(res.message)
        }
        setTimeout(() => {
        }, 2000)
      })
    },
    back() {
      // 流程完成后统一回到记录页
      EventBus.$emit('refreshDot', 'Todo')
      this.$router.push({name: 'CarRecord'})
    }
  },
}
</script>
<style>
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}
</style>
<style lang="less" scoped>
@import "./index.less";
.title {
  font-size: 32px;
  span {
    display: inline-block;
    margin-left: 10px;
  }
}

.special {
  /deep/ .fks-form-item__content {
    margin-left: 0 !important;
  }
}

.padding {
  padding: 0 36px;
}
.buttons {
  display: flex;
  padding: 18px 30px;
  bottom: 0;
  height: calc(122px + constant(safe-area-inset-bottom));
  height: calc(122px + env(safe-area-inset-bottom));
}
</style>
