/deep/ .fm-button {
  height: 88px;
  border-radius: 8px;
  font-size: 32px;
  font-weight: 500;
  line-height: 44px;
  letter-spacing: 1px;
}
/deep/ .fm-button--primary {
  background: #3C83FF;
}
/deep/ .fm-button--default {
  border: 2px solid #3C83FF;
  color: #3C83FF;
}
.pcWidth {
  width: 1200px;
  margin: 0 auto;
}
.black {
  font-size: 28px;
  font-weight: 400;
  /*color: rgba(38, 42, 52, 1);*/
  color: #888;
  word-break: break-all;
}
.approve-title {
  //font-size: 32px;
  font-weight: 400;
  color: rgba(75, 75, 75, 1);
  //line-height: 60px;
  padding: 28px 16px !important;
}
.process-cell {
  display: grid;
  grid-template-columns: 30% 70%;
}
.approve-textarea {
  background-color: #fff;
}
.approve-textarea /deep/ .fm-cell {
  // margin-top: 60px !important;
  padding: 28px 16px !important;
}
.approve-textarea-entrust /deep/ .fm-cell__value {
  /*background: #eeeeee;*/
  //border: 1px solid #bbbbbb;
  border: unset;
  //opacity: 0.5;
  //padding: 20px 20px;
  overflow-y: scroll !important;
  //max-height: 400px;
}
/deep/ .fm-field--min-height .fm-field__control {
  min-height: 360px;
}
.approve-textarea /deep/ .fm-field__word-limit {
  margin-top: 0px;
}
.btn-area {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn {
  flex: 1;
  margin: 0 16px;
}
.general-btn {
  font-size: 32px;
  color: #ffffff;
  background-color: #4545D1;
  border-radius: 4px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  width: 100%;
}
.back-btn {
  font-size: 28px;
  border: 1px solid #fbc4c4;
  border-radius: 2px;
  color: #f56c6c;
  background: #fef0f0;
  width: 100%;
}
// ----- 退回节点对话框 start -----
.reject-node-item:last-child {
  margin-bottom: 32px;
}
.reject-node {
  text-align: center;
  padding-top: 48px;
}
.reject-node.hover {
  color: #4545D1;
}
// ----- 退回节点对话框 end -----
