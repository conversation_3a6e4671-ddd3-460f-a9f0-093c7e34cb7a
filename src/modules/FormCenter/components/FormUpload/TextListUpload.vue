<template>
  <div>
    <fks-upload
      ref="formUpload"
      :file-list="fileList"
      :action="url"
      :with-credentials="true"
      :headers="{ 'Fawkes-Auth': accessToken }"
      :data="g9s"
      :on-success="initFile"
      :on-remove="deleteFile"
      v-bind="$attrs"
      :on-change="fileListChange"
      :list-type="'text'"
      v-on="$listeners"
      :disabled="disabled"
      :style="disabled ? {paddingLeft: '15px'} : {}"
      :auto-upload="true"
      :on-preview="previewFile"
      :on-download="downloadFile"
    >
      <!--:on-exceed="handleExceed"-->
      <fks-button
        size="small"
        text
        style="color: rgba(2, 122, 255, 1) !important;"
        icon="fks-icon-upload-line"
        :disabled="disabled"
      >上传</fks-button>
    </fks-upload>
    <fks-dialog
      :visible.sync="dialogVisible"
      :title="current_file.name"
      append-to-body
      class="dialog-8vh"
    >
      <div style="height: calc(100vh - 330px)">
        <pre-view :file="current_file" :key="current_file.fileToken"></pre-view>
      </div>
    </fks-dialog>
  </div>
</template>

<script>
import mixins from './mixins'
export default {
  name: "FormUploadPcView",
  mixins: [mixins]
}
</script>

<style lang="less" scoped>
/deep/ .fks-upload-list.fks-upload-list--text {
  width: calc(100% - 120px);
  &.is-disabled {
    width: 100%;
  }
}
.fks-upload-list__item {
  cursor: pointer;
  margin: 8px 0 !important;
}
.fks-upload-list__item .fks-icon-close{
  top: 15px !important;
}

.upload-list_item-size {
  color: #b4bcc7;
  font-size: 12px;
  line-height: 17px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: calc(100% - 30px);
}


</style>
