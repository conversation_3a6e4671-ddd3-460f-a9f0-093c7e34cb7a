<template>
  <div class="upload-container">
    <fks-upload
      v-if="!disabled"
      ref="formUpload"
      :file-list="fileList"
      :action="url"
      accept="image/*,application/pdf"
      :with-credentials="true"
      :headers="{ 'Fawkes-Auth': accessToken }"
      :data="g9s"
      :on-success="initFile"
      :on-remove="deleteFile"
      :show-file-list="false"
      v-bind="$attrs"
      :on-change="fileListChange"
      v-on="$listeners"
      :disabled="disabled"
      :auto-upload="true"
      :on-preview="previewFile"
      :on-download="downloadFile"
      :before-upload="beforeUpload"
      :on-progress="onProgressFile"
      :limit="4"
      multiple
      drag
    >
      <div
        class="upload-dropzone"
        :class="{ 'drag-over': isDragging }"
      >
        <div class="upload-content">
          <img src="../../../../assets/img/help/uploader.svg" class="upload-icon" />
          <div class="upload-tip2">
            拖动文件/图片到虚线框内或者 <span class="upload-link">点击上传</span>
          </div>
          <div class="upload-tip">
            按住Ctrl可同时多选，最多可上传4张图片，支持上传 JPG/JPEG/PNG 格式文件
          </div>
        </div>
      </div>
    </fks-upload>


    <div v-if="fileList.length > 0" class="preview-container flex">
      <div
        class="preview-item flex col-center row-between cursor-pointer position-relative"
        v-for="item in fileList"
        :key="item.fileToken"
        @click="handlePreview(item)"
      >
        <div class="flex">
          <svg-icon
            :icon-class="getFileType(item.name)"
            class-name="pc-file-icon"
            style="margin-right: 6px"
          />
          <div class="preview-info flex flex-column" style="width: 100px">
            <div class="title text-ellipsis">{{ item.name }}</div>
            <div class="file-size">{{ getFileSize(item.size) }}</div>
          </div>
        </div>
        <div class="flex flex-column full-height" :class="[disabled ? 'row-end' : 'row-between']">
          <i
            v-show="!disabled && (!item.progress || item.progress == 100)"
            class="fks-icon-close"
            style="width: 16px; height: 16px; color: #cccccc"
            @click.stop="deleteAttachment(item)"
          />
          <i
            v-show="!item.progress || item.progress == 100"
            class="fks-icon-download"
            style="width: 16px; height: 16px; color: #cccccc"
            @click.stop="downloadFile(item)"
          />
        </div>
        <!-- 进度条容器 -->
        <div
          v-if="item.progress > 0 && item.progress < 100"
          :style="{width: getWidth(item)}"
          style="
            position: absolute;
            bottom: 0;
            left: 0;
            height: 2px;
            background: #1890ff;
            border-radius: 1.5px;
          "
        />
      </div>
    </div>

    <fks-dialog
      v-if="currentFile"
      :visible.sync="previewDialogVisible"
      :title="currentFile.name"
      append-to-body
      class="dialog-8vh"
    >
      <div style="height: calc(100vh - 350px); overflow: hidden">
        <pre-view :file="currentFile" :key="currentFile.fileToken"></pre-view>
      </div>
    </fks-dialog>
  </div>
</template>

<script>
import PreView from '@components/PreView/index.vue'
import mixins from '@modules/FormCenter/components/FormUpload/mixins'
import formStyleMixin from '@components/InputAttachment/formStyleMixin'

export default {
  name: 'FancyDragUpload',
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: { PreView },
  mixins: [mixins, formStyleMixin],
  data() {
    return {
      isDragging: false,
      previewListExpand: false,
      previewDialogVisible: false,
      currentFile: null,
    }
  },
  methods: {
    onProgressFile(e, file) {
      let progress = e.percent;
      let id = file.uid;
      const item = this.fileList.find((item) => item.uid === id)
      if (item) {
        this.$set(item, 'progress', progress || 0)
        this.$forceUpdate()
      }
    },
    getWidth(item) {
      return (item.progress || 0) + '%'
    },
    deleteAttachment(item) {
      this.deleteFile(item)
      const token = item.response ? item.response.data.fileToken : item.fileToken;
      this.fileList = this.fileList.filter((f) => {
        const fToken = f.response ? f.response.data.fileToken : f.fileToken;
        return fToken !== token;
      });
    },
    handlePreview(item) {
      this.currentFile = item.response ? item.response.data : item;
      this.$nextTick(() => {
        this.previewDialogVisible = true;
      })
    },
  },
}
</script>

<style scoped lang="less">
@import "exclude/custom.css";
@import 'exclude/drag-uploader.css';
</style>
