<template>
  <div>
    <fks-input style="display: none" v-model="countFile"></fks-input>
    <fks-upload
      ref="formUpload"
      :file-list="fileList"
      :action="url"
      :with-credentials="true"
      :headers="{ 'Fawkes-Auth': accessToken }"
      :data="g9s"
      :on-success="initFile"
      :on-remove="deleteFile"
      v-bind="$attrs"
      :on-change="fileListChange"
      v-on="$listeners"
      :disabled="disabled"
      :style="disabled ? {paddingLeft: '15px'} : {}"
      :auto-upload="true"
      :on-preview="previewFile"
      :on-download="downloadFile"
      :before-upload="beforeUpload"
    >
      <!--:on-exceed="handleExceed"-->
      <fks-button
        size="small"
        style="float: left"
        plain
        type="primary"
        icon="fks-icon-upload-line"
        :disabled="disabled"
      >上传</fks-button
      >
      <span slot="tip" class="fks-upload__tip fks-upload-tip"
      >{{ tipText }}{{ countStr }}</span
      >
    </fks-upload>
    <fks-dialog
      :visible.sync="dialogVisible"
      :title="current_file.name"
      append-to-body
      class="dialog-8vh"
    >
      <div style="height: calc(100vh - 350px); overflow: hidden;">
        <pre-view :file="current_file" :key="current_file.fileToken"></pre-view>
      </div>
    </fks-dialog>
  </div>
</template>

<script>
import mixins from './mixins'
export default {
  name: "FormUploadPcView",
  mixins: [mixins]
}
</script>

<style lang="less" scoped>
.upload-list_item-size {
  color: #b4bcc7;
  font-size: 12px;
  line-height: 17px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: calc(100% - 30px);
}

.fks-upload-list__item {
  cursor: pointer;
  margin: 8px !important;
}

.fks-upload-list--card {
  width: 100%;
  display: flex;
  justify-content: flex-start !important;
}
/deep/ .fks-dialog__body {
  overflow: hidden !important;
}
</style>
