<template>
  <div class="flex col-center">
    <fks-upload
      v-if="!disabled"
      v-bind="$attrs"
      v-on="$listeners"
      ref="formUpload"
      :file-list="fileList"
      :action="url"
      :with-credentials="true"
      :headers="{ 'Fawkes-Auth': accessToken }"
      :data="g9s"
      :on-success="initFile"
      :on-remove="deleteFile"
      :show-file-list="false"
      :on-change="fileListChange"
      :disabled="disabled"
      :auto-upload="true"
      :on-preview="previewFile"
      :on-download="downloadFile"
      :on-progress="onProgressFile"
      :on-exceed="onExceed"
      :before-upload="beforeUpload"
      style="margin-right: 18px"
    >
      <fks-popover
        :content="tip"
        :disabled="disabled || !tip"
        placement="top-start"
        width="200"
        trigger="hover"
      >
        <div slot="reference" class="flex col-center">
          <div class="upload-btn flex col-center">
            <img width="12px" :src="require('@/assets/img/upload.png')" style="margin-right: 8px" />
            <div style="width: 56px">上传附件</div>
          </div>
        </div>
      </fks-popover>
    </fks-upload>
    <div class="attachment-text cursor-pointer" @click="$emit('handleExpand', fileList)">
      {{ countStr }}
    </div>
  </div>
</template>

<script>
import mixins from './mixins'
import { deleteFile } from '@modules/FormCenter/api'

export default {
  name: 'fee-upload',
  mixins: [mixins],
  computed: {
    tip() {
      return this.tipText || this.generatedTip
    },
    generatedTip() {
      if (this.$attrs.accept) {
        const mimeMap = {
          'image/*': ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
          'application/pdf': ['pdf'],
          'application/msword': ['doc'],
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['docx'],
          'application/vnd.ms-excel': ['xls'],
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['xlsx'],
          'text/plain': ['txt'],
        }
        // 去重处理
        const extensions = new Set()
        this.$attrs.accept.split(',').forEach(type => {
          (mimeMap[type.trim()] || []).forEach(ext => extensions.add(ext))
        });
        const str = Array.from(extensions).sort().join(', ')
        return str ? `支持格式：${str}` : ''
      } else {
        return ''
      }
    },
  },
  methods: {
    onExceed() {
      this.$message.error(`最多只能上传${this.$attrs.limit}张图片`)
    },
    onProgressFile(e, file) {
      this.$emit('onProgressFile', { progress: e.percent, id: file.uid })
    },
    deleteAttachment(fileToken) {
      deleteFile({ f8s: [fileToken] }).then((res) => {
        if (res.status) {
          this.$message.success('删除成功')
          if (this.countFile > 0) this.countFile--
          this.fileList = this.fileList.filter((item) => {
            const t = item.response ? item.response.data.fileToken : item.fileToken
            return t !== fileToken
          })
          this.$emit('getFileInfo', this.fileList)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
/deep/ .file-card {
  min-width: unset !important;
}

@import './exclude/custom.css';
</style>
