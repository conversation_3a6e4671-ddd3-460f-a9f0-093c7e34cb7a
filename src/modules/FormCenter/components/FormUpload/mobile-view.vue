<template>
  <div class="m-b-32 p-b-32">
    <!-- 指定单位，支持 rem, vh, vw -->
    <input style="display: none" v-model="countFile"></input>
    <fm-uploader
      v-show="show"
      ref="formUpload"
      v-model="uploader"
      preview-size="5rem"
      :after-read="afterRead"
      :before-read="beforeRead"
      :on-remove="beforeDelete"
      :accept="accept"
      :disabled="disabled"
    >
      <template #preview-list="previewFileList">
<!--        <div v-for="(item,index) in fileList" :key="index" class="m-b-20" :class="{'u-flex': item.progress === 100 }">-->
<!--          <template v-if="item.progress < 100" >-->
<!--            <div class="u-flex">-->
<!--              <div @click="preViewFile(item)" class="flex col-center">-->
<!--                <svg-icon class="file-type-img" :icon-class="fileIcon(item.name)"  />-->
<!--                <span class="m-l-20 font-24 tips-color">{{ item.name }}</span>-->
<!--              </div>-->
<!--              <fm-image v-if="!show" class="check-img m-l-20" src="/static/svg/check-circle1.svg" mode="widthFix"></fm-image>-->
<!--              <i v-else-if="item.progress === 100" @click="clear(item, index)" class="fm-icon fm-icon-close-circle m-l-20" color="#ff4d4f" ></i>-->
<!--            </div>-->
<!--            <fm-progress stroke-width="8" :percentage="item.progress"></fm-progress>-->
<!--          </template>-->
<!--          <template v-else>-->
<!--            <div  class="flex col-center m-t-20">-->
<!--              <div @click="preViewFile(item)" class="flex">-->
<!--                <svg-icon class="file-type-img" :icon-class="fileIcon('.' + item.extName)"  />-->
<!--                <span class="m-l-20 font-24 tips-color">{{ item.name }}</span>-->
<!--              </div>-->
<!--              <svg-icon v-if="!show" class="check-img m-l-20" icon-class="check-circle1"  />-->
<!--              <i v-else @click="clear(item, index)" class="fm-icon fm-icon-cross m-l-20"></i>-->
<!--            </div>-->
<!--          </template>-->
<!--        </div>-->
      </template>
      <fm-button icon="plus" type="primary">上传文件</fm-button>
    </fm-uploader>
    <div class="padding m-t-20" :class="{'no-list': fileList.length === 0}">
      <!-- <u-empty class="u-m-t-10" v-show="files.size === 0 && !show" text="暂无附件" icon-size="80" font-size="20" mode="list"></u-empty> -->
      <fm-empty
        v-show="fileList.length === 0 && !show"
        class="custom-image"
        :image="require('../../../../assets/img/car/no-data.png')"
        description="暂无附件"
      />
      <span v-show="fileList.length === 0 && !show" class="m-t-10"></span>
      <span v-show="fileList.length > 0 && show" class="m-t-20 font-24">已选择文件列表：</span>
    </div>
    <div v-for="(item,index) in fileList" :key="index" class="m-t-20" :class="{'u-flex': item.progress === 100 }">
      <template v-if="item.progress < 100" >
        <div class="u-flex">
          <div @click="preViewFile(item)" class="flex col-center">
            <svg-icon class="file-type-img" :icon-class="fileIcon(item.name)"  />
            <span class="m-l-20 font-24 tips-color">{{ item.name }}</span>
          </div>
          <svg-icon v-if="!show" class="check-img m-l-20" icon-class="check-circle1"  />
          <i v-else-if="item.progress === 100" @click="clear(item, index)" class="fm-icon fm-icon-close-circle m-l-20" color="#ff4d4f" ></i>
        </div>
        <fm-progress class="m-t-20" stroke-width="5" :percentage="item.progress"></fm-progress>
      </template>
      <template v-else>
        <div  class="flex col-center m-t-20">
          <div @click="preViewFile(item)" class="flex col-center">
            <svg-icon class="file-type-img" :icon-class="fileIcon(item.name)"  />
            <div class="flex flex-column">
              <div  class="m-l-20 font-28 color-black3">{{ item.name }}</div>
              <div class="m-l-20 font-24 m-t-16 tips-color">{{ item.size ? (item.size / 1000).toFixed(2) + 'KB' : ''}}</div>
            </div>
          </div>
          <svg-icon class="check-img m-l-20" icon-class="check-circle1"  />
          <i v-if="show" @click="clear(item, index)" class="fm-icon fm-icon-cross m-l-20"></i>
        </div>
      </template>
    </div>
    <fm-image-preview v-model="showImg" :images="previewImages" @change="onChangeImg">
      <template v-slot:index>第{{ indexImg }}页</template>
    </fm-image-preview>
    <fm-popup v-model="isShowVideo" mode="center" width="90%" height="50%">
      <video id="currentVideo" :src="videoPath" controls autoplay width="100%" height="100%" class="full-content"></video>
    </fm-popup>
    <fm-popup v-model="dialogVisible" mode="center" width="90%" height="50%">
      <div style="height: calc(100vh - 330px)">
        <pre-view :file="current_file" :key="current_file.fileToken"></pre-view>
      </div>
    </fm-popup>
  </div>
</template>
<script>
import mixins from '@modules/FormCenter/components/FormUpload/mixins';
import request from '@/utils/request';
import { Dialog, Toast } from 'fawkes-mobile-lib'
import { fileIcon, uuid } from '@/utils/util'
import { ImagePreview } from 'fawkes-mobile-lib';
import { deleteFile } from '@modules/FormCenter/api'
export default {
  name: 'FormUploadMobileView',
  mixins: [mixins],
  props: {
    show: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
  },
  data() {
    return {
      accessToken: this.$storage.get("access_token"),
      fileList: [], // 上传完成后接口返回的路径
      uploader: [], // 上传完成组件返回的文件信息
      accept:".jpg,.jpeg,.png,.JPG,.JPEG,.dwg,.DWG,.rar,.RAR,.zip,.ZIP,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx",
      isShowVideo: false,
      videoPath: '',
      showImg: false,
      indexImg: 0,
      previewImages: [],
      countFile: 0,
      progress: 0
    }
  },
  methods: {
    fileIcon,
    onChangeImg(index) {
      this.indexImg = index;
    },
    // 预览文件
    preViewFile(item) {
      // if (!(item.progress === 100 && item.type === 'success')) {
      //   return false;
      // }
      return false;
      if (item.progress !== 100) {
        return false;
      }
      let token = item.fileToken;
      let filePath = item?.name ;
      const index = filePath ? filePath.lastIndexOf(".") : -1;
      const ext = index !== -1 ? filePath.substr(index+1) : '';
      // let url = process.env.VUE_APP_BASE_URL + '/sys-storage/download?f8s=' + token;
      let url = 'http://**************:8088/api/sys-storage/download?f8s=' + token;
      if (['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff'].indexOf(ext.toLowerCase()) !== -1) {
        this.previewImg(url)
      } else if (['mp4', 'mov'].indexOf(ext.toLowerCase()) !== -1) {
        this.videoPath = url;
        this.isShowVideo = true;
      } else {
        this.previewFile(url, ext);
      }
    },
    // 预览图片
    previewImg(imgUrl) {
      console.log('imgUrl',imgUrl);
      // 预览图片
      this.previewImages = [imgUrl];
      this.showImg = true;
    },
    // 移除某个文件
    clear(item, index) {

      Dialog.confirm({
        title: '提示',
        message: '是否要删除该附件?',
      }).then(() => {
        this.doDelete(item, index)
      })
    },
    doDelete(item, index) {

      let token = item.fileToken; // 后端返回的文件
      // 已有文件的token数组
      const rawTokenList = this.fileList.map(item => item.fileToken);
      if (rawTokenList.indexOf(token) !== -1) {
        this.deleteList.push(item);
      }

      const deleteTokenList = this.deleteList.map((item) => {
        let token = item.fileToken
        if (item.response) {
          token = item.response.data.fileToken
        }
        return token
      })
      if (deleteTokenList.length) {
        deleteFile({ f8s: deleteTokenList }).then((res) => {
          if (res.status) {
            this.deleteList = []
          }
        })
      }

      this.beforeDelete(item, { index });
    },
    // 删除文件
    beforeDelete(file, detail) {
      // 把需要删除的项根据下标删除
      this.fileList.splice(detail.index, 1);
      // 返回最新的数组 并转换成以逗号隔开的格式   如果不需要去掉即可
      // this.$emit("input", this.fileList.join(","));
    },
    //  上传之前要做的校验
    beforeRead(file) {
      // this.accept是要上传的格式  以","分割
      const arr = this.accept.split(",");
      // 判断里面是否带有 * 号  如果带了星号就表示所有文件都可以上传 直接return
      if (arr.find((item) => item === "*") === "*") {
        return true;
      } else {
        if (this.accept) {
          let type = file.name.substr(file.name.lastIndexOf("."))
          if (this.accept.indexOf(type) === -1) {
            Toast({
              title: '请选择正确格式的文件',
              icon: 'none'
            });
            return false;
          }
        }
        return true;
        // // 把文件类型添加成数组格式
        // let imageTypes = [];
        // for (let i = 0; i < arr.length; i++) {
        //   const element = arr[i];
        //   // 因为文件形式都是以 .png,.jpg 格式传过来的，转换成数组以后需要把点去掉，然后拼接起来
        //   imageTypes.push(`image/${element.substring(1)}`);
        // }
        // // file.type格式就是 image/png
        // // 判断是否有正确的文件格式  有的话就是true  没有就是false
        // if (!imageTypes.includes(file.type)) {
        //   Toast(`请上传${this.accept}格式的文件！`);
        // }
        // return imageTypes.includes(file.type);
      }
    },
    // 上传文件请求
    afterRead(files) {
      // 上传状态
      files.status = "uploading";
      files.message = "上传中...";
      files.name = files.file.name;
      files.progress = 0;
      const index = this.fileList.length;
      this.fileList.push(files);
      this.uploadFile({
        files,
        g9s: this.g9s.g9s,
        index
      }).then(res => {
        if (!res.status) {
          // 请求未成功  状态修改为失败
          files.status = "failed";
          files.message = "上传失败";
          this.fileList.splice(index, 1);
          return false;
        }
        // 请求成功修改为成功状态
        files.message = "上传完成";
        files.status = "done";
        // 把接口返回的数据放到数组里面
        files.progress = 100;
        files.type = 'success';
        this.$set(this.fileList, index, {...files, ...res.data});
        // 转换成以逗号隔开的格式   如果不需要去掉即可
        this.$emit("input", this.fileList.join(","));
        const fileg9sTem = res.data?.groupToken;
        fileg9sTem && this.$emit("input", fileg9sTem);
        fileg9sTem && this.countFile++;
      }).catch(error => {
        // 上传失败处理
        console.error('上传失败处理', error);
      });
    },
    uploadFile(params) {
      const { files, g9s, index } = params
      let formData = new FormData();
      formData.append('g9s', g9s);
      formData.append('file', files.file, files.file.name);
      const _this = this;
      return request({
        url: '/sys-storage/upload',
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          'Fawkes-Auth': this.accessToken
        },
        onUploadProgress: event => {
          // 这里可以获取上传进度
          if (event.lengthComputable) {
            let progress = Math.ceil((event.loaded * 100) / event.total)
            if (progress <= 100) {
              files.progress = progress;
              _this.$set(_this.fileList, index, files);
            }
          }
        },
        data: formData
      });
    }
  }
}
</script>

<style scoped lang='less'>
.file-type-img {
  width: 40px;
  height: 40px;
}
.tips-color {
  color:  #909399;
}
/deep/ .fm-uploader__preview {
  display: none;
}
/deep/ .custom-image .fm-empty__image {
  width: 348px;
  height: 240px;
}
</style>
