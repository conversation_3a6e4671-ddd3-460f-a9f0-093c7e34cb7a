
.upload-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;

    .fks-upload-dragger, .fks-upload--card, .file-card {
        height: 100%;
        width: 100%;
    }
}
.upload-label {
    width: 80px;
    line-height: 40px;
    color: #333;
}
.upload-dropzone {
    height: 140px;

    border: 1px dashed #c5d1eb;
    border-radius: 4px;
    background-color: #f9fafb;
    text-align: center;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-dropzone.drag-over {
    background-color: #ecf2fe;
    border-color: #3c83ff;
}
.upload-content {
    color: #888;
    font-size: 14px;
}
.upload-icon {
    width: 32px;
    height: 32px;
}
.upload-link {
    color: #3c83ff;
    cursor: pointer;
}
.upload-tip {
    font-size: 12px;
    line-height: 12px;
    color: #999999;
}
.upload-tip2 {
    font-size: 14px;
    line-height: 14px;
    color: #555555;
    margin-bottom: 5px;
}
.upload-input {
    position: absolute;
    inset: 0;
    opacity: 0;
    cursor: pointer;
}