.upload-btn {
    border-radius: 4px;
    background: #3C83FF;
    padding: 6px 8px;
    color: #fff;
    width: 94px;
    height: 30px;
    font-size: 14px;
    box-sizing: border-box;
    cursor: pointer;
    &:hover {
        background: #3C83FFee;
    }
}

.attachment-text {
    user-select: none;
    margin: auto;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #3C83FF;
    white-space: nowrap;
}

.file-card-container {
    padding: 8px;
}

.file-card-container .file-card-item {
    border-radius: 4px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid rgba(42, 131, 230, 0.32);
    padding: 12px;
}

.preview-container {
    display: flex;
    flex-wrap: wrap;
}

.preview-container .preview-item {
    border-radius: 4px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid rgba(42, 131, 230, 0.32);
    padding: 12px;
    margin-top: 10px;
    margin-right: 10px;
}

.preview-container .preview-item .title {
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #555555;
    margin-bottom: 5px;
}

.preview-container .preview-item .file-size {
    font-size: 12px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #999999;
}