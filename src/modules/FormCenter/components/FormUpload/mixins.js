import { deleteFile, downloadFile, getFile } from '@/modules/FormCenter/api'
import { getFileSuffix, isMimeMatch, uuid } from '@/utils/util'
import download from '@/utils/downloadFile'
import Mix from '@/mixins/module'
import PreView from '@/components/PreView/index.vue'

export default {
  mixins: [Mix],
  componentName: "FormUpload",
  components: {
    PreView,
  },
  props: {
    tipText: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    value: {
      default: "",
    },
    files: {
      type: Number,
    },
  },
  data() {
    return {
      accessToken: this.$storage.get("access_token"),
      g9s: { g9s: uuid(16, 32) },
      fileList: [],
      fileg9s: this.value,
      uploadFlag: false,
      countFile: 0,
      url: process.env.VUE_APP_BASE_URL + '/sys-storage/upload',
      deleteList: [],
      dialogVisible: false,
      current_file: {},
    }
  },
  computed: {
    countStr() {
      return (
        this.countFile +
        " " +
        (this.countFile === 1 || this.countFile === 0
          ? '个附件'
          : '个附件')
      )
    },
  },
  watch: {
    fileList: {
      deep: true,
      handler(newVal) {
        this.$emit('getFileInfo', newVal);
      }
    },
    value: {
      handler(val) {
        if (val && !this.uploadFlag) {
          this.g9s.g9s = val
          const fileArr = val.split(",")
          getFile({ g9s: fileArr }).then((res) => {
            if (res.status) {
              const filetemp = res.data
              this.fileList = []
              for (let index = 0; index < filetemp.length; index++) {
                this.fileList.push({
                  name: filetemp[index].fileName,
                  // url: filetemp[index].link,
                  size: filetemp[index].size,
                  fileToken: filetemp[index].fileToken,
                })
              }
              this.countFile = this.fileList.length
            }
          })
        } else if (!this.uploadFlag) {
          // this.$emit("input", this.g9s.g9s)
          // this.fileList = []
        } else {
          return
        }
      },
      immediate: true,
    },
    countFile: {
      handler: function() {
        this.$emit('update:files', this.countFile)
        this.$emit('fileChange', this.countFile)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /**
     * @description:文件变动时处理文件
     */
    fileListChange(file) {

    },
    // 文件上传相关操作
    initFile(response, file, fileList) {
      const index = this.fileList.findIndex((item) => item.uid === file.uid)
      if (response.status) {
        const fileg9sTem = response.data.groupToken
        this.uploadFlag = true
        this.$emit("input", fileg9sTem)
        // 检查文件列表中uid为当前项的uid，然后替换
        if (index !== -1) {
          this.fileList.splice(index, 1, file)
        }
        this.countFile++
      } else {
        if (index !== -1) {
          this.fileList.splice(index, 1)
        }
        this.$message.error(response.message);
      }
    },
    deleteFile(file) {
      let token = file.fileToken
      if (file.response) {
        token = file.response.data.fileToken
      }
      // 已有文件的token数组
      const rawTokenList = this.fileList.map((item) => {
        if (item.response) {
          return item.response.data.fileToken
        }
        return item.fileToken
      })
      if (rawTokenList.indexOf(token) !== -1) {
        this.deleteList.push(file)
      }
      this.countFile--
      this.handleFileChange()
    },
    //暂时屏蔽文件下载方法，改为预览
    // downloadFile() {
    //   if (this.current_file.response) {
    //     downloadFile(this.current_file.response.data.fileToken).then(res => {
    //       download(this.current_file, res)
    //     })
    //   } else {
    //     downloadFile(this.current_file.fileToken).then(res => {
    //       download(this.current_file, res)
    //     })
    //   }
    // },
    downloadFile(file) {
      // //console.log('下载文件', file)
      if (file.response) {
        downloadFile(file.response.data.fileToken).then((res) => {
          download(file, res)
        })
      } else {
        downloadFile(file.fileToken).then((res) => {
          download(file, res)
        })
      }
    },
    beforeUpload(file) {
      // 检查g9s
      if (!this.g9s.g9s) {
        this.g9s.g9s = uuid(16, 32)
      } else if (this.g9s.g9s === '[object Object]') {
        this.g9s.g9s = uuid(16, 32)
      }
      // 检查文件类型是否符合上传要求
      if (this.$attrs.accept) {
        let type = getFileSuffix(file.name)
        console.info('type: ', type)
        const isMatch = isMimeMatch(type, this.$attrs.accept.split(','))
        if (!isMatch) {
          this.$message.error("请选择正确格式的文件")
        } else {
          // 将文件push到fileList中
          this.fileList.push(file)
          this.$emit('file', file)
        }
        return isMatch
      }
      this.fileList.push(file)
      this.$emit('file', file)
    },

    /**
     * @description: 用于预览文件
     */
    previewFile(file) {
      this.$set(this, "current_file", { ...file })
      if (this.current_file.fileToken) {
        this.dialogVisible = true
      }
    },
    /**
     * @description: 修改文件时根据表单提交时的文件情况对文件进行删除（增加删除后进行对比）
     */
    handleFileChange() {
      const deleteTokenList = this.deleteList.map((item) => {
        let token = item.fileToken
        if (item.response) {
          token = item.response.data.fileToken
        }
        return token
      })
      if (deleteTokenList.length) {
        deleteFile({ f8s: deleteTokenList }).then((res) => {
          if (res.status) {
            this.deleteList = []
          }
        })
      }
      this.$refs.formUpload.submit()
    }
  },
  created() {
    this.$on("formSubmit", () => {
      this.handleFileChange()
    })
    // this.$emit('input', this.g9s.g9s)
    // if (this.value) {
    //   // 暂时用组token
    //   this.g9s.g9s = this.value
    //   const fileArr = this.value.split(',')
    //   getFile({ g9s: fileArr }).then(res => {
    //     if (res.status) {
    //       const filetemp = res.data
    //       for (let index = 0; index < filetemp.length; index++) {
    //         this.fileList.push({
    //           name: filetemp[index].fileName,
    //           // url: filetemp[index].link,
    //           size: filetemp[index].size,
    //           fileToken: filetemp[index].fileToken
    //         })
    //       }
    //       this.countFile = this.fileList.length
    //     }
    //   })
    // } else {
    //   // this.g9s.g9s = uuid(16, 32)
    //   this.$emit('input', this.g9s.g9s)
    // }
  },
}
