<template>
  <fm-popup
    :close-on-click-modal="false"
    get-container="#app"
    position="bottom"
    :visible.sync="show">
    <fm-datetime-picker
      v-model="currentDate"
      :type="type"
      title="选择时间"
      confirm-button-text="完成"
      cancel-button-text="取消"
      :columns-order="columnsOrder"
      :formatter="formatter"
      @confirm="confirm"
      @cancel="cancel"
    />
  </fm-popup>
</template>

<script>
import {
  Popup,
  DatetimePicker
} from 'fawkes-mobile-lib'
export default {
  name: 'TimePicker',
  props: {
    type: {
      type: String,
      default: 'datetime'
    },
    show: {
      type: Boolean,
      default: false
    },
    time: {
      type: String,
      default: ''
    },
    columnsOrder: {
      type: Array,
      default() {
        return ['hour', 'minute']
      }
    }
  },
  components: {
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
  },
  data() {
    return {
      currentDate: new Date()
    }
  },
  watch: {
    time(newVal){
      this.currentDate = new Date(newVal)
    }
  },
  methods: {
    confirm() {
      this.$emit('update:time', this.type === 'date' ? `${this.$dayjs(this.currentDate).format('YYYY-MM-DD')}` : `${this.$dayjs(this.currentDate).format('YYYY-MM-DD HH:mm')}`);
      this.$emit('confirm')
      this.cancel();
    },
    cancel() {
      this.$emit('update:show', false)
    },
    formatter(type, val) {
      if (type === 'year') {
        return val + '年';
      }
      if (type === 'month') {
        return val + '月';
      }
      if (type === 'day') {
        return val + '日';
      }
      if (type === 'hour') {
        return val + '时';
      }
      if (type === 'minute') {
        return val + '分';
      }
      return val;
    },
  }
}
</script>

<style scoped>

</style>
