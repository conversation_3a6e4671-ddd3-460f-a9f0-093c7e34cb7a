<template>
  <fm-popup
    :close-on-click-modal="false"
    get-container="#app"
    position="bottom"
    @open="selectProjectOpenHandler"
    :visible.sync="show">
    <fm-datetime-picker
      v-model="currentDate"
      :type="type"
      title="选择时间"
      confirm-button-text="完成"
      cancel-button-text="取消"
      :columns-order="columnsOrder"
      :formatter="formatter"
      :filter="customFilter"
      @confirm="confirm"
      @cancel="cancel"
    />
  </fm-popup>
</template>

<script>
import {
  Popup,
  DatetimePicker
} from 'fawkes-mobile-lib'
export default {
  name: 'DateTimePicker',
  props: {
    type: {
      type: String,
      default: 'datetime'
    },
    show: {
      type: Boolean,
      default: false
    },
    time: {
      type: String,
      default: ''
    },
    columnsOrder: {
      type: Array,
      default() {
        return ['year', 'month','day', 'hour', 'minute']
      }
    },
    filter: {
      type: Function
    }
  },
  components: {
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
  },
  data() {
    const currentYear = new Date().getFullYear()
    return {
      minDate: new Date(currentYear, 0, 1),
      maxDate: new Date(currentYear + 3, 11, 31, 23, 59),
      currentDate: ''
    }
  },
  watch: {
    time: {
      immediate: true,
      handler(newVal){
        if (newVal) {
          const date = new Date(newVal);
          // 兼容部分iOS设备时间格式问题
          if (Number.isNaN(date.getTime())) {
            const dateStr = newVal.replace(/-/g, '/');
            this.currentDate= new Date(dateStr);
          } else {
            this.currentDate = date;
          }
        }
      }
    },
    show(newVal) {
      if (newVal) {
        window.addEventListener('popstate', this.preventBack);
      } else {
        window.removeEventListener('popstate', this.preventBack);
      }
    },
  },
  methods: {
    preventBack(e) {
      if (this.show) {
        e.preventDefault(); // 阻止返回
        this.$emit('update:show', false); // 触发父组件更新
      }
    },
    // popup打开
    selectProjectOpenHandler () {
      window.history.pushState(null, null, location.href)
    },
    customFilter(type, options) {
      if (this.filter instanceof Function) {
        return this.filter(type, options)
      } else {
        return options
      }
    },
    confirm() {
      let time;
      switch (this.type) {
        case 'date':
          time = this.$dayjs(this.currentDate).format('YYYY-MM-DD')
          break;
        case 'datetime':
          time = this.$dayjs(this.currentDate).format('YYYY-MM-DD HH:mm')
          break;
        case 'time':
          time = this.currentDate;
      }
      this.$emit('update:time', time);
      this.$emit('confirm', time)
      this.cancel();
    },
    cancel() {
      this.$emit('update:show', false)
    },
    formatter(type, val) {
      if (type === 'year') {
        return val + '年';
      }
      if (type === 'month') {
        return val + '月';
      }
      if (type === 'day') {
        return val + '日';
      }
      if (type === 'hour') {
        return val + '时';
      }
      if (type === 'minute') {
        return val + '分';
      }
      return val;
    },
  },
  created() {
    if (this.type === 'time') {
      this.currentDate = ''
    } else {
      this.currentDate = new Date()
    }
  },
  beforeDestroy() {
    window.removeEventListener('popstate', this.preventBack);
  },
}
</script>

<style scoped>

</style>
