<template>
  <div v-if="isPC">
    <header class="flex col-center row-between">
      <common-title title="驾驶员信息"/>
    </header>
    <fks-row v-if="isEditCar">
      <fks-col :span="24">
        <fks-form-item
          label="姓名"
          prop="driverFullName"
        >
          <fks-select
            v-model="formData.driverFullName"
            filterable
            placeholder="请选择司机"
            class="full-width"
            @change="handleChange"
          >
            <fks-option
              v-for="(item, index) in driverOptions"
              :key="index"
              :label="item.driverFullName"
              :value="item.id"
            >
            </fks-option>
          </fks-select>
        </fks-form-item>
      </fks-col>
      <fks-col :span="24">
        <fks-form-item
          label="联系电话"
          prop="driverPhone"
        >
          <fks-input
            v-model="formData.driverPhone"
            readonly
            placeholder=""
            @input="changeDriver"
          />
        </fks-form-item>
      </fks-col>
      <fks-col :span="24">
        <fks-form-item
          label="驾龄(年)"
          prop="driverAge"
        >
          <fks-input
            v-model.number="formData.driverAge"
            readonly
            placeholder=""
            @input="$forceUpdate"
          />
        </fks-form-item>
      </fks-col>
    </fks-row>
    <div v-else class="table">
      <fks-form-item label="姓名">
        <fks-input disabled readonly :value="isCarInfo ? vdDriverInfo.driverFullName || '-' : formData.driverFullName || '-'" />
      </fks-form-item>
      <fks-form-item label="联系电话">
        <fks-input disabled readonly :value="isCarInfo ? vdDriverInfo.driverPhone || '-' : formData.driverPhone || '-'" />
      </fks-form-item>
      <fks-form-item label="驾龄（年）">
        <fks-input disabled readonly :value="isCarInfo ? vdDriverInfo.driverAge || '-' : formData.driverAge || '-'" />
      </fks-form-item>
    </div>
  </div>
  <div v-else-if="isMobile">
    <blank style="margin-left: -5px"/>
    <div id="carInfo" class="flex col-center row-between m-r-32">
      <div class="sty_h">驾驶员信息</div>
      <fm-button
        v-if="isEditCar && showChooseButton"
        size="small"
        type="primary"
        @click="chooseCarDriver"
      >选择
      </fm-button>
    </div>
    <template v-if="isEditCar">
      <fm-field
        v-model="formData.driverFullName"
        :rules="getFieldRule('driverPhone', this)"
        input-align="right"
        label="姓名"
        name="driverFullName"
        :placeholder="flowConfig.driverFullName === 'readonly' ? '姓名' : '请选择'"
        required
        readonly
        @change="changeDriver"
      />
      <fm-field
        v-model="formData.driverPhone"
        :rules="getFieldRule('driverPhone', this)"
        input-align="right"
        label="联系电话"
        name="carNum"
        :placeholder="flowConfig.driverPhone === 'readonly' ? '联系电话' : '请选择'"
        required
        type="tel"
        readonly
        @change="changeDriver"
      />
      <fm-field
        v-model="formData.driverAge"
        input-align="right"
        label="驾龄(年)"
        name="driverAge"
        :placeholder="flowConfig.driverAge === 'readonly' ? '驾龄' : '请选择'"
        type="number"
        readonly
        @change="$forceUpdate"
      />
    </template>
    <fm-row v-else class="person-table">
      <fm-col :span="8" class="h-64 p-24">
        <div class="flex-content font-28 line-height-30">姓名</div>
      </fm-col>
      <fm-col :span="8" class="h-64 p-24 border-left border-right">
        <div class="flex-content font-28 line-height-30">联系电话</div>
      </fm-col>
      <fm-col :span="8" class="h-64 p-24">
        <div class="flex-content font-28 line-height-30">驾龄(年)</div>
      </fm-col>
      <fm-col :span="8" class="border-top">
        <div class="flex-content font-28 line-height-40 p-10">
          {{ isCarInfo ? vdDriverInfo.driverFullName || '-' : formData.driverFullName || '-' }}
        </div>
      </fm-col>
      <fm-col :span="8" class="border-left border-right border-top">
        <div class="flex-content font-28 line-height-40 p-10">
          {{ isCarInfo ? vdDriverInfo.driverPhone || '-' : formData.driverPhone || '-' }}
        </div>
      </fm-col>
      <fm-col :span="8" class="border-right border-top">
        <div class="flex-content font-28 line-height-40 p-10">
          {{ isCarInfo ? vdDriverInfo.driverAge || '-' : formData.driverAge || '-' }}
        </div>
      </fm-col>
    </fm-row>
  </div>
</template>
<script>
import platform from "../../../../mixins/platform";
import "../../../FormCenter/CarApply/index.less"
import Blank from '@/components/Blank/index.vue';
import { mapActions, mapState } from 'vuex'
import { getDriverList } from '@modules/ProjectCar/ProjectPortal/DriverManage/api'

export default {
  mixins: [platform],
  components: {Blank},
  name: 'DriverSelector',
  props: ['isEditCar', 'chooseCarDriver', 'formData', 'changeDriver',
    'phoneValidator', 'isCarInfo', 'vdDriverInfo', 'formatPhoneNumber',
    'showChooseButton', 'flowConfig','type', 'rules'
  ],
  data() {
    return {
      driverOptions: [],
      currentPage: 1,
      pageSize: 100,
    }
  },
  computed: {
    ...mapState(['portal']),
    watchText() {
      const a = this.formData.projectId || '';
      const b = this.formData.flagBb || '';
      return a.toString() + '%-%' + b.toString();
    },
  },
  methods: {
    ...mapActions('CarApply', ['getDriverCar']),
    getFieldRule(fieldName, context) {
      const rules = this.rules[fieldName];
      if (!rules) return [];

      // 如果包含自定义 validator，要绑定 this（上下文）
      let list = rules.map(rule => {
        if (typeof rule.validator === 'function') {
          const originalValidator = rule.validator;
          return {
            ...rule,
            validator: originalValidator.bind(context)
          };
        }
        return rule;
      })
      return list;
    },
    handleChange(id) {
      const item = this.driverOptions.find(item => item.id === id);
      this.$emit('selectDriver', item)
      this.$forceUpdate();
    },
    async fetchDriverList() {
      if (this.isEditCar) {
        const projectId = this.formData.projectId || this.portal.id;
        const projectName = this.formData.projectName || this.portal.id;
        const projectParams = (projectId && projectName) ? {projectId, projectName} : {};
        const {status, data} = await getDriverList({
          pageNo: this.currentPage,
          pageSize: this.pageSize,
          ...projectParams
        });
        if (status) {
          // 仅展示在职的司机
          this.driverOptions = data.list.filter(item => item.driverStatus === 100);
        }
      }
    }
  },
  watch: {
    watchText: {
      deep: true,
      immediate: true,
      async handler(newVal) {
        const [projectId, flagBb] = newVal.split('%-%');
        if (projectId || flagBb) {
          await this.fetchDriverList();
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
@import "~@/styles/disabled";
@import "~@/styles/input";
.border-line {
  border: 1px solid #eee;
  margin: 0 32px 32px;
}

.align-sub {
  vertical-align: sub;
}

.text-title {
  font-size: 32px;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 0;
}

</style>
