<template>
  <div v-if="isPC" style="margin-top: 15px">
    <header class="flex col-center row-between">
      <common-title title="车辆信息"/>
<!--      <fks-button-->
<!--        v-if="isEditCar && showChooseButton"-->
<!--        size="mini"-->
<!--        type="primary"-->
<!--        @click="chooseCar"-->
<!--      >-->
<!--        选择车辆-->
<!--      </fks-button>-->
    </header>
    <fks-row v-if="isEditCar">
      <fks-col :span="24">
        <fks-form-item
          label="车牌号"
          prop="carNum"
        >
          <fks-select
            v-model="formData.carNum"
            filterable
            placeholder="请选择车辆"
            class="full-width"
            no-match-text=" "
            @change="handleChange"
          >
            <fks-option
              v-for="(item, index) in carOptions"
              :key="index"
              :label="item.carName"
              :value="item.id"
            >
              <div class="flex col-center row-between">
                <div class="flex col-center">
                  <div class="m-r-10">{{item.carNum}}</div>
                  <div class="m-r-10">{{item.carName}}</div>
                  <div>{{item.carSeatNum}}座</div>
                </div>
                <card-tag style="line-height: 12px" :tag="getCardTag(item)" />
              </div>
            </fks-option>
          </fks-select>
        </fks-form-item>
      </fks-col>
      <fks-col :span="24">
        <fks-form-item
          label="车型"
          prop="carType"
        >
          <fks-input
            v-model="formData.carType"
            readonly
            placeholder=""
            @input="changeCar"
          />
        </fks-form-item>
      </fks-col>
      <fks-col :span="24">
        <fks-form-item
          label="车龄(年)"
          prop="carAge"
        >
          <fks-input
            v-model="formData.carAge"
            readonly
            placeholder=""
          />
        </fks-form-item>
      </fks-col>
    </fks-row>
    <div v-else class="table">
      <fks-form-item label="车牌号">
        <fks-input disabled readonly :value="isCarInfo ? vdCarInfo.carNum || '-' : formData.carNum || '-'" />
      </fks-form-item>
      <fks-form-item label="车型">
        <fks-input disabled readonly :value="isCarInfo ? vdCarInfo.carType || '-' : formData.carType || '-'" />
      </fks-form-item>
      <fks-form-item label="车龄（年）">
        <fks-input disabled readonly :value="isCarInfo ? vdCarInfo.carAge || '-' : formData.carAge || '-'" />
      </fks-form-item>
    </div>
  </div>
  <div v-else-if="isMobile">
    <blank style="margin-left: -5px"/>
    <div id="carInfo" ref="carInfoRef" class="flex col-center row-between m-r-32">
      <div class="sty_h">车辆信息</div>
      <fm-button
        v-if="isEditCar && showChooseButton"
        size="small"
        type="primary"
        @click="chooseCar"
      >选择
      </fm-button>
    </div>
    <template v-if="isEditCar">
      <fm-field
        v-model="formData.carNum"
        :rules="getFieldRule('carNum', this)"
        input-align="right"
        label="车牌号"
        name="carNum"
        :placeholder="flowConfig.carNum === 'readonly' ? '车牌号' : '请选择'"
        required
        readonly
        @change="changeCar"
      />
      <fm-field
        v-model="formData.carType"
        :rules="getFieldRule('carType', this)"
        input-align="right"
        label="车型"
        name="carType"
        :placeholder="flowConfig.carType === 'readonly' ? '车型' : '请选择'"
        required
        readonly
        @change="changeCar"
      />
      <fm-field
        v-model="formData.carAge"
        input-align="right"
        label="车龄(年)"
        name="carAge"
        :placeholder="flowConfig.carAge === 'readonly' ? '车龄' : '请选择'"
        type="number"
        readonly
        @change="changeCar"
      />
    </template>
    <fm-row v-else class="person-table">
      <fm-col :span="8" class="h-64 p-24">
        <div class="flex-content font-28 line-height-30">车牌号</div>
      </fm-col>
      <fm-col :span="8" class="h-64 p-24 border-left border-right">
        <div class="flex-content font-28 line-height-30">车型</div>
      </fm-col>
      <fm-col :span="8" class="h-64 p-24">
        <div class="flex-content font-28 line-height-30">车龄(年)</div>
      </fm-col>
      <fm-col :span="8" class="border-top">
        <div class="flex-content font-28 line-height-40 p-10">
          {{ isCarInfo ? vdCarInfo.carNum || '-' : formData.carNum || '-' }}
        </div>
      </fm-col>
      <fm-col :span="8" class="border-left border-right border-top">
        <div class="flex-content font-28 line-height-40 p-10">
          {{ isCarInfo ? vdCarInfo.carType || '-' : formData.carType || '-' }}
        </div>
      </fm-col>
      <fm-col :span="8" class="border-right border-top">
        <div class="flex-content font-28 line-height-40 p-10">
          {{ isCarInfo ? vdCarInfo.carAge || '-' : formData.carAge || '-' }}
        </div>
      </fm-col>
    </fm-row>
  </div>
</template>
<script>
import platform from "../../../../mixins/platform";
import "../../../FormCenter/CarApply/index.less"
import Blank from '@/components/Blank/index.vue';
import {mapActions, mapState} from "vuex";
import {getReservationInfo} from "@modules/FormCenter/CarApply/CarList/api";
import CardTag from "@components/CardFlow/components/tag.vue";
import { getCarList, getCarListByPost } from '@modules/ProjectCar/ProjectPortal/CarManage/api'

export default {
  mixins: [platform],
  components: {CardTag, Blank},
  name: 'carSelector',
  props: ['isEditCar', 'chooseCar', 'carNumValidator', 'formData', 'changeCar', 'isCarInfo', 'vdCarInfo', 'showChooseButton', 'flowConfig', 'type', 'rules'],
  data() {
    return {
      car: '',
      carOptions: [],
      currentPage: 1,
      pageSize: 100,
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    carStatus() {
      return this.enums.CarStatusEnums;
    },
    watchText() {
      const a = this.formData.projectId || '';
      const b = this.formData.flagBb || '';
      return a.toString() + '%-%' + b.toString();
    }
  },
  methods: {
    getFieldRule(fieldName, context) {
      const rules = this.rules[fieldName];
      if (!rules) return [];

      // 如果包含自定义 validator，要绑定 this（上下文）
      let list = rules.map(rule => {
        if (typeof rule.validator === 'function') {
          const originalValidator = rule.validator;
          return {
            ...rule,
            validator: originalValidator.bind(context)
          };
        }
        return rule;
      })
      return list;
    },
    getCardTag(item) {
      // 当正常情况下
      if (item.carStatus === 100) {
        return {text: item.status, color: item.status === '空闲中' ? '#03BE8A' : '#FF3F4C'}
      } else {
        if (this.carStatus) {
          const result = this.carStatus.find(el => el.key === item.carStatus);
          if (result) {
            return {text: result.value, color: '#cccccc'}
          }
        }
      }
    },
    handleChange(id) {
      const item = this.carOptions.find(item => item.id === id);
      this.$emit('selectCar', item)
      this.$forceUpdate();
    },
    async chooseCarByNum(carId) {
      await this.fetchCarList();
      this.handleChange(carId);
    },
    async fetchCarList() {
      if (this.isEditCar) {
        // 获取车辆信息
        const projectId = this.formData.projectId || this.portal.id;
        const projectName = this.formData.projectName || this.portal.name;
        const projectParams = (projectId && projectName) ? {projectId, projectName} : {};
        const {status, data} = await getCarList({
          pageNo: this.currentPage,
          pageSize: this.pageSize,
          conditions: [],
          ...projectParams
        });
        if (status) {
          const ids = data.list.map(item => item.id).join(',');
          const {data: preserveInfos} = await getReservationInfo(ids);
          // 只显示车辆状态为正常的车辆，维修，退场不显示
          this.carOptions = data.list.filter(car => car.carStatus === 100).map(item => {
            let statusText = '空闲中';
            const infos = preserveInfos ? preserveInfos[item.id] : null;
            if (infos && infos.length > 0) {
              statusText = '已预约';
            }
            return {
              ...item,
              status: statusText
            }
          })
        }
      }
    },
  },
  watch: {
    watchText: {
      deep: true,
      immediate: true,
      async handler(newVal) {
        const [projectId, flagBb] = newVal.split('%-%');
        if (projectId || flagBb) {
          await this.fetchCarList();
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.border-line {
  border: 1px solid #eee;
  margin: 0 32px 32px;
}

.sty-cell {
}

.align-sub {
  vertical-align: sub;
}

.text-title {
  font-size: 32px;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 0;
}
</style>
