<template>
  <flow-table class="max-width-1400" :data="comments" />
</template>
<script>
import FlowTable from "@components/FlowTable/index.vue";
import {getProcessHistory} from "@modules/FormCenter/api";
export default {
  name: 'CustomFlowTable',
  components: {FlowTable},
  props: ['bizId'],
  data() {
    return {
      comments: []
    }
  },
  created() {
    getProcessHistory({bizId: this.bizId}).then(
      (res) => {
        this.comments = res.data
      }
    )
  }
}
</script>
