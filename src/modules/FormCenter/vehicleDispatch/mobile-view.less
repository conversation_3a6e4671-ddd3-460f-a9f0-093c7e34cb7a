@import "../CarApply/index";
.vehicle-applcation-container {
  .sty_h {
    padding-left: 0px !important;
  }
  .description-cell > .fm-cell__title {
    padding-left: 20px !important;
  }
  /deep/ .fm-field__label {
    padding-left: 20px !important;
  }
  /deep/ .fm-cell--required::before {
    left: 0;
  }
  /deep/ .fm-field__control {
    padding-left: 22px !important;
  }
  /deep/ .fm-cell {
    padding: 24px 0;
    border-bottom: 1px solid rgba(25, 25, 25, 0.07);
  }
  .no-line {
    border-bottom: transparent !important;
  }
  .map-box {
    /deep/ .fm-cell {
      border-bottom: transparent !important;
    }
    /deep/ .fm-cell::after {
      border-bottom: transparent !important;
    }
  }
  .card-container {
    /deep/ .fm-cell {
      border-bottom: transparent !important;
    }
  }
  /deep/ .fm-collapse-item__content {
    padding-left: 0;
    padding-right: 0;
  }
  .driving-log {
    .description-cell {
      border-bottom: transparent !important;
    }
  }
  .left-container {
    .mobile-revers-btn {
      left: -20px;
    }
  }
  /deep/ .fm-cell--clickable:active {
    background-color: #fff !important;
  }
  /deep/ .trip-num::placeholder {
    color: #1919194D !important;
  }
  /deep/ .trip-num:disabled {
    background-color: transparent !important;
  }
  /deep/ textarea::placeholder,
  /deep/ input::placeholder {
    color: #1919194D !important;
  }
}
.sty_h {
  padding-left: 0px !important;
}
/deep/ .fm-badge--fixed {
  top: 24px;
  right: 40px;
}

/deep/ .fm-badge {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  padding: 0;
  font-size: 24px;
}

/deep/ .upload-cell.fm-hairline--top-bottom::after{
  border-width: 0;
}

/deep/ .fm-field__button {
  line-height: 14px;
  padding: 0;
}

.apply-person-card {
  background: #F4F7FD;
  border-radius: 12px;
  border: 0;
  padding: 0px 10px 10px 20px;
  .use-car {
    padding-bottom: 0px !important;
  }
  /deep/ .fm-cell-group--inset {
    margin: 0;
  }
  /deep/ .fm-field__control {
    margin-left: 10px !important;
  }
  /deep/ input:-internal-autofill-selected {
    background-color: #fff !important;
  }
}
.remark1 {
  padding-top: 0 !important;
}
.remark2 {
  /deep/ .fm-field {
    padding-top: 0;
  }
  /deep/ .fm-field__control {
    border-bottom: 1px solid rgba(25, 25, 25, 0.07);
  }
  .description-cell {
    border-bottom: transparent !important;
  }
}
.map-box {
  color: #3C83FF;
  /deep/ .fm-cell__left-icon {
    color: #3C83FF;
  }
  /deep/ .fm-cell__title > span {
    color: #3C83FF;
  }
}

.custom-class {
  /deep/ .blank-space {
    margin-left: -30px !important;
  }
  /deep/ #carInfo {
    margin-right: 0px !important;
  }
  /deep/ .sty_h {
    padding-left: 0px !important;
  }
  /deep/ .person-table {
    margin-left: 0px !important;
    margin-right: 0px !important;
  }
}
