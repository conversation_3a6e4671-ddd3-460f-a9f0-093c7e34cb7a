<template>
  <div v-if="isMobile" class="flex flex-column full-height">
    <fm-nav-bar
        :border="false"
        left-arrow
        title="选择司机"
        @click-left="customBack"
    >
    </fm-nav-bar>
    <fm-search
        v-model="searchValue"
        maxlength="50"
        placeholder="请输入司机名称、用户名、手机号、驾龄"
        @clear="onClear"
        @search="onSearch"
    />
    <fm-pull-refresh
      v-model="refreshing"
      class="flex-grow-1 overflow-y-auto"
      @refresh="refreshList"
    >
      <fm-list
          v-model="loading"
          :finished="finished"
          error-text="重新加载"
          finished-text="已经到底啦~"
          @load="onLoad"
      >
        <fm-cell-group :border="false">
          <template v-for="(item) in list">
            <div @click="tapItem(item)">
              <fm-radio
                class="m-l-46 user-cell"
                v-model="radio"
                :key="item?.id"
                :name="item?.id"
              >
                <fm-cell
                  :arrow="false"
                  :border-bottom="false"
                >
                  <template slot="icon">
                    <img class="car-view" src="@/assets/img/car/no-head.png"/>
                  </template>
                  <template slot="title">
                    <div class="m-l-24">
                      <div class="car-driver">
                        <span>{{ item?.driverFullName }}</span>
                        <!--                  <span class="m-l-10 d-inline-block">{{ item.driverUserName }}</span>-->
                      </div>
                      <div class="car-user color-black3 font-bold m-t-20">
                        <span>{{ item?.driverPhone.substring(0, 3) }}-{{ item?.driverPhone.substring(3, 7) }}-{{ item?.driverPhone.substring(7) }}</span>
                      </div>
                    </div>
                  </template>
                  <div slot="right-icon">
                    <!--              <div v-if="item.checkType" class="car-status text-center use">可派</div>-->
                    <!--              <div v-else class="car-status text-center no-use">不可派</div>-->
                    <div class="m-t-20 car-seat-count ">
                      <div class="drive-age-text text-center">驾龄</div>
                      <div class="drive-age text-center">{{ item?.driverAge }}</div>
                    </div>
                  </div>
                </fm-cell>
                <template #icon="props">
                  <img v-if="props.checked" src="@/assets/img/car/radio-checked.png" class="img-icon">
                  <img v-else src="@/assets/img/car/radio.png" class="img-icon">
                </template>
              </fm-radio>
            </div>
          </template>
        </fm-cell-group>
      </fm-list>
    </fm-pull-refresh>
    <flow-button :onSubmit="onSubmit" submit-text="确定"></flow-button>
  </div>
  <fks-table
      v-else
      :data="list"
      v-loading="loading"
      @current-change="tapItem"
  >
    <fks-table-column  type="selection" width="80" align="center">
      <template slot-scope="{row}">
        <fks-radio v-model="radio" :label="row.vdDriverInfo?.id">&nbsp;</fks-radio>
      </template>
    </fks-table-column>
    <fks-table-column prop="driverFullName" label="司机姓名">
      <template slot-scope="{row}">
        {{row.vdDriverInfo?.driverFullName}}
      </template>
    </fks-table-column>
    <fks-table-column prop="driverPhone" label="联系电话">
      <template slot-scope="{row}">
        {{row.vdDriverInfo?.driverPhone}}
      </template>
    </fks-table-column>
    <fks-table-column prop="driverAge" label="驾龄">
      <template slot-scope="{row}">
        {{row.vdDriverInfo?.driverAge}}
      </template>
    </fks-table-column>
  </fks-table>
</template>

<script>
import { Cell, CellGroup, List, Radio, RadioGroup, Toast } from 'fawkes-mobile-lib'
import FlowButton from '@/modules/FormCenter/components/FlowButton/index.vue';
import {mapMutations, mapState} from 'vuex';
import platform from "@/mixins/platform";
import { getDriverList } from '@modules/ProjectCar/ProjectPortal/DriverManage/api'

export default {
  name: 'DriverList',
  mixins: [platform],
  props: {
    carCompanyInfoId: {
      type: String,
      default: ''
    },
    formData: {
      type: Object
    }
  },
  data() {
    return {
      radio: '',
      loading: false,
      finished: false,
      refreshing: false,
      searchValue: '',
      list: [],
      ids: [],
      chooseItem: [],
      page: 1,
      total: 0,
      pageSize: 100,
      // account: uni.getStorageSync('account')
    }
  },
  computed: {
    ...mapState(['portal']),
  },
  watch: {
    carCompanyInfoId: {
      handler(val) {
        if (val) {
          this.onSearch()
        }
      },
      immediate: true
    }
  },
  components: {
    FlowButton,
    Toast,
    [List.name]: List,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio
  },
  created() {
    this.isPC ? this.onSearch() : '';
  },
  methods: {
    ...mapMutations('CarApply', ['SET_APPLY_CAR']),
    swipeHandler() {
      this.customBack();
    },
    onSearch() {
      this.refreshList()
    },
    onClear() {
      this.searchValue = '';
      this.refreshList()
    },
    onLoad() {
      // if (this.carCompanyInfoId) {
      //   this.getList();
      // }
      this.getList();
    },
    /**
     * 刷新列表（从第一页开始）
     */
    async refreshList() {
      this.page = 0;
      this.list = [];
      this.total = 0;
      this.loading = false;
      this.finished = false;
      this.refreshing = false;
      await this.getList();
    },
    async getList() {
      const {list} = this;
      if (this.refreshing) {
        this.list = [];
        this.refreshing = false;
      }
      const params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        searchValue: this.searchValue
      }
      this.loading = true;
      try {
        const projectId = this.formData.projectId || this.portal.id;
        const projectName = this.formData.projectName || this.portal.name;
        const projectParams = (projectId && projectName) ? {projectId, projectName} : {};
        const {status, data} = await getDriverList({...params, ...projectParams});
        if (!status) {
          this.loading = false;
          this.finished = true;
          return false;
        }
        // 过滤掉非在职状态的司机
        const validDrivers = data.list.filter(driver => driver.driverStatus === 100);
        this.list = list.concat(validDrivers);
        // let total = data.total - (page + 1) * pageSize > 0 ? data.total - (page + 1) * pageSize : 0;
        this.total = this.list.length;
        if (this.list.length >= this.total) {
          this.loading = false;
          this.finished = true;
        } else {
          this.loading = false;
          this.page++;
        }
      } catch {
        this.loading = false;
      }
    },
    customBack() {
      this.$emit('closePopupShow');
    },
    tapItem(item) {
      console.log("item", item)
      // this.ids.includes(item.id) ? this.ids.splice(this.ids.indexOf(item.id), 1) : this.ids.push(item.id);
      this.ids = [item.id];
      this.radio = item.id;
      this.chooseItem = [item];
    },
    async getDriverById(id) {
      console.log("id", id)
      this.getList();
      const item = this.list.find(i => i.id === id);
      console.log("selectItem", item)
      if (item) {
        this.ids = [item.id];
        this.radio = item.id;
        this.chooseItem = [item];
        this.SET_APPLY_CAR(item);
        this.$emit('closePopup', item);
      } else {
        if (this.isMobile) {
          Toast({
            icon: 'none',
            message: '未找到对应的驾驶员'
          });
        } else {
          this.$message.warning('未找到对应的驾驶员');
        }
      }
    },
    onSubmit() {
      if (this.chooseItem.length === 0) {
        if (this.isMobile) {
          Toast({
            icon: 'none',
            message: '请选择驾驶员'
          });
        } else {
          this.$message.warning('请选择驾驶员')
        }
        return false;
      }
      this.SET_APPLY_CAR(this.chooseItem[0]);
      this.$emit('closePopup', this.chooseItem[0])
    }
  }
}
</script>

<style lang='less' scoped>
@import "../CarList/index.less";
</style>
