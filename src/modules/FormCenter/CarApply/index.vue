<template>
  <!-- 设置表单ref值，FormCenter会使用到 -->
  <!--  v-touch:swipe="swipeHandler"-->
  <!--  v-zswipeleft='swipeHandler'-->
  <div>
    <fm-form v-if="isMobile" ref="form" validate-first>
      <!--      原则上司机住宿费和餐费自理，若我方邀请共同用餐，则餐费由我方承担-->
      <fm-cell
        v-if="type === 'add'"
        :title="formData.remindTitle"
        title-class="prompt-title"
        value=""
        value-class="prompt-value"
      />

      <div class="m-l-10">
        <template v-if="isUser || taskKey === 'UserTask_5'">
          <div class="sty_h">{{ isXcModify ? '行程信息' : '用车信息' }}</div>
          <fm-field
            v-model="formData.startTime"
            :is-link="flowConfig.startTime !== 'readonly'"
            :placeholder="flowConfig.startTime == 'readonly' ? '' : '请选择'"
            :rules="[{ required: true, message: '请选择出车时间'  }]"
            error-message-align="right"
            clickable
            input-align="right"
            label-width="10em"
            label="出车时间"
            name="startTime"
            readonly
            required
            @click.stop.native="
                  flowConfig.startTime == 'readonly' ? '' : (showCalendarStart = true)
                "
          />
          <date-time-picker
            v-if="isMobile"
            :show.sync="showCalendarStart"
            :time.sync="formData.startTime"
            title="出车时间"
            type="datetime"
          />

          <fm-field
            v-model="formData.predictEndTime"
            :is-link="flowConfig.predictEndTime != 'readonly'"
            :placeholder="flowConfig.predictEndTime == 'readonly' ? '' : '请选择'"
            :rules="[
              { required: true, message: '请选择预计返回时间' },
              { validator: endTimeValidator, message: '返回时间不能早于出发时间' }
            ]"
            error-message-align="right"
            clickable
            input-align="right"
            label="预计返回时间"
            label-width="10em"
            name="predictEndTime"
            readonly
            required
            @click.stop.native="
                flowConfig.predictEndTime == 'readonly' ? '' : (showCalendarEnd = true)
              "
          />
          <date-time-picker
            :show.sync="showCalendarEnd"
            :time.sync="formData.predictEndTime"
            label-width="10em"
            title="预计返回时间"
            type="datetime"
          ></date-time-picker>
        </template>
        <!--        <fm-field-->
        <!--          v-if="isShowDriverConfirmEndTime"-->
        <!--          v-model="formData.vdDriverConfirmEndTime"-->
        <!--          :rules="[{ required: true }]"-->
        <!--          clickable-->
        <!--          input-align="right"-->
        <!--          label="行程开始时间"-->
        <!--          label-width="10em"-->
        <!--          name="vdDriverConfirmEndTime"-->
        <!--          required-->
        <!--          readonly-->
        <!--        />-->
        <template v-if="flowConfig.startAddress == 'readonly'">
          <fm-field
            v-model="formData.startAddress"
            :readonly="true"
            :rules="[{ required: true }]"
            input-align="right"
            label="出发地"
            name="startAddress"
            required
          />
          <fm-cell-group v-show="showOther"
                         title="">
            <fm-cell :required="showOther"
                     class="description-cell"
                     title="出发地详情">
            </fm-cell>
            <fm-field
              v-model="formData.startAddressDetail"
              :placeholder="
                flowConfig.startAddressDetail == 'readonly' ? '' : '请输入出发地详情'
              "
              :readonly="flowConfig.startAddressDetail == 'readonly'"
              :required="showOther"
              :rules="[{ required: showOther, message: '请输入出发地详情' }]"
              maxlength="500"
              type="textarea"
            />
          </fm-cell-group>
        </template>
        <template v-else>
          <fm-field
            v-model="formData.startAddressStr"
            :is-link="flowConfig.startAddress != 'readonly'"
            :rules="[{ required: true, message: '请选择出发地' }]"
            :show-error="false"
            :show-error-message="false"
            clickable
            error-message-align="right"
            input-align="right"
            label="出发地"
            name="startAddress"
            readonly
            required
            scroll-to-error
            @click.stop.native="
            flowConfig.startAddress == 'readonly' ? '' : (showPickerStartAddress = true)
          "
          />
          <select-picker
            :code.sync="formData.startAddress"
            :show.sync="showPickerStartAddress"
            :text.sync="formData.startAddressStr"
            name="StartAddressEnums"
            @confirm="handleStartAddressChange"
          ></select-picker>

          <!--          <fm-field-->
          <!--            v-show="formData.startAddress === '9'"-->
          <!--            v-model="formData.startAddressDetail"-->
          <!--            type="textarea"-->
          <!--            required-->
          <!--            :rules="[{ required: true }]"-->
          <!--            input-align="right"-->
          <!--            clickable-->
          <!--            name="endAddressDetail"-->
          <!--            label="出发地详情"-->
          <!--            placeholder="请输入出发地详情"-->
          <!--          >-->
          <!--            <template #button>-->
          <!--              <fm-button class="ml-2" size="small" :icon="mapShowStart ? 'fks-icon-arrow-down' : 'fks-icon-arrow-up'" @click="getLocationStart">{{ mapShowStart ? '收起' : '展开' }}地图</fm-button>-->
          <!--            </template>-->
          <!--          </fm-field>-->
          <fm-cell-group v-show="formData.startAddress === '9'" title="">
            <fm-cell :required="formData.startAddress === '9'" class="description-cell"
                     title="出发地详情">
              <template #default>
                <fm-button :icon="mapShowStart ? 'fks-icon-arrow-down' : 'fks-icon-arrow-up'"
                           class="ml-2"
                           size="small"
                           @click="getLocation('start')">选择地点
                </fm-button>
              </template>
            </fm-cell>
            <fm-field
              v-show="formData.startAddressDetail"
              v-model="formData.startAddressDetail"
              :required="formData.startAddress === '9'"
              :rules="[{ required: formData.startAddress === '9', message: '请输入出发地详情' }]"
              maxlength="500"
              readonly
              type="textarea"
            />
          </fm-cell-group>
          <!--          <div v-if="loadMapStart">-->
          <!--            <a-map v-show="mapShowStart" class="m-16" :location-info.sync="locationInfoStart" @update="handleMapUpdateStart" />-->
          <!--          </div>-->
        </template>
        <template v-if="flowConfig.endAddress == 'readonly'">
          <fm-cell-group title="">
            <fm-cell class="description-cell" required title="目的地">
            </fm-cell>
            <fm-field
              v-if="flowConfig.endAddress == 'readonly'"
              v-model="formData.endAddress"
              :readonly="true"
              :rules="[{ required: true, message: '请输入目的地'}]"
              error-message-align="right"
              label=""
              maxlength="500"
              name="endAddress"
              required
              type="textarea"
            />
          </fm-cell-group>
          <fm-field
            v-if="flowConfig.endAddress == 'readonly'"
            v-model="formData.endAddressDetail"
            input-align="right"
            label="目的地备注"
            name="endAddressDetail"
            readonly
            type="textarea"
            maxlength="500"
          />
        </template>
        <template v-else>
          <fm-cell-group title="">
            <fm-cell class="description-cell" required title="目的地">
              <template #default>
                <fm-button :icon="mapShow ? 'fks-icon-arrow-down' : 'fks-icon-arrow-up'"
                           class="ml-2"
                           size="small"
                           @click="getLocation('end')">选择地点
                </fm-button>
              </template>
            </fm-cell>
            <fm-field
              v-show="formData.endAddress"
              v-model="formData.endAddress"
              :rules="[{ required: true, message: '请输入目的地' }]"
              maxlength="500"
              name="endAddress"
              readonly
              required
              type="textarea"
            />
          </fm-cell-group>
          <fm-cell v-show="formData.endAddress" class="description-cell pl-10" title="目的地备注"></fm-cell>
          <fm-field
            v-show="formData.endAddress"
            v-model="formData.endAddressDetail"
            :readonly="flowConfig.endAddressDetail === 'readonly'"
            error-message-align="right"
            type="textarea"
            show-word-limit
            maxlength="500"
          />
        </template>
        <template v-if="isXcModify">
          <blank style="margin-left: -5px"/>
          <div class="sty_h">用车信息</div>
        </template>
        <fm-cell-group title="">
          <fm-cell class="description-cell pl-10" required title="出车事由"></fm-cell>
          <fm-field
              v-model="formData.useCarMatter"
              :readonly="flowConfig.useCarMatter == 'readonly'"
              :rules="[{ required: true, message: '请输入出车事由' }]"
              error-message-align="right"
              maxlength="500"
              required
              show-word-limit
              type="textarea"
          />
        </fm-cell-group>
        <fm-field
            v-model="formData.contacts"
            :is-link="flowConfig.contacts !== 'readonly'"
            :placeholder="flowConfig.needCarType2 === 'readonly'? '' : '请选择'"
            :rules="[{ required: true }]"
            clickable
            input-align="right"
            label="联系人"
            name="contacts"
            readonly
            required
            @click.stop.native="flowConfig.needCarType2 === 'readonly' ?  '' : chooseApplyPerson({}, -1)"
        />

        <!--        <select-user-->
        <!--          v-else-->
        <!--          title="联系人"-->
        <!--          :userFullname.sync="formData.contactsFullName"-->
        <!--          :userName.sync="formData.contacts"-->
        <!--          :disabled="flowConfig.contacts == 'readonly'"-->
        <!--          :multiple="false"-->
        <!--          :required="true"-->
        <!--        >-->
        <!--        </select-user>-->
        <fm-field
            v-model="formData.contactsPhone"
            :readonly="flowConfig.contactsPhone == 'readonly'"
            :rules="[{ required: true },  { validator: this.phoneValidator, message: '请输入正确手机格式' }]"
            input-align="right"
            label="联系电话"
            required
            type="tel"
        />
        <fm-field
          v-if="flowConfig.useCarType === 'readonly'"
          v-model="formData.useCarTypeStr"
          :readonly="true"
          :rules="[{ required: true }]"
          input-align="right"
          label="用车类型"
          name="useCarType"
          required
        />
        <template v-else>
          <fm-field
            v-model="formData.useCarTypeStr"
            :is-link="flowConfig.useCarType != 'readonly'"
            :rules="[{ required: true, message: '请选择用车类型' }]"
            clickable
            error-message-align="right"
            input-align="right"
            label="用车类型"
            name="useCarType"
            readonly
            required
            @click.stop.native="
            flowConfig.useCarType == 'readonly' ? '' : (showPickerUseCarType = true)
          "
          />
          <select-picker
            :code.sync="formData.useCarType"
            :show.sync="showPickerUseCarType"
            :text.sync="formData.useCarTypeStr"
            name="UseCarTypeEnums"
            @confirm="confirmUseCarType"
          ></select-picker>
        </template>
        <fm-field
          v-if="isCarModify"
          v-model="formData.realCarTypeStr"
          readonly
          :rules="[{ required: true }]"
          clickable
          input-align="right"
          label="车型"
          name="realCarType"
          required
        />
        <fm-field
          v-else-if="!isShowCarCompany"
          v-model="formData.needCarType2Str"
          :is-link="flowConfig.needCarType2 !== 'readonly'"
          :rules="[{ required: true, message: '请选择车型' }]"
          clickable
          error-message-align="right"
          input-align="right"
          label="车型"
          name="needCarType2"
          readonly
          required
          @click.stop.native="flowConfig.needCarType2 === 'readonly' ?  '' : (showPickerNeedCarType2 = true) "
        />
        <select-picker
          :code.sync="formData.needCarType2"
          :show.sync="showPickerNeedCarType2"
          :text.sync="formData.needCarType2Str"
          name="CarType2Enums"
        ></select-picker>
        <fm-field
          v-if="!isModify && (taskKey === 'UserTask_7' || inNoAuthPage)"
          :value="getCarSourceStr(formData.carResourceType)"
          :rules="[{ required: true }]"
          input-align="right"
          label="车辆来源"
          label-width="10em"
          name="carResourceType"
          readonly
          required
        />
        <fm-collapse
          v-if="!isModify && (taskKey === 'UserTask_7' || (formData.carResourceType === 1 && inNoAuthPage))"
          v-model="activeCarCompany"
        >
            <fm-collapse-item name="1" class="car-company-collapse">
              <template #title>
                <fm-field
                  v-model="formData.carCompanyName"
                  :readonly="flowConfig.carCompanyName == 'readonly'"
                  :rules="[{ required: true }]"
                  clickable
                  input-align="right"
                  label="租车公司"
                  name="carCompanyName"
                  required
                >
                </fm-field>
              </template>
              <div class="apply-person-card car-com">
                <fm-cell-group>
                  <fm-cell class="person-view flex col-center">
                    <template #title>
                      <div class="font-28 color-black font-400">{{ formData.carCompanyApproverFullName }}</div>
                      <div class="color-blue font-28 font-400">{{ formData.carCompanyApproverPhone }}</div>
                    </template>
                    <template #right-icon>
                      <a :href="`tel:${formData.carCompanyApproverPhone}`" class="flex col-center d-block"
                         @click.stop="">
                        <img class="call-img" src="@/assets/img/car/call.png">
                      </a>
                    </template>
                  </fm-cell>
                </fm-cell-group>
              </div>
            </fm-collapse-item>
          </fm-collapse>
        <!-- 节点3  调度员用车任务下发租车公司界面 -->
        <template v-if="isShowCarCompany">
          <fm-field
            v-model="formData.realCarTypeStr"
            :readonly="flowConfig.realCarType == 'readonly'"
            :rules="[{ required: true }]"
            clickable
            input-align="right"
            label="车型"
            name="realCarType"
            required
          />
          <fm-field
            :value="getCarSourceStr(formData.carResourceType)"
            :rules="[{ required: true }]"
            input-align="right"
            label="车辆来源"
            label-width="10em"
            name="carResourceType"
            readonly
            required
          />
          <fm-collapse v-model="activeCarCompany">
            <fm-collapse-item name="1" class="car-company-collapse">
              <template #title>
                <fm-field
                  v-model="formData.carCompanyName"
                  :readonly="flowConfig.carCompanyName == 'readonly'"
                  :rules="[{ required: true }]"
                  clickable
                  input-align="right"
                  label="租车公司"
                  name="carCompanyName"
                  required
                >
                </fm-field>
              </template>
              <div class="apply-person-card car-com">
                <fm-cell-group>
                  <fm-cell class="person-view flex col-center">
                    <template #title>
                      <div class="font-28 color-black font-400">{{ formData.carCompanyApproverFullName }}</div>
                      <div class="color-blue font-28 font-400">{{ formData.carCompanyApproverPhone }}</div>
                    </template>
                    <template #right-icon>
                      <a :href="`tel:${formData.carCompanyApproverPhone}`" class="flex col-center d-block"
                         @click.stop="">
                        <img class="call-img" src="@/assets/img/car/call.png">
                      </a>
                    </template>
                  </fm-cell>
                </fm-cell-group>
              </div>
            </fm-collapse-item>
          </fm-collapse>
        </template>
        <fm-cell-group v-if="distanceStr && showTripInfo" title="" >
          <fm-field v-model="distanceStr" input-align="right" label="预计距离" readonly/>
        </fm-cell-group>
        <fm-cell-group v-if="timeStr && showTripInfo" title="">
          <fm-field v-model="timeStr" input-align="right" label="预计时长" readonly/>
        </fm-cell-group>
        <fm-collapse v-show="showTrack" v-model="activeNames">
          <fm-collapse-item :title="`${activeNames.length ? '收缩' : '展开'}地图`" icon="location-o"
                            name="1">
            <Track id="trackMapMobileContainer" :end-point="endPoint" :start-point="startPoint"
                   @updateTripInfo="handleTime"/>
          </fm-collapse-item>
        </fm-collapse>
        <fm-cell-group>
          <fm-cell class="description-cell" title="补充说明">
            <template #label>
              <span>
                {{
                  flowConfig.description == 'readonly' ? '' : '若用车较复杂，用车需连续两天及以上，请详细描述'
                }}
              </span>
            </template>
          </fm-cell>
          <fm-field
            v-model="formData.description"
            :readonly="flowConfig.description == 'readonly'"
            :placeholder="
              flowConfig.description == 'readonly' ? '' : '请输入补充说明'
            "
            label=""
            maxlength="500"
            name="description"
            required
            autosize
            type="textarea"
          />
        </fm-cell-group>
      </div>
      <!-- 出车任务确认 UserTask_3租车公司管理员 -->
      <div class="m-l-10">
        <blank style="margin-left: -5px"/>
        <div class="sty_h">乘车人信息</div>
        <template v-if="flowConfig.useCarPersonNum == 'readonly'">
          <fm-field
            v-model="formData.useCarPersonNum"
            :readonly="true"
            :rules="[{ required: true }]"
            input-align="right"
            label="乘车人数(人)"
            label-width="10em"
            name="useCarPersonNum"
            required
          />
        </template>
        <fm-field
          v-else
          v-model="formData.useCarPersonNum"
          :rules="[{ required: true, message: '请输入乘车人' }]"
          error-message-align="right"
          input-align="right"
          label="乘车人数"
          name="useCarPersonNum"
          required
          type="number"
        >
          <template #button>
            <fm-button
              v-if="isAdd && isUser || (taskKey === 'UserTask_0' && type === 'draft')"
              icon="plus"
              plain
              round
              size="mini"
              type="primary"
              @click="addPerson"
            ></fm-button>
          </template>
        </fm-field>
        <template v-if="isUser">
          <template v-for="(item, index) in formData.vdAfUseCarPersonList">
            <div :key="index+'d'" class="apply-person-card">
              <fm-cell-group inset>
                <fm-cell class="p-t-24 p-b-24">
                  <template #title>
                    <span v-if="flowConfig.useCarMatter !== 'readonly'"
                          class="sort-index">乘车人{{ index + 1 }}</span>
                  </template>
                  <template #default>
                    <fm-button
                      v-if="isAdd && isUser && type !== 'view' && !isXcModify"
                      icon="minus"
                      plain
                      round
                      size="mini"
                      type="primary"
                      @click.stop="minusPerson(item, index)"
                    ></fm-button>
                  </template>
                </fm-cell>
                <fm-cell v-if="flowConfig.useCarMatter !== 'readonly'">
                  <!-- 使用 title 插槽来自定义标题 -->
                  <template #title>
                    <fm-field
                      v-model="item.ucPersonFullName"
                      :maxlength="255"
                      :name="`ucPersonFullName${index}`"
                      :placeholder="'请输入姓名'"
                      :rules="[{ required: true, message: '请输入姓名' }]"
                      class="p-10"
                      clearable
                      input-align="left"
                      label=""
                      required
                      @change="item.ucPersonResource = 3"
                    >
                      <template #button>
                        <fm-button
                          v-if="isAdd"
                          :disabled="!item.ucPersonFullName"
                          size="small"
                          type="primary"
                          @click.stop="chooseApplyPerson(item,index)"
                        >选择
                        </fm-button>
                      </template>
                    </fm-field>
                  </template>
                  <template #label>
                    <fm-field
                      v-model="item.ucPersonPhone"
                      :maxlength="255"
                      :name="`ucPersonPhone${index}`"
                      :placeholder="'请输入手机号'"
                      :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/, message: '请输入正确的联系方式' }]"
                      class="p-10"
                      clearable
                      error-message=""
                      input-align="left"
                      label=""
                      required
                      type="tel"
                      @change="item.ucPersonResource = 3"
                    >
                    </fm-field>
                  </template>
                </fm-cell>
                <fm-cell v-else class="person-view flex col-center">
                  <template #title>
                    <div class="font-28 color-black font-400">{{ item.ucPersonFullName }}</div>
                    <div class="color-blue font-28 font-400">{{ item.ucPersonPhone }}</div>
                  </template>
                  <template #right-icon>
                    <a :href="`tel:${item.ucPersonPhone}`" class="flex col-center d-block"
                       @click.stop="">
                      <img class="call-img" src="@/assets/img/car/call.png">
                    </a>
                  </template>
                </fm-cell>
              </fm-cell-group>
            </div>
          </template>
        </template>
        <fm-cell-group class="remark" title="">
          <fm-cell class="description-cell" title="留言"></fm-cell>
          <fm-field
            v-model="formData.userCarRemark"
            :readonly="flowConfig.userCarRemark == 'readonly'"
            :placeholder="
              flowConfig.userCarRemark == 'readonly' ? '' : '请输入需要发送给司机的留言'
            "
            label=""
            maxlength="500"
            name="description"
            required
            autosize
            type="textarea"
          />
        </fm-cell-group>
      </div>
      <template v-if="isCar">
        <CarSelector
          :is-edit-car="isEditCar"
          :choose-car="chooseCar"
          :car-num-validator="carNumValidator"
          :form-data="formData"
          :change-car="changeCar"
          :is-car-info="isCarInfo"
          :vd-car-info="vdCarInfo"
          :show-choose-button="true"
        />
        <DriverSelector
          :is-edit-car="isEditCar"
          :choose-car-driver="chooseCarDriver"
          :form-data="formData"
          :change-driver="changeDriver"
          :phone-validator="phoneValidator"
          :is-car-info="isCarInfo"
          :vd-driver-info="vdDriverInfo"
          :format-phone-number="formatPhoneNumber"
          :show-choose-button="true"
        />
      </template>
      <template v-if="showCarCost">
        <blank v-if="isMobile" class="full-vw" style="margin-left: -5px"/>
        <div v-if="isEditCost" class="flex row-between">
          <div id="cost" ref="cost" class="sty_h">费用信息</div>
          <fm-button
            class="m-r-32 m-t-24"
            icon="plus"
            plain
            round
            size="mini"
            type="primary"
            @click="addCost"
          ></fm-button>
        </div>
        <div v-else id="cost" ref="cost" class="sty_h">费用信息</div>
        <div
          v-for="(item, index) in formData.vdAfUcCompleteFormList"
          class="m-32"
          :class="{'m-b-32': index + 1 === formData.vdAfUcCompleteFormList.length, 'cost-card': index > 0 }">
          <div v-if="index > 0" class="flex  row-between m-t-32">
            <div  class="full-width h-48 color-black font-28 font-bold p-l-32"  >
              费用{{ index + 1 }}
            </div>
            <fm-button
              v-if="index > 0 && isEditCost"
              icon="minus"
              plain
              round
              size="mini"
              type="primary"
              class="m-r-32"
              :disabled="formData.vdAfUcCompleteFormList.length === 1"
              @click.stop="delCost(index)"
            ></fm-button>
          </div>

          <car-cost
            :id="`carCost${index}`"
            :ref="`carCost${index}`"
            :index="index"
            :key="index"
            :isEdit="['UserTask_5'].includes(taskKey) && type !== 'view'"
            :sourceFormData="formData"
            :vdAfUcCompleteForm.sync="item"
          />
        </div>

<!--        <car-cost-->
<!--          id="carCost"-->
<!--          ref="carCost"-->
<!--          :isEdit="['UserTask_5'].includes(this.taskKey)"-->
<!--          :sourceFormData="formData"-->
<!--          :vdAfUcCompleteForm.sync="formData.vdAfUcCompleteForm"-->
<!--        />-->
      </template>
      <template v-if="isUser && !isCarApplyPage">
        <blank style="margin-left: -5px"/>
        <div class="m-l-10">
          <div class="sty_h">申请信息</div>
          <fm-field
              v-model="formData.applyFullName"
              :readonly="true"
              :rules="[{ required: true }]"
              input-align="right"
              label="申请人"
              name="applyFullName"
              required
          />
          <template v-if="isUser">
            <fm-field
                v-model="formData.carDispatchFullName"
                :is-link="flowConfig.carDispatchFullName != 'readonly' && !!formData.useCarType && carDispatchUserColumns.length > 1"
                :readonly="true"
                :rules="[{ required: true, message: '请选择车辆调度员' }]"
                error-message-align="right"
                input-align="right"
                label="车辆调度员"
                name="carDispatchUserName"
                required
                @click.stop.native="
            flowConfig.carDispatchFullName == 'readonly' || !formData.useCarType ? '' : (showPickerCarDispatchUser = carDispatchUserColumns.length > 1)"
            />
            <select-picker
                :code.sync="formData.carDispatchUserName"
                :column="carDispatchUserColumns"
                :is-enum="false"
                :show.sync="showPickerCarDispatchUser"
                :text.sync="formData.carDispatchFullName"
                :value-code="'userName'"
                :value-key="'userFullName'"
            ></select-picker>
          </template>
          <fm-field
              v-if="isShowCarCompany || isCarInfo"
              v-model="formData.carCompanyApproverFullName"
              :placeholder="flowConfig.carCompanyApproverFullName === 'readonly' || companyApproveColumns.length < 1 ? '' : '请选择'"
              :rules="[{ required: true }]"
              input-align="right"
              label="租车审批"
              label-width="10em"
              name="carDispatchUserName"
              readonly
              required
          />
        </div>
      </template>
      <template v-if="isApprovalInfo">
        <blank/>
        <div id="approvalInfo" ref="approvalInfo" class="sty_h">{{ isCarModify ? '租车信息' : '审批信息'}}</div>
      </template>
      <!-- 调度员用车任务下发租车公司界面 -->
      <template v-if="isChooseCarCompany">
        <fm-field
          v-model="carResourceTypeStr"
          :placeholder="'请选择'"
          :rules="[{ required: true }]"
          clickable
          input-align="right"
          is-link
          label="车辆来源"
          name="carResourceType"
          readonly
          required
          @click.stop.native="showCarResource = true"
        />
        <select-picker
          :code.sync="formData.carResourceType"
          :show.sync="showCarResource"
          :text.sync="carResourceTypeStr"
          name="CarResourceTypeEnums"
          @confirm="handleCarResourceChange"
        />
        <fm-field
          v-if="!isCarModify"
          v-model="formData.realCarTypeStr"
          :is-link="flowConfig.realCarType !== 'readonly' || isChooseCarCompany"
          :rules="[{ required: true, message: '请选择车型' }]"
          clickable
          error-message-align="right"
          input-align="right"
          label="实际车型"
          name="realCarType"
          placeholder="请选择"
          readonly
          required
          @click.stop.native="
            isChooseCarCompany ? (showPickerRealCarType = true) : flowConfig.realCarType === 'readonly' ?  '' : (showPickerRealCarType = true)
            "
        />
        <select-picker
          :code.sync="formData.realCarType"
          :show.sync="showPickerRealCarType"
          :text.sync="formData.realCarTypeStr"
          name="CarType2Enums"
        ></select-picker>
        <fm-field
          v-if="isNotUseProject"
          v-model="formData.carCompanyName"
          :placeholder="'请选择'"
          :rules="[{ required: true }]"
          clickable
          input-align="right"
          is-link
          label="租车公司"
          name="carCompanyName"
          readonly
          required
          @click.stop.native="showPickerCarCompany = true"
        />
        <select-picker
          v-if="isNotUseProject"
          :code.sync="formData.vdCarCompanyInfoId"
          :column="carCompanyColumns"
          :is-enum="false"
          :show.sync="showPickerCarCompany"
          :text.sync="formData.carCompanyName"
          :value-code="'id'"
          :value-key="'ccName'"
          @confirm="confirmCarCompany"
        ></select-picker>
        <fm-field
          v-if="isNotUseProject"
          v-model="formData.carCompanyApproverFullName"
          :is-link="flowConfig.carCompanyApproverFullName !== 'readonly' || companyApproveColumns.length > 1"
          :placeholder="flowConfig.carCompanyApproverFullName === 'readonly' || companyApproveColumns.length < 1 ? '' : '请选择'"
          :readonly="true"
          :rules="[{ required: true }]"
          input-align="right"
          label="租车审批"
          name="carDispatchUserName"
          required
          @click.stop.native="
          flowConfig.carCompanyApproverFullName === 'readonly' ? showPickerCompanyApprove = companyApproveColumns.length > 1: (showPickerCompanyApprove = companyApproveColumns.length > 1)"
        />
        <select-picker
          v-if="isNotUseProject"
          :code.sync="formData.carCompanyApproverUserName"
          :column.sync="companyApproveColumns"
          :is-enum="false"
          :show.sync="showPickerCompanyApprove"
          :text.sync="formData.carCompanyApproverFullName"
          :value-code="'userName'"
          :value-key="'userFullName'"
        ></select-picker>
        <template v-if="!isNotUseProject">
          <CarSelector
            :is-edit-car="type !== 'view'"
            :show-choose-button="false"
            :choose-car="chooseCar"
            :car-num-validator="carNumValidator"
            :form-data="formData"
            :change-car="changeCar"
            :is-car-info="isCarInfo"
            :vd-car-info="vdCarInfo"
          />
          <DriverSelector
            :is-edit-car="type !== 'view'"
            :show-choose-button="false"
            :choose-car-driver="chooseCarDriver"
            :form-data="formData"
            :change-driver="changeDriver"
            :phone-validator="phoneValidator"
            :is-car-info="isCarInfo"
            :vd-driver-info="vdDriverInfo"
            :format-phone-number="formatPhoneNumber"
          />
        </template>
      </template>
      <!--  司机确认行程开始选择时间    -->
      <template v-if="isChooseDriverTime">
        <fm-field
          v-model="formData.vdDriverConfirmEndTime"
          :rules="[{ required: true }]"
          center
          clickable
          label="行程开始时间"
          label-width="8em"
          name="vdDriverConfirmEndTime"
          placeholder="请选择"
          required
          readonly
          @click.stop.native="showCalendarDriverEndTime = true"
        >
          <template #button>
            <fm-button class="ml-2" size="small" type="primary"
                       @click.stop="useCurrentTime('vdDriverConfirmEndTime')">
              使用当前时间
            </fm-button>
          </template>
        </fm-field>
        <date-time-picker
          :show.sync="showCalendarDriverEndTime"
          :time.sync="formData.vdDriverConfirmEndTime"
          label-width="10em"
          title="开始时间"
          type="datetime"
        ></date-time-picker>
      </template>
      <!-- 乘客确认行程结束 -->
      <template v-if="isEvaluate">
        <fm-field
          v-model="formData.useCarPersonConfirmEndTime"
          :placeholder="flowConfig.useCarPersonConfirmEndTime == 'readonly' ? '' : '请选择'"
          :rules="[{ required: true }]"
          center
          label="行程结束时间"
          label-width="7em"
          name="useCarPersonConfirmEndTime"
          required
          readonly
          @click.stop.native="showCalendarEndTime = true"
        >
          <template #button>
            <fm-button class="ml-2" size="small" type="primary"
                       @click.stop="useCurrentTime('useCarPersonConfirmEndTime')">
              使用当前时间
            </fm-button>
          </template>
        </fm-field>
        <date-time-picker
          :show.sync="showCalendarEndTime"
          :time.sync="formData.useCarPersonConfirmEndTime"
          title="行程结束时间"
          type="datetime"
        ></date-time-picker>
        <div v-if="isContacts" class="drawer-content">
          <p class="bold">您对本次用车满意吗？</p>
          <div class="rate">
            <div class="stars">
              <img
                v-for="star in stars"
                :key="star"
                :src="star > form.evaluateScore ? require('@/assets/img/car/gray-star.svg') : require('@/assets/img/car/star.svg')"
                height="40px"
                width="40px"
                @click="form.evaluateScore = star"
              />
            </div>
            <span class="bold">{{ comment }}</span>
          </div>
          <p class="bold">您对本次用车的建议反馈</p>
          <textarea
            id="story"
            v-model="form.evaluateContent"
            name="story"
            placeholder="请输入您的建议反馈"
            rows="10"
          >
          </textarea>
        </div>
      </template>
      <template v-if="isModify">
        <blank style="margin-left: -5px"/>
        <div class="sty_h">变更原因</div>
        <div class="drawer-content">
           <p class="bold">您对本次用车的变更原因</p>
           <textarea
             v-model="modifycomment"
             name="story"
             placeholder="请输入您的变更原因"
             rows="10"
           >
          </textarea>
        </div>
      </template>
    </fm-form>
    <div v-if="isPC" class="pc-container">
      <fks-form
        ref="form"
        :model="formData"
        :style="{margin: '0 auto'}"
        label-position="right"
        label-width="130px"
        style="overflow-x: hidden"
      >
        <div ref="2.1">
          <div class="car-cell flex row-between">
            <div class="flex col-center">
              <div class="fks-title-icon d-inline-block align-sub"></div>
              <h4 class="text-title">用车信息</h4>
            </div>
            <fm-cell
              v-if="type === 'add'"
              :title="formData.remindTitle"
              title-class="prompt1-title"
              style="width: auto"
            />
          </div>
          <div class="border-line"></div>
          <fks-col :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
            <fks-form-item :rules="[{ required: true, message: '请选择出车时间' }]" :span="24" label="出车时间"
                           prop="startTime">
              <fks-date-picker
                v-model="formData.startTime"
                :clearable="false"
                :disabled="flowConfig.startTime === 'readonly'"
                :pickerOptions="startPickerOptions"
                class="full-width"
                placeholder="请选择出车时间"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
              >
              </fks-date-picker>
            </fks-form-item>
          </fks-col>
          <fks-col :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
            <fks-form-item
              :rules=" isView ? {} : [
                { required: true, message: '请选择预计返回时间' },
                {
                  validator: endTimeValidator, message: '返回时间不能早于出发时间'
                }
              ]"
              :span="24"
              label="预计返回时间"
              prop="predictEndTime"
            >
              <fks-date-picker
                v-model="formData.predictEndTime"
                :clearable="false"
                :disabled="flowConfig.predictEndTime === 'readonly'"
                :pickerOptions="endPickerOptions"
                class="full-width"
                placeholder="请选择预计返回时间"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
              >
              </fks-date-picker>
            </fks-form-item>
          </fks-col>
          <fks-col :lg="showOther ? 8 : 12" :md="24" :sm="24" :xl="showOther ? 8 : 12" :xs="24">
            <fks-form-item :rules="[{ required: true,message: '请选择出发地' }]"
                           label="出发地" prop="startAddress">
              <fks-select
                v-model="formData.startAddress"
                :disabled="flowConfig.startAddress === 'readonly'"
                class="full-width"
                @change="handleStartAddressChange"
              >
                <fks-option
                  v-for="(item, index) in startAddresses"
                  :key="index"
                  :label="item.value"
                  :value="item.key"
                />
              </fks-select>
            </fks-form-item>
          </fks-col>
          <fks-col :lg="16" :md="24" :sm="24" :xl="16" :xs="24">
            <fks-form-item
              v-show="showOther"
              :rules="[{ required: showOther, message: '请输入出发地详情' }]"
              label="出发地详情"
              prop="startAddressDetail"
            >
              <div class="flex col-center">
                <fks-input
                  v-model="formData.startAddressDetail"
                  :placeholder="flowConfig.startAddressDetail === 'readonly' ? '' : '请展开地图搜索出发地'"
                  class="appendButton"
                  :readonly="canEdit"
                  :disabled="!canEdit"
                  maxlength="500"
                  @focus="getLocation('start')"
                >
                  <fks-button
                    v-if="flowConfig.startAddress !== 'readonly'"
                    slot="append"
                    :icon="mapShowStart ? 'fks-icon-arrow-down' : 'fks-icon-arrow-up'"
                    type="primary"
                    @click="getLocation('start')">选择地点
                  </fks-button>
                </fks-input>
              </div>
            </fks-form-item>
          </fks-col>
          <fks-form-item :rules="[{ required: true, message: '请输入目的地' }]" label="目的地"
                         prop="endAddress">
            <div class="flex col-center">
              <fks-input
                v-model="formData.endAddress"
                :placeholder="flowConfig.startAddressDetail === 'readonly' ? '' : '请展开地图搜索目的地'"
                class="appendButton"
                :readonly="(canEdit || isXcModify) || type === 'view'"
                :disabled="(!canEdit && !isXcModify) || type === 'view'"
                maxlength="500"
                @focus="getLocation('end')"
              >
                <fks-button
                  v-if="flowConfig.endAddress !== 'readonly'"
                  slot="append"
                  :icon="mapShow ? 'fks-icon-arrow-down' : 'fks-icon-arrow-up'"
                  type="primary"
                  @click="getLocation('end')">选择地点
                </fks-button>
              </fks-input>
            </div>
          </fks-form-item>
          <fks-form-item v-show="!!formData.endAddress"
                         label="目的地备注"
                         prop="endAddressDetail">
            <fks-input
              v-model="formData.endAddressDetail"
              :disabled="flowConfig.endAddress == 'readonly'"
              :placeholder="flowConfig.endAddress == 'readonly' ? '' : '请输入目的地备注'"
            />
          </fks-form-item>
          <fks-form-item
              :rules="[{ required: true, message: '请输入出车事由' }]"
              label="出车事由"
              prop="useCarMatter"
          >
            <fks-input
                v-model="formData.useCarMatter"
                :disabled="flowConfig.useCarMatter == 'readonly'"
                :rows="5"
                maxlength="500"
                placeholder="请输入出车事由"
                show-word-limit
                type="textarea"
            />
          </fks-form-item>
          <fks-col :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
            <fks-form-item :rules="[{ required: true }]" label="联系人"
                           prop="contacts">
              <new-person-selector
                  :initial-value.sync="formData.contacts"
                  :read-only="flowConfig.needCarType2 === 'readonly'"
                  @closePopup="closePopupPerson"
              />
            </fks-form-item>
          </fks-col>
          <fks-col :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
            <fks-form-item
                :rules="[{ required: true }, { validator: phoneValidator, message: '请输入正确手机格式' }]"
                label="联系电话"
                prop="contactsPhone"
            >
              <fks-input
                  v-model="formData.contactsPhone"
                  :disabled="flowConfig.contactsPhone == 'readonly'"
              />
            </fks-form-item>
          </fks-col>
          <fks-col :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
            <fks-form-item
              :rules="[{ required: true, message: '请选择用车类型' }]"
              label="用车类型" prop="useCarTypeStr">
              <fks-select
                v-model="formData.useCarType"
                :disabled="flowConfig.useCarType === 'readonly'"
                placeholder="请选择"
                style="width: 100%"
                @change="confirmUseCarType"
              >
                <fks-option
                  v-for="(item, index) in useCarType"
                  :key="index"
                  :label="item.value"
                  :value="item.key"
                />
              </fks-select>
            </fks-form-item>
          </fks-col>
          <fks-col v-if="!isShowCarCompany" :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
            <fks-form-item :rules="[{ required: true, message: '请选择车型' }]"
                           label="车型" prop="needCarType2Str">
              <fks-select
                v-model="formData.needCarType2"
                :disabled="!(flowConfig.needCarType2 !== 'readonly')"
                placeholder="请选择车型"
                style="width: 100%"
                @change="handleNeedCar2Change"
              >
                <fks-option
                  v-for="(item, index) in carType"
                  :key="index"
                  :label="item.value"
                  :value="item.key"
                />
              </fks-select>
            </fks-form-item>
          </fks-col>
          <!-- 节点3  调度员用车任务下发租车公司界面 -->
          <fks-col v-if="isShowCarCompany" :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
              <fks-form-item
                ref="carCompanyRef"
                :readonly="flowConfig.carCompanyName == 'readonly'"
                :rules="[{ required: true, message: '请选择租车公司' }]"
                label="车型" prop="realCarType">
                <fks-input
                  v-model="formData.realCarTypeStr"
                  :disabled="flowConfig.realCarType == 'readonly'"
                />
              </fks-form-item>
            </fks-col>
          <fks-col
            v-if="isShowCarCompany || taskKey === 'UserTask_7' || inNoAuthPage || !isNotUseProject"
            :lg="12" :md="12" :sm="24" :xl="12" :xs="24"
          >
            <fks-form-item
              readonly
              :rules="[{ required: true }]"
              label="车辆来源"
              prop="carResourceType"
            >
              <fks-input :value="getCarSourceStr(formData.carResourceType)" disabled/>
            </fks-form-item>
          </fks-col>
          <fks-col
            v-if="isShowCarCompany || taskKey === 'UserTask_7' || (formData.carResourceType === 1 && inNoAuthPage)"
            :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
              <fks-collapse class="pc-car-com-col" v-model="activeCarCompany">
                <fks-collapse-item name="1">
                  <template slot="title">
                    <fks-form-item
                      ref="carCompanyRef"
                      :readonly="flowConfig.carCompanyName == 'readonly'"
                      :rules="[{ required: true, message: '请选择租车公司' }]"
                      label="租车公司" prop="carCompanyName">
                      <fks-input
                        v-model="formData.carCompanyName"
                        :disabled="flowConfig.carCompanyName == 'readonly'"
                      />
                    </fks-form-item>
                  </template>
                  <fks-form-item>
                    <div class="apply-person-card p-t-30" style="padding-bottom: 15px">
                      <fm-cell-group inset>
                        <fm-cell>
                          <div class="flex row-between col-center">
                            <div>{{ formData.carCompanyApproverFullName }}</div>
                            <div>{{ formData.carCompanyApproverPhone }}</div>
                          </div>
                        </fm-cell>
                      </fm-cell-group>
                    </div>
                  </fks-form-item>
                </fks-collapse-item>
              </fks-collapse>
          </fks-col>
          <template v-if="showTripInfo" >
            <template v-if="(isShowCarCompany || taskKey === 'UserTask_7' || (formData.carResourceType === 1 && inNoAuthPage)) && activeCarCompany.length === 0 && screenWidth > 992">
              <fks-col :lg="12" :md="12" :sm="24" :xl="12" :xs="24" >
                <fks-form-item v-if="timeStr" label="预计时长">
                  <fks-input :value="timeStr" disabled/>
                </fks-form-item>
              </fks-col>
              <fks-col :lg="12" :md="12" :sm="24" :xl="12" :xs="24" class="distance-col">
                <fks-form-item v-if="distanceStr" label="预计距离">
                  <fks-input :value="distanceStr" disabled/>
                </fks-form-item>
              </fks-col>
            </template>
            <template v-else>
              <fks-col :lg="12" :md="12" :sm="24" :xl="12" :xs="24" >
                <fks-form-item v-if="distanceStr" label="预计距离">
                  <fks-input :value="distanceStr" disabled/>
                </fks-form-item>
              </fks-col>
              <fks-col :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
                <fks-form-item v-if="timeStr" label="预计时长">
                  <fks-input :value="timeStr" disabled/>
                </fks-form-item>
              </fks-col>
            </template>
          </template>
          <fks-form-item v-if="showTrack">
            <fks-collapse v-model="activeNames">
              <fks-collapse-item name="1">
                <template slot="title">
                  <i class="fks-icon-location-outline d-inline-block mr-1"></i>
                  {{ `${activeNames.length ? '收缩' : '展开'}地图` }}
                </template>
                <Track
                    v-if="activeNames.length > 0"
                    id="trackMapContainer"
                    :end-point="endPoint"
                    :start-point="startPoint"
                    @updateTripInfo="handleTime"
                />
              </fks-collapse-item>
            </fks-collapse>
          </fks-form-item>
          <fks-form-item label="补充说明" prop="description">
            <fks-input
              type="textarea"
              autosize
              v-model="formData.description"
              :disabled="flowConfig.description == 'readonly'"
              :placeholder="flowConfig.description == 'readonly' ? '' : '若用车较复杂，用车需连续两天及以上，请详细描述'"
            />
          </fks-form-item>
        </div>
        <div ref="2.2">
          <div class="flex col-center sty-cell" style="margin-bottom: 2px">
            <div class="fks-title-icon d-inline-block align-sub"></div>
            <h4 class="text-title">乘车人信息</h4>
          </div>
          <div class="border-line"></div>
          <fks-form-item
            :rules="[{ required: true, validator: useCarPersonValidator }]"
            label="乘车人数（人）" prop="useCarPersonNum">
            <fks-input v-model="formData.useCarPersonNum"
                       :disabled="!(isAdd || (taskKey === 'UserTask_0' && type === 'draft'))"
                       :min="0"
                       class="appendButton" placeholder="请填写乘车人数"
                       type="number">
              <fks-button
                v-if="(isAdd && isUser || (taskKey === 'UserTask_0' && type === 'draft')) && !(flowConfig.useCarPersonNum === 'readonly')"
                slot="append"
                @click="addPerson"
              >添加乘车人
              </fks-button>
            </fks-input>
            <template v-if="isUser">
              <div class="p-b-32" v-if="flowConfig.useCarMatter !== 'readonly'">
                <template v-for="(item, index) in formData.vdAfUseCarPersonList">
                  <div :key="index+'d'" class="apply-person-card">
                    <fm-cell-group inset>
                      <fm-cell class="p-t-24 p-b-24">
                        <div class="flex col-center row-between" style="margin-bottom: 10px">
                          <span class="sort-index">乘车人{{ index + 1 }}</span>
                          <fks-button
                            v-if="isAdd && isUser && type !== 'view'"
                            circle
                            icon="fks-icon-minus"
                            plain
                            type="primary"
                            @click.stop="minusPerson(item, index)"
                          />
                        </div>
                      </fm-cell>
                      <fks-row v-if="flowConfig.useCarMatter !== 'readonly'" style="margin: 10px">
                        <fks-col :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
                          <fks-form-item
                            :prop="`vdAfUseCarPersonList.${index}.ucPersonFullName`"
                            :rules="[
                                    {required: true, message: '请输入姓名', trigger: 'blur'}
                                ]"
                            label="姓名"
                            label-width="60px"
                            style="margin-bottom: 20px"
                          >
                            <new-person-selector
                              :current-index="index"
                              :initial-value.sync="item.ucPersonFullName"
                              :read-only="flowConfig.useCarMatter === 'readonly'"
                              @closePopup="closePopupPerson"
                              @focus="currApplyPersonIndex = index"
                            />
                          </fks-form-item>
                        </fks-col>
                        <fks-col :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
                          <fks-form-item
                            :prop="`vdAfUseCarPersonList.${index}.ucPersonPhone`"
                            :rules="[{required: true, validator: phoneValidator, trigger: 'blur'}]"
                            label="联系电话"
                            label-width="80px"
                            style="margin-bottom: 20px"
                          >
                            <fks-input
                              v-model="item.ucPersonPhone"
                              placeholder="请输入联系电话"
                              type="number"
                              @change="item.ucPersonResource = 3"
                              @focus="currApplyPersonIndex = index"
                            />
                          </fks-form-item>
                        </fks-col>
                      </fks-row>
<!--                      <fm-cell v-if="flowConfig.useCarMatter === 'readonly'">-->
<!--                        <div class="flex row-between col-center">-->
<!--                          <div>{{ item.ucPersonFullName }}</div>-->
<!--                          <div>{{ item.ucPersonPhone }}</div>-->
<!--                        </div>-->
<!--                      </fm-cell>-->
                    </fm-cell-group>
                  </div>
                </template>
              </div>
                <div v-else-if="formData.vdAfUseCarPersonList.length" class="table p-b-32">
                    <fks-table
                            :data="formData.vdAfUseCarPersonList"
                            :header-cell-style="{background: '#f5f5f5'}"
                            border="true"
                    >
                        <fks-table-column label="序号" type="index" width="50" align="center" />
                        <fks-table-column label="姓名" prop="ucPersonFullName" align="center" />
                        <fks-table-column label="电话号码" prop="ucPersonPhone" align="center">
                            <template slot-scope="{row}">
                                {{formatPhoneNumber(row)}}
                            </template>
                        </fks-table-column>
                    </fks-table>
                </div>
            </template>

          </fks-form-item>
          <fks-form-item label="留言" prop="userCarRemark">
            <fks-input
              type="textarea"
              autosize
              v-model="formData.userCarRemark"
              :disabled="flowConfig.userCarRemark === 'readonly'"
              :placeholder="flowConfig.userCarRemark == 'readonly' ? '' : '请输入需要发送给司机的留言'"
            />
          </fks-form-item>
        </div>
        <CarSelector
          ref="2.4"
          v-if="isCar"
          :is-edit-car="isEditCar"
          :choose-car="chooseCar"
          :car-num-validator="carNumValidator"
          :form-data="formData"
          :change-car="changeCar"
          :is-car-info="isCarInfo"
          :vd-car-info="vdCarInfo"
          :show-choose-button="true"
        />
        <DriverSelector
          v-if="isCar"
          ref="2.5"
          :is-edit-car="isEditCar"
          :choose-car-driver="chooseCarDriver"
          :form-data="formData"
          :change-driver="changeDriver"
          :phone-validator="phoneValidator"
          :is-car-info="isCarInfo"
          :vd-driver-info="vdDriverInfo"
          :format-phone-number="formatPhoneNumber"
          :show-choose-button="true"
        />
        <div v-if="showCarCost" ref="2.6">
          <blank v-if="isMobile" style="margin-left: -5px"/>
          <div v-if="isEditCost" class='flex row-between'>
            <div id="cost" ref="cost" class="flex col-center sty-cell"
                 style="margin-bottom: 2px">
              <div class="fks-title-icon d-inline-block align-sub"></div>
              <h4 class="text-title">费用信息</h4>
            </div>
            <div class="m-r-30">
              <fks-button
                type="primary"
                icon="fks-icon-plus"
                size="mini"
                @click="addCost"
              >添加</fks-button>
            </div>
          </div>
          <div v-else id="cost" ref="cost" class="flex col-center sty-cell"
               style="margin-bottom: 2px">
            <div class="fks-title-icon d-inline-block align-sub"></div>
            <h4 class="text-title">费用信息</h4>
          </div>
          <div class="border-line"></div>
          <div
            v-for="(item, index) in formData.vdAfUcCompleteFormList"
            class="apply-person-card p-32"
            :class="{'m-b-32': index + 1 === formData.vdAfUcCompleteFormList.length }">
            <div  v-if="index > 0" class="flex row-between m-b-32">
              <div
                class="full-width h-48 color-black font-32 font-bold p-l-32"
              >
                费用{{ index + 1 }}
              </div>
              <fks-button
                v-if="index > 0 && isEditCost"
                type="danger"
                icon="fks-icon-minus"
                size="mini"
                :disabled="formData.vdAfUcCompleteFormList.length === 1"
                @click="delCost(index)"
              >删除</fks-button>
            </div>
            <car-cost
              :id="`carCost${index}`"
              :ref="`carCost${index}`"
              :index="index"
              :key="index"
              :isEdit="['UserTask_5'].includes(taskKey) && type !== 'view'"
              :sourceFormData="formData"
              :vdAfUcCompleteForm.sync="item"
            />
          </div>
<!--          <car-cost-->
<!--            id="carCost"-->
<!--            ref="carCost"-->
<!--            :isEdit="['UserTask_5'].includes(taskKey) && type !== 'view'"-->
<!--            :sourceFormData="formData"-->
<!--            :vdAfUcCompleteForm.sync="formData.vdAfUcCompleteForm"-->
<!--          />-->
        </div>
        <div ref="2.3">
          <template v-if="isUser && !isCarApplyPage">
            <fks-row>
              <div class="flex col-center sty-cell" style="margin-bottom: 2px">
                <div class="fks-title-icon d-inline-block align-sub"></div>
                <h4 class="text-title">申请信息</h4>
              </div>
              <div class="border-line"></div>
              <fks-col :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
                <fks-form-item
                    :rules="[{ required: true }]"
                    label="申请人" prop="applyFullName">
                  <fks-input :value="formData.applyFullName" disabled/>
                </fks-form-item>
              </fks-col>
              <fks-col :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
                <fks-form-item
                    v-if="isUser"
                    :rules="[{ required: true, message: '请选择车辆调度员', trigger: 'blur' }]"
                    label="车辆调度员" prop="carDispatchFullName">
                  <fks-select
                      v-model="formData.carDispatchFullName"
                      :disabled="!(flowConfig.carDispatchFullName !== 'readonly' && !!formData.useCarType && carDispatchUserColumns.length > 1)"
                      placeholder="请选择"
                      style="width: 100%"
                  >
                    <fks-option
                        v-for="(item, index) in carDispatchUserColumns"
                        :key="index"
                        :label="item.userFullName"
                        :value="item.userFullName"
                    />
                  </fks-select>
                </fks-form-item>
              </fks-col>
              <fks-col :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
                <fks-form-item
                    v-if="isShowCarCompany || isCarInfo"
                    ref="carCompanyRef"
                    :readonly="flowConfig.carCompanyName == 'readonly'"
                    :rules="[{ required: true, message: '请选择租车审批' }]"
                    label="租车审批" prop="carCompanyApproverFullName">
                  <fks-input
                      v-model="formData.carCompanyApproverFullName"
                      :disabled="flowConfig.carCompanyApproverFullName == 'readonly'"
                  />
                </fks-form-item>
              </fks-col>
            </fks-row>
          </template>
        </div>
        <div v-if="(((!canEdit && type !== 'view') || isChooseCarCompany) ) || isCarModify" ref="2.7">
          <div v-if="!canEdit && type !== 'view'" id="approvalInfo"
               ref="approvalInfo" class="flex col-center sty-cell"
               style="margin-bottom: 2px;width: 100%">
            <div class="fks-title-icon d-inline-block align-sub"></div>
            <h4 class="text-title">{{ isCarModify ? '租车信息' : isModify ? '变更原因' : '审批信息'}}</h4>
          </div>
          <div class="border-line"></div>
          <!-- 调度员用车任务下发租车公司界面 -->
          <template v-if="isChooseCarCompany">
            <fks-col :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
              <fks-form-item
                :rules="[{required: true, message: '请选择车辆来源', trigger: 'blur'}]"
                label="车辆来源"
                prop="carResourceType"
              >
                <fks-select
                  v-model="formData.carResourceType"
                  placeholder="请选择车辆来源"
                  class="full-width"
                  @change="handleCarResourceChange"
                >
                  <fks-option
                    v-for="(item, index) in carResources"
                    :key="index"
                    :label="item.value"
                    :value="item.key"
                  />
                </fks-select>
              </fks-form-item>
            </fks-col>
            <fks-col  v-if="!isCarModify" :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
              <fks-form-item :rules="[{ required: true, message: '请选择车型' }]"
                             label="车型" prop="realCarType">
                <fks-select
                  v-model="formData.realCarType"
                  :disabled="!(flowConfig.realCarType !== 'readonly' || isChooseCarCompany)"
                  placeholder="请选择车型"
                  class="full-width"
                  @change="handleRealCarChange"
                >
                  <fks-option
                    v-for="(item, index) in carType"
                    :key="index"
                    :label="item.value"
                    :value="item.key"
                  />
                </fks-select>
              </fks-form-item>
            </fks-col>
            <!--  车辆来源为项目部时，不显示租车公司表单项     -->
            <fks-col v-if="isNotUseProject" :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
              <fks-form-item ref="carCompanyRef"
                             :rules="[{ required: true, message: '请选择租车公司' }]"
                             label="租车公司" prop="carCompanyName">
                <fks-select
                  v-model="formData.carCompanyName"
                  placeholder="请选择"
                  style="width: 100%"
                  @change="confirmCarCompany"
                >
                  <fks-option
                    v-for="(item, index) in carCompanyColumns"
                    :key="index"
                    :label="item.ccName"
                    :value="item.ccName"
                  />
                </fks-select>
              </fks-form-item>
            </fks-col>
            <!--  车辆来源为项目部时，不显示租车审批表单项     -->
            <fks-col v-if="isNotUseProject" :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
              <fks-form-item
                :rules="[{ required: true, message: '请选择租车审批' }]"
                label="租车审批" prop="carCompanyApproverFullName">
                <fks-select
                  v-model="formData.carCompanyApproverFullName"
                  :disabled="!(flowConfig.carCompanyApproverFullName !== 'readonly' || companyApproveColumns.length > 1)"
                  class="full-width"
                  @change="handleApproverChange"
                >
                  <fks-option
                    v-for="(item, index) in companyApproveColumns"
                    :key="index"
                    :label="item.userFullName"
                    :value="item.userFullName"
                  />
                </fks-select>
              </fks-form-item>
            </fks-col>
            <fks-col v-if="!isNotUseProject" :lg="24" :md="24" :sm="24" :xl="12" :xs="24">
              <CarSelector
                :is-edit-car="type !== 'view'"
                :show-choose-button="false"
                :choose-car="chooseCar"
                :car-num-validator="carNumValidator"
                :form-data="formData"
                :change-car="changeCar"
                :is-car-info="isCarInfo"
                :vd-car-info="vdCarInfo"
              />
              <DriverSelector
                :is-edit-car="type !== 'view'"
                :show-choose-button="false"
                :choose-car-driver="chooseCarDriver"
                :form-data="formData"
                :change-driver="changeDriver"
                :phone-validator="phoneValidator"
                :is-car-info="isCarInfo"
                :vd-driver-info="vdDriverInfo"
                :format-phone-number="formatPhoneNumber"
              />
            </fks-col>
          </template>
          <!--  司机确认行程开始选择时间    -->
          <fks-form-item v-if="isChooseDriverTime" :rules="[{ required: true }]" :span="24"
                         label="行程开始时间" prop="vdDriverConfirmEndTime">
            <fks-date-picker
              v-model="formData.vdDriverConfirmEndTime"
              :clearable="false"
              class="full-width"
              placeholder="请选择行程开始时间"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
            >
            </fks-date-picker>
          </fks-form-item>
          <!-- 乘客确认行程结束 -->
          <template v-if="isEvaluate">
            <fks-form-item :rules="[{ required: true }]" :span="16" label="行程结束时间"
                           prop="useCarPersonConfirmEndTime">
              <div style="display: flex">
                <fks-date-picker
                  v-model="formData.useCarPersonConfirmEndTime"
                  :clearable="false"
                  class="full-width"
                  placeholder="请选择行程结束时间"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm"
                >
                </fks-date-picker>
                <fks-button style="margin-left: 10px" type="primary"
                            @click="useCurrentTime('useCarPersonConfirmEndTime')">
                  使用当前时间
                </fks-button>
              </div>
            </fks-form-item>
            <fks-form-item v-if="isContacts" :span="24" label="" prop="evaluateScore">
              <div class="drawer-content" style="padding: 0;">
                <p class="bold">您对本次用车满意吗？</p>
                <div class="rate">
                  <div class="stars">
                    <img
                      v-for="star in stars"
                      :key="star"
                      :src="star > form.evaluateScore ? require('@/assets/img/car/gray-star.svg') : require('@/assets/img/car/star.svg')"
                      height="40px"
                      width="40px"
                      @click="form.evaluateScore = star"
                    />
                  </div>
                  <span class="bold">{{ comment }}</span>
                </div>
                <p class="bold">您对本次用车的建议反馈</p>
                <fks-input
                  id="story"
                  v-model="form.evaluateContent"
                  :rows="10"
                  name="story"
                  placeholder="请输入您的建议反馈"
                  type="textarea">
                </fks-input>
              </div>
            </fks-form-item>
          </template>
          <!-- 变更原因 -->
          <fks-form-item v-if="isModify" :span="24" label="" prop="modifycomment">
            <div class="drawer-content" style="padding: 0;">
              <p class="bold">您对本次用车的变更原因</p>
              <fks-input
                id="story"
                v-model="modifycomment"
                :rows="10"
                name="story"
                placeholder="请输入您的变更原因"
                type="textarea">
              </fks-input>
            </div>
          </fks-form-item>
        </div>
      </fks-form>
    </div>
    <popper :show.sync="popupShow" icon="dashboard" title="选择车辆" @confirm="onConfirmCar">
      <car-list
        ref="carList"
        :carCompanyInfoId.sync="formData.vdCarCompanyInfoId"
        @closePopup="closePopupCar"
        @closePopupShow="popupShow = false;"
      ></car-list>
    </popper>
    <popper :show.sync="popupDriver" icon="user" title="选择司机" @confirm="onConfirmDriver">
      <driver-list
        ref="driverList"
        :carCompanyInfoId.sync="formData.vdCarCompanyInfoId"
        @closePopup="closePopupDriver"
        @closePopupShow="popupDriver = false;"
      ></driver-list>
    </popper>
    <popper :mobile-only="true" :show.sync="popupPerson" title="选择人员"
            @confirm="onConfirmPerson">
      <apply-person
        ref="applyPerson"
        :keywords.sync="currApplyPersonIndex === -1 ? '' : currApplyPerson.ucPersonFullName"
        @closePopup="closePopupPerson"
        @closePopupShow="popupPerson = false;"
      />
    </popper>
    <popper :show.sync="mapShow" icon="location-outline" show-navbar title="选择地点"
            @confirm="closePopupMap">
      <div v-if="loadMap">
        <a-map :location-info.sync="addressType === 'start' ? locationInfoStart : locationInfo"
               class="m-16"
               is-full @update="handleMapUpdate"/>
        <flow-button v-if="isMobile" :onSubmit="closePopupMap" submit-text="确定"></flow-button>
      </div>
    </popper>
  </div>
</template>
<script>
import {APPLY_RESOURCE, CURRENT_ROW, ENUM, OPEN_ID} from '@/store/State/stateTypes'
import AMap from '@/components/amap/index.vue';
import DateTimePicker from '@/modules/FormCenter/components/DateTimePicker/index.vue';
import SelectPicker from '@/modules/FormCenter/components/SelectPicker/index.vue';
import Blank from '@/components/Blank/index.vue';
import FlowButton from '@/modules/FormCenter/components/FlowButton/index.vue';
import Menus from '@/components/menus/index.vue'
import Popper from "@components/popper/index.vue";
import CommonTitle from '@/components/CommonTitle/index.vue'
import Track from "@components/amap/components/track.vue";
import PersonSelector from "@components/PersonSelector/index.vue";
import NewPersonSelector from "@components/PersonSelector/main.vue"
import CarSelector from "../components/CarAndDriverSelector/carSelector.vue"
import DriverSelector from "../components/CarAndDriverSelector/driverSelector.vue"
import CarCost from './CarCost';
import CarList from './CarList';
import DriverList from './DriverList';
import ApplyPerson from './ApplyPerson';
import {mapActions, mapMutations, mapState} from 'vuex';

import {
  Button,
  Cell,
  CellGroup,
  Checkbox,
  CheckboxGroup,
  Collapse,
  CollapseItem,
  Field,
  Form,
  NavBar,
  Popup,
  Stepper,
  Tab,
  Tabs,
  Toast
} from 'fawkes-mobile-lib'
import formMixin from '../formMixin'
import platform from "@/mixins/platform";
import {updateEvaluate} from '../../CarApply/components/api'
import {list} from "@modules/FormCenter/CarApply/CarCost/constant";
import dayjs from "dayjs";

const rateText = ['非常差', '差', '一般', '满意', '非常满意']
export default {
  name: 'CarApply',
  components: {
    DriverSelector,
    CarSelector,
    SelectPicker,
    DateTimePicker,
    DriverList,
    CarCost,
    CarList,
    ApplyPerson,
    Blank,
    FlowButton,
    Toast,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [NavBar.name]: NavBar,
    [Field.name]: Field,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Collapse.name]: Collapse,
    [CollapseItem.name]: CollapseItem,
    [Button.name]: Button,
    [Toast.name]: Toast,
    [Stepper.name]: Stepper,
    [Popup.name]: Popup,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
    [Form.name]: Form,
    AMap,
    Menus,
    Popper,
    CommonTitle,
    Track,
    PersonSelector,
    NewPersonSelector
  },
  mixins: [formMixin, platform],
  data() {
    return {
      screenWidth: null,
      activeNames: [],
      activeCarCompany: [],
      stars: [1, 2, 3, 4, 5],
      form: {
        evaluateScore: 0,
        evaluateContent: ''
      },
      modifycomment: '',
      isEnterDriver: false,
      isEnterCar: false,
      addressType: '',
      carCompanyColumns: [],
      carDispatchUserColumns: [],
      companyApproveColumns: [],
      showPickerCarCompany: false,
      showCarResource: false, // 移动端，展示车辆来源选择器
      showPickerCarDispatchUser: false,
      showPickerStartAddress: false,
      showPickerCompanyApprove: false,
      popupShow: false,
      popupDriver: false,
      popupPerson: false,
      showArea: false,
      mapShow: false,
      mapShowStart: false,
      loadMapStart: true,
      loadMap: true,
      isDisabled: false,
      service: {
        initial: '/vehicle-dispatch/vd/flow/detail', //初始化接口
        submit: '/vehicle-dispatch/vd/flow/commit', //提交接口
      },
      // 输入此表单数据表对应的数据对象名
      entityName: 'vehicleDispatch',
      //输入此表单的头部名称
      // formName: '用车申请',
      //表单设计第四步：输入此表单相关连流程的modelKey
      modelKey: 'vehicleDispatch',
      detailParam: [],
      tab: 0,
      carResourceTypeStr: '', // 移动端，车辆来源选择器展示的字符串
      formData: {
        userCarRemark: '', // 乘客留言
        remindTitle: '', // 提醒标题
        applyFullName: '', // 申请人
        applyUserName: '', // 申请人
        applyDepartment: '', // 申请人部门名称
        useCarType: '', // 用车类型
        useCarTypeStr: '', // 用车类型
        useCarMatter: '', // 出车事由
        useCarPersonNum: '', // 乘车人数
        startTime: '', // 出车时间
        predictEndTime: '', // 预计返回时间
        startAddress: '', // 出发地
        startAddressStr: '', // 出发地
        startAddressDetail: '', // 出发地详细地址
        endAddress: '', // 目的地
        endAddressDetail: '', // 目的地详细地址
        needCarType: '', // 车型
        needCarTypeStr: '', // 车型（1、别克GL8，2、奥迪A6，3、考斯特）
        needCarType2: '', // 车型
        needCarType2Str: '', // 车型（1、mpv，2、suv，3、轿车，4、中巴、5、大巴）
        realCarType: '', // 实际车型
        realCarTypeStr: '', // 实际车型
        carResourceType: '', // 车辆来源（1，租车公司；2，项目部）
        contacts: '', // 联系人
        contactsFullName: '', // 联系人
        contactsPhone: '', // 联系人电话
        contactsOpenId: '', // 联系人openId（飞书、微信才有这个）
        contactsResource: 1, // 联系人来源（1、飞书，2、微信，3、输入）
        description: '', // 补充说明
        carDispatchUserName: '', // 车辆调度员
        carDispatchFullName: '', // 车辆调度员
        applyUserTime: '', // 申请时间
        carCompanyName: '', // 租车公司名称
        vdCarCompanyInfoId: '', // 租车公司id
        carCompanyApproveUserName: '', // 租车审批用户名
        carCompanyApproveFullName: '', // 租车审批姓名
        prjDepName: '', // 填写人部门
        prjDepCode: '', // 填写人部门编号
        // notifyMethod: '', //通知方式
        currentExecutor: '', // 当前执行人
        currentExecutorTask: '', // 当前执行人任务节点
        carNum: '', // 车牌号
        carType: '', // 车型
        carAge: '', // 车龄
        driverUserName: '', // 司机用户名
        driverFullName: '', // 司机姓名
        driverPhone: '', // 司机手机号
        driverAge: '', // 司机驾龄
        vdAfUseCarPersonList: [], // 乘车人列表
        vdAfUcCompleteFormList: [] // 费用信息
      },
      costInfo: {
        carCostRent: '0.00',
        carCostPark: '0.00',
        carCostToll: '0.00',
        carCostGasoline: '0.00',
        journeyTotalTime: '0.00',
        journeyOverTime: '0.00',
        journeyOverTimeCost: '0.00',
        journeyTotalKm: '0.00',
        journeyOverKm: '0.00',
        journeyOverKmCost: '0.00',
        journeyCostOther: '0.00',
        journeyCostTotal: '0.00'
      },
      showcreateDate: '',
      showPicker: false,
      showCalendar: false,
      showPickerUseCarType: false, // 用车类型
      showPickerNeedCarType: false, // 车型
      showPickerNeedCarType2: false, // 车型
      showPickerRealCarType: false, // 实际车型
      showCalendarStart: false, // 出车时间
      showCalendarEnd: false, // 预计返回时间
      showCalendarEndTime: false, // 乘车人确认行程结束时间
      showCalendarDriverEndTime: false, // 司机确认行程开始时间
      flag: true,
      fileList: [],
      checkboxGroup: ['短信'],
      loading: false,
      // detailTable: [],
      delVdAfUseCarPersonList: [],
      // taskKey: 'UserTask_0', //
      // 用车申请 UserTask_0水电水利院员工, 用车申请审批UserTask_1水电水利院车辆调度员， 用车任务下发UserTask_2水电水利院车辆调度员 ;
      // 出车任务确认 UserTask_3租车公司管理员， 司机确认行程开始 UserTask_4驾驶员， 乘客确认行程结束  UserTask_5水电水利院员工
      // 用车信息填报 UserTask_6租车公司管理员 , 用车信息确认 UserTask_7水电水利院车辆调度员
      isFilterDetailTable: false,
      isDefault: false,
      vdCarInfo: {carNum: '', carType: '', carAge: ''},
      vdDriverInfo: {driverFullName: '', driverPhone: '', driverAge: ''},
      currApplyPerson: {},
      currApplyPersonIndex: -1,
      mapAfter: false,
      timeStr: '', //预计时间
      distanceStr: '', // 预计距离
      delCostList: []
    }
  },
  watch: {
    [APPLY_RESOURCE]: {
      handler(newVal) {
        if (newVal) {
          this.formData.applyResource = newVal;
        }
      },
      immediate: true
    },
    'formData.carDispatchFullName': {
      handler(newVal) {
        this.handleDispatcherChange(newVal)
      }
    },
    showTrack: {
      immediate: true,
      handler(newVal) {
          this.$nextTick(()=>{
              if (newVal && this.showTripInfo && this.startPoint && this.endPoint) {
                const obj = {
                  origin: `${this.startPoint.x},${this.startPoint.y}`,
                  destination: `${this.endPoint.x},${this.endPoint.y}`,
                  extensions: 'base',
                  output: 'json',
                  key: process.env.VUE_APP_AMAP_WEB_KEY
                }
                const params = Object.keys(obj).map(key => (`${key}=${obj[key]}`)).join('&')
                const url = `https://restapi.amap.com/v3/direction/driving?${params}`
                fetch(url).then(res => res.json()).then(res => {
                  const path = res.route.paths[0]
                  this.distanceStr = `${(Number(path.distance) / 1000).toFixed(2)}公里`
                  let str = ''
                  let append = ''
                  const duration = Number(path.duration)
                  if (duration / 60 < 60) {
                    append = `${(duration / 60).toFixed(2)}分钟`
                  } else {
                    append = `${(duration / 3600).toFixed(2)}小时`
                  }
                  this.timeStr = str + append
                })
            }
          })
      }
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'applyCar', 'currUser']),
    ...mapState(['userInfo', APPLY_RESOURCE, OPEN_ID]),
    inNoAuthPage() {
      return this.$route.path.includes('/apply/details')
    },
    startPickerOptions() {
      return {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      }
    },
    endPickerOptions() {
      const that = this
      return {
        disabledDate(time) {
          if (that.formData.startTime) {
            const d = new Date(new Date(that.formData.startTime).getTime()).setHours(0, 0, 0, 0)
            return time.getTime() < d
          }
          return true
        }
      }
    },
    // 行程信息变更
    isXcModify() {
      return this.$route.query.buttonKey === 'XC_FORM_MODIFY'
    },
    // 司机/车辆变更
    isXcDriverCarModify() {
      return this.$route.query.buttonKey === 'XC_DRIVER_CAR_MODIFY'
    },
    // 租车公司变更
    isCarModify() {
      return this.$route.query.buttonKey === 'XC_CAR_COMP_MODIFY'
    },
    isModify() {
      return this.isXcModify || this.isXcDriverCarModify || this.isCarModify;
    },
    isNotUseProject() {
      // 车辆来源没有选择项目部
      if (this.formData.carResourceType) {
        return this.formData.carResourceType.toString() !== '2'
      }
      return true
    },
    // 显示预计时长和预计距离
    showTripInfo() {
      // 用车申请，审批，出车任务确认，司机确认，司机确认行程开始这几个流程可以显示
      const taskKeys = ['UserTask_0', 'UserTask_1', 'UserTask_2', 'UserTask_3', 'UserTask_7']
      return taskKeys.findIndex(taskKey => taskKey === this.taskKey) > -1;
    },
    showOther() {
      return this.formData.startAddress === '其他' || +this.formData.startAddress === 9
    },
    isApprovalInfo: {
      get() {
        return ((this.isChooseCarCompany || this.isEvaluate || this.isChooseDriverTime) && !this.isModify) || this.isCarModify;
      },
      set() {
      }
    },
    comment() {
      return rateText[this.form.evaluateScore - 1];
    },
    canEdit() {
      return this.taskKey === 'UserTask_0'
    },
    isView() {
      return this.type === 'view';
    },
    approving() {
      return this.taskKey === 'UserTask_1'
    },
    useFormData() {
      return this.canEdit || this.approving;
    },
    startPoint: {
      get() {
        if (this.fromLocation || this.fromFormData) {
          let name = ''
          if (Number.isNaN(Number(this.formData.startAddress))) {
            name = this.formData.startAddressDetail
          } else {
            if (this.formData.startAddress === '9') {
              name = this.formData.startAddressDetail
            } else {
              name = this.formData.startAddressStr
            }
          }
          return {
            name,
            x: Number(this.locationInfoStart.longitude),
            y: Number(this.locationInfoStart.latitude)
          }
        }
        if (this.fromRow) {
          return {
            name: this.row.startAddress === '9' ? this.row.startAddressDetail : this.row.startAddressStr,
            x: Number(this.row.startAddressSmx),
            y: Number(this.row.startAddressSmy)
          }
        }
      },
      set() {
      }
    },
    endPoint: {
      get() {
        if (this.fromLocation || this.fromFormData) {
          return {
            name: `${this.formData.endAddress}`,
            x: Number(this.formData.endAddressSmx),
            y: Number(this.formData.endAddressSmy)
          }
        }
        if (this.fromRow) {
          return {
            name: `${this.row.endAddress}`,
            x: Number(this.row.endAddressSmx),
            y: Number(this.row.endAddressSmy)
          }
        }
      },
      set() {
      }
    },
    fromLocation: {
      get() {
        return this.locationInfoStart.longitude &&
          this.locationInfoStart.latitude &&
          this.locationInfo.longitude &&
          this.locationInfo.latitude
      },
      set() {
      }
    },
    fromFormData: {
      get() {
        return this.formData.startAddressSmx &&
          this.formData.startAddressSmy &&
          this.formData.endAddressSmx &&
          this.formData.endAddressSmy
      },
      set() {
      }
    },
    fromRow: {
      get() {
        return this.row.startAddressSmx &&
          this.row.startAddressSmy &&
          this.row.endAddressSmx &&
          this.row.endAddressSmy
      },
      set() {

      }

    },
    showTrack: {
      get() {
        return Boolean(this.fromLocation || this.fromFormData || this.fromRow || false)
      },
      set() {
      }
    },
    showCarCost() {
      if (this.isRead) return true;
      // 项目部用车不用展示费用信息
      if (!this.isNotUseProject) return false;
      return this.isUseCarInfo && !this.isCarApplyPage
    },
    // 出发地
    startAddresses() {
      return this.enums?.StartAddressEnums ?? [];
    },
    // 用车类型
    useCarType() {
      return this.enums?.UseCarTypeEnums ?? [];
    },
    // 车型
    carType() {
      return this.enums?.CarType2Enums ?? [];
    },
    // 车辆来源列表
    carResources() {
      return this.enums?.CarResourceTypeEnums ?? [];
    },
    // 车辆来源选择为项目部且审批成功
    isUseProjectSuccess(){
      return this.flowConfig.carResourceType === 'readonly'
    },
    useCarTypeColumns() {
      return this.useCarType.map(item => item.value);
    },
    carTypeColumns() {
      return this.carType.map(item => item.value);
    },
    submitText: {
      get() {
        const name = {
          UserTask_0: '提交',
          UserTask_1: '同意',
          UserTask_2: '同意',
          UserTask_3: '确认行程开始',
          // UserTask_4: '乘客确认行程结束',
          UserTask_4: '行程结束',
          UserTask_5: '提交',
          UserTask_6: '确认费用',
          UserTask_7: '行程确认',
        }
        return name[this.taskKey];
      },
      set() {
        this.$emit('setSubmitText', this.submitText);
      }
    },
    formName() {
      const name = {
        UserTask_0: '用车申请',
        UserTask_1: '用车申请审批',
        UserTask_2: '租车公司确认',
        UserTask_3: '司机确认行程开始',
        UserTask_4: '乘客确认行程结束',
        UserTask_5: '费用填报',
        UserTask_6: '用车信息确认',
        UserTask_7: '司机确认'
      }
      const modify = {
        XC_FORM_MODIFY: '行程信息变更',
        XC_DRIVER_CAR_MODIFY: '司机/车辆信息变更',
        XC_CAR_COMP_MODIFY: '租车公司变更'
      }
      // return this.isCarApplyPage? '详情' : this.$route.params.taskName || name[this.taskKey];
      return this.isModify ? modify[this.$route.query.buttonKey] : this.type === 'view' ? '详情' : this.$route.params.taskName || name[this.taskKey];
    },
    isRead() {
      return this.$route.query?.isRead === '1'
    },
    isCarApplyPage() {
      return ['travelTask', 'carApply'].includes(this.$route.params.page) && ['UserTask_5'].includes(this.taskKey);
    },
    isChooseCarCompany() {
      return (this.type === 'execute' && this.taskKey === 'UserTask_1' && !this.isModify) || this.isCarModify;
    },
    isShowCarCompany() {
      return (['UserTask_2', 'UserTask_3', 'UserTask_4', 'UserTask_5', 'UserTask_6'].includes(this.taskKey) || (this.isModify && this.taskKey !== 'UserTask_1')) && !this.isCarModify;
    },
    isChooseDriverTime() {
      return this.type === 'execute' && this.taskKey === 'UserTask_3' && !this.isXcDriverCarModify;
    },
    isEvaluate() {
      return this.type === 'execute' && this.taskKey === 'UserTask_4';
    },
    // 编辑费用信息
    isEditCost() {
      return this.type === 'execute' && this.taskKey === 'UserTask_5';
    },
    // 当登录人是否是联系人
    isContacts() {
      if (this.isEvaluate) {
        return this.formData.contacts === this.currUser.userFullName;
      } else {
        return false
      }
    },
    isCarInfo() {
      // return ['UserTask_3', 'UserTask_4', 'UserTask_5'].includes(this.taskKey) && this.type === 'execute';
      // 项目部用车可展示用车信息
      if (this.noAuthPage || this.isRead || this.formData.carResourceType === 2) {
        return true
      }
      return ['UserTask_2', 'UserTask_3', 'UserTask_4', 'UserTask_5', 'UserTask_6', 'UserTask_7'].includes(this.taskKey) && !this.isCarModify;
    },
    // isBack2() {
    //   return this.approvalList.length > 2 ? this.approvalList[this.approvalList.length - 2].taskKey === 'UserTask_3' : false
    // },
    // isCarDetailInfo() {
    //   return ['UserTask_4', 'UserTask_5'].includes(this.taskKey) && this.type === 'execute';
    // },
    isUser() {
      return ['UserTask_0', 'UserTask_1', 'UserTask_2', 'UserTask_3', 'UserTask_4', 'UserTask_6', 'UserTask_7'].includes(this.taskKey) || this.type === 'add';
    },
    isCar() {
      if (this.noAuthPage || this.isRead || (this.formData.carResourceType === 2 && this.type === 'view')) {
        return true
      }
      return (['UserTask_2', 'UserTask_3', 'UserTask_4', 'UserTask_5', 'UserTask_6', 'UserTask_7'].includes(this.taskKey) || this.isXcDriverCarModify) && !this.isCarModify;
    },
    isShowDriverConfirmEndTime() {
      return ['UserTask_4', 'UserTask_5', 'UserTask_6'].includes(this.taskKey);
    },
    isUseCarInfo() {
      return ['UserTask_5', 'UserTask_6'].includes(this.taskKey);
    },
    isEditCar() {
      return (this.taskKey === 'UserTask_2' && this.type === 'execute' && !this.isXcModify)
        || this.isXcDriverCarModify
        || (!this.isNotUseProject && this.type !== 'view');
    },
    locationInfo: {
      get() {
        return {
          longitude: this.formData.endAddressSmx,
          latitude: this.formData.endAddressSmy,
          name: this.formData.endAddress
        };
      },
      set(val) {
        this.$set(this.formData, 'endAddressSmx', val.longitude)
        this.$set(this.formData, 'endAddressSmy', val.latitude)
        this.$set(this.formData, 'endAddress', val.name)
      }
    },
    locationInfoStart: {
      get() {
        return {
          longitude: this.formData.startAddressSmx,
          latitude: this.formData.startAddressSmy,
          name: this.formData.startAddressDetail
        };
      },
      set(val) {
        this.$set(this.formData, 'startAddressSmx', val.longitude)
        this.$set(this.formData, 'startAddressSmy', val.latitude)
        this.$set(this.formData, 'startAddressDetail', val.name)
      }
    },
    row() {
      // 页面中使用row来指代当前记录
      return this.$store.state[CURRENT_ROW]
    },
    reimburseType() {
      // toline方法已在mixin中定义
      return this.$store.state[ENUM][this.toLine('reimburse_type')] || []
    },
    lang() {
      if (this.$storage.get('i18nLocale') === 'zh') {
        return 'zh-CN'
      } else {
        return 'en'
      }
    },
  },
  created() {
    this.init();
  },
  mounted() {
    this.$emit('mounted')
    if (this.isPC) {
      //获取屏幕尺寸
      this.screenWidth = document.body.clientWidth || window.innerWidth
      // 监听屏幕宽度和高度变化
      window.addEventListener('resize', this.handleResize, true)
    }

  },
  // 销毁监听
  beforeDestroy() {
    if (this.isPC) {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  methods: {
    ...mapMutations(['SET_CURRENT_ROW']),
    ...mapActions('CarApply', [
      'getCarDispatchUser',
      'getCarCompany',
      'getCarList',
      'getDriverList',
      'getRemindTitle',
      'getRejectCause',
      'getCurrentUser',
      'getCarCompanyApprover',
      'getEnum'
    ]),
    // 屏幕变化
    handleResize() {
      this.screenWidth = document.body.clientWidth || window.innerWidth
    },
    // 添加费用
    addCost() {
      if (this.delCostList.length === 0) {
        this.formData.vdAfUcCompleteFormList.push(JSON.parse(JSON.stringify(this.costInfo)));
        return false;
      }
      this.formData.vdAfUcCompleteFormList.push(this.delCostList[0]);
      this.delCostList.splice(0, 1);
    },
    // 删除费用
    delCost(index) {
      const data = this.formData.vdAfUcCompleteFormList.splice(index, 1);
      const list = data[0];
      const id = list.id;
      if (!id) {
        return false;
      }
      if (this.delCostList.length > 0) {
        const hasObject =  this.delCostList.find(obj => obj.id === id);
        !hasObject && this.delCostList.push(list);
      } else {
        this.delCostList.push(list)
      }
    },
    getCarSourceStr(value) {
      const item = this.carResources.find(item => item.key === value)
      return item ? item.value : '';
    },
    formatPhoneNumber(item, name = 'ucPersonPhone') {
      const numberList = item[name].toString().split('')
      // 电话号码按照3-4-4格式显示
      // 添加第一个横杠
      numberList.splice(3, 0, '-')
      // 添加第二个横杠
      numberList.splice(8, 0, '-')
      // 合并为字符串
      return numberList.join('')
    },
    useCurrentTime(property = 'useCarPersonConfirmEndTime') {
      this.formData[property] = dayjs().format('YYYY-MM-DD HH:mm');
    },
    handleTime({time, distance}) {
      this.timeStr = time;
      this.distanceStr = distance;
    },
    async prepareData() {
      await this.getEnum();
    },
    swipeHandler(direction) {
      if (direction == "left") {
        this.$emit("moveDirection", "left");
      } else if (direction == "right") {
        this.$emit("moveDirection", "right");
      }
    },
    onConfirmDriver() {
      this.$refs.driverList && this.$refs.driverList.onSubmit();
    },
    onConfirmCar() {
      this.$refs.carList && this.$refs.carList.onSubmit()
    },
    onConfirmPerson() {
      this.$refs.applyPerson && this.$refs.applyPerson.onSubmit()
    },
    handleDispatcherChange(val) {
      const item = this.carDispatchUserColumns.find(item => item.userFullName === val)
      if (item) {
        this.formData.carDispatchUserName = item.userName;
      }
    },
    handleApproverChange(val) {
      const item = this.companyApproveColumns.find(item => item.userFullName === val)
      this.formData.carCompanyApproverUserName = item.userName
    },
    handleNeedCarChange(val) {
      const item = this.carType.find(item => item.key === val)
      this.formData.needCarTypeStr = item.value
    },
    handleNeedCar2Change(val) {
      const item = this.carType.find(item => item.key === val)
      this.formData.needCarType2Str = item.value
    },
    handleRealCarChange(val) {
      const item = this.carType.find(item => item.key === val)
      this.formData.realCarTypeStr = item.value
    },
    handleCarResourceChange(val) {
      let shouldClear = false
      if (this.isPC){
        shouldClear = val === 2
      } else if (this.isMobile) {
        shouldClear = this.formData.carResourceType === 2
      }
      if (shouldClear) {
        // 当选择了项目部后，清空租车公司和租车审批的表单值
        this.formData.carCompanyName = '';
        this.formData.vdCarCompanyInfoId = '';
        this.formData.carCompanyApproveUserName = '';
        this.formData.carCompanyApproveFullName = '';
      }
    },
    handleStartAddressChange(val) {
      // 切换出发地后，需要置空
      this.locationInfoStart = {
        longitude: '',
        latitude: '',
        name: ''
      }
      this.formData.startAddressDetail = ''
      const source = this.isPC ? val : this.formData.startAddress;
      const item = this.startAddresses.find(item => item.key === source)
      this.formData.startAddressStr = item.value;

      if (source !== '9') {
        // 选择内置地点时需要手动赋值
        this.locationInfoStart = {
          longitude: Number(item.smx),
          latitude: Number(item.smy),
          name: item.value
        }
      }

    },
    init() {
      if (this.type === 'execute') {
        if (!this.currUser.phone) {
          this.getCurrentUser(this.$storage.get('username')).then(res => {
          });
        }
        this.isDisabled = true
      }

      if (this.type === 'view') {
        this.isDisabled = true
      }

      if (this.type === 'add') {
        this.locationInfoStart = {
          longitude: '',
          latitude: '',
          name: ''
        }
        this.locationInfo = {
          longitude: '',
          latitude: '',
          name: ''
        }
        this.startPoint = {
          name: '',
          x: '',
          y: ''
        }
        this.endPoint = {
          name: '',
          x: '',
          y: ''
        }
        this.fromLocation = false;
        this.fromFormData = false;
        this.fromRow = false;
        this.showTrack = false;
        this.SET_CURRENT_ROW({});
        if (this.useCarType.length > 0) {
          this.formData.useCarType = this.useCarType[0].key;
          this.formData.useCarTypeStr = this.useCarType[0].value;
          this.confirmUseCarType();
        }
        this.formData.startAddress = this.startAddresses[0].key;
        this.formData.startAddressStr = this.startAddresses[0].value;
        this.handleStartAddressChange(this.isPC ? this.formData.startAddress : '');
        this.formData.needCarType2 = this.carType[0].key;
        this.formData.needCarType2Str = this.carType[0].value;
        if (!this.currUser || !this.currUser.phone || !this.currUser.userFullName || !this.currUser.userName) {
          this.getCurrentUser(this.$storage.get('username')).then(res => {
            this.getApplyUserInfo(res.data);
          })
        } else {
          this.getApplyUserInfo(this.currUser);
        }

        this.getRemindTitle().then(res => {
          if (res.status) {
            this.formData.remindTitle = res.data;
          }
        })
      }
    },
    phoneValidator(val, value, callback) {
      if (this.isMobile) {
        if (val) {
          return /^[1][23456789][0-9]{9}$/.test(val)
          // return /^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-9])|(18[0-9])|166|198|199|191|(147))\d{8}$/.test(val)
        }
        return true
      } else {
        if (/^[1][23456789][0-9]{9}$/.test(value)) {
          callback()
        } else {
          if (!value) {
            callback(new Error('请输入手机号'))
          } else {
            callback(new Error('请输入正确的手机格式'))
          }
        }
      }
    },
    endTimeValidator(val, value, callback) {
      const startTime = new Date(this.formData.startTime).getTime()
      const endTime = new Date(this.formData.predictEndTime).getTime()

      if (this.isMobile) {
        // return startTime < endTime
        if (!this.formData.startTime || !this.formData.predictEndTime) {
          return true
        }
        if (startTime > endTime){
          return false;
        } else {
          return startTime !== endTime
        }
      } else {
        if (!this.formData.startTime || !this.formData.predictEndTime) {
          callback()
        } else if (startTime > endTime){
          callback(new Error('返回时间不能早于出发时间'))
        } else if (startTime === endTime) {
          callback(new Error('返回时间和出发时间不能相同'))
        } else {
          callback()
        }
      }
    },
    carNumValidator(val, value, callback) {
      const xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/;
      const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
      // const regExp = new RegExp(reg);
      if (this.isMobile) {
        if (!val) {
          return false;
        } else if (val.length === 7 && !xreg.test(val)) {
          // 新能源车
          // callback(new Error('车牌号格式错误'))
          // callback();
          return true;
        } else if (val.length === 8 && !creg.test(val)) {
          // 普通汽车
          // callback(new Error('车牌号格式错误'))
          // callback();
          return true;
        } else if (![7, 8].includes(val.length)) {
          // callback(new Error('车牌号格式错误'));
          return false;
        } else {
          callback();
          return true;
        }
      } else {
        if (!value) {
          callback(new Error('请输入车牌号'));
        } else if (value.length === 7 && !xreg.test(value)) {
          // 新能源车
          // callback(new Error('车牌号格式错误'))
          callback();
        } else if (value.length === 8 && !creg.test(value)) {
          // 普通汽车
          // callback(new Error('车牌号格式错误'))
          callback();
        } else if (![7, 8].includes(value.length)) {
          callback(new Error('车牌号格式错误'));
        } else {
          callback();
        }
      }

    },
    useCarPersonValidator(rule, value, callback) {
      if (value && value > 0) {
        callback()
      } else {
        callback(new Error('请输入乘车人数'))
      }
    },
    getApplyUserInfo(data) {
      const {userFullName, userName, phone, deptName} = data;
      this.formData.applyUserName = userName;
      this.formData.applyFullName = userFullName;
      this.formData.applyUserTime = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
      this.formData.applyDepartment = deptName;
      this.formData.applyUserPhone = phone;

      this.formData.contacts = userFullName; // 联系人
      // this.formData.contactsFullName = userFullName; // 联系人
      this.formData.contactsPhone = phone; // 联系人电话
      this.formData.contactsResource = this.userInfo.userType || 3; // 联系人来源
      this.formData.contactsOpenId = this.openId; // 联系人openId（飞书、微信才有这个）
    },
    // 获取当前租车审批列表
    getCompanyAuthList(id) {
      return this.getCarCompanyApprover(this.formData.vdCarCompanyInfoId || id).then(res => {
        if (!res.status) {
          this.isMobile && Toast({
            message: res.message,
            icon: 'none'
          });
          this.isPC && this.$message.warning(res.message);
          this.companyApproveColumns = [];
          return false;
        }
        this.companyApproveColumns = res.data;
        return res.data
      })
    },
    // 选择租车公司
    confirmCarCompany(val, isInit = false) {
      if (this.isPC) {
        const item = this.carCompanyColumns.find(item => item.ccName === val)
        item && (this.formData.vdCarCompanyInfoId = item.id)
      }
      // 获取审批人列表后需要根据条件默认选中项
      this.getCompanyAuthList(this.formData.vdCarCompanyInfoId || val).then(data => {
        if (data.length === 1 || this.isChooseCarCompany) {
          this.formData.carCompanyApproverFullName = data ? data[0].userFullName : '';
          this.formData.carCompanyApproverUserName = data ? data[0].userName : '';
        } else if (!isInit) {
          this.formData.carCompanyApproverFullName = '';
          this.formData.carCompanyApproverUserName = '';
        }
      })
    },
    // 选择用车类型确认
    confirmUseCarType(useCarType = -1) {
      if (this.isPC) {
        const item = this.useCarType.find(item => item.key === useCarType)
        item && (this.formData.useCarTypeStr = item.value)
      }
      this.getCarDispatchUser(useCarType === -1 ? this.formData.useCarType : useCarType).then(res => {
        this.carDispatchUserColumns = res.data.map(it => {
          return {...it.vdApprovalUser, userFullName: it.userFullName}
        });
        if (this.carDispatchUserColumns.length > 0 && this.type === 'add') {
          this.formData.carDispatchUserName = this.carDispatchUserColumns[0].userName;
          this.formData.carDispatchFullName = this.carDispatchUserColumns[0].userFullName;
        } else if (this.carDispatchUserColumns.length === 1) {
          this.formData.carDispatchUserName = this.carDispatchUserColumns[0].userName;
          this.formData.carDispatchFullName = this.carDispatchUserColumns[0].userFullName;
        } else if (useCarType === -1) {
          this.formData.carDispatchUserName = '';
          this.formData.carDispatchFullName = '';
        }
      })
    },
    // 打开选择司机列表
    chooseCarDriver() {
      if (this.taskKey === 'UserTask_2' || this.isXcDriverCarModify || !this.isNotUseProject) {
        this.popupDriver = true;
      }
    },
    // 打开选择申请人
    chooseApplyPerson(item, i) {
      if (i > -1 && !(this.isAdd && item.ucPersonFullName)) {
        return false;
      }

      this.popupPerson = true;
      this.currApplyPerson = item;
      this.currApplyPersonIndex = i;
    },
    // 选择司机
    closePopupDriver(val, flag = false) {
      if (!flag) {
        this.popupDriver = false;
      }

      const {driverAge, driverFullName, driverPhone, driverUserName, id} = val.vdDriverInfo;
      this.$set(this.formData, 'driverUserName', driverUserName); // 司机用户名
      this.$set(this.formData, 'driverFullName', driverFullName); // 司机姓名
      this.$set(this.formData, 'driverPhone', driverPhone); // 司机手机号
      this.$set(this.formData, 'driverAge',  +driverAge || ''); // 司机驾龄
      this.$set(this.formData, 'vdDriverInfoId',  id); // 司机id
      this.isEnterDriver = false;
    },
    // 输入司机信息
    changeDriver() {
      this.isEnterDriver = true;
      this.$forceUpdate();
    },
    // 输入车辆信息
    changeCar() {
      this.isEnterCar = true;
    },
    // 选择申请人员
    closePopupPerson(val, flag = false) {
      if (!flag) {
        this.popupPerson = false;
      }
      if (this.currApplyPersonIndex === -1) {
        val.ucPersonFullName && (this.formData.contacts = val.ucPersonFullName);
        val.ucPersonPhone && (this.formData.contactsPhone = val.ucPersonPhone); // 联系人电话
        val.ucPersonResource && (this.formData.contactsResource = val.ucPersonResource); // 联系人来源
        val.ucPersonOpenId && (this.formData.contactsOpenId = val.ucPersonOpenId); // 联系人openId（飞书、微信才有这个）
      } else {
        const {currentIndex, ...rest} = val
        this.currApplyPerson = rest;
        const rawData = this.formData.vdAfUseCarPersonList[this.currApplyPersonIndex]
        const i = this.isMobile ? this.currApplyPersonIndex : currentIndex;
        // 判断原数据是否有id属性，有则说明是需要编辑的，没有表示是需要新增的
        this.$set(
          this.formData.vdAfUseCarPersonList,
          i,
          rawData.id ? {...rest, id: rawData.id} : rest
        );
      }
    },
    closePopupMap() {
      this.mapShow = false;
      this.addressType === 'end' ? this.mapShow = false : this.mapShowStart = false;
    },
    // 打开选择车辆列表
    chooseCar() {
      // 调度员审批，车辆修改，车辆来源选择项目部时，可以直接选择车辆
      if (this.taskKey === 'UserTask_2' || this.isXcDriverCarModify || !this.isNotUseProject) {
        this.popupShow = true;
      }
    },
    // 关闭选择车辆
    closePopupCar(val, flag = false) {
      const {vdCarInfo} = val;
      const seatCount = vdCarInfo.carSeatNum;
      if (!flag) {
        this.popupShow = false;
      }
      this.$set(this.formData, 'cardId', vdCarInfo.id)
      this.$set(this.formData, 'carType', vdCarInfo.carType)
      this.$set(this.formData, 'carNum', vdCarInfo.carNum)
      this.$set(this.formData, 'seatCount', vdCarInfo.carSeatNum)
      this.$set(this.formData, 'carAge', +vdCarInfo.carAge || '')
      this.$set(this.formData, 'vdCarInfoId', vdCarInfo.id)
      // this.formData.carId = vdCarInfo.id;
      // this.formData.carType = vdCarInfo.carType;
      // this.formData.carNum = vdCarInfo.carNum;
      // this.formData.seatCount = vdCarInfo.carSeatNum;
      // this.formData.carAge = +vdCarInfo.carAge || '';
      // this.formData.vdCarInfoId = vdCarInfo.id;
      this.isEnterCar = false;
      this.closePopupDriver(val);
    },
    // 选择地区确认
    onConfirm(data) {
      this.showArea = false;
      this.formData.endAddress = data
        .filter((item) => !!item)
        .map((item) => item.name)
        .join('/');
    },
    addPerson() {
      // 添加乘车人
      this.$set(this.formData.vdAfUseCarPersonList, this.formData.vdAfUseCarPersonList.length, {
        ucPersonUserName: '',
        ucPersonFullName: '',
        ucPersonPhone: ''
      });
      this.formData.useCarPersonNum = this.formData.vdAfUseCarPersonList.length;
    },
    minusPerson(item, i) {
      // 移除乘车人
      if ((this.isExecute0 || (this.taskKey === 'UserTask_0' && this.type === 'draft')) && item.id) {
        this.delVdAfUseCarPersonList.push(this.vdAfUseCarPersonList.find(it => it.id === item.id));
      }
      if (this.isAdd) {
        this.formData.vdAfUseCarPersonList.splice(i, 1);
        this.formData.useCarPersonNum = this.formData.vdAfUseCarPersonList.length;
      }
    },
    getLocation(type) {
      this.addressType = type;
      type === 'end' ? this.mapShow = true : this.mapShowStart = true;
      this.mapShow = true;
      this.loadMap = true;
    },
    getLocationStart() {
      this.mapShowStart = !this.mapShowStart;
      this.loadMapStart = true;
    },
    handleMapUpdateStart(poi) {
      this.formData.startAddressSmx = poi.longitude;
      this.formData.startAddressSmy = poi.latitude;
      this.formData.startAddressDetail = poi.name;
    },
    handleMapUpdate(poi) {
      if (this.addressType === 'end') {
        this.$set(this.formData, 'endAddressSmx', poi.longitude)
        this.$set(this.formData, 'endAddressSmy', poi.latitude)
        this.$set(this.formData, 'endAddress', poi.name)
      } else {
        this.handleMapUpdateStart(poi);
      }
    },
    // 若需要特别处理获取到的表单数据，可在beforeInit中处理
    async beforeInit(res) {
      return new Promise((resolve) => {
        if (!res.status) {
          resolve()
        }
        const formData = res.data

        formData.useCarTypeStr = this.enums.UseCarTypeEnums.find(it => it.key === formData.useCarType)?.value;
        formData.needCarType2Str = formData.needCarType2 ? this.enums.CarType2Enums.find(it => it.key === formData.needCarType2)?.value : '';

        if (!formData.needCarType2) {
          formData.needCarType2 = formData.needCarType;
          formData.needCarType2Str = this.enums.CarType2Enums.find(it => it.key === formData.needCarType2)?.value;
        }
        this.detailTable = formData.vdAfUseCarPersonList;
        this.vdAfUseCarPersonList = JSON.parse(JSON.stringify(formData.vdAfUseCarPersonList));
        this.delVdAfUseCarPersonList = [];
        if (this.isExecute0 || (this.taskKey === 'UserTask_0' && this.type === 'draft') || this.isXcModify) {
          if (+formData.startAddress < 10) {
            formData.startAddressStr = this.enums.StartAddressEnums.find(it => it.key === formData.startAddress)?.value;
          } else {
            formData.startAddressStr = formData.startAddress;
            formData.startAddress = this.enums.StartAddressEnums.find(it => it.value === formData.startAddressStr)?.key;
          }
        }
        if (this.isExecute0 || (this.taskKey === 'UserTask_0' && this.type === 'draft')) {
          this.confirmUseCarType(formData.useCarType);
        }
        if (this.isChooseCarCompany) {
          formData.realCarType = formData.needCarType2;
          formData.realCarTypeStr = formData.needCarType2Str;
          // 车辆来源默认选中第一项
          if (!formData.carResourceType) {
            formData.carResourceType = this.carResources[0].key
            this.carResourceTypeStr = this.carResources[0].value
          }
          this.getCarCompany().then(res => {
            if (!res.status) {
              return false;
            }
            this.carCompanyColumns = res.data;
            if (!formData.carCompanyName) {
              formData.carCompanyName = res.data[0].ccName;
              formData.vdCarCompanyInfoId = res.data[0].id;
              this.confirmCarCompany(res.data[0].id);
            } else {
              this.confirmCarCompany(formData.vdCarCompanyInfoId, true);
            }
          });
        } else {
          formData.realCarTypeStr = this.enums.CarType2Enums.find(it => it.key == formData.realCarType)?.value;
        }

        if (this.isChooseDriverTime) {
          formData.vdDriverConfirmEndTime = this.$dayjs(new Date()).format('YYYY-MM-DD HH') + ':00';
        }
        // if (this.type === 'execute' && ['UserTask_4'].includes(this.taskKey) &&  this.approvalList[this.approvalList.length - 2].taskKey === 'UserTask_3') {
        //   formData.useCarPersonConfirmEndTime = formData.predictEndTime;
        // }
        if (this.isEvaluate) {
          formData.useCarPersonConfirmEndTime = this.$dayjs(new Date(formData.predictEndTime)).format('YYYY-MM-DD HH:mm');
        }
        if (this.isEditCost && formData.vdAfUcCompleteFormList.length === 0) {
          const str = {
            ...this.costInfo,
            vdDriverConfirmEndTime: formData.vdDriverConfirmEndTime,
            useCarPersonConfirmEndTime:  formData.useCarPersonConfirmEndTime
          }
          const startTime = dayjs(formData.vdDriverConfirmEndTime)
          const endTime = dayjs(formData.useCarPersonConfirmEndTime)
          str.journeyTotalTime = (endTime.diff(startTime, 'millisecond') / 3600000).toFixed(2)
          formData.vdAfUcCompleteFormList = [JSON.parse(JSON.stringify(str))];
        }
        if (this.isEditCost || ['UserTask_6'].includes(this.taskKey)) {
          const vdAfUcCompleteFormList = formData.vdAfUcCompleteFormList;
          vdAfUcCompleteFormList.map((item, index) => {
            const data = item;
            const str = {
              ...this.costInfo,
              vdDriverConfirmEndTime: formData.vdDriverConfirmEndTime,
              useCarPersonConfirmEndTime: formData.useCarPersonConfirmEndTime
            }

            const keys = Object.keys(str);
            for (let i = 0; i < keys.length; i++) {
              if (!data[keys[i]]) {
                if (keys[i] === 'vdDriverConfirmEndTime' || keys[i] === 'useCarPersonConfirmEndTime') {
                  formData.vdAfUcCompleteFormList[index][keys[i]] = formData[keys[i]];
                } else {
                  formData.vdAfUcCompleteFormList[index][keys[i]] = ['UserTask_6'].includes(this.taskKey)
                    ? (formData.vdAfUcCompleteFormList[index][keys[i]] || str[keys[i]])
                    : '';
                }
              }
            }
          })
          // const data = formData.vdAfUcCompleteForm;
          // const str = {
          //   // carCostRent: '0.00',
          //   // carCostPark: '0.00',
          //   // carCostToll: '0.00',
          //   // carCostGasoline: '0.00',
          //   // journeyTotalTime: '0.00',
          //   // journeyOverTime: '0.00',
          //   // journeyOverTimeCost: '0.00',
          //   // journeyTotalKm: '0.00',
          //   // journeyOverKm: '0.00',
          //   // journeyOverKmCost: '0.00',
          //   // journeyCostOther: '0.00',
          //   // journeyCostTotal: '0.00',
          //   ...this.costInfo,
          //   vdDriverConfirmEndTime: formData.vdDriverConfirmEndTime,
          //   useCarPersonConfirmEndTime: formData.useCarPersonConfirmEndTime
          // }
          // const keys = Object.keys(str);
          // for (let i = 0; i < keys.length; i++) {
          //   if (!data[keys[i]]) {
          //     if (keys[i] === 'vdDriverConfirmEndTime' || keys[i] === 'useCarPersonConfirmEndTime') {
          //       formData.vdAfUcCompleteForm[keys[i]] = formData[keys[i]];
          //     } else {
          //       formData.vdAfUcCompleteForm[keys[i]] = '';
          //     }
          //   }
          // }
        }
        // 项目部用车可以展示用车信息
        if (this.isEditCar || ['UserTask_6', 'UserTask_7'].includes(this.taskKey) || formData.carResourceType === 2) {
          this.getCarList(formData.vdCarCompanyInfoId);
          // this.getDriverList({vdCarCompanyInfoId: formData.vdCarCompanyInfoId, searchValue: ''});
          if (formData.vdCarInfo) {
            formData.carId = formData.vdCarInfo.id;
            formData.carType = formData.vdCarInfo.carType;
            formData.carNum = formData.vdCarInfo.carNum;
            formData.seatCount = formData.vdCarInfo.carSeatNum;
            formData.carAge = +formData.vdCarInfo.carAge || '';
            formData.vdCarInfoId = formData.vdCarInfo.id;
          }
          if (formData.vdDriverInfo) {
            const {
              driverAge,
              driverFullName,
              driverPhone,
              driverUserName,
              id
            } = formData.vdDriverInfo
            formData.driverUserName = driverUserName; // 司机用户名
            formData.driverFullName = driverFullName; // 司机姓名
            formData.driverPhone = driverPhone; // 司机手机号
            formData.driverAge = +driverAge || ''; // 司机驾龄
            formData.vdDriverInfoId = id; // 司机id
          }
        }
        // if (this.isBack2) {
        //   this.closePopupCar(formData.vdCarInfo)
        //   this.closePopupDriver(formData.vdDriverInfo)
        // }

        // isCarInfo vdCarInfo carNum carType carAge
        // vdDriverInfo driverFullName driverPhone driverAge
        if (this.isCarInfo || this.taskKey === 'UserTask_6' || formData.carResourceType === 2) {
          formData.vdCarInfo ? this.vdCarInfo = formData.vdCarInfo : '';
          formData.vdDriverInfo ? this.vdDriverInfo = formData.vdDriverInfo : '';
        }
        // if (!formData.contactsFullName) {
        //   formData.contactsFullName = this.$storage.get('userFullname');
        // }
        if (!formData.useCarPersonNum) {
          formData.useCarPersonNum = 1;
        }
        // 判断邮箱短信的选中状态
        // if (res.data.entityObject.notifyMethod != null) {
        //   this.checkboxGroup = res.data.entityObject.notifyMethod.split(',')
        // }
        console.log(formData)
        resolve({formData})
      })
    },
    // 操作前的校验
    validator() {
      if (this.taskKey === 'UserTask_1') {
        // 校验是否选择车辆来源
        if (!this.formData.carResourceType && !this.isXcModify){
          this.isMobile && Toast('请选择车辆来源')
          this.isPC && this.$message.warning('请选择车辆来源')
          return false
        }
        // 如果车辆来源选择项目部，则校验是否填写车辆，司机信息
        if (this.formData.carResourceType === 2) {
          const properties = [
            {key: 'carNum', text: '车牌号'},
            {key: 'carType', text: '车型'},
            {key: 'driverFullName', text: '姓名'},
            {key: 'driverPhone', text: '联系电话'}
          ]
          const returnIndex = properties.findIndex(({key}) => !this.formData[key])
          if (returnIndex > -1) {
            const text = `请填写${properties[returnIndex].text}`
            this.isMobile && Toast(text)
            this.isPC && this.$message.warning(text)
            return false
          }
        }
      }
      if (this.isChooseCarCompany && !this.formData.vdCarCompanyInfoId && this.formData.carResourceType === 1) {
        this.isMobile && Toast('请选择租车公司')
        this.isPC && this.$message.warning('请选择租车公司')
        return false
      }
      if (this.type === 'execute' && ['UserTask_2'].includes(this.taskKey) && !this.isCarModify && !this.isXcModify) {
        if (!this.formData.vdCarInfoId) {
          if (!this.formData.carNum) {
            this.isMobile && Toast('请选择车辆或者输入车牌号');
            this.isPC && this.$message.warning('请选择车辆或者输入车牌号');
            return false;
          }
          if (!this.formData.carType) {
            this.isMobile && Toast('请选择车辆或者输入车型');
            this.isPC && this.$message.warning('请选择车辆或者输入车型');
            return false
          }
          if (!this.formData.carNum && !this.formData.carType) {
            this.isMobile && Toast('请选择车辆')
            this.isPC && this.$message.warning('请选择车辆')
            return false
          }
        }
        if (!this.formData.driverFullName) {
          this.isMobile && Toast('请选择司机或者输入驾驶员姓名');
          this.isPC && this.$message.warning('请选择司机或者输入驾驶员姓名');
          return false;
        }
        if (!this.formData.driverPhone) {
          this.isMobile && Toast('请选择司机或者输入驾驶员联系电话');
          this.isPC && this.$message.warning('请选择司机或者输入驾驶员联系电话');
          return false
        }
        if (!this.formData.driverFullName && !this.formData.driverPhone) {
          this.isMobile && Toast('请选择司机')
          this.isPC && this.$message.warning('请选择司机')
          return false
        }
      }
      if (this.type === 'execute' && this.taskKey === 'UserTask_5') {
        let flag = true;
        const vdAfUcCompleteFormList = this.formData.vdAfUcCompleteFormList;
        for (let j = 0; j < vdAfUcCompleteFormList.length; j++) {
          const data = this.$refs[`carCost${j}`][0].formData;
          for (let i = 0; i < list.length; i++) {
            // 当表单项为必填且值为0.00或者为空时则提醒
            const isZero = +data[list[i].prop] === 0
            const isNullString = !Boolean(data[list[i].prop])
            const isFlag = j === 0 || (!['vdDriverConfirmEndTime', 'useCarPersonConfirmEndTime'].includes(list[i].prop) && j > 0);
             if (list[i].required && (isZero || isNullString) && isFlag) {
              this.isMobile && Toast(`请填写${list[i].label}`);
              this.isPC && this.$message.warning(`请填写${list[i].label}`)
              flag = false;
              break;
            }
          }
          if (new Date(data['vdDriverConfirmEndTime']).getTime() >= new Date(data['useCarPersonConfirmEndTime']).getTime()) {
            flag = false
            const errText = '开始时间不能晚于结束时间'
            this.$nextTick(()=>{
              this.isMobile && Toast(errText)
              this.isPC && this.$message.warning(errText)
            })
          }
          if (!flag) {
            break;
          }
        }
        // const data = this.$refs.carCost.formData;
        // for (let i = 0; i < list.length; i++) {
        //   // 当表单项为必填且值为0.00或者为空时则提醒
        //   const isZero = +data[list[i].prop] === 0
        //   const isNullString = !Boolean(data[list[i].prop])
        //   if (list[i].required && (isZero || isNullString)) {
        //     this.isMobile && Toast(`请填写${list[i].label}`);
        //     this.isPC && this.$message.warning(`请填写${list[i].label}`)
        //     flag = false;
        //     break;
        //   }
        // }
        // 开始时间不能晚于结束时间
        // if (new Date(data['vdDriverConfirmEndTime']).getTime() >= new Date(data['useCarPersonConfirmEndTime'])) {
        //   flag = false
        //   const errText = '开始时间不能晚于结束时间'
        //   this.$nextTick(()=>{
        //     this.isMobile && Toast(errText)
        //     this.isPC && this.$message.warning(errText)
        //   })
        // }
        return flag
      }
      if (this.isModify && !this.modifycomment) {
        this.isMobile && Toast('请填写变更原因')
        this.isPC && this.$message.warning('请填写变更原因')
        return false
      }
      return true
    },
    // 表单提交(审批)前
    beforeSubmit() {
      // 示例表单计划日期字段在数据库类型韦datetime，需要处理下
      // this.formData.createDate = this.formData.createDate
      //   ? this.$dayjs(this.formData.createDate).format('YYYY-MM-DD')
      //   : null
      // 如果有上传文件，则合并文件filetoken为groupToken
      // if (this.fileList.length > 0) {
      //   // 屏蔽watch监听附件值变更重新获取文件
      //   this.$refs.attachment.needToGetFile = false
      //   this.$refs.attachment.setG9s()
      // } else {
      //   this.formData.attachment = ''
      // }
      this.formData.reimburseType = this.formData.type
      // 赋值给FormCenter组件，用于流程处理时提交表单信息

      let temp = JSON.parse(JSON.stringify(this.formData))
      temp.createDate = this.$dayjs(this.formData.createDate).format(
        'YYYY-MM-DD 00:00:00'
      )
      // 当填写了开始时间才补充秒数
      temp.startTime && temp.startTime.split(':').length < 3&& (temp.startTime = temp.startTime + ':00')
      temp.predictEndTime  && temp.predictEndTime.split(':').length < 3 && (temp.predictEndTime = temp.predictEndTime + ':00')
      if (this.isExecute0 || (this.taskKey === 'UserTask_0' && this.type === 'draft')) {
        temp.startAddress = temp.startAddressStr
      }
      this.$emit('setFormData', temp)
      const taskKey = this.taskKey;
      if (this.isEditCost) {
        // 当处于用户费用填报模块中
        const data = this.formData.vdAfUcCompleteFormList;
        const keys = list.map(item => item.prop);
        this.formData.vdAfUcCompleteFormList = data.map((item, j) => {
          for (let i = 0; i < keys.length; i++) {
            // console.log( '当处于用户费用填报模块中0000', this.formData.vdAfUcCompleteFormList )
            if (!data[j][keys[i]]) {
              // 将用户未填写的字段默认补零
              item[keys[i]] = '0.00';
            }
          }
          // 若用户更改了实际开始时间/结束时间，需要同步更新
           j === 0 ? ['vdDriverConfirmEndTime', 'useCarPersonConfirmEndTime'].forEach(key => {
             // console.log( '当处于用户费用填报模块中' + key, this.formData.vdAfUcCompleteFormList )
             item[key] = this.$dayjs(data[j][key]).format('YYYY-MM-DD HH:mm:00');
             this.formData[key] = this.$dayjs(data[j][key]).format('YYYY-MM-DD HH:mm:00');
          }) : '';
          return item;
        })
        // console.log( '当处于用户费用填报模块中', this.formData.vdAfUcCompleteFormList )
      }
      if (this.type !== 'add' && this.type !== 'edit' && taskKey !== 'UserTask_0') {
        return new Promise((resolve) => {
          if (this.isContacts && !!this.form.evaluateScore) {
            const currCar = this.formData;
            const params = {
              vdApplyFormId: currCar.id,
              vdCarCompanyInfoId: currCar.vdCarCompanyInfoId,
              vdCarInfoId: currCar.vdCarInfoId,
              vdDriverInfoId: currCar.vdDriverInfoId,
              evaluateScore: this.form.evaluateScore,
              evaluateContent: this.form.evaluateContent
            }
            updateEvaluate(params).then(res => {
              if (res.status) {
                resolve(true);
              }
            }).catch(() => {
              resolve(false);
            });
          } else {
            resolve(true);
          }
        });

        // this.historyFlowDataManage();
        // this.getEntityParam();
        // this.$emit('setFormData', temp)
      } else {
        return new Promise((resolve) => {
          if (this.$refs.detailsForm) {
            this.$refs.detailsForm.validate((valid) => {
              this.uniquenessCheck().then((isUniqueness) => {
                resolve(valid && isUniqueness);
              });
            })
          } else {
            resolve(true);
          }
        });
      }
    },
    // 提交
    uniquenessCheck() {
      const taskKey = this.taskKey;
      const checkDetailsTable = this.detailTable;
      return new Promise((resolve, reject) => {
        if (!checkDetailsTable.length) {
          this.isMobile && Toast('明细表不能为空，请检查！');
          this.isPC && this.$message.warning('明细表不能为空，请检查！')
          resolve(false);
          return;
        }
        if (taskKey === 'UserTask_0' || this.type === 'edit') {
          this.getEntityParam();
          resolve(true);
          return;
        }
        this.getEntityParam();
        resolve(true);
        reject();
      });
    },
    // 获取请求参数
    getEntityParam(formProcessParam) {
      const formData = JSON.parse(JSON.stringify(this.formData));
      // 添加当前人员的顶级部门
      formData.applyTopDepartment = this.currUser.topDeptName || '';
      // 当申请时，或者申请被退回后重新填写时，且填写了时间才需要自动加上时间:00
      const isApply = this.type === 'add'
      const isReSubmit = this.type === 'execute' && this.taskKey === 'UserTask_0'
      const isTimeValid = this.formData.startTime && this.formData.predictEndTime
      if (isApply || isReSubmit) {
        formData.startAddress = formData.startAddressStr
        if (isTimeValid) {
          formData.startTime = formData.startTime + ':00'
          formData.predictEndTime = formData.predictEndTime + ':00'
        }
        // formProcessParam.variable = formData;
        formProcessParam.variable = {
          applyUserName: formData.applyUserName,
          carDispatchUserName: formData.carDispatchUserName
        };
      } else {
        if (this.isExecute0 || (this.taskKey === 'UserTask_0' && this.type === 'draft') || this.isModify) {
          formData.startAddress = formData.startAddressStr
        }
        formProcessParam.variable = {
          applyUserName: formData.applyUserName,
          carDispatchUserName: formData.carDispatchUserName,
          carCompanyApproverUserName: formData.carCompanyApproverUserName,
          driverUserName: formData.driverUserName
        }
      }
      const vdAfUcCompleteFormList = formData.vdAfUcCompleteFormList.map((item, i) => {
        const $carCost = this.isUseCarInfo ? this.$refs[`carCost${i}`][0] : '';
        const vdAfUcCompleteForm = this.isUseCarInfo ? $carCost.formData : (item || {});
        if (this.isUseCarInfo) {
          vdAfUcCompleteForm.journeyCostTotal = $carCost.journeyCostTotal
        }
        if (this.isEditCar) {
          vdAfUcCompleteForm.vdCarCompanyInfoId = formData.vdCarCompanyInfoId
          formData.vdAfUcCompleteFormList[i].vdCarCompanyInfoId = formData.vdCarCompanyInfoId
        }
        if (this.type === 'execute' && this.taskKey === 'UserTask_5') {
          vdAfUcCompleteForm.vdCarCompanyInfoId = formData.vdCarCompanyInfoId
          vdAfUcCompleteForm.vdCarInfoId = formData.vdCarInfoId
          vdAfUcCompleteForm.vdDriverInfoId = formData.vdDriverInfoId
        }

        if (this.type === 'execute' && this.taskKey === 'UserTask_6') {
          vdAfUcCompleteForm.formConfirm = 1 // 确认无误（1、确认，null 流程还没到这）
        }
        return vdAfUcCompleteForm;
      })
      // const vdAfUcCompleteForm = this.isUseCarInfo ? this.$refs.carCost.formData : (formData.vdAfUcCompleteForm || {});
      let inputVdCarInfo = {};
      let inputVdDriverInfo = {};

      if (this.isChooseCarCompany) {
        formData.needCarType2 = formData.realCarType;
        formData.needCarType2Str = formData.realCarTypeStr;
      }
      if (this.isUseCarInfo) {
        // vdAfUcCompleteForm.journeyCostTotal = this.$refs.carCost.journeyCostTotal
      }
      if (this.isEditCar) {
        // vdAfUcCompleteForm.vdCarCompanyInfoId = formData.vdCarCompanyInfoId
        // formData.vdAfUcCompleteForm.vdCarCompanyInfoId = formData.vdCarCompanyInfoId
        formProcessParam.variable.driverUserName = formData.driverUserName
        if (this.isEnterCar) {
          const {carAge, carNum, carType} = formData
          inputVdCarInfo = {
            carAge,
            carNum,
            carType,
            carResource: 2
          }
          Reflect.deleteProperty(formData, 'carId')
          Reflect.deleteProperty(formData, 'seatCount')
          Reflect.deleteProperty(formData, 'vdCarInfoId')
          Reflect.deleteProperty(formData, 'carAge')
          Reflect.deleteProperty(formData, 'carNum')
          Reflect.deleteProperty(formData, 'carType')
        } else {
          formData.carResource = 1
        }
        if (this.isEnterDriver) {
          const {driverAge, driverFullName, driverPhone} = formData
          inputVdDriverInfo = {
            driverAge,
            driverFullName,
            driverPhone,
            driverResource: 2
          }
          Reflect.deleteProperty(formData, 'driverAge')
          Reflect.deleteProperty(formData, 'driverFullName')
          Reflect.deleteProperty(formData, 'driverName')
          Reflect.deleteProperty(formData, 'driverPhone')
          Reflect.deleteProperty(formData, 'vdDriverInfoId')
        } else {
          formData.driverResource = 1
        }
      }

      if (this.type === 'execute' && this.taskKey === 'UserTask_3') {
        formData.vdDriverConfirmEndTime = formData.vdDriverConfirmEndTime + ':00';
      }

      if (this.type === 'execute' && this.taskKey === 'UserTask_4') {
        formData.useCarPersonConfirmEndTime = formData.useCarPersonConfirmEndTime + ':00';
      }

      if (this.type === 'execute' && this.taskKey === 'UserTask_5') {
        // vdAfUcCompleteForm.vdCarCompanyInfoId = formData.vdCarCompanyInfoId
        // vdAfUcCompleteForm.vdCarInfoId = formData.vdCarInfoId
        // vdAfUcCompleteForm.vdDriverInfoId = formData.vdDriverInfoId
      }

      if (this.type === 'execute' && this.taskKey === 'UserTask_6') {
        // vdAfUcCompleteForm.formConfirm = 1 // 确认无误（1、确认，null 流程还没到这）
      }

      if (this.isChooseCarCompany) {
        formProcessParam.carCompanyApproveUserName = formData.carCompanyApproverUserName
      }

      // 携带车辆来源参数
      formProcessParam.carResourceType = formData.carResourceType

      // carCost
      if (typeof +formData.startAddress === 'number') {
       formData.startAddress = formData.startAddressStr
      }
      return {
        delVdAfUseCarPersonList: this.delVdAfUseCarPersonList, // 申请表单-删除的乘车人员
        inputVdCarInfo, // 输入的车辆信息
        inputVdDriverInfo, // 输入的驾驶员信息
        formProcessParam, // 发起流程参数
        vdAfUcCompleteForm: vdAfUcCompleteFormList.length > 0 ? vdAfUcCompleteFormList[0] : {}, // 申请表单-用车完成填报
        vdAfUcCompleteFormList, // 申请表单-用车完成填报
        vdAfUseCarPersonList: this.isAdd ? this.formData.vdAfUseCarPersonList.map(item => {
          item.ucPersonResource = item.ucPersonResource || 3
          return item;
        }) : this.formData.vdAfUseCarPersonList, // 申请表单-乘车人员
        vdApplyForm: formData, // 申请表单-主表单
        modifycomment: this.isModify ? this.modifycomment : null
      }
    },
    // 限制步进器最多只能三位小数
    stepperChange(val) {
      // 当有值或为数字时判断
      if (val === '' || val == null || isNaN(val)) {
        return false
      }
      let reg = /^\d+\.?\d{0,3}$/
      let newVal = null
      if (!reg.test(val)) {
        Toast('限制正数或3位小数')
        val = val.toString()
        newVal =
          val.indexOf('.') > -1 ? val.slice(0, val.indexOf('.') + 4) : val
        setTimeout(() => {
          this.$set(this.formData, 'useCarPersonNum', newVal)
        }, 500)
      }
    },
    checkbox(value) {
      this.formData.notifyMethod = value.toString()
    },
    fileChange(item) {
      this.fileList = item.fileList
    },
  }
}
</script>

<style lang="less" scoped>
@import "./index.less";

.appendButton {
  /deep/ .fks-input-group__append {
    background-color: rgba(23, 144, 254, 0.1) !important;
    color: #027AFF !important;
    border: 1px solid rgba(23, 144, 254, 0.4) !important;
  }

  /deep/ .fks-input__inner {
  }
}

.pl-10 .fm-cell__title > span {
  padding-left: 10px;
}

.special {
  /deep/ .fks-form-item__content {
    margin-left: 0 !important;
  }
}

.pc-container {
  padding: 32px;

  .table {
    margin-top: 32px
  }

  .align-sub {
    vertical-align: sub;
  }

  .text-title {
    font-size: 32px;
    font-weight: bold;
    margin-top: 0;
    margin-bottom: 0;
  }

  .border-line {
    border: 1px solid #eee;
    margin: 0 32px 32px;
  }

  .sty-cell {
    padding: 20px 32px;
  }

  .car-cell {
    padding: 20px 32px;

    .fm-cell {
      padding: 0;
    }

    .fm-cell__title {
      width: 100%;
      text-align: right;
    }

    /deep/ .fm-cell__title, .fm-cell__value {
      -webkit-box-flex: none;
      -webkit-flex: none;
      flex: none;
    }

    /deep/ .fm-cell__title > span {
      color: #ff4d4f !important;
    }
  }
}

/deep/ .fks-input.is-disabled .fks-input__inner {
  background: #F4F4F4;
  color: #333 !important;
}

/deep/ .fks-textarea.is-disabled .fks-textarea__inner {
  background: #F4F4F4;
  color: #333 !important;
}

@media only screen and (min-width: 992px) {
  .distance-col {
    position: relative;
    left: 0;
    top: 0;
    transform: translate(0, -108px);
  }
}
</style>
