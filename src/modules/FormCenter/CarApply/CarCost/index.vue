<template>
  <fm-form v-if="isMobile" ref="form" validate-first>
    <template v-if="isEdit">
      <div v-if="index === 0" class="cost-card">
        <fm-field
          v-model="formData.vdDriverConfirmEndTime"
          :rules="[{ required: true, message: '请选择实际开始时间' }]"
          clearable
          error-message-align="right"
          input-align="right"
          label="实际开始时间"
          label-width="120"
          placeholder="请选择实际开始时间"
          required
          @click.stop.native="showCalendarStart = true"
        />
        <date-time-picker
            :show.sync="showCalendarStart"
            :time.sync="formData.vdDriverConfirmEndTime"
            title="开始时间"
            type="datetime"
        />
        <fm-field
          v-model="formData.useCarPersonConfirmEndTime"
          :rules="[{ required: true, message: '请选择实际结束时间' }]"
          clearable
          error-message-align="right"
          input-align="right"
          label="实际结束时间"
          label-width="120"
          placeholder="请选择实际结束时间"
          required
          @click.stop.native="showCalendarEnd = true"
        />
        <date-time-picker
            :show.sync="showCalendarEnd"
            :time.sync="formData.useCarPersonConfirmEndTime"
            title="结束时间"
            type="datetime"
        />
      </div>
      <div v-if="index === 0" class="p-b-30 empty-black bg-white"></div>
      <div :class="{'cost-card': index === 0}">
        <div  v-if="index === 0" class="full-width h-48 color-black  font-28 font-bold p-l-32 fee-black border-radius-8 m-t-32">
          费用{{ index + 1 }}
        </div>
        <fm-field
            v-model="formData.carCostRent"
            :placeholder="getPlaceholder('元')"
            :rules="[{ required: true, message: '请填写租金' }, { pattern: /^(\d{1,6})(\.\d{0,2})?$/, message: '请输入正确金额！' }]"
            clearable
            error-message-align="right"
            input-align="right"
            label="租金(元)"
            required
            type="number"
        />
        <fm-field
            v-model="formData.carCostPark"
            :placeholder="getPlaceholder('元')"
            clearable
            error-message-align="right"
            input-align="right"
            label="停车费(元)"
            type="number"
        />
        <fm-field
            v-model="formData.carCostToll"
            :placeholder="getPlaceholder('元')"
            clearable
            error-message-align="right"
            input-align="right"
            label="过路费(元)"
            type="number"
        />
        <fm-field
            v-model="formData.carCostGasoline"
            :placeholder="getPlaceholder('元')"
            clearable
            error-message-align="right"
            input-align="right"
            label="汽油费(元)"
            type="number"
        />
        <fm-field
            v-model="formData.journeyTotalTime"
            :placeholder="getPlaceholder('小时')"
            :rules="[{ required: true, message: '请填写总时间(小时)' }]"
            clearable
            error-message-align="right"
            input-align="right"
            label="总时间(小时)"
            label-width="10em"
            required
            type="number"
        />
        <fm-field
            v-model="formData.journeyOverTime"
            :placeholder="getPlaceholder('小时')"
            clearable
            error-message-align="right"
            input-align="right"
            label="超时间(小时)"
            label-width="10em"
            type="number"
        />
        <fm-field
            v-model="formData.journeyOverTimeCost"
            :placeholder="getPlaceholder('元')"
            clearable
            error-message-align="right"
            input-align="right"
            label="超时费(元)"
            type="number"
        />
        <fm-field
            v-model="formData.journeyTotalKm"
            :placeholder="getPlaceholder('公里')"
            :rules="[{ required: true, message: '请填写总公里' }]"
            clearable
            error-message-align="right"
            input-align="right"
            label="总公里"
            required
            type="number"
        />
        <fm-field
            v-model="formData.journeyOverKm"
            :placeholder="getPlaceholder('公里')"
            clearable
            error-message-align="right"
            input-align="right"
            label="超公里"
            label-width="10em"
            name="journeyOverKm"
            type="number"
        />
        <fm-field
            v-model="formData.journeyOverKmCost"
            :placeholder="getPlaceholder('元')"
            clearable
            error-message-align="right"
            input-align="right"
            label="超公里费(元)"
            label-width="10em"
            type="number"
        />
        <fm-field
            v-model="formData.journeyCostOther"
            :placeholder="getPlaceholder('元')"
            clearable
            error-message-align="right"
            input-align="right"
            label="住宿费等其他费用(元)"
            label-width="11em"
            type="number"
        />
        <fm-field
            v-model="journeyCostTotal"
            :readonly="true"
            input-align="right"
            label="费用合计(元)"
            label-width="10em"
            name="journeyCostTotal"
        />
        <div class="textarea-type">
          <fm-cell-group title="备注">
            <fm-field
                v-model="formData.remarks"
                :placeholder="isEdit ? '请输入补充说明' : ''"
                maxlength="500"
                name="remarks"
                show-word-limit
                type="textarea"
            />
          </fm-cell-group>
        </div>
      </div>
    </template>
    <template v-else>
      <template v-if="index === 0">
        <div class="cost-card">
          <fm-field
              v-model="formData.vdDriverConfirmEndTime"
              :readonly="true"
              :rules="[{ required: true }]"
              input-align="right"
              label="实际开始时间"
              label-width="120"
              name="vdDriverConfirmEndTime"
              required
          />
          <fm-field
              v-model="formData.useCarPersonConfirmEndTime"
              :readonly="true"
              :rules="[{ required: true }]"
              input-align="right"
              label="实际结束时间"
              label-width="120"
              name="useCarPersonConfirmEndTime"
              required
          />
        </div>
        <div class="p-b-30 empty-black bg-white"></div>
      </template>
      <div :class="{'cost-card': index === 0}">
        <div  v-if="index === 0" class="full-width h-48 color-black  font-28 font-bold p-l-32 fee-black border-radius-8 m-t-32">
          费用{{ index + 1 }}
        </div>
        <fm-field
            v-model="formData.carCostRent"
            :readonly="true"
            :rules="[{ required: true }]"
            input-align="right"
            label="租金(元)"
            name="carCostRent"
            required
        />
        <fm-field
            v-model="formData.carCostPark"
            :readonly="true"
            input-align="right"
            label="停车费(元)"
            name="carCostPark"
        />
        <fm-field
            v-model="formData.carCostToll"
            :readonly="true"
            input-align="right"
            label="过路费(元)"
            name="carCostToll"
        />
        <fm-field
            v-model="formData.carCostGasoline"
            :readonly="true"
            input-align="right"
            label="汽油费(元)"
            name="carCostGasoline"
        />
        <fm-field
            v-model="formData.journeyTotalTime"
            :readonly="true"
            input-align="right"
            label="总时间(小时)"
            name="journeyTotalTime"
        />
        <fm-field
            v-model="formData.journeyOverTime"
            :readonly="true"
            input-align="right"
            label="超时间(小时)"
            name="journeyOverTime"
        />
        <fm-field
            v-model="formData.journeyOverTimeCost"
            :readonly="true"
            input-align="right"
            label="超时费(元)"
            name="journeyOverTimeCost"
        />
        <fm-field
            v-model="formData.journeyTotalKm"
            :readonly="true"
            input-align="right"
            label="总公里"
            name="journeyTotalKm"
        />
        <fm-field
            v-model="formData.journeyOverKm"
            :readonly="true"
            input-align="right"
            label="超公里"
            name="journeyOverKm"
        />
        <fm-field
            v-model="formData.journeyOverKmCost"
            :readonly="true"
            input-align="right"
            label="超公里费(元)"
            name="journeyOverKmCost"
        />
        <fm-field
            v-model="formData.journeyCostOther"
            :readonly="true"
            input-align="right"
            label="住宿费等其他费用(元)"
            label-width="11em"
            name="journeyCostOther"
        />
        <fm-field
            v-model="formData.journeyCostTotal"
            :readonly="true"
            input-align="right"
            label="费用合计(元)"
            name="journeyCostTotal"
        />
        <div class="textarea-type">
          <fm-cell-group class="m-l-10" title="备注">
            <fm-field
                v-model="formData.remarks"
                :placeholder="isEdit ? '请输入补充说明' : ''"
                :readonly="true"
                maxlength="500"
                name="remarks"
                show-word-limit
                type="textarea"
            />
          </fm-cell-group>
        </div>
      </div>
    </template>
  </fm-form>
  <fks-form
      v-else
      ref="form" :model="formData"
      label-position="right"
      label-width="130px"
  >
    <template  v-for="(item, i) in list">
      <fks-col  :lg="24" :md="24" :sm="24" :xl="24" :xs="24" v-if="index === 0 && i === 2" class="fee-black border-radius-8 m-t-32 ">
        <div class="full-width h-48 color-black font-32 font-bold p-l-32">
          费用{{ index + 1 }}
        </div>
      </fks-col>
      <fks-col
        v-if="index === 0 ? true : index > 0 && !['vdDriverConfirmEndTime', 'useCarPersonConfirmEndTime'].includes(item.prop)"
        :key="item.prop"
        :lg="12" :md="24" :sm="24" :xl="12" :xs="24">
        <fks-form-item
            :class="index === 0 && [1, 0].includes(i) ? ['border-radius-8'] :[]"
            :key="item.prop"
            :label="item.label"
            :prop="item.prop"
            :rules="[{ required: item.required }]"
        >

          <div v-if="isEdit">
            <fks-input
              v-if="item.type === 'input-number'"
              v-model="formData[item.prop]"
              :placeholder="getPlaceholder(item.unit)"
              style="width: 100%"
            >
              <template slot="append">
                {{ item.unit ? `${item.unit}` : '' }}
              </template>
            </fks-input>

            <fks-date-picker
                v-else-if="item.type === 'date'"
                v-model="formData[item.prop]"
                :pickerOptions="getPickerOptions(item.prop)"
                :placeholder="`请选择${item.label}`"
                class="full-width"
                clearable
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:00"
            />
          </div>
          <div v-else>
            <fks-input
                v-if="item.type === 'input-number'"
                v-model="formData[item.prop]"
                class="full-width"
                disabled
            >
              <template slot="append">
                {{ item.unit ? `${item.unit}` : '' }}
              </template>
            </fks-input>
            <fks-input
                v-else-if="item.type === 'date'"
                v-model="formData[item.prop]"
                class="full-width"
                disabled
            />
          </div>

        </fks-form-item>
        <div v-if="index === 0 && [1, 0].includes(i)" class="p-b-30 empty-black bg-white"></div>
      </fks-col>
    </template>
    <fks-form-item label="费用合计">
      <fks-input :value="journeyCostTotal" disabled>
        <template slot="append">
          元
        </template>
      </fks-input>
    </fks-form-item>
    <fks-form-item label="备注" prop="remarks">
      <fks-input
          v-model="formData.remarks"
          :disabled="!isEdit"
          :placeholder="isEdit ? '请输入补充说明' : ''"
          :rows="5"
          maxlength="500"
          show-word-limit
          type="textarea"
      />
    </fks-form-item>
  </fks-form>
</template>

<script>
import platform from "@/mixins/platform";
import {list} from './constant'
import dayjs from "dayjs";
import DateTimePicker from "@modules/FormCenter/components/DateTimePicker/index.vue";

export default {
  name: 'CarCost',
  components: {DateTimePicker},
  mixins: [platform],
  props: {
    index: {
      type: Number,
      default: -1
    },
    isEdit: {
      type: Boolean,
      default: true
    },
    vdAfUcCompleteForm: {
      type: Object,
      default() {
        return {}
      }
    },
    sourceFormData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    vdAfUcCompleteForm: {
      handler(val) {
        this.formData = val;
        this.journeyCostTotal = val.journeyCostTotal;
        if (val.vdDriverConfirmEndTime && val.useCarPersonConfirmEndTime) {
          // 默认补充journeyTotalTime
          // this.getTotalTime(val.vdDriverConfirmEndTime, val.useCarPersonConfirmEndTime)
        }
      },
      immediate: true,
      deep: true
    },
    // 'sourceFormData.vdAfUcCompleteFormList': {
    //   handler(newVal) {
    //     // console.log('vdAfUcCompleteForm',newVal)
    //     // !this.isEdit && this.$nextTick(() => {
    //     //   this.$set(this.formData, 'vdDriverConfirmEndTime', newVal.vdDriverConfirmEndTime)
    //     //   this.$set(this.formData, 'useCarPersonConfirmEndTime', newVal.useCarPersonConfirmEndTime)
    //     // })
    //   },
    //   deep: true
    //
    // },
    'formData.vdDriverConfirmEndTime': {
      handler(newVal) {
        this.getTotalTime(newVal, this.formData.useCarPersonConfirmEndTime)
      }
    },
    'formData.useCarPersonConfirmEndTime': {
      handler(newVal) {
        this.getTotalTime(this.formData.vdDriverConfirmEndTime, newVal)
      }
    }
  },
  data() {
    return {
      list,
      showCalendarStart: false,
      showCalendarEnd: false,
      formData: {
        vdDriverConfirmEndTime: '', // 实际开始时间
        useCarPersonConfirmEndTime: '', // 实际结束时间
        carCostRent: '', // 租金
        carCostPark: '', // 停车费
        carCostToll: '', // 过路费
        carCostGasoline: '', // 汽油费
        journeyTotalTime: '', // 总时间
        journeyOverTime: '', // 超时间
        journeyOverTimeCost: '', // 超时费
        journeyTotalKm: '', // 总公里
        journeyOverKm: '', // 超公里
        journeyOverKmCost: '', // 超公里费
        journeyCostOther: '', // 住宿费等其他费用
        journeyCostTotal: '', // 费用合计
        remarks: '', // 备注
      }
    }
  },
  computed: {
    journeyCostTotal: {
      get() {
        const carCostRent = +this.formData.carCostRent || 0;
        const carCostPark = +this.formData.carCostPark || 0;
        const carCostToll = +this.formData.carCostToll || 0;
        const carCostGasoline = +this.formData.carCostGasoline || 0;
        const journeyCostOther = +this.formData.journeyCostOther || 0;
        const journeyOverTimeCost = +this.formData.journeyOverTimeCost || 0;
        const journeyOverKmCost = +this.formData.journeyOverKmCost || 0;
        const total = carCostRent + carCostPark + carCostToll + carCostGasoline + journeyOverTimeCost + journeyCostOther + journeyOverKmCost;
        return total.toFixed(2);
      },
      set() {
        this.formData.journeyCostTotal = this.journeyCostTotal;
      }
    },
  },
  methods: {
    getPickerOptions(prop) {
      const that = this;
      switch (prop) {
        case 'vdDriverConfirmEndTime':
          return {
            disabledDate() {
              return false
            }
          }
        case 'useCarPersonConfirmEndTime':
          return {
            disabledDate(time) {
              if (that.formData.vdDriverConfirmEndTime) {
                return time.getTime() < new Date(that.formData.vdDriverConfirmEndTime).getTime() - 8.64e7
              }
              return true
            }
          }
      }
    },
    getTotalTime(startDate, endDate) {
      if (this.isEdit) {
        if (startDate && endDate) {
          const startTime = dayjs(startDate)
          const endTime = dayjs(endDate)
          this.formData.journeyTotalTime = (endTime.diff(startTime, 'millisecond') / 3600000).toFixed(2)
        } else {
          this.formData.journeyTotalTime = ''
        }
      }
    },
    getPlaceholder(unit) {
      if (unit === '元') {
        return '￥0.00'
      } else if (unit === '小时') {
        return '0'
      } else if (unit === '公里') {
        return '0.00'
      }
    }
  }
}
</script>

<style scoped>
.empty-black {
  height: 64px;
  width: calc(100% + 64px);
  margin-left: -32px;
}
.fee-black {
  margin-left: -14px;
  width: calc(100% + 34px);
}
</style>
