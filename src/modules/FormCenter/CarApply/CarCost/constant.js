export const list = [
  {
    prop: 'vdDriverConfirmEndTime',
    label: '实际开始时间',
    type: 'date',
    required: true,
    unit: ''
  },
  {
    prop: 'useCarPersonConfirmEndTime',
    label: '实际结束时间',
    type: 'date',
    required: true,
    unit: ''
  },
  {
    prop: 'journeyTotalTime',
    label: '总时间',
    type: 'input-number',
    required: true,
    unit: '小时'
  },
  {
    prop: 'journeyTotalKm',
    label: '总公里',
    type: 'input-number',
    required: true,
    unit: '公里'
  },
  {
    prop: 'carCostRent',
    label: '租金',
    type: 'input-number',
    required: true,
    unit: '元'
  },
  {
    prop: 'carCostPark',
    label: '停车费',
    type: 'input-number',
    unit: '元'
  },
  {
    prop: 'carCostToll',
    label: '过路费',
    type: 'input-number',
    unit: '元'
  },
  {
    prop: 'carCostGasoline',
    label: '汽油费',
    type: 'input-number',
    unit: '元'
  },
  {
    prop: 'journeyOverTime',
    label: '超时间',
    type: 'input-number',
    unit: '小时'
  },
  {
    prop: 'journeyOverTimeCost',
    label: '超时费',
    type: 'input-number',
    unit: '元'
  },
  {
    prop: 'journeyOverKm',
    label: '超公里',
    type: 'input-number',
    unit: '公里'
  },
  {
    prop: 'journeyOverKmCost',
    label: '超公里费',
    type: 'input-number',
    unit: '元'
  },
  {
    prop: 'journeyCostOther',
    label: '住宿费等其他费用',
    type: 'input-number',
    unit: '元'
  }
]
