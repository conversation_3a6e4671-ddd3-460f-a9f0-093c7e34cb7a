* {
  box-sizing: border-box;
}

.content-container {
  height: auto !important;
  overflow-y: auto;
  overflow-x: hidden;
  /*margin-top: 220px;*/
}
/deep/.fm-field--disabled .fm-field__label {
  color: #000;
}

/deep/ .fm-hairline--top-bottom::after {
///deep/ .fm-hairline-unset--top-bottom::after {
  border-width: 0 0 2px 0;
}

/deep/ .fm-cell-group__title {
  color: #000;
}
.field-attachment {
  flex-direction: column;
  /deep/ .fm-field__control--right {
    justify-content: flex-start;
  }
}

.textarea-type /deep/ .fm-field__value {
  border: 1px solid #bbbbbb;
  opacity: 0.5;
}

.prompt-value {
  display: none;
}
.person-table {
  text-align: center;
  border: 1px solid #eee;
  line-height: 49px;
  margin: 32px;
  font-size: 16px;
  color: #191919;
}
.sty_h {
  font-size: 31px;
  font-weight: 500;
  color: #191919;
  padding: 32px;
}
.apply-person-card {
  margin-top: 24px;
  background: #F3F5F9;
  border-radius: 12px;
  border: 2px solid #DDDDDD;
  padding: 24px;
}
.apply-person-card.car-com {
  margin: 0;
  padding: 32px;
}

/deep/ .car-company-collapse>.fm-cell {
  padding: 0 20px 0 0;
  display: flex;
  align-items: center;
}
/deep/ .car-company-collapse .fm-field {
  padding-right: 0;
}

/deep/ .apply-person-card .fm-field__control {
  height: 72px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 2px solid rgba(25,25,25,0.07);
  margin-left: 30px;
  text-indent: 20px;
}
/deep/ .description-cell {
  .star {
    position: relative;
    &:before {
      position: absolute;
      left: -20px;
      top: -40px;
      color: #ff4d4f;
      font-size: 28px;
      content: '*';
    }
  }
}
/deep/ .description-cell.fm-cell::after {
  border: 0;
}

/deep/ .apply-person-card .fm-cell--required::before {
  top: 30px
}
.cost-card {
  //border: 2px solid #F4F7FD;
  border: 2px solid rgba(25,25,25,0.07);
  box-shadow: 0 4px 32px 0 rgba(104,115,127,0.06);
}
/deep/ .apply-person-card .fm-cell {
  background: #F4F7FD;
  padding: 0 1px 0 0;
}
.apply-person-card .sort-index{
  font-size: 28px;
  font-weight: 400;
  line-height: 40px;
  color: #555555;
  text-align: center;
  font-style: normal;
}

/deep/ .fm-field__label,
/deep/ .fm-cell__title>span {
  height: 40px;
  font-size: 28px;
  font-weight: 400;
  // color: rgba(25,25,25,0.6);
  color: #999999;
  line-height: 40px;
}
// 编辑状态下左侧title字体样式
.left-cell-title {
  /deep/ .fm-cell__title>span {
    color: #555555;
  }
}
// 局部编辑(流转中）
.part-flow {
  /deep/ .fm-cell__title {
    color: #555555;
  }
  /deep/ .fm-cell__title>span {
    color: #555555;
  }
}
/deep/ .fm-field__label,
/deep/ .fm-cell__title>span,
/deep/ .fm-field__body>.fm-field__control {
  padding-left: 6px;
}

/deep/ .fm-field>.fm-cell__label {
  height: 34px;
  padding-left: 10px;
  font-size: 24px;
  font-weight: 400;
  color: rgba(25,25,25,0.3);
  line-height: 34px;
}


/deep/ .fm-cell__title.prompt-title {
  padding: 32px;
  background: rgba(255,63,76,0.08);
  border-radius: 8px;
  font-size: 24px;
  font-weight: 400;
  color: #FF3F4C;
  line-height: 34px;
}
/deep/ .fm-cell__title.prompt-title>span {
  color: #FF3F4C;
}

/deep/ .fm-field__body>input {
  font-size: 28px;
  font-weight: 400;
  color: #191919;
  line-height: 40px;
}
/deep/ .fm-field__body>textarea {
  font-size: 28px;
  font-weight: 400;
  color: #191919;
}

/deep/ .fm-field {
  padding: 32px 32px 24px 32px;
}
/deep/ .fm-search .fm-field {
  padding: 0;
}
/deep/ .fm-field__prefix-icon {
  padding-top: 10px;
}
/deep/ .fm-field.fm-cell::after {
  border-bottom: 2px solid rgba(25,25,25,0.07);
}

/deep/ .amap-input {
  padding: 16px 20px;
}

.call-phone {
  padding: 22px 48px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 2px solid #3C83FF;
  font-size: 32px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #3C83FF;
  line-height: 44px;
  letter-spacing: 1px;
}

/deep/.description-cell + .fm-cell--required::before {
  display: none;
}

/deep/ .description-cell > .fm-cell__title{
  padding-left: 4px;
}
/deep/ .remark.fm-hairline--top-bottom::after,
/deep/ .remark.fm-hairline-unset--top-bottom::after {
  border: 0;
}

.textarea-box {

}

/deep/.apply-person-card>.fm-hairline--top-bottom::after,
/deep/.apply-person-card .fm-cell::after  {
  border-width: 0;
}


.call-img {
  width: 94px;
  height: 96px;
}
@import "../../CarApply/components/rateDrawer.less";

/deep/ .pc-car-com-col.fks-collapse {
  border-top-width: 0;
  border-bottom-width: 0;
  .apply-person-card {
    padding: 32px 32px 0;
    margin: 0;
  }
  .fks-collapse-item__header .fks-icon-arrow-right {
    margin-top: -30px;
  }
  .fks-collapse-item__wrap,
  .fks-card__header,
  .fks-collapse-item__header {
    border-bottom-width: 0;
  }
}

/deep/ .fks-textarea.is-disabled .fks-textarea__inner {
  color: #191919 !important;
}

/deep/ .fm-cell__right-icon {
  font-size: 0.37333rem;
  line-height: 0.55333rem;
  color: #3C83FF;
}
/deep/ .fm-icon-arrow:before {
  padding-top: 1.4px;
}
