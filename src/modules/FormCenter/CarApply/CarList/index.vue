<template>
  <div
    v-if="isMobile"
    class="flex flex-column full-height"
  >
    <fm-nav-bar
      :border="false"
      left-arrow
      title="选择车辆"
      @click-left="customBack"
    >
    </fm-nav-bar>
    <fm-search
      v-model="searchValue"
      maxlength="50"
      placeholder="请输入车辆名称、车牌号、座位数"
      @clear="onClear"
      @search="onSearch"
    />
    <fm-pull-refresh
      v-model="refreshing"
      class="flex-grow-1 overflow-y-auto"
      @refresh="refreshList"
    >
        <fm-list
          v-model="loading"
          :finished="finished"
          error-text="重新加载"
          finished-text="已经到底啦~"
          @load="onLoad"
        >
          <fm-cell-group :border="false">
            <template v-for="(item) in list">
              <div @click="tapItem(item)">
                <fm-radio :key="item.id" v-model="radio" :name="item.id"
                          class="m-l-46 user-cell">
                  <fm-cell :arrow="false" :border-bottom="false" @click="tapItem(item)">
                    <template slot="icon">
                      <div :class="{'choose': ids.includes(item.id)}"
                           class="text-center flex row-center col-center">
                        <img class="car-img" src="@/assets/img/car/icon_car.png"/>
                      </div>
                    </template>
                    <template slot="title">
                      <div class="m-l-24">
                        <div class="car-info">
                          <span class="d-inline-block font-bold text-ellipsis" style="max-width: 200px">{{ item.carType }}</span>
                          <span class="color-black9 font-28 font-400 m-l-34">
                        {{ item.carType2 | transferEnums('CarType2Enums') }}</span>
                        </div>
                        <div class="car-user m-t-20">
                          <!--                      <span>{{  item.carName }}</span>-->
                          <span class="m-l-10 d-inline-block">{{ item.carNum }}</span>
                        </div>
                      </div>
                    </template>
                    <div slot="right-icon">
                      <!--              <div v-if="item.checkType" class="car-status text-center use">可派</div>-->
                      <!--              <div v-else class="car-status text-center no-use">不可派</div>-->
                      <div class="m-t-20 car-seat-count">
                        <div class="drive-age-text text-center">座位</div>
                        <div class="drive-age text-center">{{ item.carSeatNum }}</div>
                      </div>
                    </div>
                  </fm-cell>
                  <template #icon="props">
                    <img v-if="props.checked" class="img-icon"
                         src="@/assets/img/car/radio-checked.png">
                    <img v-else class="img-icon" src="@/assets/img/car/radio.png">
                  </template>
                </fm-radio>
              </div>
            </template>
          </fm-cell-group>
        </fm-list>
      </fm-pull-refresh>

    <flow-button v-if="isMobile" :onSubmit="onSubmit" submit-text="确定"></flow-button>
  </div>
  <div v-else>
    <fks-table
      v-loading="loading"
      :data="list"
      :row-key="getRowKey"
      :tree-props="{children: 'children'}"
      default-expand-all
      @current-change="tapItem"
      max-height="400"
    >
      <fks-table-column align="center" width="80">
        <template slot-scope="scope">
          <fks-radio v-if="!scope.treeNode || scope.treeNode.level !== 1" v-model="radio"
                     :label="getScope(scope)">&nbsp;
          </fks-radio>
        </template>
      </fks-table-column>
      <fks-table-column label="车牌" prop="carNum" width="100">
        <template slot-scope="{row}">
          {{ row.vdCarInfo.carNum }}
        </template>
      </fks-table-column>
      <fks-table-column label="名称" prop="carName" width="150">
        <template slot-scope="{row}">
          {{ row.vdCarInfo.carName }}
        </template>
      </fks-table-column>
      <fks-table-column label="型号" prop="carType">
        <template slot-scope="{row}">
          {{ row.vdCarInfo.carType }}
        </template>
      </fks-table-column>
      <fks-table-column label="座位" prop="carSeatNum">
        <template slot-scope="{row}">
          {{ row.vdCarInfo.carSeatNum }}
        </template>
      </fks-table-column>
      <fks-table-column label="司机">
        <template slot-scope="{row}">
          {{ row.vdDriverInfo.driverFullName }}
        </template>
      </fks-table-column>
      <!--      <fks-table-column label="车龄" prop="carAge">-->
      <!--        <template slot-scope="{row}">-->
      <!--          {{ row.vdCarInfo.carAge }}-->
      <!--        </template>-->
      <!--      </fks-table-column>-->
      <fks-table-column label="车辆状态" prop="statusText">
        <template slot-scope="{row}">
          <fks-tag v-if="row.statusText" :type="row.statusText === '空闲中' ? 'success' : 'danger'">
            {{ row.statusText }}
          </fks-tag>
        </template>
      </fks-table-column>
      <fks-table-column label="出发地点" width="200">
        <template slot-scope="{row}">
          <overflow-tooltip v-if="row.tripInfo" :text="row.tripInfo.startAddress"/>
        </template>
      </fks-table-column>
      <fks-table-column label="出发时间" width="160">
        <template slot-scope="{row}">
          {{ row.tripInfo ? $dayjs(row.tripInfo.startTime2).format('YYYY-MM-DD HH:mm') : '' }}
        </template>
      </fks-table-column>
      <fks-table-column label="目标地点" width="200">
        <template slot-scope="{row}">
          <overflow-tooltip v-if="row.tripInfo" :text="row.tripInfo.endAddress"/>
        </template>
      </fks-table-column>
      <fks-table-column label="到达时间" width="160">
        <template slot-scope="{row}">
          {{ row.tripInfo ? $dayjs(row.tripInfo.endTime2 || row.tripInfo.endTime).format('YYYY-MM-DD HH:mm') : '' }}
        </template>
      </fks-table-column>
      <fks-table-column label="乘坐人数">
        <template slot-scope="{row}">
          {{ getPassengerNumber(row) }}
        </template>
      </fks-table-column>
      <fks-table-column label="乘坐人">
        <template slot-scope="{row}">
          <overflow-tooltip v-if="row.tripInfo && row.tripInfo.passengerInfo"
                            :text="getPassengerName(row)"/>
        </template>
      </fks-table-column>
    </fks-table>
  </div>
</template>

<script>
import {Cell, CellGroup, List, Radio, RadioGroup, Toast} from 'fawkes-mobile-lib'
import FlowButton from '@/modules/FormCenter/components/FlowButton/index.vue';
import {mapMutations, mapState} from 'vuex';
import platform from "@/mixins/platform";
import {getReservationInfo} from "@modules/FormCenter/CarApply/CarList/api";
import {getPassengerList} from "@/api/carApply";
import OverflowTooltip from "@components/OverflowTooltip/index.vue";
import { getCarListByPost } from '@modules/ProjectCar/ProjectPortal/CarManage/api'

export default {
  name: 'CarList',
  mixins: [platform],
  props: {
    carCompanyInfoId: {
      type: String,
      default: ''
    },
    formData: {
      type: Object
    }
  },
  data() {
    return {
      loading: false,
      finished: false,
      refreshing: false,
      list: [],
      ids: [],
      chooseItem: [],
      page: 1,
      total: 0,
      pageSize: 100,
      radio: '',
      searchValue: ''
      // account: uni.getStorageSync('account')
    }
  },
  components: {
    OverflowTooltip,
    FlowButton,
    Toast,
    [List.name]: List,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio
  },
  computed: {
    ...mapState(['portal']),
  },
  created() {
    this.onLoad();
  },
  methods: {
    ...mapMutations('CarApply', ['SET_APPLY_CAR']),
    getRowKey(row) {
      return row.tripInfo ? row.tripInfo.id : row.vdCarInfo.id
    },
    getScope(scope) {
      return scope.row.vdCarInfo.id
    },
    getPassengerNumber(row) {
      if (row.tripInfo && row.tripInfo.passengerInfo) {
        return row.tripInfo.passengerInfo.length
      }
    },
    getPassengerName(row) {
      return row.tripInfo.passengerInfo.map(item => item.ucPersonFullName).join(',')
    },
    swipeHandler() {
      this.customBack();
    },
    onSearch() {
      this.refreshList()
    },
    onClear() {
      this.searchValue = '';
      this.refreshList()
    },
    onLoad() {
      this.getList();
    },
    /**
     * 刷新列表（从第一页开始）
     */
    async refreshList() {
      this.page = 0;
      this.list = [];
      this.total = 0;
      this.loading = false;
      this.finished = false;
      this.refreshing = false;
      await this.getList();
    },
    async getList() {
      const {list, page, pageSize} = this;
      if (this.refreshing) {
        this.list = [];
        this.refreshing = false;
      }
      const params = {
        pageNo: page,
        pageSize: this.pageSize,
        conditions: []
      };
      // 搜索 this.searchValue
      this.loading = true;
      try {
        const projectId = this.formData.projectId || this.portal.id;
        const projectName = this.formData.projectName || this.portal.name;
        const projectParams = (projectId && projectName) ? {projectId, projectName} : {};
        const {status, data} = await getCarListByPost(
          {...params, ...projectParams},
          {searchValue: this.searchValue}
        );
        // 过滤掉非正常状态的车辆
        const records = data.list.filter(item => item.carStatus === 100);
        if (!status) {
          this.loading = false;
          return false;
        }
        const newList = list.concat(records);
        const ids = newList.map(item => item.id).join(',');
        const {data: preserveInfos} = await getReservationInfo(ids);
        if (preserveInfos) {
          let passengers = []
          Object.keys(preserveInfos).forEach(key => {
            passengers = passengers.concat(preserveInfos[key])
          })
          const passengerIds = passengers.map(item => item.id).join(',')
          const {data: passengersData} = await getPassengerList(passengerIds)
          // 将乘车人信息注入到预约信息中
          if (passengersData) {
            Object.keys(preserveInfos).forEach(key => {
              if (preserveInfos[key]) {
                preserveInfos[key].forEach(item => {
                  if (passengersData[item.id]) {
                    item.passengerInfo = passengersData[item.id]
                  }
                })
              }
            })
          }

          if (this.isPC) {
            this.list = newList.map(item => {
              const infos = preserveInfos[item.id];
              if (infos) {
                item.statusText = infos.length === 0 ? '空闲中' : '已预约';
                if (infos.length > 0) {
                  const [firstItem, ...rest] = infos;
                  item.tripInfo = firstItem;
                  if (infos.length > 1) {
                    item.children = rest.map(el => {
                      return {
                        ...item,
                        tripInfo: el
                      }
                    })
                  }
                  return item;
                }
              } else {
                item.statusText = '空闲中';
              }
              return item;
            });
          } else if (this.isMobile) {
            this.list = newList;
          }
        } else {
          this.list = newList;
        }

        let total = data.total - (page + 1) * pageSize > 0 ? data.total - (page + 1) * pageSize : 0;
        this.total = total + this.list.length || 0
        if (this.list.length >= this.total) {
          this.loading = false;
          this.finished = true;
        } else {
          this.loading = false;
          this.page++;
        }
      } catch (e) {
        this.loading = false;
        this.finished = true;
      }
    },
    customBack() {
      this.$emit('closePopupShow');
    },
    tapItem(item) {
      // if (!item.checkType) {
      //   Toast({
      //     icon: 'none',
      //     message: '车辆不可派，不允许选择!'
      //   });
      //   return false;
      // }
      // this.ids.includes(item.id) ? this.ids.splice(this.ids.indexOf(item.id), 1) : this.ids.push(item.id);
      this.ids = [item.id];
      this.radio = item.id;
      this.chooseItem = [item];
    },
    onSubmit() {
      if (this.chooseItem.length === 0) {
        Toast({
          icon: 'none',
          message: '请选择车辆'
        });
        return false;
      }
      this.SET_APPLY_CAR(this.chooseItem[0]);
      this.$emit('closePopup', this.chooseItem[0])
      // uni.navigateBack()
    }
  }
}
</script>

<style lang='less' scoped>
@import "index.less";
</style>
