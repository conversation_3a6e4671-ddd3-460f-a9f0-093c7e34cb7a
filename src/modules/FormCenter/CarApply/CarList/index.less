.active-car {
  background-color: #E9F4FF;
}
.car-view {
  width: 95px;
  height: 95px;
  background: rgba(26,144,254,0.1);
  border-radius: 95px;
  &.choose {
    background: #1A90FE;
  }
  .car-img {
    width: 56px;
    height: 50px;
  }
  .user-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}
.car-info {
  font-size: 30px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 44px;
}
.car-driver {
  height: 34px;
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 34px;
}
.car-user {
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: bold;
  color: #333333;
  line-height: 34px;
}
.car-status {
  padding: 4px 16px;
  border-radius: 3px;
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 32px;
  &.use {
    border: 1px solid #1A90FE;
    background: rgba(26,144,254,0.1);
    color: #1A90FE;
  }
  &.no-use {
    border: 1px solid #FF4D4F;
    background:  rgba(255,77,79,0.1);
    color: #FF4D4F;
  }
}
.car-seat-count {
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 32px;
}
.car-seat-count.font-28 {
  font-size: 28px;
}
.img-icon {
  width: 36px;
  height: 36px;
}
/deep/.user-cell.fm-radio {
  padding: 36px 0 36px 0;
  margin-right: 44px;
  border-bottom: 2px solid #E3E3E3;
}
/deep/.user-cell.fm-radio:last-child {
  border-bottom: 0 solid #E3E3E3;
}
/deep/.user-cell .fm-cell {
  padding: 0 32px 0 0;
}
/deep/ .user-cell .fm-radio__label {
  width: 100%;
}

.drive-age-text {
  height: 34px;
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 34px;
}

.drive-age {
  font-size: 28px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: bold;
  color: #3C83FF;
  line-height: 40px;
}
.car-type {
  width: 136px;
  height: 96px;
}
