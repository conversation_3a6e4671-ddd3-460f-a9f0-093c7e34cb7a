<template>
  <div
    v-if="isMobile"
    v-touch:swipe.left="swipeHandler"
    v-touch:swipe.right="swipeHandler"
    class="full-height"
  >
    <div class="apply-person-container">
      <fm-nav-bar
        :border="false"
        left-arrow
        title="选择人员"
        @click-left="customBack"
      >
      </fm-nav-bar>
      <fm-search
        v-if="!keywords"
        v-model="searchValue"
        maxlength="50"
        placeholder="请输入关键字"
        @clear="onClear"
        @search="onSearch"
      />
      <fm-pull-refresh class="refresh" v-model="refreshing" @refresh="refreshList">
        <fm-list
          v-model="loading"
          :finished="finished"
          error-text="重新加载"
          finished-text="已经到底啦~"
          @load="onLoad"
        >
          <fm-cell-group :border="false" style="margin-bottom: 70px;">
            <fm-radio-group v-model="radio">
              <template v-for="(item) in list">
                <fm-radio class="m-l-46  user-cell"  :key="item.user_id" :name="item.user_id">
                  <fm-cell
                    :arrow="false"
                    :border-bottom="false"
                    class="flex col-center"
                    style="font-palette: 0;"
                    @click="tapItem(item)"
                  >
                    <!--              :style="{'background-color': ids.includes(item.user_id) ? '#E9F4FF' : 'transparent'}"-->
                    <template slot="title">
                      <!--                    <div :class="{'choose': ids.includes(item.user_id)}"-->
                      <!--                         class="car-view text-center flex row-center col-center">-->
                      <!--                      <img :src="item.avatar.avatar_origin" class="user-img">-->
                      <!--                    </div>-->
                      <div class="flex col-center">
                        <img :src="item.avatar.avatar_origin" class="car-view">
                        <span class="m-l-16">{{ item.name }}</span>
                      </div>
                    </template>
                    <div slot="right-icon">
                      <div class="m-t-20 car-seat-count font-28 flex col-center">
                        <span>{{ item.deptName }}</span>
                      </div>
                    </div>
                  </fm-cell>
                  <template #icon="props">
                    <img v-if="props.checked" src="@/assets/img/car/radio-checked.png" class="img-icon">
                    <img v-else src="@/assets/img/car/radio.png" class="img-icon">
                  </template>
                </fm-radio>
              </template>
            </fm-radio-group>
          </fm-cell-group>
        </fm-list>
      </fm-pull-refresh>
    </div>
    <flow-button ref="submitBtn" :onSubmit="onSubmit" submit-text="确定"></flow-button>
  </div>
  <fks-select
      v-else
      v-model="searchValue"
      :loading="loading"
      :disabled="readOnly"
      :remote-method="searchContacts"
      filterable
      reserve-keyword
      remote
      placeholder="请输入姓名"
      @change="handleChange"
      @blur="handleBlur"
      @focus="$emit('focus')"
      @visible-change="handleVisibleChange"
      style="width: 100%"
  >
    <fks-option
        v-for="item in list"
        :key="item.user_id"
        :value="item.name"
    >
      <div class="option">
        <div class="user">
          <img :src="item.avatar.avatar_origin">
          <span>{{ item.name }}</span>
        </div>
        <span class="dept">{{ item.deptName }}</span>
      </div>
    </fks-option>
  </fks-select>
</template>

<script>
import {Cell, CellGroup, List, Toast, RadioGroup, Radio } from 'fawkes-mobile-lib';
import FlowButton from '@/modules/FormCenter/components/FlowButton/index.vue';
import {mapActions, mapMutations, mapState} from 'vuex';
import {APPLY_RESOURCE, OPEN_ID} from '@/store/State/stateTypes'
import platform from "@/mixins/platform";

export default {
  name: 'ApplyPerson',
  mixins: [platform],
  props: {
    keywords: {
      type: String,
      default: ''
    },
    initialValue: {
      type: String
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    currentPerson: {
      type: Object
    }
  },
  data() {
    return {
      radio: '',
      loading: false,
      finished: false,
      refreshing: false,
      searchValue: '',
      list: [],
      ids: [],
      chooseItem: [],
      page: 1,
      total: 0,
      pageSize: 10,
      pageToken: '',
      name: '', //联系人名称，
      changeTrigger: true // 当change事件触发时，visible-change事件就不触发了
    }
  },
  computed: {
    ...mapState('CarApply', ['driverList', 'currUser']),
    ...mapState([OPEN_ID, APPLY_RESOURCE]),
    openId() {
      const thirdPartyUser = this.$storage.getObject('thirdPartyUser');
      return thirdPartyUser['tpOpenId']
    }
  },
  watch: {
    initialValue: {
      immediate: true,
      handler(newVal){
        newVal && (this.searchValue = newVal)
      }
    },
    keywords: {
      handler(val) {
        this.isMobile && this.onSearch(val)
      },
      immediate: true
    }
  },
  components: {
    FlowButton,
    Toast,
    [List.name]: List,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio
  },
  methods: {
    ...mapMutations('CarApply', ['SET_APPLY_CAR']),
    ...mapActions('CarApply', ['getApplyPerson']),
    ...mapActions('FeiShu', ['getFeiSuUserInfo']),
    swipeHandler (direction) {
      this.customBack()
    },
    handleBlur() {
      // this.searchValue = this.initialValue
    },
    handleVisibleChange(visible){
      if (!visible && this.searchValue && this.changeTrigger) {
        this.onSubmit()
      }
    },
    handleChange(val) {
      this.$emit('change', val)
      this.changeTrigger = false
      const item = this.list.find(item => item.name === val)
      if (item) {
        this.tapItem(item)
        this.onSubmit();
      }
    },
    searchContacts(query) {
      this.onSearch(query)
    },
    async onSearch(val) {
      this.searchValue = val
      await this.refreshList();
    },
    onClear() {
      this.chooseItem = [];ss
      this.isMobile && (this.searchValue = '');
      this.refreshList()
    },
    Clear() {
      this.searchValue = '';
    },
    onLoad() {
      // this.refreshList();
    },
    /**
     * 刷新列表（从第一页开始）
     */
    async refreshList() {
      this.page = 0;
      this.list = [];
      this.total = 0;
      this.loading = false;
      this.finished = false;
      this.refreshing = false;
      await this.getList();

    },
    async getList() {
      // if (this.applyResource !== 2) {
      //   this.finished = true;
      //   this.loading = false;
      //   return false;
      // }
      if (!this.searchValue || this.finished === true) {
        this.finished = true;
        return false;
      }
      const {list, page, pageSize} = this;
      if (this.refreshing) {
        this.list = [];
        this.refreshing = false;
      }
      // const openId = this.currUser?.vdThirdPartyUserList ? this.currUser.vdThirdPartyUserList.find(item => item.tpUserType === 1)['tpOpenId'] : '';
      const openId = this.openId;
      const params = {
        openId, // 飞书openId
        pageSize,
        pageToken: this.pageToken, // 分页标识，获取首页不需要填写，获取下一页时传入上一页返回的分页标识值。请注意此字段的值并没有特殊含义，请使用每次请求所返回的标识值
        userName: this.currUser.userName, // 凤翎userName
        query: this.searchValue // 要执行搜索的字符串，一般为用户名
      }
      // if (this.applyResource !== 2) {
      //   this.finished = true;
      //   this.loading = false;
      // } else {
      //   this.loading = true;
      // }
      try {
        this.loading = true;
        const res = await this.getApplyPerson(params);
        this.loading = false;
        if (!res.status) {
          this.loading = false;
          return false;
        }
        if (res.data.pageToken) {
          this.pageToken = res.data.page_token
        }
        this.list = list.concat(res.data?.users || []);
        // 如果没有找到联系人，则将用户输入做为最终结果
        if (this.list.length === 0) {
          this.$emit('closePopup', {
            ucPersonFullName: this.searchValue,
            currentKey: this.currentPerson.currentKey
          })
        }
        // let total = data.total - (page + 1) * pageSize > 0 ? data.total - (page + 1) * pageSize : 0;
        this.total = this.list.length
        if (!res.data.has_more) {
          this.loading = false;
          this.finished = true;
        } else {
          this.loading = false;
          this.page++;
        }
      } catch (e) {
        this.loading = false;
        this.finished = true;
      }
    },
    customBack() {
      this.$emit('closePopupShow');
    },
    tapItem(item) {
      if (item) {
        this.ids = [item.user_id];
        this.chooseItem = [item];
      }
    },
    onSubmit() {
      if (this.chooseItem.length === 0 && this.isMobile) {
          Toast({
            icon: 'none',
            message: '请选择人员'
          });
        return false;
      }
      console.log('submit执行', this.chooseItem)
      if (this.chooseItem.length === 0 ){
        // 手动传用户数据
        const data = {
          ucPersonFullName: this.searchValue,
          ucPersonResource: 3,
          currentKey: this.currentPerson.currentKey
        }
        this.$emit('closePopup', data)
      } else {
        // const openId = this.currUser.vdThirdPartyUserList.find(item => item.tpUserType === 1)['tpOpenId'];
        const openId = this.openId;
        const params = {
          openId,
          searchOpenId: this.chooseItem[0].open_id
        }
        this.$refs.submitBtn && (this.$refs.submitBtn.loading = true);
        this.getFeiSuUserInfo(params).then(res => {
          if (!res.status) {
            return false
          }
          const data = {
            id: this.chooseItem[0].id,
            ucPersonFullName: res.data.user.name,
            ucPersonOpenId: res.data.user.openId,
            ucPersonUnionId: res.data.user.unionId,
            ucPersonPhone: res.data.user.mobile.split('+86')[1],
            ucPersonResource: 1,
            ucPersonUserName: res.data.user.en_name,
            currentKey: this.currentPerson.currentKey
          }
          this.searchValue = res.data.user.name;
          this.$refs.submitBtn && (this.$refs.submitBtn.loading = false);
          this.$emit('closePopup', data)
          this.onClear();

        }).catch(e => {
          this.$refs.submitBtn && (this.$refs.submitBtn.loading = false);
        }).finally(()=>{
          this.changeTrigger = true
        })
      }
    }
  }
}
</script>

<style lang='less' scoped>
@import "../CarList/index.less";
.auto-fill {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
}
.apply-person-container {
  height: calc(100% - 0.8rem - 80px);
  display: flex;
  flex-direction: column;
  .refresh {
    flex-grow: 1;
  }
}
.option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  span {
    display: inline-block;
  }
  .user {
    display: flex;
    align-items: center;

    img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 10px;
    }

  }

  .dept {
    font-size: 24px;
  }
}
</style>
