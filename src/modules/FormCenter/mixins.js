import Mix from '@/mixins/module'
import {getHistoryNodes, getModelXML, getProcessHistory, getUserTaskDetail, submitForm} from './api'
import {backDlg, processAbandon} from './components/FlowApproval/api'
import emitter from 'fawkes-lib/src/mixins/emitter'
import platform from "@/mixins/platform";
import {Dialog, Toast} from 'fawkes-mobile-lib'
import {UserTask_0, UserTask_200, UserTask_500} from "@utils/constants";
import {mapActions, mapState} from 'vuex'
import EventBus from "@utils/eventBus";
import { editForm } from '@/api/carApply'
import { deleteDraft, FORM } from '@utils/draftUtil'
import { ButtonKeys } from '@store/modules/FormController/formFieldConfig'

export default {
  name: 'FormCenter',
  componentName: 'FormCenter',
  inject: ['reload'],
  mixins: [Mix, emitter, platform],
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  provide() {
    return {
      appendButtons: [],
      parentComponent: this
    }
  },
  data() {
    return {
      type: this.params.type || this.$route.params.type,
      formKey: this.params.formKey || this.$route.params.formKey || 'vehicleDispatch',
      bizId: this.params.bizId || this.$route.params.bizId,
      taskId: this.params.taskId || this.$route.params.taskId,
      taskKey: this.params.taskKey || this.$route.params.taskKey,
      fromPage: this.params.fromPage || null,
      entityName: '',
      formName: '', //表单名称
      service: {}, //提交地址和初始化地址
      dynamicForm: '', // 动态组件
      comment: '', // 流程评论内容
      processInstanceId:
        this.params.processInstanceId ||
        this.$route.params.processInstanceId ||
        this.$route.query.processInstanceId ||
        '', // 流程实例ID
      taskNodeName: '', // 当前任务节点名称
      // 通过子组件getForm传递的变量
      customButton: [], // 自定义按钮数组（根据数组在流程按钮后渲染出对应的按钮）
      // notes: '', // 表单提示部分的html，根据其生成在对应位置
      // 表单提交时或者表单初始化时赋值的变量
      // 表单评论信息
      comments: [],
      formBtnList: [],
      isCrossNodeReturn: false,
      returnedTargets: [],
      submitText: '提交',
      commentSeries: {
        taskName: 'taskName',
        userName: 'assignee',
        realName: 'assigneeName',
        solveType: 'approveStateName',
        approveDate: 'approveDate',
        createDate: 'createDate',
        flag: 'approveState',
        comment: 'comment',
        delegateList: 'delegateList'
      },
      flowInfo: {
        xml: '',
        //flow-comment组件不传下面的参数会报forEach的错
        currentNode: [],
        userTask: [],
        exclusiveGateway: []
      },
      flowData: {
        modelKey: ''
      },
      preSaveValidateProps: [], //暂存校验字段
      //上传中的文件列表
      uploadingList: [],
      buttonLoading: false,
      FormDetailValid: true,
      showApproveBtn: false
    }
  },
  computed: {
    ...mapState('CarApply', ['currUser']),
    currentButtonKey() {
      return this.$route.query.buttonKey || this.params.buttonKey;
    },
    vdUserInfo() {
      return this.$storage.getObject('user')
    },
    isNotifyButtonKey() {
      // 允许弹出通知的buttonKey
      const allowButtonKey = [
        ButtonKeys.XC_DRIVER_CAR_MODIFY,
        ButtonKeys.XC_FORM_MODIFY,
        ButtonKeys.FORM_MODIFY_ADMIN,
      ]
      return allowButtonKey.includes(this.currentButtonKey)
    }
  },
  watch: {
    'flowData.modelKey': {
      handler(val) {
        if (val && !this.noAuthPage) { // 短信详情页不获取流程信息
          this.initProcess()
        }
      }
    },
    'uploadingList.length'(val) {
      this.buttonLoading = val > 0
    },
    comments: {
      handler(val) {
        this.returnedTargets = this.getRetreatableNodes();
        this.$refs.formTemp.setReturnNodeListByKeys(this.returnedTargets);
        // 获得val最后一个节点，判断它的taskKey与this.taskKey是否跨节点了
        const nodeOrder = [
          'UserTask_0',
          'UserTask_100',
          'UserTask_200',
          'UserTask_300',
          'UserTask_400',
          'UserTask_500'
        ];

        if (Array.isArray(val) && val.length > 1) {
          const prevComment = val[val.length - 2];
          const isReject = prevComment.approveState === 'reject';
          const lastIndex = nodeOrder.indexOf(prevComment.taskKey);
          const currentIndex = nodeOrder.indexOf(this.taskKey);
          console.log("prevComment", prevComment, currentIndex);

          this.isCrossNodeReturn = isReject && (lastIndex - currentIndex > 1);
        } else {
          this.isCrossNodeReturn = false;
        }
      },
      deep: true,
    },
    formBtnList: {
      handler(val) {
        this.returnedTargets = this.getRetreatableNodes();
        this.$refs.formTemp.setReturnNodeListByKeys(this.returnedTargets);
      },
      deep: true,
    }
  },
  mounted() {
    this.initForm()
    //监听form-upload的上传事件
    this.$on('uploading', (fileId) => {
      //上传文件时，将文件id加入到uploadingList中
      this.uploadingList.push(fileId)
    })
    this.$on('uploaded', (fileId) => {
      //上传成功后,将文件id从uploadingList中移除
      const index = this.uploadingList.indexOf(fileId)
      if (index > -1) {
        this.uploadingList.splice(index, 1)
      }
    })
    //监听明细表校验的结果
    this.$on('FormDetailValidate', (valid) => {
      this.FormDetailValid = valid
    })
    // 监听按钮loading
    this.$on('buttonLoading', (val) => {
      this.buttonLoading = val;
    })
  },
  methods: {
    ...mapActions('CarApply', ['getFlowChange']),
    getRetreatableNodes() {
      const approved = new Set(
        (this.comments || [])
          .filter(c => c.approveDate)
          .map(c => c.taskKey)
      );

      const rejectBtn = (this.formBtnList || []).find(b => b.customCode === 'REJECT');
      const targets = (rejectBtn?.targetNode || []);

      return targets.filter(n => approved.has(n.value || n));
    },
    returnBack(item) {
      const formData = this.getFormData()
      this.comment = item.comment || formData.comment || '';
      // 根据流程图判断退回节点
      let targetKey = item.targetNode[0]

      // 当处于车辆调度员派车且用车事由为工地内部时，则退回到用车申请流程
      if (formData.flagGd && this.taskKey === UserTask_200) {
        targetKey = UserTask_0
      }
      if (item.returnNode) {
        targetKey = item.returnNode
      }
      // 没有填写退回原因需要提示
      if (!this.comment) {
        this.isMobile && Toast('请填写退回原因')
        this.isMobile && this.tabClick(2)
        this.isPC && this.$message.warning('请填写退回原因')
        // this.isPC && this.$refs.formTemp.$refs['section-10'].$el.scrollIntoView({behavior: 'smooth'})
        return;
      }
      const params = {
        taskId: this.taskId,
        bizId: '',
        targetKey: targetKey,
        comment: this.comment,
        isSubToPre: item.rejectType
      }
      // return
      this.buttonLoading = true;
      backDlg(params).then((res) => {
        if (res.status) {
          this.isMobile && Toast('退回成功', 'middle')
          this.isPC && this.$message.success('退回成功')
          // 跳转回去
          this.back();
        } else {
          this.isMobile && Toast.fail(res.message)
          this.isPC && this.$message.error(res.message)
        }
      }).finally(() => {
        this.buttonLoading = false;
      })
    },
    async onAction(item) {
      // 车辆调度员修改表单数据
      if (this.currentButtonKey === 'FORM_MODIFY_ADMIN') {
        this.buttonLoading = true;
        this.handleEditForm(item, true)
        return false;
      }
      if (item.type === 'feeSave') {
        // 行车日志填报暂存事件触发
        this.buttonLoading = true;
        this.handleEditForm(item)
        return false;
      }
      // 暂存
      if (item.type === 'saveDraft') {
        this.buttonLoading = true
        this.handleAction(item);
        return false;
      }
      // 废弃
      if (item.type === 'abandon') {
        this.abandonTask()
        return false;
      }
      // 退回
      if (item.type === 'reject') {
        if (this.isMobile) {
          Dialog.confirm({
            title: '提示',
            message: '是否要执行退回操作?',
          }).then(() => {
            this.returnBack(item);
          })
        } else {
          this.returnBack(item);
        }
        return;
      }
      if (!this.$refs.formTemp.$refs.formRef) {
        // 未定义form的引用值时
        this.handleCustomValidator()
        return false;
      }
      this.$refs.formTemp.$refs.formRef.validate().then(() => {
        this.handleCustomValidator(item)
      }).catch((e) => {
        if (Array.isArray(e)) {
          const fallbackText = '表单校验未通过'
          const message = e.map(item => item.message).join('，')
          Toast(message || fallbackText)
        }
        this.locateToErr(); // 表单验证不通过时跳转到指定位置
      })
      // this.$refs.formTemp.$refs.formRef.validate(valid => {
      //   if (valid) {
      //     this.handleAction(item)
      //   } else {
      //     this.locateToErr();
      //   }
      // })
    },
    // 废弃
    abandonTask() {
      if (this.isMobile) {
        Dialog.confirm({
          // 组件除show外的属性
          title: '提示',
          message: '是否要执行废弃操作?',
        })
          .then(() => {
            this.confirmAbandon()

          })
          .catch((e) => {
          })
      } else {
        this.$confirm('是否要执行废弃操作？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          deleteMode: true
        }).then(() => {
          this.confirmAbandon()
        }).catch(() => {

        });
      }

    },

    confirmAbandon() {
      const flowData = {
        taskId: this.taskId,
        comment: this.comment,
      }
      this.loading = true
      this.buttonLoading = true;
      return processAbandon(flowData)
        .then((res) => {
          EventBus.$emit('refreshDot', 'Todo');
          this.isMobile && Toast('废弃成功', 'middle')
          this.isMobile && Toast.clear()
          this.isPC && this.$message.success('废弃成功')
          this.back('abandon');
        })
        .catch((err) => {
          console.error(err)
        }).finally(() => {
          this.loading = false;
          this.buttonLoading = false;
        })
    },
    back(trigger) {
      EventBus.$emit('refreshDot', 'Todo');
      this.$emit('refresh', trigger) // 通知组件重新刷新数据
      if (this.isMobile) {
        this.$router.push({name: 'CarRecord'}).then(() => {
          this.buttonLoading = false
        })
      } else if (this.fromPage) {
        this.$router.push({name: 'ProjectIndex'}).then(() => {
          this.buttonLoading = false
        })
      } else if (this.isPC && this.type === 'add') {
        this.$router.push({name: 'projectDone'}).then(() => {
          this.buttonLoading = false
        })
      } else {
        this.drawerVisible = false;
        this.refresh = true; // 表单审批后需要重新刷新表格
      }
    },
    handleEditForm(item, isBack = false) {
      this.buttonLoading = true;
      const form = this.$refs.formTemp.getEntityParam(this.useProcess(item))
      editForm(form).then((res) => {
        if (res.status) {
          this.isMobile && Toast('保存成功')
          this.isPC && this.$message.success('保存成功')
          if (isBack) {
            this.back();
          }
        }
      }).finally(() => {
        this.buttonLoading = false;
      })
    },
    handleAction(item) {
      const form = this.$refs.formTemp.getEntityParam(this.useProcess(item))
      this.comment = item.comment || form.comment || '';
      // console.info('🚀🚀', '表单提交 -->', form, `<-- mixins.js/handleAction`)
      // return;
      this.buttonLoading = true;
      if (!!this.currentButtonKey) {
        this.flowChange(form);
        return false;
      }
      submitForm(this.service.submit, form)
        .then(res => {
          if (res.status) {
            deleteDraft(this.vdUserInfo.id + form.vdApplyForm.projectId, FORM)
            this.isMobile && Toast('提交成功')
            this.isPC && this.$message.success('提交成功')
            this.back();
          } else {
            this.isMobile && Toast(res.message || '操作失败')
            this.isPC && this.$message.error(res.message || '操作失败')
          }
        }).catch(() => {
        this.buttonLoading = false
      })
    },
    // 流程变更
    flowChange(data) {
      try {


        const {vdApplyForm, modifycomment, formProcessParam, vdAfWaypointList} = data;
        const params = {
          flowButtonKey: this.currentButtonKey,
          currentUserName: this.$storage.get('username'), // 当前登录人用户名
          vdAfFlowChangeRecord: {
            vdApplyFormId: vdApplyForm.id,
            changeCause: modifycomment,
            changeFullName: this.currUser.userFullName,
            changeUserName: this.currUser.userName,
            changeUserDept: this.currUser.deptName,
            changeTypeKey: this.currentButtonKey
          },
          vdAfWaypointList
        }
        // 租车公司变更
        if (params.flowButtonKey === 'XC_CAR_COMP_MODIFY') {
          params.vdApplyForm = {
            id: vdApplyForm.id,
            carCompanyName: vdApplyForm.carCompanyName,
            carCompanyApproverFullName: vdApplyForm.carCompanyApproverFullName,
            carCompanyApproverUserName: vdApplyForm.carCompanyApproverUserName,
            vdCarCompanyInfoId: vdApplyForm.vdCarCompanyInfoId
          }
        }
        // 修改司机/车辆信息
        if (params.flowButtonKey === 'XC_DRIVER_CAR_MODIFY') {
          console.log('carNum', vdApplyForm)
          // const { carNum, carType, carAge, driverFullName, driverPhone, driverAge } = vdApplyForm
          const {carId, driverId} = vdApplyForm
          params.vdApplyForm = {
            carId,
            driverId,
            id: formProcessParam.bizId
          }
          // params.inputVdDriverInfo = {
          //   driverFullName,
          //   driverPhone,
          //   driverAge
          // }
          // params.inputVdCarInfo = {
          //   carNum,
          //   carType,
          //   carAge
          // }
        }
        // 行程信息变更
        if (params.flowButtonKey === 'XC_FORM_MODIFY') {
          params.vdApplyForm = {
            id: vdApplyForm.id,
            startAddress: vdApplyForm.startAddress,
            startAddressDetail: vdApplyForm.startAddressDetail,
            endAddress: vdApplyForm.endAddress,
            startTime: this.$dayjs(vdApplyForm.startTime).format('YYYY-MM-DD HH:mm') + ':00',
            // predictEndTime: this.$dayjs(vdApplyForm.predictEndTime).format('YYYY-MM-DD HH:mm') + ':00',
            endTime: this.$dayjs(vdApplyForm.endTime).format('YYYY-MM-DD HH:mm') + ':00',
            useCarTripType: vdApplyForm.useCarTripType,
            useCarJgTimeHour: vdApplyForm.useCarJgTimeHour,
            useCarJgTimeMinute: vdApplyForm.useCarJgTimeMinute
          }
        }

        this.getFlowChange(params).then(res => {
          if (!res.status) {
            this.isDisable = false;
            return false;
          }
          if (this.isMobile) {
            Toast(this.formName + '成功', 'middle')
          } else {
            this.$message.success(this.formName + '成功')
          }
          setTimeout(() => {
            Toast.clear()
            this.back();
          }, 1000)
        }).finally(() => {
          this.buttonLoading = false;
        })

      } catch (e) {
        this.buttonLoading = false;
        console.log(' 流程变更', e)
      }
    },
    handleCustomValidator(item) {
      if (typeof this.$refs.formTemp.validator === 'function') {
        if (this.$refs.formTemp.validator()) {
          if (this.isMobile) {
            Dialog.confirm({
              title: '提示',
              message: '是否提交?',
            }).then(() => {
              this.handleAction(item)
            })
          } else {
            this.handleAction(item)
          }
        }
      } else {
        // 没有自定义的表单校验规则，直接提交
        if (this.isMobile) {
          Dialog.confirm({
            title: '提示',
            message: '是否提交?',
          }).then(() => {
            this.handleAction(item)
          })
        } else {
          this.handleAction(item)
        }
      }
    },
    useProcess(buttonInfo, variable) {
      let processData = {
        modelKey: this.flowData.modelKey,
        bizId: this.bizId,
        processInstanceId: this.processInstanceId,
        // formKey: this.formKey,
        variable: this.formData,
        formKey: this.formKey === 'carApply' ? 'vehicleDispatch' : this.formKey,
        stageFlag: buttonInfo.type === 'saveDraft' ? 1 : 0,
        taskId: this.taskId,
        approval: buttonInfo.type === 'saveDraft' ? 'stage' : '',
        comment: this.comment,
        targetKey: buttonInfo.targetKey,
      }
      return processData
    },
    async submit(buttonInfo) {
      this.buttonLoading = true
      //派发事件，让明细表校验
      this.broadcast('FormDetail', 'validateForm')
      //明细表校验不通过,直接return
      if (!this.FormDetailValid) {
        return
      }
      this.broadcast('FormUpload', 'setG9s')
      const result = await this.$refs.formTemp.beforeSubmit(buttonInfo)
      //如果beforeSubmit返回了false,就不进行后续操作了
      if (result === false) {
        this.buttonLoading = false;
        return
      }
      let variable = result ? result : this.getFormData()
      let submitData = {
        entityName: this.entityName,
        entityObject: variable,
        detailParamList: this.getDetailParamList(),
        formProcessParam: this.useProcess(buttonInfo, variable)
      }
      submitForm(this.service.submit, submitData)
        .then((res) => {
          this.buttonLoading = false
          if (res.status) {
            this.onSuccess(res, 'SUBMIT')
          }
        })
        .catch(() => {
          this.buttonLoading = false
        })
    },
    async handleSubmit(buttonInfo) {
      let valid = false
      try {
        valid = await this.$refs.formTemp.$refs.form.validate()
      } catch (err) {
        valid = false
      }
      if (!valid) {
        return
      }
      this.submit(buttonInfo)
    },
    async handlePreSave(buttonInfo) {
      let valid = true
      if (this.preSaveValidateProps.length > 0) {
        this.$refs.formTemp.$refs.form.validateField(
          this.preSaveValidateProps,
          (err) => {
            err && (valid = false)
          }
        )
      }
      if (!valid) {
        return
      }
      this.submit(buttonInfo)
    },
    handleAbandon() {
      this.$confirm(this.LANG.ABANDON_CONFIRM, {
        title: this.LANG.TIPS,
        type: 'warning',
        confirmButtonText: this.LANG.CONFIRM,
        cancelButtonText: this.LANG.CANCEL,
        confirmButtonClass: 'fks-button--danger',
        deleteMode: true
      }).then(() => {
        const flowData = {
          taskId: this.taskId,
          bizId: this.bizId,
          comment: this.comment,
          formKey: this.formKey
        }
        processAbandon(flowData)
          .then((res) => {
            this.buttonLoading = false
            if (res.status) {
              this.onSuccess(res, 'ABANDON')
            }
          })
          .catch((err) => {
            this.buttonLoading = false
            console.log(err)
          })
      })
    },
    async onSuccess(res, type) {
      if (res.status) {
        this.$message({
          type: 'success',
          message: (this.LANG[type] || '') + ' ' + this.LANG.SUCCESS,
          showClose: true
        })
      } else {
        this.$message({
          type: 'warning',
          message: (this.LANG[type] || '') + ' ' + this.LANG.FAILED,
          showClose: true
        })
      }
      //如果表单有onSuccess方法，则先执行此方法，如果返回值不===false就关闭弹窗
      if (this.$refs.formTemp.onSuccess) {
        const result = await this.$refs.formTemp.onSuccess(res, type)
        if (result !== false) {
          this.$emit('success', res, type) //派发给表格页面的router-view的success
        }
      } else {
        //没有onSuccess方法则直接关闭弹窗
        this.$emit('success', res, type) //派发给表格页面的router-view的success
      }
    },
    initForm() {
      const reqs = this.isPC ? require.context(
        '@/modules/FormCenter',
        true,
        /pc-view\.vue/,
        'lazy'
      ) : require.context(
        '@/modules/FormCenter',
        true,
        /mobile-view\.vue/,
        'lazy'
      )
      reqs.keys().forEach((key) => {
        if (RegExp(`\\/${this.formKey}\\/`).test(key)) {
          reqs(key).then((res) => {
            this.dynamicForm = res.default
          })
        }
      })
    },
    initProcess() {
      //有taskId,获取流程图
      if (this.taskId) {
        // 若有taskId则获取当前任务节点名称
        getUserTaskDetail(this.taskId).then((res) => {
          if (res.status && res.data.taskName) {
            this.taskNodeName = '—' + res.data.taskName
          }
        })
      }
      if (this.type !== 'add') {
        getProcessHistory({taskId: this.taskId, bizId: this.bizId}).then(
          (res) => {
            this.comments = res.data
          }
        )
      }
      getModelXML({modelKey: this.flowData.modelKey}).then((res) => {
        this.flowInfo.xml = res.data.bpmnXml
        if (this.bizId) {
          getHistoryNodes({
            // taskId: this.taskId
            bizId: this.bizId
          }).then((resp) => {
            this.flowInfo = Object.assign(this.flowInfo, resp.data)
          })
        }
      })
    },
    setFormName(formName) {
      this.formName = formName
    },
    setModelKey(modelKey) {
      this.flowData.modelKey = modelKey
    },
    setEntityName(entityName) {
      this.entityName = entityName
    },
    setService(service) {
      this.service = service
    },
    setPreSaveValidateProps(props = []) {
      this.preSaveValidateProps = props
    },
    setSubmitText(submitText) {
      this.submitText = submitText;
    },
    getFormData() {
      try {
        return this.$refs.formTemp.$data.formData
      } catch (err) {
        return {}
      }
    },
    //明细表数据
    getDetailParamList() {
      try {
        return this.$refs.formTemp.$data.detailParamList
      } catch (err) {
        return []
      }
    }
  }
}
