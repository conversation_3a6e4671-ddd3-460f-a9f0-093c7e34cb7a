.main-container {
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &.outSideBorder {
    border: 1px solid #eeeeee;
    margin: 60px auto;
    padding: 60px 60px 0;
    border-radius: 8px;
    max-width: 1600px;
  }

  .special {
    /deep/ .fks-form-item__content {
      margin-left: 0 !important;
    }
  }
}

.component,
.cus-form {
  height: calc(100% - 122px - env(safe-area-inset-bottom));
  height: calc(100% - 122px - constant(safe-area-inset-bottom));
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
}

.buttons {
  display: flex;
  padding: 18px 30px;
  bottom: 0;
  height: calc(122px + constant(safe-area-inset-bottom));
  height: calc(122px + env(safe-area-inset-bottom));
}

.border-20 {
  height: 100%;
  border-right: 30px solid #F3F5F9 ;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 400px;
}

.task-detail-form {
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */
  padding-bottom: 122px;
}

/deep/ .fm-field--disabled .fm-field__label {
  color: #000;
}

/deep/ .fm-cell-group__title {
  color: #000;
}
