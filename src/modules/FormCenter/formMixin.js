/*
 * @Author: gao_m3
 * @Date: 2022-07-27 13:52:49
 * @LastEditors: xie_sm
 * @LastEditTime: 2023-04-11 09:55:13
 * @Descripttion:
 */
import { getFormData } from './api'

export default {
  data () {
    return {
      type: this.$route.params.type,
      bizId: this.$route.params.bizId,
      // 明细表对象
      detailTable: {},
      isDefault: true
    }
  },
  props: {
    taskKey: {
      type: String,
      default: 'UserTask_0'
    },
    approvalList: {
      type: Array,
      default() {
        return []
      }
    },
    comments: {

    }
  },
  computed: {
    submitText: {
      get() {
        return '提交';
      },
      set() {
        this.$emit('setSubmitText', this.submitText);
      }
    },
    isAdd() {
      return this.type === 'add' || (this.taskKey === 'UserTask_0' && this.type === 'execute')
    },
    isExecute0() {
      return this.taskKey === 'UserTask_0' && this.type === 'execute';
    },
    // 行程信息变更
    isXcModify() {
      return this.$route.query.buttonKey === 'XC_FORM_MODIFY' && this.$route.params.formKey === 'vehicleDispatch';
    },
    // 司机/车辆变更
    isXcDriverCarModify() {
      return this.$route.query.buttonKey === 'XC_DRIVER_CAR_MODIFY' && this.$route.params.formKey === 'vehicleDispatch';
    },
    flowConfig () {
      let temp = {}
      if (this.type === 'view' || this.type === 'execute') {
        const xc = ['startTime', 'predictEndTime', 'startAddress', 'startAddressDetail', 'endAddress', 'endAddressDetail'];
        // const xcDriverCar = ['realCarType', 'carCompanyName', 'carCompanyName'];
        const xcDriverCar = [];
        Object.keys(this.formData).forEach((key) => {
          if ((this.isXcModify && xc.includes(key)) || (this.isXcDriverCarModify && xcDriverCar.includes(key))) {

          } else {
            temp[key] = 'readonly'
          }
        });
      }
      const taskKey = this.$route.params.taskKey
      if (taskKey === 'UserTask_0' && this.isXcModify ) {
        temp.useCarPersonNum = 'readonly';
      }
      if (
        this.type === 'execute' && !this.isXcModify &&
        (taskKey === 'UserTask_0' || taskKey === 'fawkes_custom_flow_start')
      ) {
        Object.keys(this.formData).forEach((key) => {
          temp[key] = ''
        })
      }
      this.customConfig && this.customConfig(temp)
      return temp
    },
    // 后端接口对应的明细表列表
    detailParamList: {
     get() {
       let tempArr = [];
       Object.keys(this.detailTable).forEach((key) => {
         let tempObj = {
           detailEntityArray: this.detailTable[key],
           detailEntityName: key
         };
         tempArr.push(tempObj);
       });
       return tempArr;
     },
     set() {

     }
    },
  },
  watch: {},
  async created () {
    if (this.$route.path.includes('/apply/details')) {
      await this.prepareData();
    }
    this.$emit('setFormName', this.formName) //设置表单左上角弹窗名称
    this.$emit('setModelKey', this.modelKey) //设置modelKey
    this.$emit('setEntityName', this.entityName) //设置实体对象名称，需要询问后端
    this.$emit('setService', this.service)
    this.$emit('setPreSaveValidateProps', this.preSaveValidateProps)
    this.$emit('setDefault', this.isDefault);
    this.$emit('setSubmitText', this.submitText);
    if (this.type !== 'add') {
      let params = {
        entityName: this.entityName,
        id: this.bizId,
        // detailEntityNameList: ["ReimburseDetail", "ReimburseAttachement"],
      }
      if (Array.isArray(this.detailEntityNameList) && this.detailEntityNameList.length > 0) {
        params.detailEntityNameList = this.detailEntityNameList
      }
      getFormData(this.service.initial, params, this.isDefault ? 'post' : 'get').then(async (res) => {
        // 若业务表单中定义了beforeInit方法
        const result = this.beforeInit && (await this.beforeInit(res))
        if (result) {
          this.formData = result.formData
          this.detailParamList = result?.detailParamList || []
        } else {
          this.formData = res?.data?.entityObject??{}
          this.detailParamList = res?.data?.detailParamList || this.detailParamList
        }
        // 回填表单和子表信息到Formcenter组件中
        this.$emit('setFormData', this.formData)
        this.$emit('setDetailParamList', this.detailParamList)
      })
    }
  },
  beforeDestroy () {
    this.formData = {}
    this.detailParamList = []
  },
  methods: {
    // 驼峰转换下划线
    toLine (name) {
      return name.replace(/([A-Z])/g, '_$1').toLowerCase()
    },
  },
}
