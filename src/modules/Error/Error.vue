<!--
 * @Author: gao_m3
 * @Date: 2022-08-05 08:39:16
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-08-05 08:40:12
 * @Descripttion:
-->
<!--
 * @Author: <EMAIL>
 * @Date: 2019-11-01 15:02:52
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-07-12 16:03:25
 * @Description: 无效路径统一跳转页
 -->
<template>
  <div class="wscn-http404">
    <div v-if="type === 'emptyPortal'" class="text">
      <div class="tone">无门户权限</div>
      <div class="desc">发生了一些问题</div>
      <div class="tip">如有疑问，请反馈到管理员</div>
      <div class="btn">
        <fm-button type="primary" style="margin-left: 10px" @click="feedback">问题反馈</fm-button>
      </div>
    </div>
    <div v-else class="text">
      <div class="tone">{{type}}</div>
      <div class="desc">发生了一些问题</div>
      <div class="tip">{{errorMessage}}</div>
      <div class="btn">
        <fm-button type="primary" style="margin-left: 10px" @click="feedback">问题反馈</fm-button>
      </div>
    </div>
  </div>
</template>

<script>
import img_404_cloud from '@/assets/img/error/404_cloud.png'
import * as StateTypes from '@/store/State/stateTypes'
import {mapState} from 'vuex'
export default {
  props: {
    type: {
      default: '404',
    },
  },
  data() {
    return {
      img_404_cloud,
      errorMessage: ''
    }
  },
  computed: {
    ...mapState([StateTypes.APPLY_RESOURCE]),
    portals() {
      return this.$store.state.portals.length
    },
  },
  mounted() {
    if(this.$route.query.errorMessage) {
      this.errorMessage = this.$route.query.errorMessage
    }
  },
  methods: {
    back() {
      if (this[StateTypes.APPLY_RESOURCE] === 3) {
        this.$router.push('/login')
      } else {
        this.$router.replace({path: '/', query: {reLogin: 'true'}})
      }

    },
    feedback() {
      window.open('https://b8ijl3262y.feishu.cn/share/base/form/shrcn3CzajFG3KpeToxGS6arDmb', '_blank');
    }
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.wscn-http404 {
  display: flex;
  align-items: center;
  overflow: hidden;

  img {
    width: 100%;
  }

  .pic-404 {
    position: relative;

    &__child {
      position: absolute;

      &.left {
        width: 80px;
        opacity: 0;
        animation-name: cloudLeft;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-delay: 1s;
        animation-fill-mode: forwards;
      }

      &.mid {
        width: 46px;
        opacity: 0;
        animation-name: cloudMid;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-delay: 1.2s;
        animation-fill-mode: forwards;
      }

      &.right {
        width: 62px;
        opacity: 0;
        animation-name: cloudRight;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-delay: 1s;
        animation-fill-mode: forwards;
      }

      @keyframes cloudLeft {
        0% {
          top: 17px;
          left: 220px;
          opacity: 0;
        }

        20% {
          top: 33px;
          left: 188px;
          opacity: 1;
        }

        80% {
          top: 81px;
          left: 92px;
          opacity: 1;
        }

        100% {
          top: 97px;
          left: 60px;
          opacity: 0;
        }
      }

      @keyframes cloudMid {
        0% {
          top: 10px;
          left: 420px;
          opacity: 0;
        }

        20% {
          top: 40px;
          left: 360px;
          opacity: 1;
        }

        70% {
          top: 130px;
          left: 180px;
          opacity: 1;
        }

        100% {
          top: 160px;
          left: 120px;
          opacity: 0;
        }
      }

      @keyframes cloudRight {
        0% {
          top: 100px;
          left: 500px;
          opacity: 0;
        }

        20% {
          top: 120px;
          left: 460px;
          opacity: 1;
        }

        80% {
          top: 180px;
          left: 340px;
          opacity: 1;
        }

        100% {
          top: 200px;
          left: 300px;
          opacity: 0;
        }
      }
    }
  }

  .text {
    min-width: 500px;
    padding-left: 128px;

    .tone {
      margin-bottom: 20px;
      font-weight: bold;
      font-size: 24px;
      line-height: 32px;
      color: #0b4870;
      opacity: 0;
      animation-name: slide-up;
      animation-duration: 1s;
      animation-fill-mode: forwards;
    }

    .desc {
      margin: 16px 0 32px;
      font-size: 24px;
      line-height: 32px;
      color: #191919;
      opacity: 0;
      animation-name: slide-up;
      animation-duration: 1s;
      animation-delay: 0.1s;
      animation-fill-mode: forwards;
    }

    .tip {
      font-size: 16px;
      line-height: 20px;
      color: #555;
      opacity: 0;
      animation-name: slide-up;
      animation-duration: 1s;
      animation-delay: 0.2s;
      animation-fill-mode: forwards;
    }

    .btn {
      display: flex;
      justify-content: flex-end;
      margin-top: 48px;
      opacity: 0;
      animation-name: slide-up;
      animation-duration: 1s;
      animation-delay: 0.3s;
      animation-fill-mode: forwards;
    }

    @keyframes slide-up {
      0% {
        opacity: 0;
        transform: translateY(100px);
      }

      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
}
</style>
