<!--
 * @Author: gao_m3
 * @Date: 2022-08-05 08:39:37
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-08-05 09:14:50
 * @Descripttion: 
-->
<template>
  <div class="error">
    <router-view />
  </div>
</template>

<script>
import Mix from '@/mixins/module'
export default {
  name: 'Error',
  mixins: [Mix],
}
</script>

<style>
.error {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
</style>
