.search-bar-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;

  .search-bar-header, .search-bar-body, .search-bar-title, .search-bar-footer {
    width: 100%;
    box-sizing: border-box;  /* 保证子元素也应用 border-box */
  }
  .search-bar-title {
    color: #32363DFF;
  }
  .search-bar-header {
    color: #555555CC;
    display: flex;
    justify-content: space-between;
    align-items: center;
    div {
      display: flex;
      align-items: center;
      gap: 5px;
    }
  }
  .search-bar-body {
    color: #555555FF;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;

    .search-bar-item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 8px 0;

      .item-operator {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;
      }
    }
  }
  .search-bar-footer {
    display: flex;
    justify-content: space-between;
  }
}

.search-bar-container .fks-divider--horizontal {
  margin: 34px 0 !important;
}

.search-bar-container .fks-input__inner {
  padding: 16px !important;
  height: 68px !important;
  line-height: 68px !important;
}
.search-param-dialog .fks-dialog__body {
  padding: unset !important;
}
.search-param-dialog .fks-dialog--medium {
  border-radius: 8px;
}