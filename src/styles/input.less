/deep/ .fks-input .fks-input__inner {
  border-radius: 12px;
  border-color: #dddddd;
  height: 64px;
  line-height: 64px;
}
/deep/ .fks-input:has(.fks-input-group__append) {
  .fks-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

/deep/ .fks-input .fks-input__suffix {
  i {
    color: #cccccc;
    &.clear-icon:hover {
      color: #333;
    }
  }
}

/deep/.fks-dropdown-link {
  color: #333;
  cursor: pointer;
}
///deep/.fks-date-editor--daterange.fks-input__inner {
//  width: 500px;
//  margin: 5px 20px;
//}
///deep/.fks-input--medium .fks-input__inner {
//  width: 500px;
//  margin: 5px 20px !important;
//}
/deep/.fks-input__suffix {
  margin-right: 20px !important;
}
/deep/.fks-date-editor .fks-range-separator {
  padding: 0;
  font-size: 24px;
}
.custom-input-placeholder ::placeholder {
  font-size: 24px;
}
