.newTheme {
  margin: 0;
  /deep/ .fks-form-item {
    margin-bottom: 24px;
    padding: 0 !important;
    font-family: initial !important;
  }

  /deep/ .fks-form-item .fks-form-item__label {
    text-align: left;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #555555;
    position: relative;
    padding-left: 20px;
    padding-bottom: 0 !important;

    &:before {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  /deep/ .fks-form-item .fks-form-item__content {
    padding-left: 20px;
    line-height: 30px !important;
  }

  /deep/ .fks-form-item__error {
    color: #ff4d4f;
    font-size: 24px;
    line-height: 1;
    top: auto;
    left: auto;
    position: relative;
    padding-top: 0;
    padding-left: 0;
  }

  /deep/ .fks-form-item:has(input:disabled) {
    /*margin-bottom: 0 !important; 这段代码会让表单项挨在一起，所以注释掉*/
  }

  /deep/ .fks-form-item:has(textarea:disabled) {
    margin-bottom: 0;
  }

  /deep/ .fks-form-item:has(.fee-item) {
    margin-bottom: 0;
  }

  ///deep/ .fks-input {
  //  max-width: 980px;
  //}
  ///deep/ .fks-textarea {
  //  max-width: 980px;
  //}
  ///deep/ .fks-select {
  //  max-width: 980px;
  //}
  ///deep/ .fks-date-editor {
  //  max-width: 980px;
  //}
  .apply-person-card {
    max-width: 980px;
    /deep/ .fks-form-item__content {
      margin-left: 0 !important;
    }
  }
}

/deep/ .fks-form-item.hideLabel {
  .fks-form-item__content {
    margin-left: 0 !important;
  }
}

/deep/ .fks-input.is-disabled  {
  //background: #F4F4F4;
  &.appendButton .fks-textarea__inner {
    color: #3C83FF !important;
  }
  .fks-input__inner {
    color: #333 !important;
    //background: transparent !important;
    //border: none !important;
  }

}

/deep/ .fks-textarea.is-disabled {
  //background: #F4F4F4;
  &.appendButton .fks-textarea__inner {
    color: #3C83FF !important;
  }
  .fks-textarea__inner {
    color: #333 !important;
    //background: transparent !important;
    //border: none !important;
  }
}

/deep/ .fks-form.fks-form--label-top {
  .fks-form-item {
    margin-bottom: 0 !important;
    padding: 0 !important;
    font-family: initial !important;
  }

  .fks-form-item__label {
    padding: 0;
    //position: relative !important;
    //&:before {
    //  position: absolute;
    //  left: 0;
    //  top: 50%;
    //  transform: translateY(-50%);
    //}
  }
}

/deep/ .fks-form-item.vertical-form-item {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  label {
    line-height: unset !important;
    margin-right: 12px !important;
    width: 160px !important;
    white-space: nowrap;
  }
  .fks-form-item__content {
    flex-grow: 1;
  }
}

.border-style {
  border-radius: 12px !important;
}


/deep/ .file-card{
  display: flex;
  align-content: center;
}

.notify-content {
  background-color: rgba(60, 131, 255, 0.1);
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 28px;
  position: relative; /* 确保伪元素能够基于这个容器定位 */
}

.notify-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background-color: rgba(60, 131, 255, 1);
  border-radius: 12px 0 0 12px;
}
.notify-tips {
  font-weight: normal;
  letter-spacing: 0px;
  color: #FF4D4F;
}
