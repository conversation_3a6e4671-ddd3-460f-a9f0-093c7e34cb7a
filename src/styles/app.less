.m-16 {
  margin: 16px;
}
.full-vw {
  width: 100vw;
}
.full-width {
  width: 100% !important;
}
.full-height {
  height: 100%;
}
.half-width {
  width: 50% !important;
}
.eighty-height {
  height: 80% !important;
}
.eighty-width {
  width: 80% !important;
}
.ninety-height {
  height: 90% !important;
}
.ninety-width {
  width: 90% !important;
}
.width-33 {
  width: 33% !important;
}
.max-width-490 {
  max-width: 980px !important;
}
.max-width-1400 {
  max-width: 1400px;
}
.max-width-1600 {
  max-width: 1600px;
}
.position-relative {
  position: relative !important;
}
.position-absolute {
  position: absolute !important;
}
.mt-2 {
  margin-top: 10px !important;
}
.cursor-pointer {
  cursor: pointer !important;
}
.height-60 {
  height: 600px !important;
}

.height-100 {
  height: 1000px !important;
}
.map-full {
  height: calc(100vh - 220px);
}
.z-index {
  z-index: 99;
}
.top-2 {
  top: 10px !important;
}
.right-2 {
  right: 10px !important;
}
.left-2 {
  left: 10px !important;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.flex {
  display: flex;
}
.flex-grow-1 {
  flex-grow: 1;
}
.row-start {
  justify-content: flex-start;
}
.row-center {
  justify-content: center;
}
.row-between{
  justify-content: space-between;
}
.row-end {
  justify-content: flex-end;
}
.col-baseline {
  align-items: baseline;
}
.col-center {
  align-items: center;
}
.col-end {
  align-items: flex-end;
}
.col-start {
  align-items: start;
}
.column{
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap
}
.flex-column {
  flex-direction: column;
}
.m-l-24 {
  margin-left: 24px;
}
.m-b-16 {
  margin-bottom: 16px;
}
.m-b-24 {
  margin-bottom: 24px !important;
}
.m-l-10 {
  margin-left: 10px;
}
.m-l-20 {
  margin-left: 20px;
}
.m-r-10 {
  margin-right: 10px;
}
.m-t-20 {
  margin-top: 20px;
}

.m-l-16 {
  margin-left: 16px;
}
.m-l-34 {
  margin-left: 34px;
}
.m-l-40 {
  margin-left: 40px !important;
}
.m-l-46 {
  margin-left: 46px;
}
.m-b-10 {
  margin-bottom: 10px;
}
.m-b-16 {
  margin-bottom: 16px;
}
.m-b-28 {
  margin-bottom: 28px;
}
.m-l-48 {
  margin-left: 48px;
}
.m-t-30 {
  margin-top: 30px;
}
.m-l-30 {
  margin-left: 30px;
}
.m-b-30 {
  margin-bottom: 30px;
}
.m-r-30 {
  margin-right: 30px;
}
.m-32 {
  margin: 32px;
}
.m-l-32 {
  margin-left: 32px !important;
}

.m-r-32 {
  margin-right: 32px !important;
}
.m-b-32 {
  margin-bottom: 32px;
}
.m-b-36 {
  margin-bottom: 36px;
}
.m-t-32 {
  margin-top: 32px !important;
}
.m-t-24 {
  margin-top: 24px;
}
.m-r-20 {
  margin-right: 20px !important;
}
.m-r-18 {
  margin-right: 20px !important;
}
.m-r-16 {
  margin-right: 16px;
}
.p-l-16 {
  padding-left: 16px;
}
.p-l-20 {
  padding-left: 20px;
}
.p-24 {
  padding: 24px;
}
.p-32 {
  padding: 32px;
}
.p-l-32 {
  padding-left: 32px;
}
.p-r-32 {
  padding-right: 32px;
}
.p-r-30 {
  padding-right: 30px;
}
.p-b-32 {
  padding-bottom: 32px;
}
.p-10 {
  padding: 10px !important;
}
.p-0 {
  padding: 0 !important;
}
.p-b-8 {
  padding-bottom: 8px !important;
}
.p-t-8 {
  padding-top: 8px !important;
}
.p-b-4 {
  padding-bottom: 4px !important;
}
.p-t-4 {
  padding-top: 4px !important;
}
.p-t-24 {
  padding-top: 24px !important;
}
.p-b-100 {
  padding-bottom: 100px;
}
.d-inline-block {
  display: inline-block;
}
.d-block {
  display: block;
}

.ml-16 {
  margin-left: 16px;
}
.ml-31 {
  margin-left: 31px;
}
.font-24 {
  font-size: 24px;
}
.font-28 {
  font-size: 28px;
}
.font-32 {
  font-size: 32px;
}
.font-48 {
  font-size: 48px;
}
.font-bold {
  font-weight: bold;
}
.font-400 {
  font-weight: 400;
}
.font-500 {
  font-weight: 500;
}
.border-left {
  border-left: 1px solid  #eee;
}
.border-right {
  border-right: 1px solid  #eee;
}
.border-top {
  border-top: 1px solid  #eee;
}
.border-bottom {
  border-bottom: 1px solid  #eee;
}
.border {
  border: 1px solid  #eee;
}
.line-height-40 {
  line-height: 40px;
}
.line-height-48 {
  line-height: 48px;
}
.line-height-30 {
  line-height: 30px;
}
.h-48 {
  height: 48px;
}

.h-96 {
  height: 96px;
}
.h-64 {
  height: 64px;
}
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.text-break {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: keep-all
}
.bg-white {
  background: #FFFFFF !important;
}

.color-black {
  color: #191919;
}
.color-black6 {
  color: rgba(25,25,25,0.6);
}
.color-black3 {
  color: #333333;
}
.color-black9 {
  color: #999999;
}
.color-blue {
  color: #3C83FF;
}
.color-gray {
  color: grey;
}
.color-red {
  color: #FF4D4F;
}
.opacity-4 {
  opacity: 0.4;
}
.opacity-6 {
  opacity: 0.6;
}
.vertical-top {
  vertical-align: top;
}
html{
  touch-action:none;
  touch-action:pan-y;
}

.overflow-y-auto {
  overflow-y: auto
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-hidden {
  overflow: hidden;
}

.border-radius-8 {
  border-radius: 8px
}

.z-index-top {
  z-index: 10001 !important;
}
