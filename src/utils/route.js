/*
 * @Author: gao_m3
 * @Date: 2022-08-05 09:01:50
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-08-05 14:41:33
 * @Descripttion:
 */
/*
 * @Author: <EMAIL>
 * @Date: 2020-08-27 20:29:37
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-21 18:01:45
 * @Description: 权限相关方法
 */
import {setRoutes} from '@/router'
import {getRouter} from '@/api/user'
import {arrayToTree} from '@/utils/array'
import {evil, isJSON} from '@/utils/util'
import store from '@/store'
import storage from '@/utils/storage'
import {appPathPrefix, mainApp, subApp, useMainRoute} from '@/config'
import {router} from '@/modules/FormCenter/commonFormRoute'
import Vue from 'vue'


/**
 * @description: 加载线上路由
 */
export const getUserRoutes = function () {
  // console.info('🚀🚀', 'getUserRoutes 调用，获取线上路由 -->', `<-- route.js/getUserRoutes`)
  return new Promise((resolve, reject) => {
    //当处于qiankun下的子应用，且设置为使用父应用传入的数据作为路由数据源
    if (useMainRoute && Vue.prototype.$InQianKun) {
      return resolve(getOwnRoutes(Vue.prototype.$fksMainStore.state.subRoutes))
    } else {
      let data = {portalId: store.state.portal.id}
      let ua = navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        wx.miniProgram.getEnv((res) => {
          if (res.miniprogram) {        //在小程序
          } else {
            //在公众号
            if (storage.get('access_token')) {
              getRouter(data).then(res => {
                store.commit('SET_FIRST_LOAD', false)
                return resolve(getOwnRoutes(res.data))
              }).catch((e) => {
                return resolve(getOwnRoutes())
              })
            } else {
              return resolve(getOwnRoutes())
            }
          }
        })
      } else {
        getRouter(data).then(res => {
          store.commit('SET_FIRST_LOAD', false);
          return resolve(getOwnRoutes(res.data))
        }).catch((e) => {
          console.log(e)
          return resolve(getOwnRoutes())
        })
      }
    }
  })
}

/**
 * @description:  只保留路径与基础路径一致的节点
 */
export const getOwnRoutes = function (routeList = []) {

  let res = {
    data: routeList
  }
  let routes = []

  let prefix = Vue.prototype.$appBasePath

  if (res.data instanceof Array) {

    let array = parseMenu(res.data)
    //排序
    array.sort((v1, v2) => {
      //自定义应用往后排

      if (v1.meta?.config?.indexOf('customApp') - v2.meta?.config?.indexOf('customApp') != 0) {
        return (v1.meta.config.indexOf('customApp') - v2.meta.config.indexOf('customApp'))
      }
      return isNaN(v1.sort) && isNaN(v2.sort) ? 0 : (isNaN(v2.sort) ? -v1.sort - 1 : isNaN(v1.sort) ? v2.sort + 1 : v1.sort - v2.sort)
    })

    try {
      // 解析为带children树结构的格式
      array = arrayToTree(array, 'parentId')
    } catch (error) {
      console.error(error)
    }

    routes = routes.concat(array.filter((a) => {
      return !a.meta.config.includes('customApp') || (a.children instanceof Array && a.children.length > 0)
    }))
  }

  // routes只保留路径与基础路径一致的节点
  routes = routes.filter(item => {
    return item.path.includes(prefix)
  })


  //子应用将以/sub_app_xx为根节点下的二级节点提升为一级节点
  if (subApp) {
    let newRoutes = []
    for (let route of routes) {
      if (route.children && route.path == prefix)
        route.children.forEach(child => {
          if (child.path.indexOf('/') !== 0)
            child.path = '/' + child.path
          newRoutes.push(child)
        })
      else {
        newRoutes.push(route)
      }
    }
    routes = newRoutes
  }


  //添加路由异名并开始导入组件
  for (let i = 0; i < routes.length; i++) {
    addAlias(routes[i])
    importComponent(routes[i])
  }

  routes = addSpecialBaseRoute(routes)

  store.commit('SET_ROUTES', routes)
  setRoutes(routes)

  return routes.length > 0


}

const parseMenu = function (menus) {
  const array = []
  const formTemp = []
  for (let i = 0; i < menus.length; i++) {
    try {
      //路由转换
      const menuItem = {...menus[i]}
      menuItem.meta = isJSON(menuItem.meta) ? JSON.parse(menuItem.meta) : {}
      if (!menuItem.meta.config) {
        menuItem.meta.config = []
      }
      if (menuItem.meta?.config?.includes('disabled') || menuItem.enable === false) {
        continue
      }
      // menuItem.component = () => import(`@/modules${menus[i].component}`)
      menuItem.name = menuItem.code

      menuItem.meta.query || (menuItem.meta.query = {})

      if (menuItem.meta.query instanceof Array) {
        menuItem.meta.query = menuItem.meta.query.reduce((pval, val) => {
          pval[val.key] = val.value
          return pval
        }, {})
      }

      //链接携带token
      if (menuItem.meta?.config?.includes('token')) {
        Object.assign(menuItem.meta.query, {token: storage.get('access_token')})

      }

      //链接携带token
      if (menuItem.meta?.config?.includes('form')) {
        formTemp.push(...router(menuItem.path, menuItem.id))
      }

      //内嵌页面处理
      if (menuItem.meta.type == 'iframe') {
        Object.assign(menuItem.meta.query, {url: menuItem.path})
        menuItem.path = '/iframe/' + menuItem.id
        menuItem.component = '/Iframe/index.vue'
      }


      //1.8.3及之前版本后可兼容菜单显影

      // if (menuItem.hidden) {
      //   menuItem.meta.config instanceof Array ? (!menuItem.meta.config.includes('invisible') && menuItem.meta.config.push('invisible')) : menuItem.meta.config = ['invisible']
      // }

      menuItem.path || (menuItem.path = '')
      menuItem.meta.menuId = menuItem.id
      menuItem.icon = menuItem.icon || ''
      array.push(menuItem)
    } catch (error) {
      console.error(error)
    }
  }
  array.push(...formTemp)
  return array
}


/**
 * @description: 根节点为路径为/sub_app_xx时，对其下的子节点进行处理
 *               route的name由yy改为sub_app_xx_yy
 *               路径从/XX 改为/sub_app_xx/XX
 *               添加路径/sub_app_xx/home及/sub_app_xx/404
 */
export function addAlias(route) {
  if (!subApp) return

  let nodeBasePath = Vue.prototype.$appBasePath

  let nodeBaseName = Vue.prototype.$appName

  //route的name由yy改为sub_app_xx_yy
  if (route.name && !route.name.includes(nodeBaseName)) {
    route.name = nodeBaseName + '_' + route.name
  }

  // 路径从/XX 改为/sub_app_xx/XX
  if (route.path.indexOf('/') === 0 && route.path.indexOf(nodeBasePath) != 0) {
    route.alias = nodeBasePath + route.path
    // route.path = nodeBasePath + route.path
  }

  if (route.children) {
    for (let childroute of route.children) {
      addAlias(childroute)
    }
  }
}

const addSpecialBaseRoute = function (routes) {
  //非子应用下加入*通配
  if (!Vue.prototype.$InQianKun) {
    routes.push({path: '*', redirect: '/404', meta: {config: ['invisible']}})
  }
  return routes
}


const deleteComponent = function (item) {
  if (item.component)
    delete item.component
  if (item.children) {
    for (let i = 0; i < item.children.length; i++) {
      deleteComponent(item.children[i])
    }
  }
}


const importComponent = function (item) {

  //主应用模式下，不加载子级的子应用的组件
  if (mainApp) {
    let subPrefix = '/' + appPathPrefix
    if (item.path.includes(subPrefix)) {
      deleteComponent(item)
      return false
    }
  }

  try {
    let uri = item.component
    if (/^ModuleView/.test(uri)) {
      item.component = evil(uri)
    } else {
      item.component = () => import(`@/modules${uri}`)
    }
  } catch (e) {
    debugger
    // return e
  }

  if (item.children) {
    for (let i = 0; i < item.children.length; i++) {
      importComponent(item.children[i])
    }
  }
}
