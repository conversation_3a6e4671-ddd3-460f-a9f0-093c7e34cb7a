import request from '@/utils/request'
export const app_id = process.env.VUE_APP_FEISHU_APP_ID;
// 获取飞书code
export function getFeishuCode() {
  console.info('🚀🚀', 'window.h5sdk -->', window.h5sdk, `<-- feishuapi.js/getFeishuCode`)
  return new Promise((resolve, reject) => {
      window.h5sdk.error(err => {
        reject(JSON.stringify(err))
      });
      // 通过ready接口确认环境准备就绪后才能调用API

      window.h5sdk.ready(() => {
        // 调用JSAPI tt.requestAuthCode 获取 authorization code
        tt.requestAuthCode({
          appId: app_id,
          success(res) {
            resolve(res.code)
          },
          fail(err) {
            reject(JSON.stringify(err))
          }
        })
      })
  })
}
// 获取userToken
export function getFeishuUserToken (code) {
  return request({
    url: '/vehicle-dispatch/feishu/open-apis/authen/v1/access_token',
    params: {
      code
    }
  })
}

// 公众号端判断是否过期
export function isUserTokenExpired(unionId){
  return request({
    url: '/vehicle-dispatch/feishu/open-apis/authen/v1/access_token/expire',
    params: {unionId}
  })
}

