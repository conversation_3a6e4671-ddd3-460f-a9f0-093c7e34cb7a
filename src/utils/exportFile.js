import {
  Message
} from 'fawkes-lib';
export function fileReader(res, callback, message = '正在导出数据') {
  const reader = new FileReader();
  reader.readAsText(res);
  reader.onload = function (result) {
    try {
      const resData = JSON.parse(result.target.result);
      Message.error(resData.message);
      return false;
    } catch (err) {
      if (err instanceof SyntaxError) {
        Message.success(message);
        // 解析成对象失败，说明是文档流
        exportFile(res);
        if (callback instanceof Function) {
          callback();
        }
      } else {
        Message.error(`导出失败：${err.message}`);
      }
    }
  };
}

export function exportFile(response) {
  const blob = new Blob([ response.data || response ], { type: response.headers[ 'content-type' ] }); // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet这里表示xlsx类型
  const desprsitions = response.headers[ 'content-disposition' ].split(';');
  let filename = '';
  for (let i = 0; i < desprsitions.length; i++) {
    if (desprsitions[ i ].indexOf('filename') > -1 || desprsitions[ i ].indexOf('name') > -1) {
      const filenames = desprsitions[ i ].split('=');
      if (filenames.length > 1) {
        // filename = decodeURI(filenames[ 1 ].trim());
        filename = window.decodeURIComponent(filenames[ 1 ].trim());
      }
    }
  }
  if ('download' in document.createElement('a')) {
    const downloadElement = document.createElement('a');
    let href = '';
    if (window.URL) {
      href = window.URL.createObjectURL(blob);
    } else {
      href = window.webkitURL.createObjectURL(blob);
    }
    // console.log('Message', response.headers[ 'content-type' ], filename, response);
    // downloadElement.href = `data:text/plain;charset=utf-8,${encodeURIComponent(href)}`;
    downloadElement.href = href;
    downloadElement.download = filename;
    document.body.appendChild(downloadElement);
    downloadElement.click();
    if (window.URL) {
      window.URL.revokeObjectURL(href);
    } else {
      window.webkitURL.revokeObjectURL(href);
    }
    document.body.removeChild(downloadElement);
  } else {
    navigator.msSaveBlob(blob, filename);
  }
}
