/*
 * @Author: <EMAIL>
 * @Date: 2021-09-09 18:34:39
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-07-13 10:39:21
 * @Description: 校验密码
 */
import { Dialog } from 'fawkes-mobile-lib'
import store from '@/store'
import storage from '@/utils/storage'

/**
 * 判断密码状态
 * 初始密码
 * 密码过期
 * 强制修改
 */
export const checkPwdStatus = async function () {
  return new Promise((resolve, reject) => {
    //密码提示
    let message = ''

    // if (store.state.accountConfig.mandatoryModify == 0 || storage.get('passwordTip') === 'false') {
    //   //初始密码提示
    //   resolve(false)
    //   return false
    // }

    // if (store.state.user.isInitPwd !== false) {
    //   message = '您的密码为初始密码，请点击修改前往密码修改界面'
    // }

    // //密码过期提示
    // if (store.state.user.pwdIsExpired === true) {
    //   message = '您的账号密码即将过期，请点击修改前往密码修改界面'
    // }

    // if (!message) {
    //   storage.set('passwordTip', false)
    //   resolve(false)
    //   return false
    // }

    // if (store.state.accountConfig.mandatoryModify == 1) {
    //   //提示
    //   Dialog.confirm({
    //     message: message

    //   }).then(() => {
    //     resolve('/privacyCenter')
    //   }).catch(() => {
    //     resolve(false)
    //   })
    //   storage.set('passwordTip', false)
    // }
    // else {
    //   //强制修改时直接跳转
    //   resolve('/privacyCenter')
    // }
  })
}

//密码规则校验

let phonePattern = /^.*(((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18([0-9])))\d{8})|(16[6])|(19[8-9]).*$/ //手机号
let cardPattern = /^.*([1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]).*$/ //身份证
let regNumber = /\d+/ //验证0-9的任意数字最少出现1次。
let regString = /[a-zA-Z]+/ //验证大小写26个字母任意字母最少出现1次
let regUpString = /[A-Z]/
let regLowString = /[a-z]/

export function pwdRuleCheck (password) {
  let accountConfig = store.state.accountConfig
  let pwd = password
  let pwSpecial = pwd.length - pwd.replace(/[^a-z0-9A-Z]/g, '').length
  if (accountConfig.passwordLevel && accountConfig) {
    switch (accountConfig.passwordLevel) {
      case 'custom': {
        switch (accountConfig.charDemand) {
          case '0': {
            if (
              pwd.length > accountConfig.pwMinLength - 1 &&
              pwd.length < accountConfig.pwMaxLength + 1
            ) {
              return sensitivityCheck(pwd)
            } else {
              return false
            }
          }
          case '1': {
            if (
              pwd.length > accountConfig.pwMinLength - 1 &&
              pwd.length < accountConfig.pwMaxLength + 1 &&
              regNumber.test(pwd) &&
              regString.test(pwd)
            ) {
              return sensitivityCheck(pwd)
            } else {
              return false
            }
          }
          case '2': {
            if (
              pwd.length > accountConfig.pwMinLength - 1 &&
              pwd.length < accountConfig.pwMaxLength + 1 &&
              regNumber.test(pwd) &&
              regUpString.test(pwd) &&
              regLowString.test(pwd)
            ) {
              return sensitivityCheck(pwd)
            } else {
              return false
            }
          }
          case '3': {
            if (
              pwd.length > accountConfig.pwMinLength - 1 &&
              pwd.length < accountConfig.pwMaxLength + 1 &&
              regNumber.test(pwd) &&
              regUpString.test(pwd) &&
              regLowString.test(pwd) &&
              pwSpecial > 0
            ) {
              return sensitivityCheck(pwd)
            } else {
              return false
            }
          }
        }
        break
      }
      case 'normal': {
        if (
          pwd.length > accountConfig.pwMinLength - 1 &&
          pwd.length < accountConfig.pwMaxLength + 1 &&
          regNumber.test(pwd)
        ) {
          return sensitivityCheck(pwd)
        } else {
          return false
        }
      }
      case 'easy': {
        if (
          pwd.length > accountConfig.pwMinLength - 1 &&
          pwd.length < accountConfig.pwMaxLength + 1
        ) {
          return sensitivityCheck(pwd)
        } else {
          return false
        }
      }
    }
  } else {
    return false
  }
}
export function sensitivityCheck (pwd) {
  let accountConfig = store.state.accountConfig
  if (accountConfig.sensitiveInfo) {
    if (phonePattern.test(pwd) || cardPattern.test(pwd)) {
      return false
    } else {
      return true
    }
  } else {
    return true
  }
}
