import storage from "@utils/storage";

export const PROJECT_PORTAL = '项目门户'
export const COMPANY_PORTAL = '数据舱'
export const UserTask_0 = 'UserTask_0'
export const UserTask_100 = 'UserTask_100'
export const UserTask_200 = 'UserTask_200'
export const UserTask_300 = 'UserTask_300'
export const UserTask_400 = 'UserTask_400'
export const UserTask_500 = 'UserTask_500'
export const getTaskNumber = (str) => {
  const regex = /UserTask_(\d+)/g;
  if (str) {
    const match = regex.exec(str)
    if (match) {
      return match[1]
    } else {
      return -1
    }
  }
  return -1;
}
export const formatNumber = (str) => +str || 0
// 流程状态tag颜色表
export const processStatusTable = {
  pc: {
    0: 'link',
    1: 'success',
    2: 'danger',
    3: 'info',
    10000: 'danger'
  },
  mobile: {}
}

export const processColorTable = {
  pc: {
    0: '#3C83FF',
    3: '#FFA418',
    1: '#03BE8A',
    2: '#FF3F4C',
    10000: '#FF3F4C'
  },
  mobile: {}
}

// 车辆状态tag颜色表
export const carStatusColorTable = {
  pc: {
    100: '#3C83FF',
    200: '#FFA418',
    300: '#CCCCCC',
    400: '#FF3F4C',
  }
}
export const feeConfigs = [
  {
    prop: 'startTime2',
    label: '实际开始时间',
    type: 'date',
    pcType: 'date',
    required: true,
    unit: '',
    show: false
  },
  {
    prop: 'endTime2',
    label: '实际结束时间',
    type: 'date',
    pcType: 'date',
    required: true,
    unit: '',
    show: false
  },
  {
    prop: 'km1',
    attachmentName: 'attachment2',
    label: '出车前里程',
    type: 'input-number',
    pcType: 'input-attachment',
    required: true,
    limit: 1,
    unit: '公里'
  },
  {
    prop: 'km2',
    attachmentName: 'attachment3',
    label: '回车后里程',
    type: 'input-number',
    pcType: 'input-attachment',
    required: true,
    limit: 1,
    unit: '公里'
  },
  {
    prop: 'km3',
    attachmentName: 'attachment3',
    label: '本次行车里程',
    type: 'input-number',
    pcType: 'input-number',
    computed: true,
    readonly: true,
    limit: 1,
    unit: '公里'
  },
  {
    prop: 'cost1',
    attachmentName: 'cost1Fj',
    selectName: 'cost1Type',
    label: '公路通行费（元）',
    tooltip: '包含高速公路、一级、二级公路通行费',
    type: 'input-attachment',
    pcType: 'input-attachment',
    labelWidth: 300,
    required: true,
    unit: '元'
  },
  {
    prop: 'cost7',
    attachmentName: 'cost7Fj',
    selectName: 'cost7Type',
    label: '桥、闸通行费（元）',
    type: 'input-attachment',
    pcType: 'input-attachment',
    required: true,
    unit: '元'
  },
  {
    prop: 'cost4',
    attachmentName: 'cost4Fj',
    selectName: 'cost4Type',
    label: '车辆燃油费（元）',
    type: 'input-attachment',
    pcType: 'input-attachment',
    required: true,
    unit: '元'
  },
  {
    prop: 'cost4Field1',
    label: '燃油标号',
    type: 'select',
    pcType: 'select',
    optionsKey: 'ApplyFormFyRyTypeEnums',
    required: false,
  },
  {
    prop: 'other2',
    label: '加油量（升）',
    type: 'input-number',
    pcType: 'input-number',
    required: false,
    unit: '升'
  },

  {
    prop: 'cost8',
    attachmentName: 'cost8Fj',
    label: '车辆清洗费（元）',
    type: 'input-attachment',
    pcType: 'input-attachment',
    required: true,
    unit: '元'
  },
  {
    prop: 'cost9',
    attachmentName: 'cost9Fj',
    selectName: 'cost9Type',
    label: '停车费（元）',
    type: 'input-attachment',
    pcType: 'input-attachment',
    required: true,
    unit: '元'
  },
  {
    prop: 'cost2',
    attachmentName: 'cost2Fj',
    label: '住宿费（元）',
    type: 'input-attachment',
    pcType: 'input-attachment',
    required: false,
    unit: '元'
  },
  {
    prop: 'cost5',
    attachmentName: 'cost5Fj',
    label: '其他（元）',
    type: 'input-attachment',
    pcType: 'input-attachment',
    required: true,
    unit: '元'
  },
]
const portal = storage.get('portal');
const portalId = portal ? JSON.parse(portal).id : '';
export const GLOBAL_SEARCH = 'globalSearch';
export const searchConfigHashTable = {
  // 项目门户
  ProjectIndex: {
    defaultSearchProp: 'searchValue',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/flow/userTasks',
      params: {pageNo: 1, pageSize: 10}
    }
  },
  CarRecord: {
    defaultSearchProp: 'searchValue',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/flow/page',
      params: {
        pageNo: 1,
        pageSize: 10,
        userName: storage.get('username')
      }
    }
  },
  DrivingLog: {
    defaultSearchProp: 'searchValue',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/flow/page',
      params: {
        pageNo: 1,
        pageSize: 10,
        userName: storage.get('username')
      }
    }
  },
  CarManage: {
    defaultSearchProp: 'carNum',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/car/page',
      params: {
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  DriverManage: {
    defaultSearchProp: 'driverName',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/driver/page',
      params: {
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  // 公司门户
  CompanyIndex: {
    defaultSearchProp: 'searchValue',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/flow/userTasks',
      params: {pageNo: 1, pageSize: 10}
    }
  },
  // 公司门户
  companyTodo: {
    defaultSearchProp: 'searchValue',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/flow/userTasks',
      params: {pageNo: 1, pageSize: 10}
    }
  },
  ProjectConfig: {
    defaultSearchProp: 'searchValue',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/project/page',
      params: {pageNo: 1, pageSize: 10}
    }
  },
  carRepo: {
    defaultSearchProp: 'carNum',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/car/page',
      params: {
        pageNo: 1,
        pageSize: 10,
        projectId: portalId
      }
    }
  },
  driverRepo: {
    defaultSearchProp: 'driverName',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/vd/driver/page',
      params: {
        pageNo: 1,
        pageSize: 10,
        projectId: portalId
      }
    }
  },
  UserManagement: {
    defaultSearchProp: 'searchValue',
    configs: [],
    queryInstance: {
      url: '/vehicle-dispatch/user/page',
      params: {
        pageNo: 1,
        pageSize: 10,
        projectId: portalId
      }
    }
  }
}

export const formTabs = [
  {label: '用车信息', value: 'section-1'},
  {label: '乘车人信息', value: 'section-2'},
  {label: '车辆信息', value: 'section-3'},
  {label: '驾驶员信息', value: 'section-4'},
  {label: '行车日志填报', value: 'section-5'},
  {label: '申请信息', value: 'section-6'},
  {label: '行程信息', value: 'section-7'},
  {label: '变更原因', value: 'section-8'},
  {label: '审批记录', value: 'section-9'},
  {label: '审批意见', value: 'section-10'},
]

// 车辆管理-tabs
export const carManagementTabs = [
  {label: '基本信息', name: 'CarManageBaseInfo', icon: 'base'},
  {label: '保险信息', name: 'InsuranceInfo', icon: 'insurance'},
  {label: '年检信息', name: 'AnnualInspectionInfo', icon: 'annual-inspect'},
  {label: '维修保养信息', name: 'MaintenanceInfo', icon: 'maintenance'},
  {label: '通行证信息', name: 'PassportInfo', icon: 'passport'},
  {label: '违章/事故信息', name: 'ViolationAndAccident', icon: 'violation'}
]


// 车辆管理-tabs
export const driverTabs = [
  {label: '司机信息', name: 'CarManageBaseInfo'},
  {label: '填写信息', name: 'InsuranceInfo'},
]

// 修改，行程变更，车辆/司机变更提示词
export const changeTips = {
  FORM_MODIFY_ADMIN: '修改地址、司机车辆信息后不会通知用车人。如需通知，请使用行程信息变更，车辆/司机变更功能。',
  XC_FORM_MODIFY: '修改行程信息后，将会通知用车人。如不需通知，请使用修改功能。',
  XC_DRIVER_CAR_MODIFY: '修改车辆/司机信息后，将会通知司机。如不需通知，请使用修改功能。'
}
