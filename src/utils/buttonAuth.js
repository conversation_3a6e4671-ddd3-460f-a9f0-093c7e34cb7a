
export function getAuth(type) {


  // 获取权限列表和当前路由 path
  let permissions = this.$store.getters.permissions;

  let path = this.$route.path;

  // 拆分 path 的最后一部分
  let lastPathSegment = path.substring(path.lastIndexOf('/') + 1);

  // 构造需要匹配的权限 code
  let requiredCode = `${lastPathSegment}:${type}`;

  // 遍历 permissions 的 code 字段，判断是否有权限
  let hasPermission = permissions.some(permission => permission.code === requiredCode);

  return hasPermission;
}
