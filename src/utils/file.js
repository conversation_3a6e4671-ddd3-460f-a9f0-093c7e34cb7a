/*
 * @Author: xie_sm
 * @Date: 2022-02-28 16:07:02
 * @LastEditors: xie_sm
 * @LastEditTime: 2022-05-18 15:31:11
 * @FilePath: \mobile-template\src\utils\file.js
 * @Description:
 *
 */
import { downloadImage } from '@/api/file'

//常用文本
export const download = (file, blobs) => {
  let blob = new File([blobs], { type: 'application/octet-stream' })
  if ('download' in document.createElement('a')) {
    const downloadElement = document.createElement('a')
    let href = ''
    if (window.URL) {
      href = window.URL.createObjectURL(blob)
    } else {
      href = window.webkitURL.createObjectURL(blob)
    }
    downloadElement.href = href
    downloadElement.download = file.fileName || file.name
    document.body.appendChild(downloadElement)
    downloadElement.click()
    if (window.URL) {
      window.URL.revokeObjectURL(href)
    } else {
      window.webkitURL.revokeObjectURL(href)
    }
    document.body.removeChild(downloadElement)
    URL.revokeObjectURL(href) //释放 Blob
  } else {
    navigator.msSaveBlob(blob, file.fileName || file.name)
  }
  return
}

//excel
export function exportFile(response) {
  const blob = new Blob([response.data], { type: response.headers['content-type'] }) // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet这里表示xlsx类型
  const desprsitions = (response.headers['content-disposition'] || '').split(';')
  let filename = ''
  for (let i = 0; i < desprsitions.length; i++) {
    if (desprsitions[i].indexOf('filename') > -1) {
      const filenames = desprsitions[i].split('=')
      if (filenames.length > 1) {
        filename = decodeURI(filenames[1].trim())
      }
    }
  }
  if ('download' in document.createElement('a')) {
    const downloadElement = document.createElement('a')
    let href = ''
    if (window.URL) {
      href = window.URL.createObjectURL(blob)
    } else {
      href = window.webkitURL.createObjectURL(blob)
    }
    downloadElement.href = href
    downloadElement.download = filename
    document.body.appendChild(downloadElement)
    downloadElement.click()
    if (window.URL) {
      window.URL.revokeObjectURL(href)
    } else {
      window.webkitURL.revokeObjectURL(href)
    }
    document.body.removeChild(downloadElement)
  } else {
    navigator.msSaveBlob(blob, filename)
  }
}
/**
 *
 * @param {*} dataurl 文件路径，前缀为data:协议的URL
 * @param {*} filename 文件名
 * @returns js文件对象
 */
export function dataURLtoFile(dataurl, filename) {
  const arr = dataurl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)

  // eslint-disable-next-line no-plusplus
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }

  return new File([u8arr], filename, { type: mime })
}

export function isImage(filename) {
  const IMAGE_LIST = ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'svg']
  if (filename) {
    let ext = filename.substr(filename.lastIndexOf('.') + 1)
    return IMAGE_LIST.indexOf(ext.toLowerCase()) !== -1
  } else {
    return false
  }
}

/**
 *  下载文件
 * @param {*} fileToken 文件id
 * @param {*} type 文件类型 https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Basics_of_HTTP/MIME_types
 */
export function downloadFile(fileToken, type) {
  downloadImage(fileToken).then((res) => {
    if (res) {
      const blob = new Blob([res.data], {
        type,
      })
      const downloadElement = document.createElement('a')
      let href = ''
      if (window.URL) {
        href = window.URL.createObjectURL(blob)
      } else {
        href = window.webkitURL.createObjectURL(blob)
      }
      downloadElement.href = href
      // decodeURIComponent解决中文文件名乱码
      downloadElement.download = decodeURIComponent(
        res?.headers?.['content-disposition'].split('=')[1] || 'filename'
      )
      document.body.appendChild(downloadElement)
      downloadElement.click()
      if (window.URL) {
        window.URL.revokeObjectURL(href)
      } else {
        window.webkitURL.revokeObjectURL(href)
      }
      document.body.removeChild(downloadElement)
    }
  })
}

/**
 *
 * @param {*} fileSize 文件大小，默认为字节
 * @param {*} decimals 保留几位小数精度
 * @returns 对应单位的文件大小
 */
export function calcFileSize(fileSize, decimals = 2) {
  // 文件大小非字符或小于0
  if (Number.isNaN(Number(fileSize)) || Number(fileSize) <= 0) {
    return 0
  }

  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']

  let i = 0
  let originalSize = Number(fileSize)

  for (i; originalSize > 1024; i++) {
    originalSize /= 1024
  }

  return parseFloat(originalSize.toFixed(decimals)) + units[i]
}

// export default {
//   dataURLtoFile,
//   calcFileSize,
//   isImage,
// }
