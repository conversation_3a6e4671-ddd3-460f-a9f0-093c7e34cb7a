/*
 * @Author: <EMAIL>
 * @Date: 2019-11-28 10:42:58
 * @LastEditors: ye_xf
 * @LastEditTime: 2022-08-10 15:19:05
 * @Description:  全局配置入口
 */
import FawkesMobileLib from 'fawkes-mobile-lib'//移动端组件库
import 'fawkes-mobile-lib/lib/index.css'
import { SvgIcon, ModuleView } from '@/components'
import CommonTitle from "@components/CommonTitle/index.vue";


import { nodePermission, redirectTo } from '@/permission'
import Directives from '@/directives'
import Filters from '@/filters'
import Decorators from '@/decorators'

import 'amfe-flexible'//自动将px转化为em，自适应移动端
import request from '@/utils/request' //axios封装
import storage from '../storage'

// 添加normalize.css 为默认的HTML元素样式上提供了跨浏览器的高度一致性
import 'normalize.css/normalize.css'
import '@/styles/index.less' // 引入全局样式
import Loading from '@/components/loading/loading'
import common from '@/common/common.js'
import dayjs from 'dayjs'
import router from '@/router'

Window.prototype.ModuleView = ModuleView
const plugin = {}
plugin.install = function (Vue) {
  //   Vue.mixin(mixin)



  //   !Vue.prototype.$InQianKun && import('fawkes-lib/lib/theme-chalk/index.css').then(() => {
  //     themeInit()
  //   })



  Vue.component('svg-icon', SvgIcon) //在线svg组件注册，本地模块可改为离线svg组件
  Vue.component('common-title', CommonTitle)
  //   Vue.component('excel-export', ExcelExport)

  //svg载入

  const req = require.context('@/assets/svg', true, /\.svg$/)
  const requireAll = (requireContext) =>
    requireContext.keys().map(requireContext)
  requireAll(req)

  Vue.use(Directives)
  Vue.use(Filters)
  Vue.use(Decorators)

  //   Vue.prototype.$FksWebsocket = FksWebsocket
  Vue.prototype.$axios = request
  Vue.prototype.nodePermission = nodePermission
  Vue.prototype.redirectTo = redirectTo

  Vue.prototype.$dayjs = dayjs
  Vue.prototype.$storage = storage

  //组件库
  Vue.use(FawkesMobileLib)

  // 全局返回处理函数，默认返回路由上一级，为处理设备返回键
  Vue.prototype.$back = function () {
    router.go(-1)
  }

  Vue.use(Loading)
  Vue.use(common)
}
export default plugin
