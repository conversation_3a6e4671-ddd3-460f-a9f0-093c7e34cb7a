import request from '@/utils/request';

// 保存草稿
export function saveDraft(userId, businessKey, draftContext) {
  const data = { userId, businessKey, draftContext };
  return request({
    url: '/vehicle-dispatch/draft/save',
    method: 'post',
    data
  });
}

// 获取草稿
export function getDraft(userId, businessKey) {
  const data = { userId, businessKey };
  return request({
    url: '/vehicle-dispatch/draft/get',
    method: 'post',
    data
  });
}

// 删除草稿
export function deleteDraft(userId, businessKey) {
  saveDraftDebounced.cancel();
  const data = { userId, businessKey };
  return request({
    url: '/vehicle-dispatch/draft/del',
    method: 'post',
    data
  });
}


// 保存草稿
export function tableConfigSave(userId, businessKey, draftContext) {
  const data = { userId, businessKey, draftContext };
  return request({
    url: '/vehicle-dispatch/tableConfig/save',
    method: 'post',
    data
  });
}

// 获取草稿
export function tableConfigGet(userId, businessKey) {
  const data = { userId, businessKey };
  return request({
    url: '/vehicle-dispatch/tableConfig/get',
    method: 'post',
    data
  });
}

// 删除草稿
export function tableConfigDel(userId, businessKey) {
  const data = { userId, businessKey };
  return request({
    url: '/vehicle-dispatch/tableConfig/del',
    method: 'post',
    data
  });
}


function debounce(func, delay, callback) {
  let timer;


  const debouncedFn = function(...args) {
    return new Promise((resolve, reject) => {
      if (timer) clearTimeout(timer);

      timer = setTimeout(() => {
        try {
          const result = func.apply(this, args);
          if (callback) callback();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, delay);
    });
  };

  debouncedFn.cancel = function() {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };
  return debouncedFn;
}

// 防抖包装保存草稿
export const saveDraftDebounced = debounce(saveDraft, 3000);

export const saveTableConfigDebounced = debounce(tableConfigSave, 1000);

export const DRIVER = "DRAFT_DRIVER_BATCH_SAVE"
export const CAR = "DRAFT_CAR_BATCH_SAVE"
export const PROJECT = "DRAFT_PROJECT_BATCH_SAVE"
export const FORM = "DRAFT_FORM_SAVE"
