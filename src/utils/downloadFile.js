const download = (file, blobs) => {
  let blob = new File([blobs], { type: 'application/octet-stream' });
  if ('download' in document.createElement('a')) {
    const downloadElement = document.createElement('a');
    let href = '';
    if (window.URL) {
      href = window.URL.createObjectURL(blob);
    } else {
      href = window.webkitURL.createObjectURL(blob);
    }
    downloadElement.href = href;
    downloadElement.download = file.fileName || file.name;
    document.body.appendChild(downloadElement);
    downloadElement.click();
    if (window.URL) {
      window.URL.revokeObjectURL(href);
    } else {
      window.webkitURL.revokeObjectURL(href);
    }
    document.body.removeChild(downloadElement);
    URL.revokeObjectURL(href); // 释放 Blob
  } else {
    navigator.msSaveBlob(blob, file.fileName || file.name);
  }
};

export default download;
