import request from '@/utils/request';

// 保存草稿
export function queryTips(projectId, tipPlace) {
  const data = { projectId, tipPlace };
  return request({
    url: '/vehicle-dispatch/tips/query',
    method: 'get',
    params: data
  });
}

export const tipsMap = {
  dispatchTitle: "TIP1",
  dispatchTime:  "TIP2",
  addCar:        "TIP3",
  addDriver:     "TIP4",
  carLc:         "TIP5",
  carAge:        "TIP6",
}
