// import wx from 'weixin-js-sdk'
// import { systemSetting } from '@/settings/index'
import { Toast } from 'fawkes-mobile-lib'
import dayjs from 'dayjs'
import store from '@/store/index.js';
import { getSign } from '@/utils/request/sign'
import { getWxSign } from '../api/wx'
const systemSetting = {
  // 腾讯地图Key
  txKey: 'xxxxxxxx',
  // 跳转App公共地址
  mapUrlConfig: {
    baidu: 'baidumap://map/walknavi',
    gaode: {
      ios: 'iosamap://navi',
      android: 'androidamap://viewMap'
    },
    tengxun: 'qqmap://map/marker'
  }
}

/**
 * 跳转第三方地图 微信
 * @param addressInfo 地址信息
 */
export const navigationWx = (addressInfo) => {
  const { lat, lng, name, address } = addressInfo
  const href = window.location.href.split('#')[0]
  const url = encodeURIComponent(href);
  store.dispatch('WeChat/getWxSign', url).then(res => {
    if (!res) {
      return false;
    }
    const { data } = res;
    console.log('====data=======', data, res)
    wx.config({
      debug: true, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: data.appId, // 必填，公众号的唯一标识
      timestamp: data.timestamp, // 必填，生成签名的时间戳
      nonceStr: data.nonceStr, // 必填，生成签名的随机串
      signature: data.signature,// 必填，签名
      // jsApiList: ['openLocation'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
      jsApiList: ['checkJsApi', 'getLocation', 'openLocation'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
    });
    wx.ready(function () {
      checkJsApi(addressInfo)
      // openLocation(addressInfo)
    })
  });
  wx.error(function(res) {
    console.log('通过error接口处理失败验证',res);
    // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
  });
}
function checkJsApi(addressInfo) {
  wx.checkJsApi({
    jsApiList: ["getLocation", "openLocation"], // 需要检测的JS接口列表，所有JS接口列表见附录2,
    success: function(res) {
      console.log('checkJsApi success====', res)
      getLocation(addressInfo);
    },
    error(err) {
      console.log('checkJsApi error====', err)
    }
  });
}

function getLocation(addressInfo) {
  wx.getLocation({
    type: "gcj02", // 默认为wgs84的 gps 坐标，如果要返回直接给 openLocation 用的火星坐标，可传入'gcj02'
    success: function (res) {
      // 以键值对的形式返回，可用的api值true，不可用为false
      // 如：{"checkResult":{"chooseImage":true},"errMsg":"checkJsApi:ok"}
      console.log('getLocation success====', res)
      openLocation(addressInfo)
    },
    error(err) {
      console.log('getLocation error====', err)
    }
  });
}

function openLocation(addressInfo) {
  const { lat, lng, name, address } = addressInfo
  wx.openLocation({
    type: "gcj02",
    latitude: Number(lat),// 纬度，浮点数，范围为90 ~ -90
    longitude: Number(lng),// 经度，浮点数，范围为180 ~ -180。
    name: name, // 位置名
    scale: 18,// 地图缩放级别,整形值,范围从1~28。默认为最大
    address: address || '', // 地址详情说明
    success:function(res){
      console.log('openLocation success====', res)
    },
    fail:function(res){
      console.log('openLocation 失败',res)
    }
  });
}
/**
 * 打开高德或百度地图
 * @param {*} latitude
 * @param {*} longitude
 * @param {*} name  导航目的地名称
 * @param {*} type 1 百度地图 2 高德地图
 */
export const navToMap = (addressInfo, type) => {
  const { lat, lng, name, address } = addressInfo
  const addressStr = address || ''
  let url
  const u = navigator.userAgent
  //判断ios
  const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
  //判断Android
  const isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1
  if (!isIOS && !isAndroid) {
    return Toast("此设备暂不支持")
  }
  if (type === 1) {
    // 百度
    //网页应用调起iOS百度地图方式举例
    // <a href="baidumap://map/show?zoom=18&center=39.989956,116.323066&src=ios.baidu.openAPIdemo">地图主图区</a>
    url = `${systemSetting.mapUrlConfig.baidu}?destination=${lat},${lng}&title=${name}&content=${addressStr}&output=html&coord_type=gcj02&src=ios.baidu.openAPIdemo`
  } else if (type === 3) {
    url = `${systemSetting.mapUrlConfig.tengxun}?marker=coord:${lat},${lng};title:${name};addr:${addressStr}&referer=${systemSetting.txKey}`
  } else {
    // 高德
    // let params = `?sourceApplication=amap&dname=${name}&dlat=${lat}&dlon=${lng}&dev=0&t=2`
    let params = `?sourceApplication=amap&poiname=${name}&lat=${lat}&lon=${lng}&dev=0&t=2`
    const { android, ios } = systemSetting.mapUrlConfig.gaode
    url = (isAndroid ? android : ios) + params
  }
  if (url) {
    window.location.href = url
  }
}

// 判断微信环境
export const isWx = () => {
  var ua = navigator.userAgent.toLowerCase();
  if (/micromessenger/.test(ua)) {
    return new Promise(resolve => {
      wx.miniProgram.getEnv(function (res) {
        if (res.miniprogram) {
          // 小程序种
          resolve("mini-wx");
        } else {
          // 微信环境种
          resolve("wx");
        }
      });
    });
  } else {
    // 不在微信种
    return new Promise(resolve => {
      resolve("no-wx");
    });
  }
}
