/*
 * @Author: zhong_m
 * @Date: 2021-12-06 11:32:17
 * @LastEditTime: 2022-08-08 14:24:38
 * @LastEditors: Please set LastEditors
 * @Description: 通用配置默认值，项目运行请通过系统配置修改
 * @FilePath: \central-system\src\config\app\general.js
 */

module.exports = {
  //主题与布局
  /**
   * @type {number} side | top | mix
   * @description Whether show the navigation on the top
   */
  topMenu: 0,
  theme: '#3C83FF',
  /**
   * @type {number} tag | breadcrumb | none
   * @description Whether need tagsView
   */
  tagsView: 0,
  /**
   * @type {boolean} true | false
   * @description Whether need stripe
   */
  stripe: false,

  //站点信息
  title: '凤翎平台',
  logo: './static/img/logo.png',
  logoIconOffset: 0, // logo图标居中时的偏移量，单位px
  websiteLogo: './favicon.ico',
  footerView: true,
  copyright: 'copyright@fawkes 2021',

  //水印
  watermarkPosi: 8,
  pageWatermark: false,
  imgWatermark: false,

  //语言与时区
  language: 'zh-CN',
  timezone: '(UTC+08:00) 北京',
  dateFormat: '',
  // 门户
  projectPortalDefaultRoute: '/projectCar/projectPortal/projectIndex', // pc端项目门户
  companyPortalDefaultRoute: '/projectCar/companyPortal/companyIndex', // pc端公司门户
  mobileCompanyPortalDefaultRoute: '/projectCar/companyPortal/companyIndex', // 移动端公司门户
  mobileProjectPortalDefaultRoute: '/projectCar/projectPortal/carRecord', // 移动端项目门户
}
