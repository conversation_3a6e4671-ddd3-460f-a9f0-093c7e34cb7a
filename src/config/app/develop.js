/*
 * @Author: zhong_m
 * @Date: 2021-12-06 11:33:20
 * @LastEditTime: 2022-08-30 14:18:47
 * @LastEditors: Please set LastEditors
 * @Description: 开发配置，用于开发调试
 * @FilePath: \central-system\src\config\app\develop.js
 */

module.exports = {

  appName: 'XMB_CAR',//应用名称，会影响缓存前缀;子应用模式下会影响路由加载
  CLIENT: 'fawkes', // 应用公钥
  CLIENT_SECRET: 'fawkes_secret', // 应用私钥
  PORTAL_ID: '1302785644422082562', //默认一级门户id
  custom_flow_prefix: 'fawkes_custom_flow_', // 自定义流程前缀
  baseProxy: 'api', // 接口代理节点
  /**
   * @type {boolean} true | false
   * @description 是否加载本地 路由|权限
   */
  localRoute: false,

  /**
  * @type {boolean} true | false
  * @description 是否开启埋点
  */
  fawkesAnalysis: false,

  /**
   * @type {string}
   * @description 密码加密方式:MD5、SM4、SM2
   */
  pwdEncrypType: 'SM4',

  /**
   * @type {boolean} true | false
   * @description 是否开启多门户
   */
  multiPortal: true,

  /**
   * @type {Number}
   * @description 校验服务器时间戳间隔
   */
  tsDvalue: 180000,

  /**
   * @type {Number}
   * @description 签名有效期，最短为180
   */
  TTL: 180,

  //代理设置,开发环境默认baseUrl为/api，如需代理至其它地址，请在默认代理前设置路径
  //修改后需重启项目
  proxy: {
    // '/api/xxx': {
    //   target: `http://****/`,
    //   changeOrigin: true,
    //   pathRewrite: {
    //     '/api': ''
    //   },
    '/api/feishu': {
      target: 'https://open.feishu.cn/',
      changeOrigin: true,
      pathRewrite: {
        '/api/feishu': ''
      }
    },
    '/weixin': {
      target: 'https://api.weixin.qq.com/',
      changeOrigin: true,
      pathRewrite: {
        "/weixin": ""
      }
    },
    '/cbe': {
      target: 'http://**************:5566/',
      changeOrigin: true,
      pathRewrite: {
        "/cbe": ""
      }
    },
    // '/': {
    //   target: 'http://***********:9630', // 日超
    //   ws: false, // 代理的WebSockets
    //   changeOrigin: true, // 需要虚拟主机站点
    //   pathRewrite: {
    //     '^/': '/'
    //   }
    // },
    '/api/sign': {
      // target: 'http://***********:9673/api/sign/',
      target: 'https://daminproxy.hdec.com/xmb/api/sign/',
      changeOrigin: true,
      pathRewrite: {
        '/api/sign': '',
      },
    },
    '/api': {
      // 移动端快速打包平台后端服务
      // target: 'http://***********:9673/api/',
      target: 'https://daminproxy.hdec.com/xmb/api/',
      changeOrigin: true,
      pathRewrite: {
        '/api': '',
      },
      logLevel: 'debug',
    },
    '/filePreview': {
      target: 'https://apigateway.hdec.com/fawkes-new/staging/filePreview',
      changeOrigin: true,
      pathRewrite: {
        '/filePreview': ''
      }
    },
    '/sub_app_wp/': {
      target: 'https://apigateway.hdec.com/fawkes-new/staging/sp',
      changeOrigin: true,
      pathRewrite: {
        '/sub_app_wp': ''
      }
    }
  }
}
