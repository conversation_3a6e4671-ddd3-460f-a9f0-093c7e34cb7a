/*
 * @Author: xie_sm
 * @Date: 2022-03-10 16:03:01
 * @LastEditors: xie_sm
 * @LastEditTime: 2022-05-10 16:45:55
 * @FilePath: \mobile-template\src\common\common.js
 * @Description:
 *
 */
import store from '../store'
import { ENUM } from '@/store/State/stateTypes'
import { debounce } from '@/utils/util'

export default {
  install: function (Vue) {
    Vue.filter('transferEnum', function (value, enumName) {
      if (!value) {
        return
      }
      if (typeof value !== 'string') {
        value = value.toString()
      }
      if (!store.state[ENUM][enumName]) {
        return value
      }
      let obj = store.state[ENUM][enumName].find((i) => {
        return i.code == value
      })
      if (!obj) {
        return value
      }
      return obj['zh-CN']
      // return obj[getLangConfig()]
    })
    Vue.filter('transferEnums', function (value, enumName) {
      const flag = +value === 0 ? false : !value;
      if (flag) {
        return
      }
      // if (typeof value !== 'string') {
      //   value = value.toString()
      // }
      if (!store.state.CarApply.enums[enumName]) {
        return value
      }
      let obj = store.state.CarApply.enums[enumName].find((i) => {
        return +i.key == +value
      })
      if (!obj) {
        return value
      }
      return obj.value
      // return obj[getLangConfig()]
    })
    Vue.directive('debounce', {
      bind(el, { value }, vnode) {
        const [target, timeout, immediate] = value
        const debounced = debounce(target, timeout, immediate, vnode)
        el.addEventListener('click', debounced)
        el._debounced = debounced
      },
      destroy(el) {
        el.removeEventListener('click', el._debounced)
      },
    })
  },
}
