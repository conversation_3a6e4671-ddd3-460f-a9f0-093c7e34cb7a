import { throttle } from 'throttle-debounce'; // 实现下拉加载更多的指令
// 实现下拉加载更多的指令

export default {
  bind(el, binding) {
    const className = '.fks-select-dropdown__wrap.fks-scrollbar__wrap';
    const wrapper = el.querySelector(className);
    wrapper.addEventListener(
      'scroll',
      throttle(500, function () {
        const condition = this.scrollHeight - Math.ceil(this.scrollTop) <= this.clientHeight;
        if (condition && this.scrollTop !== 0) {
          binding.value();
        }
      })
    );
  }
};
