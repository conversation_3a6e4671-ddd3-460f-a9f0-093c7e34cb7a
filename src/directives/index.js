/*
 * @Author: <EMAIL>
 * @Date: 2021-11-09 10:32:07
 * @LastEditors: zhong_m
 * @LastEditTime: 2021-12-29 09:53:24
 * @Description: 自定义指令注册
 */
import loadmore from './loadmore'
import loadMoreSelect from "@/directives/loadMoreSelect";

// 自定义指令
const directives = {
  loadmore,
  loadMoreSelect
}

export default {
  install (Vue) {
    Object.keys(directives).forEach((key) => {
      Vue.directive(key, directives[key])
    })
  },
}
