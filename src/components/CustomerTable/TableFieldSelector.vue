<template>
  <fks-popover
    ref="popper"
    placement="bottom-end"
    trigger="click"
    width="250"
    popper-class="customer-popover"
    @hide="onHide"
    @show="onShow"
  >
    <!-- 插槽：自定义内容 -->
    <slot name="custom" slot="reference" v-if="$slots.custom" />
    <!-- 默认按钮（如果没有自定义插槽时显示） -->

    <div  v-else slot="reference"  class="table-btn fks-icon-system-config" size="small" text
          :style="markBtn ? 'background-color: #FEF6DD' : ''">
      字段显示
    </div>
    <div class="field-config-container">
      <div class="filter-header">字段配置</div>
      <main>
        <div ref="sortableContainer" class="sortable-container">
          <div
            v-for="(field, index) in sortedTableConfig"
            :key="`${field.field}-${index}`"
            class="field-item"
            :class="field.classText"
          >
            <!-- 整行都可以拖拽 -->
            <div class="drag-handle fks-icon-drag"></div>

            <span class="field-label">{{ field.label }}</span>

            <div
              class="field-visibility"
              @click="toggleFieldVisibility(field)"
            >
              <img
                :src="field.visible ? require('@/assets/img/fieldFilter/show.svg') : require('@/assets/img/fieldFilter/hide.svg')"
                alt="Field Visibility"
                class="visibility-icon"
              />
            </div>
          </div>
        </div>
        <!-- 底部固定的按钮区域 -->
        <div class="reset-btn-container">
          <fks-button class="sub-btn" @click="resetSettings">重置</fks-button>
        </div>
      </main>
    </div>

  </fks-popover>

</template>
<script>
import NewPersonSelector from "@components/PersonSelector/main.vue"
import {mapActions, mapMutations, mapState} from "vuex";
import Sortable from 'sortablejs'

export default {
  name: 'TableFieldConfig',
  components: {NewPersonSelector},
  props: {
    groupedFields: {
      type: Object,
    },
    selectedFields: {
      type: Array
    },
    sortedTableConfig: {
      type: Array,
    },
    oldFields: {
      type: Array
    },
    markBtn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isCleared: false,
      isChanged: false,
      value1: [],
      newList: [],
    }
  },
  watch: {
  },
  created() {
  },
  mounted() {
    this.columnDrop();
  },
  methods: {
    resetSettings() {
      const newSortedList = this.oldFields.map(oldItem => {
        return this.newList.find(newItem => newItem.field === oldItem);
      }).filter(Boolean);
      newSortedList.forEach(item => item.visible = true)
      this.newList = newSortedList;
      this.$emit("update-sort", this.newList);
      this.$emit("reset-field")
    },
    // 控制字段的显示或隐藏
    toggleFieldVisibility(fieldProp) {
      this.$set(fieldProp, 'visible', !fieldProp.visible);
      let list = this.sortedTableConfig.filter(item => item.visible).map(item => item.field);
      this.$emit('update-selected', list);
    },
    columnDrop() {
      let state = this;
      const container = this.$refs.sortableContainer;  // 获取整个容器

      // 初始化 Sortable
      Sortable.create(container, {
        animation: 180, // 拖拽动画时间
        delay: 0, // 拖动延迟
        filter: ".filter",
        onStart: evt => {
          state.saveTableConfigFlag = true;
        },
        onEnd: evt => {
          // 获取拖动的元素的旧位置和新位置
          let oldIndex = evt.oldIndex; // 旧位置
          let newIndex = evt.newIndex; // 新位置

          if (oldIndex === newIndex) {
            return; // 如果位置未发生改变，则无需更新
          }

          // 使用结构化的方式更新数组，确保 Vue 能检测到数据变化
          const newList = [...state.newList];
          const movedItem = newList.splice(oldIndex, 1)[0]; // 从旧位置移除
          newList.splice(newIndex, 0, movedItem); // 插入到新位置

          // 更新 state.newList 的引用
          state.newList = newList;

          // 触发更新事件
          state.$emit("update-sort", state.newList);
        }
      });

    },

    onHide() {
      if (this.isCleared) return;
      if (this.isChanged && this.filters.some(filter => filter.prop)) {
        this.$emit('query', true);
      }
    },
    onShow() {
      this.isCleared = false;
      this.isChanged = false;
      this.newList = [...this.sortedTableConfig];
    },
  }
}
</script>

<style lang="less" scoped>
@import "exclude/fieldSelector.css";
.filter-container {

  header {
    margin-bottom: 20px;
    color: #cccccc;
  }

  .field-group {
    margin-bottom: 20px; /* 每个分组底部的间隔 */
  }

  .field-header {
    font-weight: bold;
    font-size: 24px;
    margin-bottom: 10px; /* 标题和内容之间的间距 */
  }

  .checkbox-group {
    width: 100% !important;
    display: flex;
    justify-content: flex-start;
    align-content: center;
    flex-wrap: wrap; /* 允许换行 */
  }

  .checkbox-item {
    flex-basis: calc(30%); /* 每个选择框占1/3的宽度，减去单边的间距 */
    box-sizing: border-box;
    margin-right: 0 !important;
  }
}

/deep/ .fks-date-editor--daterange.fks-input__inner {
  width: 100%;
}

.reset-btn-container {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  box-sizing: border-box;
}
</style>
