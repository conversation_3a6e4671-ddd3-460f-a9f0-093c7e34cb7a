<template>
  <fks-select
    v-model="localSelected"
    multiple
    filterable
    collapseTags
    :placeholder="'请选择项目'"
    class="custom-select full-width"
    @change="handleSelectChange"
    v-load-more-select="searchMore"
    :filter-method="handleQueryChange"
    @visible-change="handleVisibleChange"
  >
    <fks-option
      v-for="option in localOptions"
      :key="option.id"
      :label="option.projectName"
      :value="option.projectName"
    ></fks-option>

    <div v-if="loading" class="flex col-center row-center">
      <i class="fks-icon-loading" />
    </div>
    <fks-option
      v-if="finished"
      class="flex col-center row-center"
      disabled
      value=""
    >
      <span style="color: #8492a6; font-size: 12px; text-align: center">加载到底了</span>
    </fks-option>
  </fks-select>
</template>

<script>
import { saveDraft } from '@utils/draftUtil'
import { debounce } from '@utils/util'

export default {
  props: {
    value: {
      type: String,
      required: true,
    },
    options: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    finished: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      localSelected: [], // 当前选中的值
      localOptions: [],  // 排序后的选项列表
      debouncedSearch: null,
    };
  },
  watch: {
    // 深度监听 options 数组
    options: {
      deep: true,
      immediate: true, // 保证初始加载时也触发 handler
      handler(newOptions) {
        this.updateOptions();
      },
    },
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.localSelected = newVal.split(",");
          this.updateOptions();
        }
      },
    },
    localSelected: {
      immediate: true,
      handler() {
        this.updateOptions();
      },
    },
  },
  created() {
    // 防抖函数设置，延迟 1000ms 后触发
    this.debouncedSearch = debounce(() => {
      this.emitLoadMore();
    }, 1000);
  },
  methods: {
    emitLoadMore() {
      this.$emit("searchMore", 0, this.query)
    },
    resetQuery() {
      this.query = '';
      this.updateOptions();
    },
    handleVisibleChange(isVisible) {
      if (!isVisible) {
        this.resetQuery();
      }
    },
    searchMore() {
      if (this.finished) return;
      // 翻页查询，增加1页，query保持不变
      this.$emit("searchMore", 1, this.query)
    },
    // 处理搜索输入
    handleQueryChange(newValue) {
      if (newValue == null) {
        console.log("失去焦点")
      }
      this.query = newValue
      // 不翻页查询，按用户搜索结果来搜索
      if (newValue && newValue.length > 0)  {
        this.debouncedSearch();
      }
      this.updateOptions()
    },

    // 根据选中状态排序选项
    updateOptions() {
      const selectedSet = new Set(this.localSelected);
      const selected = this.options.filter((opt) =>
        selectedSet.has(opt.id)
      );
      let unselected = this.options.filter(
        (opt) => !selectedSet.has(opt.id)
      );
      if (this.query) {
        const queryLower = this.query.toLowerCase();
        unselected = unselected.filter(opt =>
          opt.projectName.toLowerCase().includes(queryLower)
        );
      }

      let list = [...selected, ...unselected];
      this.$set(this.$data, "localOptions", list);
    },
    // 触发选中值更新
    handleSelectChange() {
      this.$emit("input", this.localSelected); // 使用 v-model 双向绑定
    },
  },
};
</script>
