.table-component{
  padding: 0 20px;
  height: calc(100% - 66px);  /* 设置高度为 100% 减去 66px */
  /*overflow-y: hidden;*/
  /*overflow-x: hidden;*/
}

.table-component .fks-table__body-wrapper{
  background: #F8F9FA;
}


.table-btn {
  border-radius: 16px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #EEEEEE !important;
  cursor: pointer;
  padding: 4px 12px;
  text-align: center;
  font-size: 14px;

  font-weight: normal;
  line-height: normal;
  color: #333333;
}

.table-btn::before {
  position: relative;
  left: -4px;
}

.table-btn:hover {
  background: #e8e9e9 !important;
}

.table-header {
  display: flex;
  justify-content: flex-start; /* 从左侧开始排列 */
  align-items: center; /* 垂直居中对齐 */
  gap: 10px; /* 设置子元素之间的间隔为 10px */
  margin-bottom: 16px;

}

.fks-pagination__sizes {
  position: relative;
  top: -2px;
}

.fks-table--medium {
  border-radius: 8px;
  border: 1px solid #EEEEEE;
}

/* 使文本内容自动换行 */
.text-ellipsis {
  white-space: nowrap;  /* 防止文本换行，默认值 */
  overflow: hidden;     /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出文本部分显示省略号 */
}

/* 支持长文本换行，并处理溢出情况 */
.text-ellipsis.wrap {
  white-space: normal;  /* 允许文本换行 */
  word-wrap: break-word; /* 在必要时进行单词换行 */
  word-break: break-word; /* 在单词边界强制换行 */
  overflow-wrap: break-word; /* 支持较长的单词或者 URL 自动换行 */
}
