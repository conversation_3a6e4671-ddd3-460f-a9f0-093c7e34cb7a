
.field-config-container {
    width: 100%;
    max-height: 75vh; /* 设置最大高度为 80vh */
    display: flex;
    flex-direction: column;
    user-select: none;
}

.filter-header {
    padding: 10px;
    font-size: 16px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
    font-weight: normal;
    line-height: normal;
    color: #32363D;
}

main {
    flex: 1;
    overflow-y: auto;
}

.field-item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background 0.2s;
    padding: 10px;
    margin: 4px;
    border-radius: 4px;
}

.field-item:hover {
    background-color: #f9f9f9;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

/* 拖拽区域 */
.drag-handle {
    font-size: 14px;
    color: #999;
    margin-right: 10px;
    cursor: move;
}

/* 字段图标 */
.field-icon {
    font-size: 14px;
    color: #555;
    margin-right: 8px;
}

/* 字段标签 */
.field-label {
    flex: 1; /* 占据剩余空间 */
    font-size: 14px;
    color: #333;
}

/* 显示/隐藏按钮 */
.field-visibility {
    font-size: 14px;
    color: #999;
    cursor: pointer;
}


/* 隐藏 .field-visibility */
.filter .field-visibility {
    display: none; /* 隐藏 */
}

/* 设置 .filter 灰色背景和禁用鼠标 */
.filter {
    background-color: #F8F9FA; /* 灰色背景 */
    pointer-events: none; /* 禁用鼠标操作 */
    cursor: not-allowed; /* 禁用样式 */
}

.customer-popover {
    padding: 0 !important;
    border-radius: 6px;
}



.sub-btn {
    padding: 5px 10px;
    color: #333;
    border-color: #cccccc;
    transition: all 0.3s ease; /* 平滑过渡效果 */
}

.sub-btn:hover {
    color: #333;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.sub-btn:active {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
    transform: translateY(5px); /* 模拟点击下沉效果 */
}