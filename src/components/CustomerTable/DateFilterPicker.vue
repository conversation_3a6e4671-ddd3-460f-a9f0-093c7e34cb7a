<template>
  <div class="date-filter">
    <!-- 日期选择列表 -->
    <fks-select v-model="selectedOption"
                style="width: 100px;"
                @change="computedDate"
                placeholder="选择日期">
      <fks-option v-for="(item, index) in datePickerList"
                  :key="index"
                  :label="item.name"
                  :value="item.name" />
    </fks-select>

    <!-- 日期选择器，仅在选择 '具体日期' 时显示 -->
    <fks-date-picker
      v-if="selectedOption === '具体日期'"
      clearable
      style="width: 140px"
      type="date"
      v-model="selectedDate"
      value-format="yyyy-MM-dd"
      placeholder="yyyy-MM-dd"
      @change="computedDate"
    />
  </div>
</template>

<script>
export default {
  name: 'DateFilterPicker',
  props: {
    condition: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 日期选择列表
      datePickerList: [
        { name: "具体日期", dateDiff: 0, unit: "day", chooseTime: true }, // 具体日期，特殊处理
        { name: "今天", dateDiff: 0 , unit: 'day' }, // 今天
        { name: "明天", dateDiff: 1 , unit: 'day'  }, // 明天
        { name: "昨天", dateDiff: -1, unit: 'day' }, // 昨天
        { name: "本周", dateDiff: 0 , unit: 'week' }, // 本周
        { name: "上周", dateDiff: -1, unit: 'week' }, // 上周
        { name: "本月", dateDiff: 0 , unit: 'month' }, // 本月
        { name: "上月", dateDiff: -1, unit: 'month' } // 上月
      ],
      selectedOption: '具体日期', // 默认选择今天
      selectedDate: null, // '具体日期' 时选择的日期
    };
  },
  computed: {
  },
  methods: {

    computedDate() {
      const today = new Date();
      let date, endDate;

      // 根据 selectedOption 处理日期
      const selectedItem = this.datePickerList.find(item => item.name === this.selectedOption);
      const { dateDiff, unit } = selectedItem;

      if (selectedItem.chooseTime) {
        // 选择的是具体日期，直接使用用户选择的日期
        date = this.selectedDate || today;
        endDate = date;
      } else {
        // 处理日期差异：dateDiff 是一个对象，包含数值和时间单位（day、week、month）
        switch (unit) {
          case 'day':
            date = new Date(today.setDate(today.getDate() + dateDiff));
            endDate = date;
            break;
          case 'week':
            // 每周7天，计算偏移量
            const currentDay = today.getDay(); // 获取今天是星期几 (0-6, 0 表示星期日)
            const diffToMonday = (currentDay === 0 ? -6 : 1) - currentDay; // 计算从今天到周一的偏移量
            date = new Date(today.setDate(today.getDate() + diffToMonday + (dateDiff * 7))); // 设定为本周周一并偏移

            endDate = new Date(date);
            endDate.setDate(date.getDate() + 6); // 加6天，得到周日
            break;
          case 'month':
            // 月份的偏移，设定为本月的第一天
            date = new Date(today.setMonth(today.getMonth() + dateDiff)); // 设置月份
            date.setDate(1); // 设置为本月的第一天

            // 获取本月的最后一天
            endDate = new Date(date);
            endDate.setMonth(endDate.getMonth() + 1); // 进入下个月
            endDate.setDate(0); // 设置为下个月的0号，得到上个月的最后一天
            break;
          default:
            date = today; // 如果没有匹配的单位，默认使用当前日期
            break;
        }
      }


      const startTime = new Date(date); // 开始时间，设置为00:00:00
      startTime.setHours(0, 0, 0, 0); // 设置为当天的00:00:00

      // 结束时间设定
      const endTime = new Date(endDate || date); // 使用endDate，若没有则使用date
      endTime.setHours(23, 59, 59, 999); // 设置为当天的23:59:59

      // 格式化日期并触发更新事件
      const dateResult = this.formatDate(startTime);
      const endDateResult = this.formatDate(endTime);
      this.$emit('chooseTime', this.condition, dateResult, endDateResult);
    },
    // 格式化日期
    formatDate(date) {
      // 如果 date 不是 Date 对象，强制转换为 Date
      if (!(date instanceof Date)) {
        date = new Date(date);
      }

      const yyyy = date.getFullYear();
      const mm = (date.getMonth() + 1).toString().padStart(2, '0');
      const dd = date.getDate().toString().padStart(2, '0');
      const hh = date.getHours().toString().padStart(2, '0');
      const min = date.getMinutes().toString().padStart(2, '0');
      const ss = date.getSeconds().toString().padStart(2, '0');

      return `${yyyy}-${mm}-${dd} ${hh}:${min}:${ss}`;
    }
  }
};
</script>

<style>
.date-filter {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

</style>
