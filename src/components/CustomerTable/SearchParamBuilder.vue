<template>
  <fks-dialog :visible="visible" width="600px" :before-close="beforeClose"
              custom-class="search-param-dialog">
    <template #title>
      <div class="search-bar-title" style="font-size: 16px; padding-left: 4px">
        <div>筛选</div>
      </div>
    </template>

    <div class="search-bar-container" style="font-size: 14px; padding: 20px 0">
      <div class="search-bar-header" style="padding: 0 20px; margin-bottom: 8px">
        <div>设置筛选条件</div>
      </div>

      <div class="search-bar-body" style="padding: 0 20px">
        <div
          v-for="(condition, index) in userConditions"
          :key="index"
          style="margin: 8px 0"
          class="search-bar-item"
        >
          <fks-select
            v-model="condition.field"
            :id="'field-' + index"
            class="item-field"
            style="width: 140px; height: 32px"
            @change="updateOperators(condition, index)"
          >
            <fks-option
              v-for="(item, i) in options"
              :key="i"
              :label="item.fieldName"
              :value="item.field"
            />
          </fks-select>

          <div class="item-operator">
            <fks-select
              v-model="condition.operator"
              :id="'operator-' + index"
              style="width: 80px; height: 32px"
              :disabled="!condition.field"
              placeholder=""
            >
              <fks-option
                v-for="(op, i) in condition.operators"
                :key="i"
                :label="op.operatorName"
                :value="op.operator"
              />
            </fks-select>

            <div style="width: 250px; height: 32px; line-height: 32px">

              <ProjectSelect
                v-if="condition.field === 'projectName' && condition.operator === 'IN'"
                ref="userSelect"
                :options="projectList"
                :value="condition.value"
                :finished="pageFinish"
                :loading="projectSearchLoading"
                @input="($event) => handleUserChange(condition, $event)"
                @searchMore="loadMoreData"
              />

              <DateFilterPicker
                @chooseTime="chooseTime"
                :condition.sync="condition"
                v-else-if="condition.fieldType === 'Date'"
              />

              <template v-else-if="condition.fieldEnum && condition.fieldEnum.length > 0">
                <fks-select
                  v-model="condition.value"
                  :id="'operator-' + index"
                  multiple
                  collapseTags
                  style="width: 250px; height: 32px"
                  placeholder=""
                >
                  <fks-option
                    v-for="(op, i) in condition.fieldEnum"
                    :key="i"
                    :label="op.value"
                    :value="op.key"
                  />
                </fks-select>
              </template>
              <fks-input
                v-else
                v-model="condition.value"
                placeholder="请输入要搜索的值"
                :type="getInputType(condition.field)"
              />
            </div>

            <fks-button
              style="color: #333"
              class="fks-icon-close"
              @click="removeCondition(index)"
              text
            ></fks-button>
          </div>
        </div>
      </div>
      <div class="search-bar-footer" style="padding: 0 8px">
        <fks-button style="color: #333" class="fks-icon-plus" @click="addItem" text
          >新增</fks-button
        >
        <fks-button style="color: #333" class="fks-icon-check" @click="confirmSearch" text
          >确定</fks-button
        >
      </div>
    </div>

    <template slot="footer"> </template>
  </fks-dialog>
</template>
<script>
import DateFilterPicker from '@components/CustomerTable/DateFilterPicker.vue'
import ProjectSelect from '@components/CustomerTable/ProjectSelect.vue'
import UserSelect from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/UserSelect.vue'
import { getProjectList, getUserList } from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'

export default {
  name: 'SearchParamBuilder',
  components: {
    UserSelect,
    DateFilterPicker,
    ProjectSelect,
  },
  props: {
    options: {
      type: Array,
      default: null,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isCleared: false,
      isChanged: false,
      userConditions: [],
      queryParams: [],

      projectList: [],
      projectMap: {},
      projectPage: 1,
      projectSearchLoading: false,
      pageFinish: false,
    }
  },
  created() {
    let flag = false;
    this.options.forEach(item => {
      if (item.field === 'projectName') {
        flag = true;
      }
    })
    if (flag) {
      this.loadMoreData(0);
    }
  },
  methods: {
    loadDefaultItem() {
      this.options.forEach(item => {
        if (item.default) {
          let param = JSON.parse(JSON.stringify(item));

          let list = param.operators;
          list = list.filter(op => op.operatorDefault);
          // param.operators = list;
          param.operator = list[0].operator
          param.operatorName = list[0].operatorName
          if (param.fieldEnum) {
            param.fieldEnum = param.fieldEnum.map(fe => {
              fe.key = fe.key + "";
              return fe;
            })
            param.value = item.value.split(",");
          }
          this.userConditions.push(param);
        }
      })
      this.confirmSearch();
    },
    loadMoreData(page, query) {
      let param = {
        pageSize: 20,
        projectName: query
      }
      // 有分页就往后推进，没有代表只根据关键字搜索
      if (page > 0) {
        if (this.pageFinish) return;
        this.projectPage += page;
        param.pageNo = this.projectPage;
      } else {
        if (query && query.length < 1) return;
        param.pageNo = 1;
      }
      this.projectSearchLoading = true;
      getProjectList(param).then(response => {
        if (response.data.isLastPage) {
          this.pageFinish = true;
        }
        const newUsers = response.data.list;
        this.setProjects(newUsers);
        this.projectSearchLoading = false;
      })
    },

    setProjects(newUsers) {
      // 将新数据按 id 去重并更新 userMap
      newUsers.forEach(item => {
        if (!this.projectMap[item.id]) {
          this.$set(this.projectMap, item.id, item);
        }
      });
      const updatedUserList = Object.values(this.projectMap);
      this.$set(this.$data, "projectList", updatedUserList);
    },
    handleUserChange(row, event) {
      row.value = [...event].join(",");
    },
    chooseTime(condition, value, unit) {
      condition.value = value + "," + unit;
    },
    confirmSearch() {
      let list = JSON.parse(JSON.stringify(this.userConditions))
      if (list && list.length > 0) {
        list = list.filter((item) => {
          return item.field != null
        })
        list.forEach((item) => {
          item.operators = item.operators.filter((op) => op.operator === item.operator)
          if (item.field === 'projectName' && item.operators[0].operator === 'IN') {
            item.value = item.value.split(',').map(val => `'${val.trim()}'`).join(",");
          }
          if (item.fieldEnum && item.fieldEnum.length > 0) {
            item.value = item.value.join(",");
          }
        })
      }
      console.log("通知更新")
      this.$emit('confirmFilter', list)
      this.$emit('update:visible', false)
    },
    beforeClose() {
      this.$emit('update:visible', false)
    },
    addItem() {
      this.userConditions.push({
        field: null,
        fieldName: '',
        fieldType: '',
        operator: '',
        operators: '',
        fieldEnum: [],
        value: '',
      })
    },

    removeCondition(index) {
      this.userConditions.splice(index, 1)
    },
    updateOperators(value, index) {
      let filed = this.options.filter((item) => item.field === value.field)[0]
      let item = { ...filed }
      item.operator =
        filed.operators && filed.operators.length > 0 ? filed.operators[0].operator : ''
      this.$set(this.userConditions, index, item)
    },

    getInputType(field) {
      // 根据字段类型返回不同的输入框类型
      if (field === 'carNum') {
        return 'text' // 车牌号可能是字母和数字，使用文本框
      }
      return 'text' // 其他字段类型可以根据需求更改
    },
  },
}
</script>
<style lang="less">
@import '~@/styles/searchparam';
@import '~@/styles/button';
</style>
