<template>
  <div class="table-component" :id="'tableContainer-' + uniqueId" :style="{backgroundColor: backGroundColor}">
    <div class="table-header" :id="'tableHeader-' + uniqueId" style="user-select: none;" v-if="showButtons && showButtons.length > 0">
      <TableFieldSelector  v-if="showButtons.includes('filedConfig')"
                           :grouped-fields="groupedFields"
                           :selected-fields="selectedFields"
                           :sortedTableConfig="sortedTableConfig"
                           :oldFields="oldList"
                           @update-sort="updateSort"
                           @update-selected="updateShowFiled"
                           @reset-field="resetFieldConfig"
                           :mark-btn="markFilterBtn"

      />
      <SearchParamBuilder ref="searchParam" @confirmFilter="handleFilter" :visible.sync="filterVisible" :options="searchConfigs" />
      <div v-if="showButtons.includes('showActive')" class="table-btn" size="small" text>
        <fks-checkbox v-model="isShowActive" @change="hanldeChangeShow">显示未激活菜单</fks-checkbox>
      </div>
      <div v-if="showButtons.includes('filter')" class="table-btn fks-icon-filter" @click="showFilter" size="small" text
           :style="searchParamCount > 0 ? 'background-color: #D1DDFA' : ''">
        <span v-if="searchParamCount > 0" style="font-size: 13px">{{ searchParamCount }}</span>
        筛选
      </div>
      <div v-if="showButtons.includes('sort') && !disableMutation"  class="table-btn fks-icon-sort-down" @click="handleSort" size="small" text>排序</div>
      <div v-if="showButtons.includes('export') && getAuth('export')" class="table-btn fks-icon-share-outline" @click="exportData" size="small" text>导出</div>
      <div v-if="showButtons.includes('batchAdd') && !disableMutation && getAuth('batchAdd')" class="table-btn fks-icon-folder-add" @click="batchAdd" size="small" text>批量新增</div>
      <div v-if="showButtons.includes('add') && !disableMutation && getAuth('add')"  class="table-btn fks-icon-plus" @click="addItem" size="small" text>新增</div>

      <fks-popconfirm v-if="showButtons.includes('delete') && !disableMutation && getAuth('delete')" title="确认删除？" @onConfirm="deleteItem" class="m-l-10">
        <div
          slot="reference"
          class="table-btn fks-icon-delete"
          style="color: #FF4143;"
        >
          删除
        </div>
      </fks-popconfirm>
    </div>

    <div :id="'toolBar-' + uniqueId">
      <slot name="toolBar"></slot>
    </div>
    <!-- 表格 -->
    <div class="table-body draggable" :key="tableHeight" :style="{'height': tableHeight + 'px !important'}">
      <fks-table
        :header-cell-style="{
          background: '#FAFAFA',
          color: '#333333 !important',
          fontWeight: 'normal',
          fontSize: '14px',
        }"
        ref="table"
        key="customerTable"
        row-key="id"
        :show-summary="summaryMethod && dataList.length > 0"
        :sticky="true"
        :summary-method="summaryMethod"
        v-loading="loading"
        :cell-style="{ color: '#333333 !important' }"
        :data="dataList" style="width: 100%"
        :max-height="tableHeight"
        :empty-text="'暂无数据'"
        :header-cell-class-name="cellClass"
        @select-all="handleSelectAll"
        @select="handleRowSelect"
        @sort-change="hanldeSort"
        @row-click="chooseCurrRow"
        >

        <fks-table-column v-if="showSelection" type="selection" width="50" />
        <fks-table-column
          type="index"
          align="center"
          fixed="left"
          label="#"
          :width="summaryMethod ? 100 : 70"
        >
          <template slot-scope="scope">
            {{ scope.$index + (currentPage - 1) * pageSize + 1 }}
          </template>
        </fks-table-column>
        <fks-table-column
          v-for="(column, index) in filteredTableConfig"
          :key="column.prop + index"
          :label="column.label"
          v-if="column.visible"
          :prop="column.prop"
          :formatter="column.formatter"
          :fixed="column.fixed"
          :width="column.width"
          :cell-style="{ textAlign: 'left' }"
          :render-header="column.isRenderHeader? renderFn : null"
          :sortable="column.isSort ? 'custom' : false"
        >
          <!-- 如果是customer类型列，使用插槽 -->
          <template v-if="column.customer" v-slot="scope">
            <slot :name="`column-${column.prop}`" :scope="scope"></slot>
          </template>

          <template v-else v-slot="scope">
            <div
              class="text-ellipsis"
              :style="{
                maxWidth: column.width || '100%',
                cursor: column.clickable ? 'pointer' : 'auto',
                color: column.clickable ? '#409EFF' : '#333333',
              }"
              :title="formatColumnData(scope.row[column.prop], column)"
              @click="column.clickable ? handleColumnClick(scope.row, column.prop) : () => {}"
            >
              {{  LANG[scope.row[column.prop]] ? formatColumnData(LANG[scope.row[column.prop]], column) : formatColumnData(scope.row[column.prop], column) }}
          </div>
        </template>

        </fks-table-column>
      </fks-table>
    </div>
    <div v-if="showPage" class="full-width table-footer" :id="'pagination-' + uniqueId" style="display: flex; justify-content: flex-end; margin: 16px auto; user-select: none;" >
      <fks-pagination
        bordered
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        layout=" prev, pager, next, sizes, jumper"
        :total="total">
      </fks-pagination>
    </div>
  </div>
</template>
<script>
import { getAuth } from '@utils/buttonAuth'
import { mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import CompactedSearchBar from '@components/CompactedSearchBar/index.vue'
import TableFieldSelector from '@components/CustomerTable/TableFieldSelector.vue'
import SearchParamBuilder from '@components/CustomerTable/SearchParamBuilder.vue'
import EventBus from '@utils/eventBus'

import Sortable from 'sortablejs';
import { saveTableConfigDebounced, tableConfigDel, tableConfigGet } from '@utils/draftUtil'

export default {
  name: 'TempTable',
  components: {
    CompactedSearchBar, TableFieldSelector, SearchParamBuilder
  },
  props: {
    backGroundColor: {
      type: String,
      default: '#F8F9FA'
    },
    dataList: {
      type: Array,
      default: () => []
    },
    tableConfig: {
      type: Array,
      default: () => []
    },
    searchConfigs: {
      type: Array,
      default: () => []
    },
    currentPage: {
      type: Number,
      default: 1
    },
    total: {
      type: Number,
      default: 0
    },
    loading: {
      type: Boolean,
      default: false
    },
    showButtons: {
      type: Array,
      default: () => ['add', 'batchAdd', 'delete', 'export', 'filter', 'filedConfig']
    },
    selectedFields: {
      type: Array,
      default: () => []
    },
    batchSelectable: {
      type: Boolean,
      default: true
    },
    initPageSize: {
      type: Number,
      default: 10
    },
    pageSizes: {
      type: Array,
      require: false,
      default: () => [10, 15, 30, 50, 100]
    },
    // 是否显示表头复选框
    showSelection: {
      type: Boolean,
      default: true
    },
    summaryMethod: {
      type: Function
    },
    LANG: {
      type: Object,
      default:() => ({})
    },
    isTree: {
      type: Boolean,
      default: false
    },
    renderFn: {
      type: Function,
      default: () => {
        return Function;
      },
    },
    // 配置是否能够拖拽
    draggable: {
      type: Boolean,
      default: true
    },
    // 配置是否显示分页插件
    showPage: {
      type: Boolean,
      default: true
    },
    // 配置是否能通过点击选择对应行数据
    chooseRow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      keyword: '', // 用户输入的关键字
      oldList: [],
      newList: [],
      selectedRows: [],
      queryParams: {},
      pageSize: 10,
      tableHeight: 0,
      filterVisible: false,
      saveTableConfigFlag: false,
      sortedTableConfig: [],
      isShowActive: true,
      uniqueId: this.generateUniqueId(),
      prevSelection: [],
      searchParamCount: 0,
      // 是否标记过滤按钮的背景颜色
      markFilterBtn: false,
    }
  },
  computed: {
    ...mapState([StateTypes.TABLE_LOADING, StateTypes.IS_PROJECT_CLOSED]),
    disableMutation() {
      return this[StateTypes.IS_PROJECT_CLOSED]
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    // 根据 group 字段分组字段
    groupedFields() {
      return this.tableConfig.reduce((acc, field) => {
        const group = field.group || '其他';
        if (!acc[group]) acc[group] = [];
        acc[group].push(field);
        return acc;
      }, {});
    },
    filteredTableConfig() {
      return this.tableConfig.map(field => ({
        ...field,
        visible: this.selectedFields.includes(field.prop), // 设置 visible 属性
      }));
    }
  },
  watch: {
    // 监听 newList 变化，并根据 newList 更新 tableConfig
    newList: {
      handler(newList) {
        let list = this.newList
          .map(prop => {
            return this.tableConfig.find(item => item.prop === prop);
          });
        this.$emit("update:tableConfig", list)
        this.saveTableConfig();
        this.$forceUpdate()
      },
      deep: true, // 确保嵌套对象的变化也能被检测到
    },
    selectedFields: {
      handler(newSelected) {
        this.$nextTick(() => {
          // 2. 调用 doLayout 来重新计算表格布局
          if (this.$refs.table && typeof this.$refs.table.doLayout === 'function') {
            this.$refs.table.doLayout();
          }
        });
        this.saveTableConfig();
        this.markFilterBtn = this.tableConfig.length !== newSelected.length;
      },
      deep: true
    },
    tableHeight(newHeight) {
      this.$nextTick(() => {
        this.columnDrop(); // 高度变化后重新初始化拖拽
      });
    },
  },
  mounted() {
    this.loadTableConfig()
    this.pageSize = this.initPageSize;
    this.$nextTick(() => {
      this.calculateTableHeight();
      setTimeout(() => {
        this.calculateTableHeight();
      }, 2000)
    })
    window.addEventListener('resize', this.calculateTableHeight); // 监听窗口大小变化

    let list = this.tableConfig.map(item => item.prop);
    this.oldList = JSON.parse(JSON.stringify(list))
    this.newList = JSON.parse(JSON.stringify(list))
    this.columnDrop();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight); // 清理监听器
  },
  methods: {
    getAuth,
    loadDefaultSearchParam() {
      this.$refs.searchParam.loadDefaultItem();
    },
    generateUniqueId() {
      return Math.random().toString(36).substr(2, 9);  // 生成一个随机ID
    },
    handleSort() {
      this.$emit('handleSort')
    },
    hanldeChangeShow() {
      this.$emit('showActive',this.isShowActive)
    },
    saveTableConfig() {
      // 用户如果还没做操作，那就不保存
      if (!this.saveTableConfigFlag) return;
      let fieldList = [...this.newList];
      let showFields = [...this.selectedFields]
      let list = fieldList.map(field => {
        return {
          field: field,
          visible: showFields.includes(field), // 判断是否在 showFields 中，决定 visible 是否为 true 或 false
        };
      });

      let userId = this.userInfo.userId
      const routePath = this.$route.path;
      if (list) {
        saveTableConfigDebounced(userId, routePath, JSON.stringify(list));
        this.settingSortedTableConfig(list);
      }
    },
    columnDrop() {
      let state = this;
      const wrapperTr = document.querySelector('.draggable .fks-table__header-wrapper tr');
      Sortable.create(wrapperTr, {
        animation: 180,
        delay: 0,
        filter:".draggable-filter",
        onStart: evt => {
          state.saveTableConfigFlag = true;
        },
        onEnd: evt => {
          // 获取被拖动的元素
          let oldI = evt.oldIndex - 2;
          let newI = evt.newIndex - 2;
          const movedItem = state.newList[oldI];
          state.newList.splice(oldI, 1);
          state.newList.splice(newI, 0, movedItem);
        }
      })
    },

    handleFilter(filters) {
      this.queryParams.conditions = filters;
      this.prevSelection = [];
      this.searchParamCount = this.queryParams.conditions.length;
      this.$emit("searchData", 1, this.pageSize, this.queryParams)
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.prevSelection = [];
      this.$emit('searchData', 1, this.pageSize);
      this.$forceUpdate()
    },
    handleCurrentChange(val) {
      this.prevSelection = [];
      this.$emit('searchData', val, this.pageSize);
    },
    showFilter() {
      this.filterVisible = true;
    },
    exportData() {
      let list = this.sortedTableConfig;

      let fieldList = list.filter(item => item.visible)
        .filter(item => {
          return item.label != '操作'
        })
        .map(item => {
        return {
          field: item.field,
          fieldName: item.label
        }
      })
      this.$emit('export-data', fieldList);
      this.$message.info("导出中，请稍等");
    },
    batchAdd() {
      this.$emit('batch-add');
    },
    addItem() {
      this.$emit('add-item');
    },
    deleteItem() {
      this.$emit('delete-item', this.selectedRows);
    },
    updateSort(newVal) {
      let list = newVal.map(item => item.field);
      this.saveTableConfigFlag = true;
      this.$set(this.$data, 'newList', list);
    },
    resetFieldConfig() {
      let userId = this.userInfo.userId
      const routePath = this.$route.path;
      tableConfigDel(userId, routePath)
      // 初始配置，设置成newList
      this.newList = this.oldList;
      let fieldList = this.tableConfig.map(item => item.prop)
      console.log('filedList', fieldList, this.tableConfig);
      this.$emit("update:selectedFields", fieldList);
    },
    updateShowFiled(newVal) {
      this.saveTableConfigFlag = true;
      this.$emit("update:selectedFields", newVal);
    },
    unSelect(rows) {
      let list = rows;
      // 取消全选操作
      const indicesToRemove = [];
      list.forEach(row => {
        const index = this.selectedRows.findIndex(selectedRow =>
          selectedRow.id === row.id
        );
        if (index !== -1) {
          indicesToRemove.push(index); // 将待删除的index存入数组
        }
      });

      rows.forEach((item) => {
        // 在 dataList 中找到对应的 item
        const index = this.dataList.findIndex(dataItem => dataItem.id === item.id);

        if (index !== -1) {
          // 绑定 dataList 的 item
          const boundItem = this.dataList[index];
          this.$refs.table.toggleRowSelection(boundItem, false);
        }
      });

      indicesToRemove.reverse().forEach(index => {
        this.selectedRows.splice(index, 1); // 删除对应的行
      });
    },
    handleColumnClick(row, prop) {
      this.$emit("clickRow", row, prop)
    },
    handleSelectAll(selection, bool) {
      if (this.isTree) {
        if (this.$refs.table.$refs.tableHeader.isAllSelected) {
          this.checkRow(selection, true);
        } else {
          this.$refs.table.clearSelection();
        }
        return
      }

      if (bool) {
        // 全选操作
        selection.forEach(row => {
          if (!this.selectedRows.some(selectedRow =>
            selectedRow.id === row.id
          )) {
            this.selectedRows.push(row); // 添加未选中的行
          }
        });
        // 记录本次全选结果，下次取消勾选时使用
        this.prevSelection = JSON.parse(JSON.stringify(selection));
      } else {
        let list = this.prevSelection;
        // 取消全选操作
        list.forEach(row => {
          const index = this.selectedRows.findIndex(selectedRow =>
            selectedRow.id === row.id
          );
          if (index !== -1) {
            this.selectedRows.splice(index, 1); // 删除已选中的行
          }
        });
      }
      this.$emit("select-all", this.selectedRows);
    },
    checkRow(data, bool) {
      data.forEach((item) => {
        this.$refs.table.toggleRowSelection(item, bool);
        if (item.children) {
          this.checkRow(item.children, bool);
        }
      });
    },
    handleSeletionAll(select) {
      if (this.$refs.tree.$refs.tableHeader.isAllSelected) {
        this.checkRow(select, true);
      } else {
        this.$refs.tree.clearSelection();
      }
    },
    hanldeSort(data) {
      this.$emit('hanldeSort',data)
    },
    reCheckRows() {
      const selectedIds = this.selectedRows.map(row => row.id);  // 获取所有已选中行的唯一标识符

      // 遍历 dataList，根据索引获取对应的行进行选中
      this.dataList.forEach((dataRow, index) => {
        // 判断当前行是否在 selectedIds 中，如果在则选中
        if (selectedIds.includes(dataRow.id)) {
          console.log("标记", dataRow)
          this.$refs.table.toggleRowSelection(dataRow, true);  // 通过 rows 中的行来标记
        }
      });

    },
    chooseCurrRow(row, column, event) {
      if (!this.chooseRow) return;
      const index = this.selectedRows.findIndex(selectedRow =>
        selectedRow.id === row.id
      );
      let bool = index !== -1;
      if (index !== -1) {
        this.selectedRows.splice(index, 1);
      } else {
        // 如果行不存在，添加它
        this.selectedRows.push(row);
      }
      this.$emit("selectItem", [row], bool);
      this.$refs.table.toggleRowSelection(row, !bool);
    },
    handleRowSelect(selectedRows,row) {
      console.log(this.isTree,'====this.isTree====')
      if (this.isTree) {
        if (!row.children) {
          return false;
        }
        if (this.$refs.table.selection.includes(row)) {
          this.checkRow(row.children, true);
        } else {
          this.checkRow(row.children, false);
        }
        return
      }

      if (!this.batchSelectable) {
        // 如果是单选模式，取消其他选中项
        if (selectedRows.length > 0) {
          let row = selectedRows[selectedRows.length - 1];
          this.selectedRows = [row]; // 保存当前选中的行

          // 进入单选模式时，清除所有已选中的行
          this.$refs.table.clearSelection();  // 清空所有选中项
          if (selectedRows.length > 0) {
            // 选中当前行
            this.$refs.table.toggleRowSelection(row, true);
          }
        }
      } else {
        this.selectedRows = selectedRows;
      }
      this.$emit("selectItem", selectedRows);
    },
    loadTableConfig() {
      let userId = this.userInfo.userId
      const routePath = this.$route.path;
      tableConfigGet(userId, routePath).then(res => {

        let initialList = this.tableConfig.map(item => ({
          field: item.prop, // 使用 prop 作为 field
          visible: true,     // 默认为可见
        }));

        let list = JSON.parse(res.data)
        if (list) {

          list.forEach(item => {
            let fieldItem = initialList.find(field => field.field === item.field);
            if (fieldItem) {
              fieldItem.visible = item.visible;
            }
            if (item.visible === false) {
              this.markFilterBtn = true;
            }
          });

          initialList.sort((a, b) => {
            const indexA = list.findIndex(item => item.field === a.field);
            const indexB = list.findIndex(item => item.field === b.field);
            return indexA - indexB;
          });
        }
        let fieldList = initialList.map(item => item.field);
        let showFields = initialList.filter(item => item.visible).map(item => item.field);
        this.newList = [...fieldList];
        this.settingSortedTableConfig(initialList);
        this.$emit("update:selectedFields", [...showFields]);
      });
    },
    settingSortedTableConfig(list) {
      // 找到第一个 clickable 的索引
      const firstClickableIndex = this.tableConfig.findIndex((item) => item.clickable);
      this.sortedTableConfig = list.map((item, index) => {
        const matchedItem = this.tableConfig.find((fieldItem) => fieldItem.prop === item.field);

        let classText = '';
        if (index <= firstClickableIndex) {
          classText = 'filter';
        }
        if (matchedItem && (matchedItem.clickable || matchedItem.fixed)) {
          classText = 'filter';
        }
        return {
          ...item,
          label: matchedItem ? matchedItem.label : '',
          classText, // 设置交互样式
        };
      });
    },
    calculateTableHeight() {
      this.$nextTick(() => {
        // 使用 getElementById 获取元素
        const parentHeight = document.getElementById(`tableContainer-${this.uniqueId}`)?.offsetHeight || 0;
        const toolHeight = document.getElementById(`toolBar-${this.uniqueId}`)?.offsetHeight || 0;
        const headerHeight = document.getElementById(`tableHeader-${this.uniqueId}`)?.offsetHeight || 0;
        const paginationHeight = document.getElementById(`pagination-${this.uniqueId}`)?.offsetHeight || 0;

        if (headerHeight == 0 && paginationHeight == 0 && toolHeight == 0 && headerHeight == 0) {
          this.$set(this.$data, 'tableHeight', parentHeight);
        } else {
          this.$set(this.$data, 'tableHeight', parentHeight - toolHeight - headerHeight - paginationHeight - 60);
        }
      })
    },

    // 日期格式化：yyyy-mm-dd
    formatDate(value) {
      if (!value) return '';
      const date = new Date(value);
      const year = date.getFullYear();
      const month = ('0' + (date.getMonth() + 1)).slice(-2);
      const day = ('0' + date.getDate()).slice(-2);
      return `${year}-${month}-${day}`;
    },
    cellClass(row) {
      // 确定第一个 clickable 列的索引
      const firstClickableIndex = this.tableConfig.findIndex(column => column.clickable);

      if (row.columnIndex < 2 || (firstClickableIndex !== -1 && row.columnIndex - 2 <= firstClickableIndex)) {
        if (row.columnIndex == 0) {
          return 'draggable-filter'
        }
        return 'draggable-filter filter';
      }
      let column = this.tableConfig[row.columnIndex - 2];
      if (column) {
        return column.fixed || column.clickable ? 'draggable-filter filter' : '';
      }
      return '';
    },
    // 格式化列数据：根据列配置判断是否格式化
    formatColumnData(value, column) {
      if (column.formatDate) {
        return this.formatDate(value);
      }
      if (column.enums) {
        if (value) {
          let list = column.enums;
          return list.find(item => item.key == value).value;
        }
      }
      if (!value) return column.default ? column.default : '/';
      return value;
    },
  }
}
</script>
<style scoped lang="less">
@import '~@/styles/disabled';
@import '~@/styles/button';
@import '~@/styles/scrollbar';
@import '~@components/CustomerTable/exclude/customerTable.css';

/deep/ .fks-table__fixed-footer-wrapper tbody td {
  background-color: #fafafa;
}
/deep/ .fks-table__footer-wrapper tbody td, .fks-table__header-wrapper tbody td {
  background-color: #fafafa;
}
</style>
