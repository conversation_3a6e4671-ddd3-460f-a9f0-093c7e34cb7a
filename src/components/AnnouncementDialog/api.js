import request from '@utils/request'

export function getUpdateNotice (params) {
  return request({
    url: '/vehicle-dispatch/version/notice/info',
    method: 'get',
    params,
  })
}

export function confirmNotice (params) {
  return request({
    url: '/vehicle-dispatch/version/notice/confirm',
    method: 'get',
    params,
  })
}

export function versionNoticeList () {
  return request({
    url: '/vehicle-dispatch/version/list',
    method: 'get'
  })
}

export function getVersionNoticeDetail (id) {
  return request({
    url: '/vehicle-dispatch/version/detail',
    method: 'get',
    params: {
      id
    },
  })
}
