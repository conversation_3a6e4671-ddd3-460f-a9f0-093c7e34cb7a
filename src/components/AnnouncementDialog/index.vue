<template>
  <fks-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    custom-class="announcement-dialog"
  >
    <div class="dialog-content">
      <div class="dialog-header">
        <div class="title">
          {{title}}
        </div>
        <div class="create-info">
          <div class="author">
            {{creator}}
          </div>
          <div class="create-time">
            {{createTime}}
          </div>
        </div>
      </div>
      <div class="content">
        <div class="content-item" v-for="(item, index) in contentList" :key="index">
          <div class="content-title">
            {{ item.title }}
          </div>
          <div class="content-details">
            <!-- 内层循环遍历 itemList -->
            <div class="content-subitem" v-for="(subItem, subIndex) in item.itemList" :key="subIndex">
              <div class="subitem-name">
                {{ subItem.name }}
              </div>
              <ul class="subitem-contents">
                <!-- 遍历每个contents -->
                <li class="sub-item-content" v-for="(content, contentIndex) in subItem.contents" :key="contentIndex">
                  <img src="@/assets/img/dialog/sub-item.svg" width="14px" height="14px">
                  <span>
                  {{ content }}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <fks-button class="dialog-btn" @click="handleClose" type="primary">我已知晓</fks-button>
    </div>
  </fks-dialog>
</template>

<script>
import { confirmNotice, getUpdateNotice } from '@components/AnnouncementDialog/api'

export default {
  data() {
    return {
      dialogVisible: false,
      version: 1,
      title: "",
      creator: "",
      createTime: "",
      contentList: [
      ]
    };
  },
  created() {
    this.getNotice();
  },
  computed: {
    userInfo() {
      return this.$storage.getObject('user')
    },
    localVersion() {
      return localStorage.getItem('version')
    },
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
      let userId = this.userInfo.userId
      confirmNotice({ userId, version: this.localVersion})
    },
    async getNotice() {
      let userId = this.userInfo.userId
      let res = await getUpdateNotice({
        userId, version: this.localVersion
      });
      if (res.data) {
        let data = res.data
        let {
          version, title, author, publishDate, contentList
        } = data;
        this.version = version;
        this.title = title;
        this.creator = author;
        this.createTime = publishDate;
        if (contentList != null) {
          this.contentList = JSON.parse(contentList);
        }
        this.dialogVisible = true;
      }
    }
  }
};
</script>

<style>
@import "exclude/index.css";
</style>
