<template>
  <fks-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    custom-class="update-log-pc-dialog"
    :before-close="handleBeforeClose"
  >
    <div class="title-content">
      更新日志
    </div>
    <div class="title-back-img">
      <img src="@/assets/img/dialog/title-back.png" width="673" height="200">
    </div>
    <div class="log-img">
      <img src="@/assets/img/dialog/update-log.png" width="230" height="230">
    </div>
    <div class="dialog-gradient-overlay"></div>
    <div class="update-content">
      <div class="update-list" >
        <div class="list-item" v-for="(item, index) in versionList" :key="index" :class="item.actived ? 'active-item' : ''"
            @click="getUpdateContent(item)">
          <div class="version-str">
            {{ 'V' + item.version }}
          </div>
          <div class="date-str">
            {{ formatDate(item.publishDate) }}
          </div>
        </div>
      </div>

      <div class="content">
        <div class="content-item" v-for="(item, index) in contentList" :key="index">
          <div class="content-title">
            {{ item.title }}
          </div>
          <div class="content-details">
            <!-- 内层循环遍历 itemList -->
            <div class="content-subitem" v-for="(subItem, subIndex) in item.itemList" :key="subIndex">
              <div class="subitem-name">
                {{ subItem.name }}
              </div>
              <ul class="subitem-contents">
                <!-- 遍历每个contents -->
                <li class="sub-item-content" v-for="(content, contentIndex) in subItem.contents" :key="contentIndex">
                  <img src="@/assets/img/dialog/sub-item.svg" width="14px" height="14px">
                  <span>
                  {{ content }}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <fks-button class="dialog-btn" @click="handleClose" type="primary">关闭</fks-button>
    </div>
  </fks-dialog>
</template>

<script>
import {
  confirmNotice,
  getUpdateNotice, getVersionNoticeDetail,
  versionNoticeList
} from '@components/AnnouncementDialog/api'

export default {
  data() {
    return {
      dialogVisible: false,
      contentList: [
      ],
      versionList: [],
      noticeDetail: {},
      contentLoading: false,
      versionLoading: false,
    };
  },
  created() {
    this.getNotice();
  },
  computed: {
    userInfo() {
      return this.$storage.getObject('user')
    },
    localVersion() {
      return localStorage.getItem('version')
    },
  },
  methods: {
    async getUpdateContent(version) {
      this.versionList.forEach(item => {
        this.$set(item, 'actived', false); // 使用 this.$set 确保响应式
      });
      this.$set(version, 'actived', true); // 确保 version.actived 是响应式的
      let id = version.id;

      let res = await getVersionNoticeDetail(id);
      if (res.data) {
        let data = res.data
        let {
          contentList
        } = data;
        if (contentList != null) {
          this.contentList = JSON.parse(contentList);
        }
      }
    },
    formatDate(dateStr) {
      if (!dateStr) return '';
      return dateStr
        .replace('年', '-')
        .replace('月', '-')
        .replace('日', '');
    },
    openDialog() {
      this.getNotice()
      this.dialogVisible = true;
    },
    handleBeforeClose(done) {
      this.versionList = [];
      this.contentList = [];
      done();
    },
    handleClose() {
      this.dialogVisible = false;
    },
    async getNotice() {
      let resList = await versionNoticeList();
      this.versionList = resList.data;
      if (this.versionList && this.versionList.length > 0) {
        this.getUpdateContent(this.versionList[0]);
      }
    }
  }
};
</script>

<style>
@import "exclude/pcView.css";
</style>
