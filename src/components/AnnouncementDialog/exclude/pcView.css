.update-log-pc-dialog {
    border-radius: 8px;
    position: relative; /* 保证子元素可以绝对定位 */
    width: 1000px; /* 默认 PC端设置宽度为 1000px */
    max-width: 80%;
}

.update-log-pc-dialog .fks-dialog__header {
    position: relative;
    display: none;
}

.update-log-pc-dialog .fks-dialog__body {
    overflow: hidden;
    padding: 2px !important;
}

.title-back-img {
    position: absolute;
    top: 0;
    right: 0;
    width: 673px;
    height: 200px;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.log-img {
    position: absolute;
    top: -63px;
    right: 24px;
    height: 230px;
    width: 230px;
    pointer-events: none;
    z-index: 2;
}

.dialog-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    height: 300px;
    width: 100%;
    background: linear-gradient(180deg, #D6E1FF 0%, #FFFFFF 100%);
    z-index: 0;
    pointer-events: none; /* 不影响点击事件 */
    border-radius: 8px;
}

.head-back-img {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.update-log-pc-dialog {
    .title-content, .update-content {
        z-index: 10;
        position: relative;
    }
    .update-content {
        height: calc(60vh - 84px);
    }

    .title-content {
        padding: 16px 20px;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: normal;
        color: #32363D;
    }


    .update-content {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-content: center;
        margin: 26px 30px 32px;
        .update-list {
            height: 100%;
            border-right: 1px solid rgba(84, 131, 247, 0.196);
            width: 105px;
            box-sizing: border-box;
            overflow-y: auto;
            scrollbar-width: none; /* Firefox 隐藏滚动条 */
            -ms-overflow-style: none; /* IE10+ 隐藏滚动条 */

            &::-webkit-scrollbar {
                display: none; /* Chrome / Safari 隐藏滚动条 */
            }


            .active-item {
                border-right: 2px solid #5483F7;
                margin-right: -2px; /* 补偿边框增加的宽度，使其对齐 */
                .version-str {
                    color: #5483F7 !important;
                }
            }
            .list-item:last-child {
                margin-bottom: 0;
            }

            .list-item {
                padding-bottom: 4px;
                margin-bottom: 20px;
                user-select: none;
                cursor: pointer;
                box-sizing: border-box;
                max-width: 100%; /* 确保不超出父容器的宽度 */
                height: 48px;
                word-break: break-word; /* 防止长文本溢出 */
                padding-right: 30px;

                .version-str {
                    font-size: 16px;
                    font-weight: 500;
                    color: rgba(0, 0, 0, 0.8483);
                }

                .date-str {
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.45);
                }
            }
        }


        .dialog-btn {
            border-radius: 4px;
            background: #5483F7;
            box-shadow: 3px 3px 6px 0px rgba(84, 131, 247, 0.4);

            font-size: 14px;
            font-weight: normal;
            line-height: normal;
            color: #FFFFFF;
        }

        .content {
            flex: 1;
            overflow-y: auto;
            padding: 0 18px;
        }

        .content-title {
            font-size: 18px;
            font-weight: 500;
            line-height: normal;
            color: rgba(0, 0, 0, 0.8483);
            margin: 20px 0;
        }

        .content-subitem {
            margin-bottom: 14px;
        }

        .subitem-name {
            font-size: 16px;
            font-weight: normal;
            line-height: normal;
            color: rgba(0, 0, 0, 0.8483);
            padding-left: 14px; /* 稍微增加 padding-left，给 before 元素留空间 */
            position: relative; /* 确保伪元素定位 */
        }

        .subitem-name::before {
            content: ''; /* 确保伪元素可见 */
            height: 15px;
            width: 4px;
            border-radius: 2px;
            opacity: 1;
            background: #5483F7;
            position: absolute; /* 使用绝对定位 */
            left: 0; /* 向左对齐 */
            top: 53%; /* 垂直居中对齐 */
            transform: translateY(-50%); /* 完全居中对齐 */
        }


        .subitem-contents {
            padding-left: 16px;
            img {
                margin-right: 8px;
            }
        }

        .sub-item-content {
            margin-top: 10px;
            vertical-align: middle; /* 使图片和文字垂直居中 */
            line-height: 16px;

            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-direction: row;
        }
    }
}
