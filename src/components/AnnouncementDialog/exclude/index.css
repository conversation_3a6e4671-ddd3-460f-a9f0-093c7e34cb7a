.announcement-dialog {
    border-radius: 8px;
}

.announcement-dialog .fks-dialog__body {
    background: url("../../../assets/img/dialog/background.png") no-repeat;
    background-position: top left; /* 背景从左上角开始 */
    background-size: 100% auto;
    overflow: hidden;
    padding: 2px !important;
}

.announcement-dialog .dialog-header {
    padding: 18px 18px 0;
}

.announcement-dialog {
    width: 1000px; /* 默认 PC端设置宽度为 1000px */
    max-width: 80%;
}

@media (max-width: 768px) {  /* 当屏幕宽度小于或等于 768px 时（通常为移动设备） */
    .announcement-dialog {
        width: 90vw; /* 移动端宽度为 80% 的视口宽度 */
    }
    .announcement-dialog .dialog-content .title {
        font-size: 16px !important;
    }
    .announcement-dialog .dialog-content .create-info {
        gap: 10px;
        font-size: 10px !important;
    }
}



.announcement-dialog .fks-dialog__header {
    display: none;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}

.dialog-btn {
    border-radius: 4px;
    background: #5483F7;
    box-shadow: 3px 3px 6px 0px rgba(84, 131, 247, 0.4);

    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    color: #FFFFFF;
}



@media (max-width: 768px) {  /* 当屏幕宽度小于或等于 768px 时（通常为移动设备） */
    .announcement-dialog .dialog-footer {
        display: flex;
        justify-content: center;
    }
    .announcement-dialog .dialog-btn {
        width: 110px !important;
        border-radius: 58px;
    }
    .announcement-dialog .dialog-content .content {
         /*max-height: 50vh !important;*/
        max-height: calc(60vh - 84px) !important;
     }


}


.dialog-content .title {
    font-size: 20px;
    font-weight: bold;
    line-height: normal;
    color: rgba(0, 0, 0, 0.8483);
}

.dialog-content .create-info {
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
    color: rgba(0, 0, 0, 0.45);
    display: flex;
    justify-content: flex-start;
    align-content: center;
    gap: 32px;
    margin: 11px 0;
}

.dialog-content .content {
    height: calc(60vh - 88px);
    overflow-y: auto;
    padding: 0 18px;
}

.content-title {
    font-size: 18px;
    font-weight: 500;
    line-height: normal;
    color: rgba(0, 0, 0, 0.8483);
    margin: 20px 0;
}

.content-subitem {
    margin-bottom: 14px;
}

.subitem-name {
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
    color: rgba(0, 0, 0, 0.8483);
    padding-left: 14px; /* 稍微增加 padding-left，给 before 元素留空间 */
    position: relative; /* 确保伪元素定位 */
}

.subitem-name::before {
    content: ''; /* 确保伪元素可见 */
    height: 15px;
    width: 4px;
    border-radius: 2px;
    opacity: 1;
    background: #5483F7;
    position: absolute; /* 使用绝对定位 */
    left: 0; /* 向左对齐 */
    top: 53%; /* 垂直居中对齐 */
    transform: translateY(-50%); /* 完全居中对齐 */
}


.subitem-contents {
    padding-left: 16px;
    img {
        margin-right: 8px;
    }
}

.sub-item-content {
    margin-top: 10px;
    vertical-align: middle; /* 使图片和文字垂直居中 */
    line-height: 16px;

    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: row;
}