<template>
  <fm-popup
      v-if="isMobile"
      ref="popup"
      :style="{ height: '100%' }"
      :visible.sync="show"
      :duration="animateDuration"
      get-container="#app"
      position="bottom"
      @close="beforeClose"
      @open="selectProjectOpenHandler"
  >
    <fm-nav-bar
        v-if="showNavbar"
        :border="false"
        :title="title"
        left-arrow
        @click-left="closePopup"
    />
    <slot/>
  </fm-popup>
  <fks-dialog v-else-if="isPC && !mobileOnly" :before-close="beforeClose"  :visible.sync="show" top="3%">
    <template slot="title">
      <div class="flex col-center row-between">
        <i v-if="icon" :class="`fks-icon-${icon}`" />
        <span style="font-size: 16px;display: inline-block;">{{title}}</span>
      </div>
    </template>
    <slot/>
    <div slot="footer" style="margin: 0 auto;display: flex;justify-content: flex-end">
      <fks-button
        icon="fks-icon-check"
        type="text"
        style="color: #333;"
        @click="confirm"
      >
        {{btnName}}
      </fks-button>
    </div>
  </fks-dialog>
</template>

<script>
import platform from "@/mixins/platform";
import {Popup} from "fawkes-mobile-lib";


export default {
  name: 'Popper',
  mixins: [platform],
  components: {
    [Popup.name]: Popup,
  },
  props: {
    icon: {
      type: String
    },
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    showNavbar: {
      type: Boolean,
      default: false
    },
    mobileOnly: {
      type: Boolean,
      default: false
    },
    btnName: {
      type: String,
      default: '提交'
    }
  },
  data() {
    return {
      animateDuration: 0.3, // 默认弹窗动画时间0.3s，
      touchEvents: ['touchstart', 'touchend', 'touchmove']
    }
  },
  watch: {
    show(newVal) {
      if (newVal && this.isMobile) {
        window.addEventListener('popstate', this.preventBack);
      } else {
        window.removeEventListener('popstate', this.preventBack);
      }
    },
  },
  methods: {
    preventBack(e) {
      if (this.show) {
        e.preventDefault(); // 阻止返回
        // this.show = false; // 改为关闭弹框
        this.$emit('update:show', false); // 触发父组件更新
      }
    },
    // 判断当前环境
    isWechat() {
      var ua = window.navigator.userAgent.toLowerCase();
      if (ua.match(/micromessenger/i) == 'micromessenger') { // 判断是否在微信浏览器内
        return true;
      } else {
        return false;
      }
    },
    closePage() {
      // isWechat是我写的一个判断当前环境是否是微信内置浏览器 的方法
      if (!this.isWechat()) {
        return false;
      } // 非微信环境下，不做处理
      var ua= window.navigator.userAgent.toLowerCase();
      var u = navigator.userAgent;
      var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //g
      var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
      setTimeout(function() {
        //安卓手机
        if (isAndroid) {
          document.addEventListener("WeixinJSBridgeReady", function() {
            WeixinJSBridge.call("openWindow");
          }, false);
        }
        if (isIOS) {
          //ios手机
          WeixinJSBridge.call("openWindow");
        }
      }, 100);
    },
    closePopup() {
      this.$emit('update:show', false)
      this.$emit('closePopup')
    },
    beforeClose() {
      if (this.isPC) {
        this.$emit('update:show', false)
        this.$emit('close')
      } else {
        this.selectProjectCloseHandler();
      }
    },
    // popup关闭
    selectProjectCloseHandler () {
      window.removeEventListener('popstate', this.popstateHandler, false)
    },
    // popup打开
    selectProjectOpenHandler () {
      window.history.pushState(null, null, location.href)
      // window.history.pushState(null, null, '#') // 模拟新的一页history记录
      window.addEventListener('popstate', this.popstateHandler) //添加popstate事件监听
    },
    popstateHandler (e) {
      this.animateDuration = 0 // 防止侧滑出现多次动画
      this.$emit('update:show', false);
      window.history.back();
      this.closePage();
    },

    moveHandle () {
      this.$emit('update:show', false)
    },
    confirm() {
      this.$emit('confirm')
    },
    preventDefault(e){
      e.preventDefault()
    },
  },
  beforeDestroy() {
    window.removeEventListener('popstate', this.preventBack);
  },
  // mounted() {
  //   if (this.$refs.popup.$el) {
  //     this.touchEvents.forEach(event => {
  //       this.$refs.popup.$el.addEventListener(event, this.preventDefault)
  //     })
  //   }
  // },
  // beforeDestroy() {
  //   if (this.$refs.popup.$el) {
  //     this.touchEvents.forEach(event => {
  //       this.$refs.popup.$el.removeEventListener(event, this.preventDefault)
  //     })
  //   }
  // }  // mounted() {
  //   if (this.$refs.popup.$el) {
  //     this.touchEvents.forEach(event => {
  //       this.$refs.popup.$el.addEventListener(event, this.preventDefault)
  //     })
  //   }
  // },
  // beforeDestroy() {
  //   if (this.$refs.popup.$el) {
  //     this.touchEvents.forEach(event => {
  //       this.$refs.popup.$el.removeEventListener(event, this.preventDefault)
  //     })
  //   }
  // }
}
</script>
<style lang="less" scoped>
/deep/ .fks-dialog__footer {
  padding: 16px 32px;
  border-top: 1px solid #DFE0E2;
}
/deep/ .fks-dialog__body {
  padding: 40px;
}
</style>
