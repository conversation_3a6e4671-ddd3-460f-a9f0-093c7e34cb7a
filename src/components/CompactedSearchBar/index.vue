<template>
  <fks-popover
    ref="popper"
    placement="bottom-end"
    trigger="click"
    width="550"
    @hide="onHide"
    @show="onShow"
  >
    <div class="filter-container">
      <header>设置筛选条件</header>
      <main>
        <div
          v-for="filter in filters"
          :key="filter.id"
          class="filter-item"
        >
          <fks-select
            v-model="filter.prop"
            class="m-r-20"
            @change="(val) => handleTypeChange(val, filter.id)"
          >
            <fks-option
              v-for="(item, index) in configs"
              :key="index"
              :disabled="disabledOption(item)"
              :label="item.label"
              :value="item.prop"
            />
          </fks-select>
          <fks-date-picker
            v-if="filter.type === 'daterange'"
            v-model="filter.value"
            end-placeholder="结束日期"
            range-separator="~"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd"
          />
          <div
            v-else-if="filter.type === 'daterange-check'"
            class="flex col-center">
            <fks-date-picker
              v-model="filter.value"
              :clearable="false"
              class="full-width"
              end-placeholder="结束时间"
              range-separator="~"
              start-placeholder="开始时间"
              type="daterange"
              value-format="yyyy-MM-dd"
            />
            <fks-checkbox
              v-model="filter.isCheck"
              class="m-l-24"
              @change="(val) => handleCurrentMonthChange(val, filter)"
            >
              本月
            </fks-checkbox>
          </div>
          <new-person-selector
            v-else-if="filter.type === 'person-selector'"
            @closePopup="(val) => closePopupPerson(val, filter)"
          />
          <fks-select v-else-if="filter.type === 'select'" v-model="filter.value">
            <fks-option
              v-for="(item, index) in getOptions(filter)"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </fks-select>
          <fks-input
            v-else
            v-model="filter.value"
            :placeholder="getPlaceholder(filter)"
          />
          <i class="fks-icon-close m-l-20" @click="handleRemove(filter)"/>
        </div>
      </main>
      <footer>
        <fks-button icon="fks-icon-add" type="text" @click="addFilter">添加条件</fks-button>
        <fks-button type="text" @click="clearFilters">清空</fks-button>
      </footer>
    </div>
    <slot name="custom" slot="reference" v-if="$slots.custom" />
    <div v-else slot="reference" class="flex col-center">
      <fks-button icon="fks-icon-filter" type="primary">筛选条件</fks-button>
      <div
        class="flex m-l-10 tags-container"
        style="border: 1px solid #ddd;border-radius: 4px;padding: 5px"
      >
        <fks-tag
          v-if="filters.length === 0 || tags.length === 0"
          type="info"
        >
          暂无筛选条件
        </fks-tag>
        <div
          v-else
          class="flex"
        >
          <fks-tag
            type="info"
            closable
            @close="handleClose(tags[0].prop)"
            style="margin-right: 5px"
          >
            {{tags[0].label}}
          </fks-tag>
          <fks-tag type="info" v-if="tags.length > 1">+{{tags.length - 1}}</fks-tag>
        </div>
      </div>
    </div>
  </fks-popover>

</template>
<script>
import {uuid} from '@/utils/util'
import NewPersonSelector from "@components/PersonSelector/main.vue"
import {mapActions, mapMutations, mapState} from "vuex";
import * as StateTypes from '@/store/State/stateTypes'

export default {
  name: 'CompactedSearchBar',
  components: {NewPersonSelector},
  props: {
    id: {
      type: String,
      required: true
    },
    configs: {
      type: Array,
      required: true
    },
    optionConfigs: {
      type: Object
    }
  },
  data() {
    return {
      filters: [],
      isCleared: false,
      isChanged: false,
      value1: []
    }
  },
  computed: {
    ...mapState([StateTypes.FILTERS]),
    tags() {
      return this.filters
        .filter(filter => filter.prop)
        .filter(filter => filter.value !== '' && filter.value !== undefined)
        .map(filter => {
          const title = this.configs.find(item => item.prop === filter.prop).label + '：'
          if (filter.type === 'select') {
            const item = this.optionConfigs[filter.prop].find(item => item.value === filter.value);
            return {
              prop: filter.prop,
              label: title + (item ? item.label : '')
            }
          } else if (filter.type === 'daterange' || filter.type === 'daterange-check') {
            const [start, end] = filter.value;
            return {
              prop: filter.prop,
              label: `${title}${start}-${end}`
            }
          } else {
            return {
              prop: filter.prop,
              label: title + filter.value
            }
          }
        })
    }
  },
  watch: {
    filters: {
      deep: true,
      handler(newVal) {
        this.isChanged = true;
        const result = newVal
          .filter(item => item.prop)
          .reduce((acc, cur) => {
            return Object.assign(acc, {[cur.prop]: cur.value})
          }, {})
        this.SET_FILTERS({id: this.id, filters: newVal, type: 'set'})
        this.$emit('change', result);
      }
    }
  },
  created() {
    this.getCurrentUser(this.$storage.get('username'))
  },
  mounted() {
    if (this.savedFilters[this.id]) {
      this.filters = JSON.parse(JSON.stringify(this.savedFilters[this.id]));
      setTimeout(() => {
        this.$emit('query', true);
        this.$emit('pushParam', this.filters);
      })
    }
  },
  methods: {
    ...mapActions('CarApply', ['getCurrentUser']),
    ...mapMutations(['SET_FILTERS']),
    handleClose(prop) {
      const index = this.filters.findIndex(item => item.prop === prop);
      this.filters.splice(index, 1);
      this.$nextTick(()=>{
        this.$emit('query', true)
        this.$emit('pushParam', this.filters);
      })
    },
    handleCurrentMonthChange(val, filter) {
      if (val) {
        let start = this.$dayjs().startOf('month').format('YYYY-MM-DD');
        let end = this.$dayjs().endOf('month').format('YYYY-MM-DD');
        filter.value = [start, end];
      } else {
        filter.value = [];
      }
    },
    getOptions(filter) {
      return this.optionConfigs[filter.prop];
    },
    handleRemove(filter) {
      const index = this.filters.findIndex(item => item.id === filter.id);
      this.filters.splice(index, 1);
      this.$emit('pushParam', this.filters);
    },
    disabledOption(item) {
      return this.filters.findIndex(filter => filter.prop === item.prop) > -1
    },
    // 选择申请人员
    closePopupPerson(val, filter) {
      filter['value'] = val.ucPersonFullName;
    },
    getPlaceholder(filter) {
      const item = this.configs.find(el => el.prop === filter.prop);
      if (item) {
        return `请输入${item.label}`
      }
    },
    handleTypeChange(val, id) {
      const item = this.configs.find(el => el.prop === val);
      if (item) {
        const filter = this.filters.find(el => el.id === id);
        filter.type = item.type;
      }
    },
    addFilter() {
      this.filters.push({
        id: uuid(4, 8),
        prop: '',
        value: '',
        type: ''
      })
    },
    onHide() {
      if (this.isCleared) return;
      if (this.isChanged && this.filters.some(filter => filter.prop)) {
        this.$emit('query', true);
        this.$emit('pushParam', this.filters);
      }
    },
    onShow() {
      this.isCleared = false;
      this.isChanged = false;
    },
    clearFilters() {
      this.filters = [];
      this.isCleared = true;
      this.$emit('clear')
      this.SET_FILTERS({id: this.id, type: 'remove'})
      this.$refs.popper.doClose();
    }
  }
}
</script>

<style lang="less" scoped>
.filter-container {

  header {
    margin-bottom: 20px;
    color: #cccccc;
  }

  .filter-item {
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: 30% 65% 5%;

    i {
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

/deep/ .fks-date-editor--daterange.fks-input__inner {
  width: 100%;
}
</style>
