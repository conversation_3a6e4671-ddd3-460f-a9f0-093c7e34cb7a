<template>
  <div v-if="g9s">
    <div v-if="files.length" class="flex">
      <pre-view
        v-for="(file, index) in files"
        :key="index"
        :file="file"
        show-thumbnail
        style="margin-right: 8px;"
        @click.native="handlePicClick(index)"
      />
    </div>
    <div v-else>暂无图片</div>
    <fks-image-viewer
      v-if="showViewer"
      :url-list="picList"
      :initial-index="picIndex"
      :on-close="handleClose"
    />
  </div>
  <div v-else>暂无图片</div>
</template>

<script>
import {getFile} from "@/api/file";
import PreView from "@components/PreView/index.vue";
import FksImageViewer from "@components/PicturePreview/FksImageViewer.vue";

export default {
  name: 'PicturePreview',
  components: {FksImageViewer, PreView},
  props: {
    g9s: {
      type: String,
      required: true,
    },
    fileListStyle: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      files: [],
      showViewer: false,
      picIndex: 0
    }
  },
  computed: {
    picList() {
      return this.files.map(file => `${process.env.VUE_APP_BASE_URL}/sys-storage/download_image?f8s=${file.fileToken}`)
    }
  },
  methods: {
    handlePicClick(index) {
      this.showViewer = true;
      this.picIndex = index;
    },
    handleClose() {
      this.showViewer = false;
    },
    getFilesByG9s(g9s) {
      getFile({g9s: [g9s]}).then((res) => {
        if (res.status) {
          this.files = res.data.map(item => ({name: item.fileName, fileToken: item.fileToken, extName: item.extName}))
        }
      })
    }
  },
  created() {
    if (this.g9s) {
      this.getFilesByG9s(this.g9s)
    }
  },
  watch: {
    g9s: {
      handler(newVal) {
        if (newVal) {
          this.files = [];
          this.getFilesByG9s(newVal)
        }
      }
    }
  }
}
</script>
