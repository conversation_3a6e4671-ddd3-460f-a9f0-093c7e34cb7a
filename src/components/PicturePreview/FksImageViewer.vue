<template>
  <transition name="viewer-fade">
    <div
      ref="fks-image-viewer__wrapper"
      :style="{ 'z-index': zIndex }"
      class="fks-image-viewer__wrapper"
      tabindex="-1"
      @click="handleClick"
    >
      <div class="fks-image-viewer__mask"/>
      <span class="fks-image-viewer__btn fks-image-viewer__close" @click="hide">
        <i class="fks-icon-circle-close text-white"/>
      </span>
      <template v-if="!isSingle">
        <span
          :class="{ 'is-disabled': !infinite && isFirst }"
          class="fks-image-viewer__btn fks-image-viewer__prev"
          @click="prev"
        >
          <i class="fks-icon-arrow-left"/>
        </span>
        <span
          :class="{ 'is-disabled': !infinite && isLast }"
          class="fks-image-viewer__btn fks-image-viewer__next"
          @click="next"
        >
          <i class="fks-icon-arrow-right"/>
        </span>
      </template>
      <!-- ACTIONS -->
      <div class="fks-image-viewer__btn fks-image-viewer__actions">
        <div class="fks-image-viewer__actions__inner">
          <i class="fks-icon-zoom-out" @click="handleActions('zoomOut')"/>
          <i class="fks-icon-zoom-in" @click="handleActions('zoomIn')"/>
          <i class="fks-image-viewer__actions__divider"/>
          <i :class="mode.icon" @click="toggleMode"/>
          <i class="fks-image-viewer__actions__divider"/>
          <i class="fks-icon-refresh-left" @click="handleActions('anticlocelise')"/>
          <i class="fks-icon-refresh-right" @click="handleActions('clocelise')"/>
        </div>
      </div>
      <!-- CANVAS -->
      <div class="fks-image-viewer__canvas" v-loading="loading">
        <div v-for="(url, i) in urlList" :key="url">
          <img
            ref="img"
            ref-in-for
            :src="url"
            :style="[i === index ? imgStyle : { display: 'none' }, { opacity: imgLoaded ? 1 : 0, transition: 'opacity 0.3s ease' }]"
            class="fks-image-viewer__img"
            @load="handleImgLoad"
            @error="handleImgError"
            @touchstart="handleTouchStart($event, i)"
            @touchmove="handleTouchMove($event, i)"
            @touchend="handleTouchEnd($event, i)"
            @mousedown="handleMouseDown($event, i)"
            @wheel.prevent="handleWheel($event, i)"
          />
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import {off, on} from 'fawkes-lib/src/utils/dom';
import {isFirefox, rafThrottle} from 'fawkes-lib/src/utils/util';

const Mode = {
  CONTAIN: {
    name: 'contain',
    icon: 'fks-icon-full-screen'
  },
  ORIGINAL: {
    name: 'original',
    icon: 'fks-icon-c-scale-to-original'
  }
};

const mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel';
const NORMAL_SIZE = {width: 1920, height: 1080};

export default {
  name: 'FksImageViewer',

  props: {
    urlList: {
      type: Array,
      default: () => []
    },
    zIndex: {
      type: Number,
      default: 2000
    },
    onSwitch: {
      type: Function,
      default: () => {
      }
    },
    onClose: {
      type: Function,
      default: () => {
      }
    },
    initialIndex: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      index: this.initialIndex,
      isShow: false,
      infinite: true,
      loading: false,
      mode: Mode.CONTAIN,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      },
      // 👇 缺失的部分：必须添加
      startTouches: [],         // 记录起始 touches，用于移动或缩放基准
      initialDistance: 0,       // 缩放初始双指间距
      lastScale: 1,             // 上一次 scale，作为缩放计算参考
      lastOffsetX: 0,           // 上一次 offsetX，作为拖动基准
      lastOffsetY: 0,            // 上一次 offsetY，作为拖动基准
      imgLoaded: false,
    };
  },
  computed: {
    isSingle() {
      return this.urlList.length <= 1;
    },
    isFirst() {
      return this.index === 0;
    },
    isLast() {
      return this.index === this.urlList.length - 1;
    },
    currentImg() {
      return this.urlList[this.index];
    },
    currentImgEl() {
      const imgs = this.$refs.img;
      return Array.isArray(imgs) ? imgs[this.index] : imgs;
    },
    imgStyle() {
      const {scale, deg, offsetX, offsetY, enableTransition} = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? 'transform .3s' : '',
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`
      };
      if (this.mode === Mode.CONTAIN) {
        // style.maxWidth = style.maxHeight = '100%';
        style.width = 'auto';
        style.height = 'auto';
      }
      return style;
    }
  },
  watch: {
    index: {
      handler: function (val) {
        this.reset();
        this.onSwitch(val);
      }
    },
    currentImg() {
      this.$nextTick(() => {
        const $img = this.$refs.img[0];
        if (!$img.complete) {
          this.loading = true;
        }
        if ($img && $img.complete) {
          // 图片已经加载完成了（浏览器缓存了）
          this.handleImgLoad();
        }
      });
    }
  },
  mounted() {
    this.deviceSupportInstall();
    // add tabindex then wrapper can be focusable via Javascript
    // focus wrapper so arrow key can't cause inner scroll behavior underneath
    this.$refs['fks-image-viewer__wrapper'].focus();

    this.lastScale = this.transform.scale;
    this.lastOffsetX = this.transform.offsetX;
    this.lastOffsetY = this.transform.offsetY;

    // 获取当前图片 DOM
    const el = this.$refs.img?.[this.index];
    if (el) {
      el.addEventListener('touchmove', this.preventBrowserBackSwipe, { passive: false });
    }
  },

  beforeDestroy() {
    const el = this.$refs.img?.[this.index];
    if (el) {
      el.removeEventListener('touchmove', this.preventBrowserBackSwipe);
    }
  },
  methods: {

    preventBrowserBackSwipe(e) {
      e.preventDefault(); // 阻止默认浏览器滑动操作（后退、滚动等）
    },
    handleClick(e) {
      if (e.target.className === 'fks-image-viewer__mask') {
        this.hide();
      }
    },
    hide() {
      this.deviceSupportUninstall();
      this.onClose();
    },
    deviceSupportInstall() {
      this._keyDownHandler = rafThrottle(e => {
        const keyCode = e.keyCode;
        switch (keyCode) {
          // ESC
          case 27:
            this.hide();
            break;
          // SPACE
          case 32:
            this.toggleMode();
            break;
          // LEFT_ARROW
          case 37:
            this.prev();
            break;
          // UP_ARROW
          case 38:
            this.handleActions('zoomIn');
            break;
          // RIGHT_ARROW
          case 39:
            this.next();
            break;
          // DOWN_ARROW
          case 40:
            this.handleActions('zoomOut');
            break;
        }
      });
      this._mouseWheelHandler = rafThrottle(e => {
        const delta = e.wheelDelta ? e.wheelDelta : -e.detail;
        if (delta > 0) {
          this.handleActions('zoomIn', {
            zoomRate: 0.015,
            enableTransition: false
          });
        } else {
          this.handleActions('zoomOut', {
            zoomRate: 0.015,
            enableTransition: false
          });
        }
      });
      on(document, 'keydown', this._keyDownHandler);
      on(document, mousewheelEventName, this._mouseWheelHandler);
    },
    deviceSupportUninstall() {
      off(document, 'keydown', this._keyDownHandler);
      off(document, mousewheelEventName, this._mouseWheelHandler);
      this._keyDownHandler = null;
      this._mouseWheelHandler = null;
    },
    handleImgLoad() {
      const imgEl = this.currentImgEl;
      const wrapperEl = this.$refs['fks-image-viewer__wrapper'];

      if (!imgEl || !wrapperEl) return;

      const imgWidth = imgEl.naturalWidth || imgEl.width;
      const imgHeight = imgEl.naturalHeight || imgEl.height;
      const wrapperWidth = wrapperEl.clientWidth;
      const wrapperHeight = wrapperEl.clientHeight;

      // 容器高宽比
      const wrapperRatio = wrapperWidth / wrapperHeight;
      // 图片高宽比
      const imgRatio = imgWidth / imgHeight;

      let scale;
      if (imgRatio > wrapperRatio) {
        // 图片比容器更宽，按宽适配
        scale = wrapperWidth / imgWidth;
      } else {
        // 图片比容器更高，按高适配
        scale = wrapperHeight / imgHeight;
      }

      // 防止放大（如果图片本身就小于容器）
      scale = Math.min(scale, 1);

      this.transform.scale = scale; // 预留边距
      this.lastScale = this.transform.scale;
      this.loading = false;
      setTimeout(() => {
        this.imgLoaded = true;
      }, 100)
    },
    handleImgError(e) {
      this.loading = false;
      e.target.alt = '加载失败';
    },

    // ---------- 移动端触控 ----------
    handleTouchStart(e, i) {
      if (i !== this.index) return;
      this.startTouches = e.touches;
      if (e.touches.length === 2) {
        this.initialDistance = this.getDistance(e.touches);
      }
    },
    handleTouchMove(e, i) {
      if (i !== this.index) return;
      if (e.touches.length === 1 && this.startTouches.length === 1) {
        const dx = e.touches[0].clientX - this.startTouches[0].clientX;
        const dy = e.touches[0].clientY - this.startTouches[0].clientY;
        this.transform.offsetX = this.lastOffsetX + dx;
        this.transform.offsetY = this.lastOffsetY + dy;
      } else if (e.touches.length === 2) {
        const newDistance = this.getDistance(e.touches);
        const scaleFactor = newDistance / this.initialDistance;
        let newScale = this.lastScale * scaleFactor;
        this.transform.scale = Math.max(0.1, Math.min(3, newScale));
      }
    },
    handleTouchEnd(e, i) {
      if (i !== this.index) return;
      this.lastScale = this.transform.scale;
      this.lastOffsetX = this.transform.offsetX;
      this.lastOffsetY = this.transform.offsetY;
    },
    getDistance(touches) {
      const dx = touches[0].clientX - touches[1].clientX;
      const dy = touches[0].clientY - touches[1].clientY;
      return Math.sqrt(dx * dx + dy * dy);
    },
    handleWheel(e, i) {
      if (i !== this.index) return;
      const imgEl = this.currentImgEl;
      if (!imgEl || typeof imgEl.getBoundingClientRect !== 'function') return;

      const rect = imgEl.getBoundingClientRect();
      const offsetX = e.clientX - rect.left;
      const offsetY = e.clientY - rect.top;
      const ratioX = offsetX / rect.width;
      const ratioY = offsetY / rect.height;

      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      let newScale = this.transform.scale + delta;
      newScale = Math.max(0.1, Math.min(3, newScale));

      const dx = (newScale - this.transform.scale) * rect.width * (ratioX - 0.5);
      const dy = (newScale - this.transform.scale) * rect.height * (ratioY - 0.5);

      this.transform.offsetX -= dx;
      this.transform.offsetY -= dy;
      this.transform.scale = newScale;

      this.lastScale = newScale;
      this.lastOffsetX = this.transform.offsetX;
      this.lastOffsetY = this.transform.offsetY;
    },
    handleMouseDown(e, i) {
      if (i !== this.index || this.loading || e.button !== 0) return;

      const {offsetX, offsetY} = this.transform;
      const startX = e.pageX;
      const startY = e.pageY;
      this._dragHandler = rafThrottle(ev => {
        this.transform.offsetX = offsetX + ev.pageX - startX;
        this.transform.offsetY = offsetY + ev.pageY - startY;
      });
      on(document, 'mousemove', this._dragHandler);
      on(document, 'mouseup', () => {
        off(document, 'mousemove', this._dragHandler);
      });

      e.preventDefault();
    },
    reset() {
      this.transform = {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      };
    },
    toggleMode() {
      if (this.loading) return;

      const modeNames = Object.keys(Mode);
      const modeValues = Object.values(Mode);
      const index = modeValues.indexOf(this.mode);
      const nextIndex = (index + 1) % modeNames.length;
      this.mode = Mode[modeNames[nextIndex]];
      this.reset();
    },
    prev() {
      if (this.isFirst && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index - 1 + len) % len;
    },
    next() {
      if (this.isLast && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index + 1) % len;
    },
    handleActions(action, options = {}) {
      if (this.loading) return;
      const {zoomRate, rotateDeg, enableTransition} = {
        zoomRate: 0.2,
        rotateDeg: 90,
        enableTransition: true,
        ...options
      };
      const {transform} = this;
      switch (action) {
        case 'zoomOut':
          if (transform.scale > 0.2) {
            transform.scale = parseFloat(
              (transform.scale - zoomRate).toFixed(3)
            );
          }
          break;
        case 'zoomIn':
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
          break;
        case 'clocelise':
          transform.deg += rotateDeg;
          break;
        case 'anticlocelise':
          transform.deg -= rotateDeg;
          break;
      }
      transform.enableTransition = enableTransition;
    }
  }
};
</script>
