<template>
  <div @click.stop="">
    <slot>
    </slot>
    <fm-button v-if="!isSlotBtn" class="set-btn hover" @click.stop="navigateLine" ontouchstart>去导航</fm-button>
    <fm-action-sheet
      :visible.sync="isShowSheet"
      title="使用地图打开"
      :actions="sheetList"
      cancel-text="取消"
      description="如果点击无响应，可能是您还没有安装该APP"
      close-on-click-action
      @select="handleSheetSelect"
    />
  </div>
</template>

<script>
import { navigationWx, isWx, navToMap } from '@/utils/map.js';
import { ActionSheet, Button } from 'fawkes-mobile-lib'
export default {
  name: 'NavigateMap',
  props: {
    isSlotBtn: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {
    [Button.name]: But<PERSON>,
    [ActionSheet.name]: ActionSheet
  },
  data() {
    return {
      isShowSheet: false,
      sheetList: [
        {
          name: '百度地图',
          id: 1,
        },
        {
          name: '高德地图',
          id: 2,
        },
        {
          name: '腾讯地图',
          id: 3,
        }
      ]
    }
  },
  methods: {
    navigateLine() {
      isWx().then((res) => {
        if (res !== 'no-wx') {
          navigationWx(this.getLocation())
        } else {
          this.isShowSheet = true
        }
      })
    },
    handleSheetSelect(action) {
      navToMap(this.getLocation(), action.id)
    },
    getLocation() {
      const { endAddressSmx, endAddressSmy, endAddress, endAddressDetail } = this.data
      return {
        lat: +endAddressSmy, // 纬度
        lng: +endAddressSmx, // 经度
        name: endAddress, // 目的地名称
        address: endAddressDetail // 目的地详细地址
      }
    }
  }
}
</script>

<style lang="less" scoped>
/*button {*/
/*  width: 100%;*/
/*  height: 84px;*/
/*  border-radius: 50px;*/
/*  color: #333333;*/
/*  font-size: 34px;*/
/*  background: linear-gradient(90deg, #4DBFF8 0%, #3586C9 100%);*/
/*  color: #fff;*/
/*  border: 0;*/
/*}*/
//@import "../../modules/CarApply/components/index.less";
/deep/ .set-btn.fm-button {
  width: calc(100% - 32px);
  height: 88px;
  padding: 22px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 2px solid #3C83FF;
  font-size: 32px;
  font-weight: 500;
  color: #3C83FF;
  line-height: 44px;
  letter-spacing: 1px;
}
</style>
