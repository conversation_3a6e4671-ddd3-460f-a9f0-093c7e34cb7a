import { calcFileSize } from '@utils/file'

export default {
  methods: {
    getFileType(filename) {
      const ext = filename.split('.').pop().toLowerCase()

      const excelExt = ['xls', 'xlsx']
      const wordExt = ['doc', 'docx']
      const pdfExt = ['pdf']
      const imageExt = ['png', 'jpg', 'jpeg', 'gif', 'bmp']

      if (excelExt.includes(ext)) return 'excel'
      if (wordExt.includes(ext)) return 'word'
      if (pdfExt.includes(ext)) return 'pdf'
      if (imageExt.includes(ext)) return 'image'

      return 'unknown'
    },
    // 新增文件名截断方法
    truncateFileName(filename) {
      const maxLength = 30 // 可视区域最大字符数
      const ext = filename.split('.').pop() // 获取文件后缀

      // 分离主文件名和扩展名
      const basename = filename.slice(0, -(ext.length + 1))

      // 当文件名总长度不足时直接返回
      if (filename.length <= maxLength) return filename

      // 计算保留前后字符数（前6后4含扩展名）
      const frontChars = 6
      const endChars = 4

      // 构造截断后的文件名
      return `${basename.substr(0, frontChars)}...${basename.substr(-endChars)}.${ext}`
    },
    getFileSize(size) {
      return calcFileSize(size)
    }
  }
}
