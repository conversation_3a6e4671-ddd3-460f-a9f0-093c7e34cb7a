import storage from '@utils/storage'

export default {
  props: {
    formData: {
      type: Object,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    tipText: {
      type: String,
      default: '',
    },
    selectOptions: {
      type: Array,
      default: () => [],
    },
    inputName: {
      type: String,
    },
    inputUnit: {
      type: String
    },
    selectName: {
      type: String,
    },
    attachmentName: {
      type: String,
    },
  },
  data() {
    return {
      selectVal: '',
    }
  },
  computed: {
    showSelect() {
      // 分是否disable两个场景
      if (this.disabled) {
        // 判断是否选择了支付方式，有则展示，无则不显示
        return Number(this.formData[this.selectName]) > 0;
      } else {
        // 只要有下拉框选项就能展示
        return this.selectOptions.length > 0 && this.selectName
      }
    }
  },
  methods: {
    getDefaultPayment() {
      // 从localStorage读取上次保存的用户支付方式的偏好
      return storage.get(this.selectName) ? Number(storage.get(this.selectName)) : '';
    },
    handleSelectChange(val) {
      this.$set(this.formData, this.selectName, val)
      storage.set(this.selectName, val)
    },
  },
  watch: {
    formData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        const localSelectData = this.getDefaultPayment();
        const savedData = newVal[this.selectName];
        this.selectVal = savedData || localSelectData || '';
        if (!savedData && localSelectData) {
          this.$set(this.formData, this.selectName, localSelectData)
        }
      }
    }
  }
}
