<template>
  <div>
    <div class="flex col-center row-between">
      <fks-input
        v-if="inputName"
        v-model="formData[inputName]"
        :disabled="disabled"
        style="margin-right: 18px"
      >
        <template slot="append">
          {{ inputUnit || '' }}
        </template>
      </fks-input>
      <fks-form-item v-if="showSelect" :prop="selectName" style="margin-right: 18px">
        <fks-select
          v-model="selectVal"
          :disabled="disabled"
          class="full-width"
          style="min-width: 120px"
          placeholder="支付方式"
          clearable
          @change="handleSelectChange"
        >
          <fks-option
            v-for="item in selectOptions"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </fks-select>
      </fks-form-item>
      <fee-upload
        ref="feeUploadRef"
        v-model="formData[attachmentName]"
        v-bind="$attrs"
        :disabled="disabled"
        :tip-text="tipText"
        @handleExpand="handleExpand"
        @getFileInfo="getFileInfo"
        @onProgressFile="handleProgressFile"
        @file="$emit('file', $event)"
      />
    </div>
    <div v-if="previewListExpand && previewList.length > 0" class="preview-container flex">
      <div
        class="preview-item flex col-center row-between cursor-pointer position-relative"
        v-for="(item, index) in previewList"
        :key="item.fileToken"
        @click="handlePreview(item, index)"
      >
        <div class="flex">
          <svg-icon
            :icon-class="getFileType(item.name)"
            class-name="pc-file-icon"
            style="margin-right: 6px"
          />
          <div class="preview-info flex flex-column" style="width: 100px">
            <div class="title text-ellipsis">{{ item.name }}</div>
            <div class="file-size">{{ getFileSize(item.size) }}</div>
          </div>
        </div>
        <div
          v-if="!showProgressBar(item)"
          class="flex flex-column full-height"
          :class="[disabled ? 'row-end' : 'row-between']"
        >
          <fks-popover
            placement="top"
            width="160"
            v-show="!disabled"
            trigger="manual"
            v-model="item._visible"
            style="line-height: 16px"
          >
            <p>确认删除该附件？</p>
            <div style="text-align: right; margin: 0">
              <fks-button size="mini" type="text" @click="item._visible = false">取消</fks-button>
              <fks-button
                type="primary"
                size="mini"
                @click="
                  item._visible = false
                  deleteAttachment(item)
                "
                >确定
              </fks-button>
            </div>
            <i
              slot="reference"
              class="fks-icon-close"
              style="width: 16px; height: 16px; color: #cccccc"
              @click.stop="item._visible = true"
            />
          </fks-popover>
          <i
            class="fks-icon-download"
            style="width: 16px; height: 16px; color: #cccccc"
            @click.stop="downloadAttachment(item)"
          />
        </div>
        <!-- 进度条容器 -->
        <div
          v-if="showProgressBar(item)"
          :style="{ width: getWidth(item) }"
          style="
            position: absolute;
            bottom: 0;
            left: 0;
            height: 2px;
            background: #1890ff;
            border-radius: 1.5px;
          "
        />
      </div>
    </div>
    <fks-image-viewer
      v-if="showViewer"
      :url-list="picList"
      :initial-index="picIndex"
      :on-close="handleClose"
    />
    <fks-dialog
      v-if="!showViewer && previewDialogVisible"
      :visible.sync="previewDialogVisible"
      :show-fullscreen="true"
      :title="currentFile.name || currentFile.fileName"
      append-to-body
      class="dialog-8vh"
    >
      <div class="content-wrapper">
        <pre-view
          :file="currentFile"
          :key="currentFile.fileToken"
          style="flex: 1; min-height: 0"
        ></pre-view>
      </div>
    </fks-dialog>
  </div>
</template>

<script>
import FeeUpload from '@modules/FormCenter/components/FormUpload/FeeUpload.vue'
import inputAttachmentMixin from '@components/InputAttachment/inputAttachmentMixin'
import formStyleMixin from '@components/InputAttachment/formStyleMixin'
import PreView from '@components/PreView/index.vue'
import { downloadFile } from '@modules/FormCenter/api'
import download from '@utils/downloadFile'
import FksImageViewer from '@components/PicturePreview/FksImageViewer.vue'
import { getFile } from '@/api/file'

export default {
  name: 'input-attachment-pc-view',
  mixins: [inputAttachmentMixin, formStyleMixin],
  components: { PreView, FeeUpload, FksImageViewer },
  data() {
    return {
      previewList: [],
      previewListExpand: false,
      previewDialogVisible: false,
      currentFile: null,
      currentFileType: false,
      showViewer: false,
      picIndex: 0,
      files: [],
      extTypePic: ['jpg', 'jpeg', 'png'],
    }
  },
  computed: {
    picList() {
      return this.files.map(
        (file) => `${process.env.VUE_APP_BASE_URL}/sys-storage/download_image?f8s=${file.fileToken}`
      )
    },
  },
  methods: {
    showProgressBar(item) {
      return item.progress > 0 && item.progress < 100
    },
    getWidth(item) {
      return (item.progress || 0) + '%'
    },
    handleProgressFile({ progress, id }) {
      const item = this.previewList.find((item) => item.uid === id)
      if (item) {
        this.$set(item, 'progress', progress || 0)
        this.$forceUpdate()
      }
    },
    deleteAttachment(item) {
      const file = item.response ? item.response.data : item
      if (file.fileToken) {
        this.$refs.feeUploadRef.deleteAttachment(file.fileToken)
      }
    },
    downloadAttachment(item) {
      downloadFile(item.fileToken).then((res) => {
        download(item, res)
      })
    },
    handlePreview(item, index) {
      if (item.response) {
        this.currentFileType = item.type.startsWith('image/')
        this.currentFile = item.response.data
        this.picIndex = index;
      } else {
        // 检查 name 是否以 extTypePic 中的任一扩展名结尾
        this.currentFileType = this.extTypePic.some((ext) =>
          item.name.toLowerCase().endsWith(`.${ext}`)
        )
        this.currentFile = item
        this.picIndex = 0;
      }
      if (this.currentFileType) {
        this.$nextTick(() => {
          if (item.response) {
            const g9s = item.response.data.groupToken
            this.getFilesByG9s(g9s)
          } else {
            this.files = [item]
          }
          this.showViewer = true
        })
      } else {
        this.$nextTick(() => {
          this.previewDialogVisible = true
        })
      }
    },
    handleClose() {
      this.showViewer = false
    },
    getFilesByG9s(g9s) {
      getFile({ g9s: [g9s] }).then((res) => {
        if (res.status) {
          this.files = res.data
            .filter((item) => this.extTypePic.includes(item.extName.toLowerCase()))
            .map((item) => ({
              name: item.fileName,
              fileToken: item.fileToken,
              extName: item.extName,
            }))
        }
      })
    },
    handleExpand(fileList) {
      this.previewList = fileList.map((item) => {
        let obj = { _visible: false }
        // 由于上传时，item是一个File对象，File对象是不可迭代的，所以不能用解构语法，在此使用for in循环来复制属性
        for (const itemKey in item) {
          obj[itemKey] = item[itemKey]
        }

        return obj
      })
      this.previewListExpand = !this.previewListExpand
    },
    getFileInfo(fileList) {
      this.$emit('getFileInfo', fileList)
      this.previewList = fileList.map((item) => {
        let obj = { _visible: false }
        // 由于上传时，item是一个File对象，File对象是不可迭代的，所以不能用解构语法，在此使用for in循环来复制属性
        for (const itemKey in item) {
          obj[itemKey] = item[itemKey]
        }

        return obj
      })
      this.previewListExpand = true
    },
    validator(rule, value, callback) {
      // 如果输入框填写的值大于0，则必须要选择支付方式，否则可以不用选
      if (Number(this.formData[this.inputName]) > 0) {
        if (value) {
          callback()
        } else {
          callback(new Error('请选择支付方式'))
        }
      } else if (Number(this.formData[this.inputName]) === 0) {
        callback()
      }
    },
  },
}
</script>
<style lang="less" scoped>
@import './exclude/input-attachment-pc.css';
/* 全局覆盖对话框内容区域样式 */
/deep/ .fks-dialog__body {
  height: 100% !important;
  display: flex;
  flex-direction: column;
}

/deep/ .content-wrapper {
  height: calc(100vh - 350px) !important;
}

/deep/ .is-fullscreen {
  height: 100% !important;
  overflow: hidden !important; /* 禁用滚动 */

  .content-wrapper {
    height: 100vh !important;
  }
}
</style>
