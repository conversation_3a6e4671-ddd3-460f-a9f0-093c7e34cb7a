<template>
  <fm-collapse v-model="activeNames" class="input-attachment-mobile-collapse">
    <fm-collapse-item name="1">
      <div slot="title">
        <fm-field
          v-model="formData[inputName]"
          @click.native.stop
          input-align="right"
          error-message-align="right"
          placeholder="￥0.00"
          label-width="180px"
          clearable
          :readonly="disabled"
          :required="required"
          :rules="[{ validator, message: '请输入有效的数字' }]"
        >
          <template slot="label">
            <div class="flex col-center">
              <div>{{label}}</div>
              <fks-popover
                v-if="tooltip"
                :content="tooltip"
                placement="top-start"
                popper-class="cost1-popper-class"
                trigger="click"
              >
                <i slot="reference" class="fks-icon-info-outline" style="color: #999999;font-size: 16px" />
              </fks-popover>
            </div>
          </template>
          <template slot="suffix-icon">
            <img
              width="20px"
              height="20px"
              style="vertical-align: text-bottom; margin-left: 10px"
              :style="{ transform: activeNames.includes('1') ? 'rotate(0deg)' : 'rotate(-90deg)' }"
              :src="require('@/assets/img/downArrow-blue.svg')"
              @click="activeNames = activeNames.includes('1') ? [] : ['1']"
            />
          </template>
        </fm-field>
      </div>
      <div slot="right-icon"></div>
      <div class="collapse-el">
        <fm-field
          v-if="showSelect"
          :value="paymentStr"
          :required="required"
          :readonly="disabled"
          :placeholder="disabled ? '' : '请选择'"
          :rules="[{ validator: paymentValidator, message: '请选择支付方式' }]"
          label-width="100"
          label="支付方式"
          input-align="right"
          readonly
          error-message-align="right"
          style="margin-bottom: 12px"
          @click="handleClick"
        >
          <template slot="suffix-icon">
            <img
              v-if="!disabled"
              width="20px"
              height="20px"
              style="vertical-align: text-bottom; margin-left: 10px"
              :style="{ transform: showPopup ? 'rotate(0deg)' : 'rotate(-90deg)' }"
              :src="require('@/assets/img/downArrow-blue.svg')"
            />
          </template>
        </fm-field>
        <form-upload-mobile-new
          v-model="formData[attachmentName]"
          :disabled="disabled"
          :accept="accept"
        />
      </div>
    </fm-collapse-item>
    <select-picker
      :show.sync="showPopup"
      :column="selectOptions"
      :code.sync="formData[selectName]"
      :is-enum="false"
      value-code="key"
      @confirm="handleSelectChange"
    />
  </fm-collapse>
</template>

<script>
import { isDefined } from '@utils/types'
import inputAttachmentMixin from '@components/InputAttachment/inputAttachmentMixin'
import SelectPicker from '@modules/FormCenter/components/SelectPicker/index.vue'
import FormUploadMobileNew from '@modules/FormCenter/components/FormUpload/mobile-view-new.vue'

export default {
  name: 'input-attachment-mobile-view',
  components: { FormUploadMobileNew, SelectPicker },
  mixins: [inputAttachmentMixin],
  props: {
    label: {
      type: String,
    },
    required: {
      type: Boolean,
      default: false,
    },
    tooltip: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      activeNames: ['1'],
      showPopup: false,
      // accept: 'image/*,application/pdf',
      // 使用 image/* 在不同手机浏览器中唤醒方式不同，会优先唤醒相册而不是文件系统
      accept:".jpg,.jpeg,.png,.JPG,.JPEG,.pdf",
    }
  },
  computed: {
    paymentStr() {
      if (this.selectVal) {
        return this.selectOptions.find((item) => Number(item.key) === Number(this.selectVal)).value
      } else {
        return ''
      }
    },
  },
  methods: {
    handleClick() {
      if (!this.disabled) {
        this.showPopup = !this.showPopup
      }
    },
    validator(value) {
      return isDefined(value) && /^(?:[1-9]\d*(?:\.\d{1,2})?|0(?:\.\d{1,2})?)$/.test(value)
    },
    paymentValidator(value) {
      if (Number(this.formData[this.inputName]) > 0) {
        return isDefined(value)
      } else {
        return true
      }
    },
  },
  watch: {
    disabled(newVal) {
      if (newVal) {
        this.showPopup = false
      }
    }
  }
}
</script>

<style scoped lang="less">
:deep(.fm-collapse-item__title.fm-cell) {
  padding: 0 !important;
  display: flex;
  align-items: center;
}
:deep(.fm-collapse-item__content) {
  padding: 26px 23px !important;
  background: #f4f7fd;
}

:deep(.collapse-el .fm-cell--required::before) {
  left: 10px !important;
}

:deep(.collapse-el .fm-cell) {
  padding-right: 24px !important;
  border-radius: 8px;
}

:deep(.fm-cell__title.fm-field__label) {
  white-space: nowrap !important;
}
</style>
<style>
.cost1-popper-class {
  padding: 5PX 10PX;
}
</style>
