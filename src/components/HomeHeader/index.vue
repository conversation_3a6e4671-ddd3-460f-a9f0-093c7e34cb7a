<template>
  <div class="nav-menu" style="z-index:100; box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.03)">
    <div class="header" style="user-select: none;">
      <img :src="require('@/assets/img/homepage/new-logo.png')" width="43px" height="26px"
           style="border-radius: 6px;object-fit: contain; padding-top: 1px"/>
<!--      <div class="divider" />-->
      <span style="font-size: 19px; padding-bottom: 1px" class="second-title">华智车管</span>
      <portal-switch style="margin-left: 46px;flex-shrink: 0" />
    </div>
    <div class="right">
      <div class="infos">
<!--        帮助中心-->
        <HelpCenter></HelpCenter>
<!--        <div class="feedback" @click="jumpToOpManual" style="border-radius: 6px; margin-right: 24px; user-select: none;">-->
<!--          <img :src="require('@/assets/img/searchbar/option.svg')"-->
<!--               style="width: 16px; height: 16px; object-fit: contain; margin: 0 4px;" />-->
<!--          <span>操作手册</span>-->
<!--        </div>-->
<!--        <div class="feedback" @click="jumpToFeedBack" style="border-radius: 6px; margin-right: 8px; user-select: none;">-->
<!--          <img :src="require('@/assets/img/searchbar/question.svg')"-->
<!--               style="width: 16px; height: 16px; object-fit: contain; margin: 0 4px;" />-->
<!--          <span>问题反馈</span>-->
<!--        </div>-->
        <img v-if="avatarUrl" :src="avatarUrl"/>
        <div v-else class="avatar avatar-text">
          {{ userFullName | getFamilyName }}
        </div>
        <div class="username" @click="visible = true">
          <span>
            {{ userFullName }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PortalSwitch from "@components/HomeHeader/components/portal-switch.vue";
import avatarMixin from "@/mixins/avatarMixin";
import HelpCenter from '@components/HelpCenter/HelpCenter.vue'
import { getFeedBackLink, getOpManualLink } from '@/api/carApply'

export default {
  name: "HomeHeader",
  mixins: [avatarMixin],
  components: {PortalSwitch, HelpCenter},
  data() {
    return {
      visible: false,
    }
  },
  methods: {
    // 跳转到操作手册
    jumpToOpManual() {
      getOpManualLink().then(res => {
        window.open(res.data, '_blank');  // _blank 表示在新窗口或标签页中打开
      })
    },
    jumpToFeedBack() {
      getFeedBackLink().then(res => {
        window.open(res.data, '_blank');  // _blank 表示在新窗口或标签页中打开
      })
    }
  }
}
</script>

<style lang="less" scoped>
.avatar {
  width: 82px;
  height: 82px;
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.12);
  margin-left: 10px;
}

.avatar-text {
  line-height: 82px;
  text-align: center;
  font-size: 40px;
  color: #fff;
  background-color: #4545d1;
}
.nav-menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.0301);

  .header {
    display: flex;
    align-items: center;

    .divider {
      width: 4px;
      height: 40px;
      background-color: #00000014;
      margin-left: 10px;
      margin-right: 12px;
    }

    span {
      display: inline-block;
      margin: 0 20px;
      color: #000000;
      line-height: 42px;
      text-align: left;
      font-style: normal;
      white-space: nowrap; /* 禁止换行 */
    }

    i {
      font-size: 64px;
      display: inline-block;
      cursor: pointer;
    }
  }
  .right {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    .infos {
      display: flex;
      align-items: center;
      font-size: 32px;
      margin: 0 20px;

      .feedback {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        cursor: pointer;
        margin-right: 20px;
        span {
          white-space: nowrap; /* 禁止换行 */
          display: inline-block;
        }
      }
      .username {
        cursor: pointer;

        i {
          display: inline;
          margin-left: 10px;
          width: 36px;
          height: 36px;
        }
      }

      img {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        margin-left: 20px;
        margin-right: 20px;
      }
    }


  }
}
</style>
