<template>
  <div v-if="isPC" class="portal-switch-container">
    <fks-radio-group v-model="secondLevelPortalName" @change="handleChange">
      <fks-radio
        v-for="portal in portalRadios"
        :label="portal.name"
        :key="portal.id"
      >
        {{ portal.name }}
      </fks-radio>
    </fks-radio-group>
    <fks-popover
      v-if="showSelect"
      ref="popoverRef"
      placement="bottom"
      trigger="click"
      popper-class="portal-select-popover"
      width="540"
      @show="handlePopperShow"
      @hide="handlePopperHide"
    >
      <div class="portal-select-options flex flex-column full-height overflow-y-auto overflow-x-hidden">
        <div
          class="recent-projects"
          style="border-bottom: 1px solid #F1F1F0"
        >
          <template v-if="favoriteProjects.length">
            <div style="font-size: 12px;margin-bottom: 6px;padding-left: 24px" class="title">收藏项目</div>
            <div class="project-list">
              <div
                class="project-item"
                v-for="item in favoriteProjects"
                :key="item.id"
                style="padding: 6px 0 6px 10px;white-space: normal"
                @click="handleNodeClick(item)"
              >
                <div class="project-name">
                  <div style="padding-left: 32px">{{ item.name }}</div>
                  <div class="collect-icon" @click.stop="handleCollectClick(item)">
                    <i v-if="!item.isCollect" class="fks-icon-star-off" style="font-size: 15px"></i>
                    <i v-else class="fks-icon-star-on" style="color: #FEC73C;font-size: 16px"></i>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <div style="font-size: 12px;margin-bottom: 6px;padding-left: 24px" class="title">近6个月常用项目</div>
          <div class="project-list">
            <div
              class="project-item"
              v-for="project in recentlyUsedProjects"
              :key="project.id"
              style="padding: 6px 0 6px 10px;white-space: normal"
              @click="handleRecentClick(project.id)"
            >
              <div class="project-name">
                <div style="padding-left: 32px">{{ project.name }}</div>
              </div>
            </div>
          </div>
        </div>
        <fks-tree
          ref="portalTreeRef"
          :data="groupedThirdPortalTreeData"
          :default-expanded-keys="defaultExpandKeys"
          :props="{
            children: 'children',
            label: 'name',
            value: 'id'
          }"
          node-key="id"
          highlight-current
          class="portal-tree"
          @node-click="handleNodeClick"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse"
        >
          <template slot-scope="{ data }">
            <div v-if="data.id" class="portal-leaf-content">
              <div class="portal-leaf">
                {{data.name}}
              </div>
              <div class="portal-leaf-icon" @click.stop="handleCollectClick(data)">
                <i v-if="!data.isCollect" class="fks-icon-star-off" style="color: #999999;font-size: 15px"></i>
                <i v-else class="fks-icon-star-on" style="color: #FEC73C;font-size: 16px"></i>
              </div>
            </div>
            <div v-else class="portal-node">{{data.name}}</div>
          </template>
        </fks-tree>
      </div>
      <div slot="reference" style="height: 26px">
        <fks-tooltip
          :disabled="disableTooltip"
          :content="inputVal"
          placement="bottom" effect="light"
          popper-class="portal-select-tooltip"
        >
          <div class="full-height">
            <fks-input
              ref="inputRef"
              v-model="inputVal"
              :style="{width: inputWidth}"
              class="portal-select"
              style="max-width: 300px"
              size="mini"
              placeholder="请输入项目"
              @input="searchVal = inputVal"
            >
              <i slot="suffix" class="fks-icon-arrow-down" />
            </fks-input>
          </div>
        </fks-tooltip>
      </div>
    </fks-popover>
  </div>
  <div v-else-if="isMobile" class="portal-infos">
    <div class="title">门户信息</div>
    <div class="info">
      <div class="info-item">
        <span>二级门户</span>
      </div>
      <div class="info-value">
        <span>{{ secondLevelPortalName }}</span>
        <!--        <i class="fks-icon-arrow-right"></i>-->
      </div>
    </div>
    <div class="info project" v-if="showSelect">
      <div class="info-item">
        <span>项目</span>
      </div>
      <div class="info-value flex col-center row-end" @click="showProjectPicker = true">
        <span>{{ currentPortalName }}</span>
        <i class="fks-icon-arrow-right"></i>
      </div>
    </div>
    <select-picker
      :show.sync="showProjectPicker"
      :is-enum="false"
      :column="mobileThirdLevelPortals"
      @confirm="handleSelectChange"
    />
  </div>
</template>

<script>
import {mapActions, mapMutations, mapState} from 'vuex';
import * as StateTypes from "@store/State/stateTypes";
import * as ActionTypes from "@store/Action/actionTypes"
import * as mutationTypes from "@store/Mutation/mutationTypes";
import {COMPANY_PORTAL, PROJECT_PORTAL} from "@utils/constants";
import platform from "@/mixins/platform";
import SelectPicker from "@modules/FormCenter/components/SelectPicker/index.vue";
import portalSwitchMixin from "@/mixins/portalSwitchMixin";

export default {
  name: "portal-switch",
  components: {SelectPicker},
  mixins: [platform, portalSwitchMixin],
  data() {
    return {
      secondLevelPortalName: null,
      showPicker: false,
      showProjectPicker: false,
      searchVal: '',
      inputVal: '',
      isOverflow: false,
      popperVisible: false,
      defaultExpandKeys: []
    }
  },
  computed: {
    disableTooltip() {
      // 当文字没有溢出，或者弹窗已经打开时，禁止tooltip
      return !this.isOverflow || this.popperVisible
    },
    inputWidth() {
      return this.inputVal.length * 12 + 40 + 'px';
    },
    showSelect() {
      return this[StateTypes.SECOND_LEVEL_PORTAL] === PROJECT_PORTAL && (this.currentPortalId !== this[StateTypes.DATA_BASE_PORTAL].companyPortalId);
    },
    dataBasePortals() {
      const data_base_portal = this[StateTypes.PORTALS].find(item => item.id === this[StateTypes.DATA_BASE_PORTAL].companyPortalId);
      if (data_base_portal) {
        return [data_base_portal]
      } else {
        return []
      }
    },
    portalRadios() {
      return this.dataBasePortals.concat(this[StateTypes.PORTALS].filter(item => (item.name === PROJECT_PORTAL)));
    },
    mobileThirdLevelPortals() {
      return this.thirdLevelPortals.map(item => ({code: item.id, value: item.name, key: item.id}))
    }
  },

  methods: {
    ...mapMutations([mutationTypes.SET_SECOND_LEVEL_PORTAL, mutationTypes.SET_PROJECT_CLOSED, mutationTypes.SET_OLD_PROJECT_PORTAL, mutationTypes.SET_VD_PROJECT_INFO]),
    ...mapActions([ActionTypes.CHANGE_PORTAL, ActionTypes.GET_RECENTLY_USED_PORTALS]),
    handleNodeExpand(data) {
      // 任选该部门下的一个三级门户，放在defaultExpandKeys中, 用于展开该部门
      if (data.children && data.children.length) {
        this.defaultExpandKeys.push(data.children[0].id);
      }
    },
    handleNodeCollapse(data) {
      if (data.children && data.children.length) {
        // 在defaultExpandKeys中寻找该部门下的一个三级门户，将其移除
        const i = this.defaultExpandKeys.findIndex(item => {
          return data.children.find(child => child.id === item) ;
        })
        if (i > -1) {
          this.defaultExpandKeys.splice(i, 1);
        }
      }
    },
    filterMethod(searchVal) {
      this.searchVal = searchVal;
    },
    handlePopperShow() {
      this.popperVisible = true;
      // 设置选中项
      this.$nextTick(() => {
        this.$refs.portalTreeRef.setCurrentKey(this.currentPortalId);
      })
    },
    handlePopperHide() {
      // 当弹框关闭时，将inputVal设置为当前选中项的名称
      this.popperVisible = false;
      this.inputVal = this.currentPortalName;
    },
    handleChange(val) {
      // 如果切换到项目门户，则需要将门户切换为项目门户下的三级门户
      let portal;
      if (val === PROJECT_PORTAL) {
        if (this[StateTypes.OLD_PROJECT_PORTAL]) {
          portal = JSON.parse(JSON.stringify(this[StateTypes.OLD_PROJECT_PORTAL]))
        } else {
          // 从收藏项目中寻找
          if (this.favoriteProjects.length > 0) {
            portal = this.favoriteProjects[0];
          } else if (this.recentlyUsedProjects.length > 0) {
            // 从常用项目中寻找
            portal = this.recentlyUsedProjects[0];
          } else {
            // 从三级门户中寻找
            portal = this[StateTypes.PORTALS].filter(item => item.parentName === PROJECT_PORTAL)[0]
          }
        }
      } else {
        // 切换到数据舱
        this[mutationTypes.SET_OLD_PROJECT_PORTAL](JSON.parse(JSON.stringify(this[StateTypes.PORTAL])))
        portal = this[StateTypes.PORTALS].find(item => item.id === this[StateTypes.DATA_BASE_PORTAL].companyPortalId);
      }
      this[ActionTypes.CHANGE_PORTAL](portal);
      this.$router.replace({path: "/distribute"});
    },

  },
  mounted() {
    this.secondLevelPortalName = this.showSelect ? this[StateTypes.SECOND_LEVEL_PORTAL] : this[StateTypes.PORTAL].name;
    this.inputVal = this.currentPortalName;
    // 每次切换项目后需要更新常用项目列表
    this[ActionTypes.GET_RECENTLY_USED_PORTALS]();
  },
  watch: {
    inputVal() {
      // 检查输入框是否文字溢出
      setTimeout(() => {
        const el = this.$refs.inputRef.$el.querySelector('.fks-input__inner');
        if (el) {
          const scrollWidth = el.scrollWidth;
          const clientWidth = el.clientWidth;
          this.isOverflow = scrollWidth > clientWidth;
        }
      })
    }
  }
}
</script>
<style>
.portal-select-tooltip {
  border: none !important;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2) !important;

  .popper__arrow {
    border-style: none !important;
  }
}
</style>
<style lang="less">
.fks-tree-node__content {
  &:hover {
    background: rgba(220, 228, 250, 0.5);
  }
}
.fks-tree-node.is-current:focus > .fks-tree-node__content{
  background-color: rgba(220, 228, 250, 0.5) !important;
}

.fks-tree--highlight-current .fks-tree-node.is-current > .fks-tree-node__content {
  background-color: rgba(220, 228, 250, 0.5) !important;
}

.fks-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.fks-icon-arrow-down {
  font-size: 24px;
}

.portal-select-popover {
  height: 1000px;
}

.portal-tree {
  .portal-leaf-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    .portal-leaf-icon {
      width: 1rem;
      z-index: 2;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .portal-leaf {
    color: #555555;
    white-space: normal;
    flex: 1;
  }
  .portal-node {
    color: #191919;
    white-space: normal;
  }
}
</style>
<style lang="less" scoped>
/deep/ .fks-input__prefix {
  position: relative;
  box-sizing: border-box;
  border: 1px solid #ffffff00;
  padding: 0 56px;
  height: 56px;
  line-height: 56px;
  color: #606266;
  left: 0;
  visibility: hidden;
}
/deep/ .fks-radio__label {
  font-size: 26px !important;
}
/deep/ .fks-checkbox, .fks-radio {
  margin-right: 48px !important;
  .fks-radio__input.is-checked .fks-radio__inner {
    color: #3C83FF !important;
  }
}

/deep/ .fks-tree.portal-tree {
  .fks-tree-node__expand-icon {
    color: rgba(0, 0, 0, 0.65);
  }

  .fks-tree-node__content {
    height: unset;
    padding-top: 12px;
    padding-bottom: 12px;
  }
}


/deep/ .fks-input__inner {
  position: absolute;
  padding-left: 30px !important;
}

/deep/ .fks-input.fks-input--mini.portal-select {
  .fks-input__inner {
    top: -16px;
  }
}

.portal-switch-container {
  display: flex;
  align-items: center;

  .portal-select {
    min-width: 300px;
    text-align: start;
  }

}

.recent-projects {
  .title {
    color: #999999;
  }
  .project-name {
    color: #555555;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .collect-icon {
      width: 1rem;
      z-index: 2;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .project-item {
    &:hover {
      cursor: pointer;
      background: rgba(220, 228, 250, 0.5);
    }
    &:last-child {
      margin-bottom: 24px;
    }
  }
}

.portal-infos {
  margin: 36px 60px;

  .title {
    font-size: 32px;
    font-weight: bold;
    color: #191919;
    margin-bottom: 30px;
  }

  .info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    &.project {
      display: grid;
      grid-template-columns: 20% 80%;
    }

    .info-item {
      display: flex;
      align-items: center;

      span {
        display: inline-block;
        margin-left: 10px;
        letter-spacing: 2px;
        font-size: 28px;
        color: rgba(25, 25, 25, 0.6);
      }
    }

    .info-value {
      font-size: 28px;
      color: #191919;
      letter-spacing: 2px;
    }
  }
}
</style>
