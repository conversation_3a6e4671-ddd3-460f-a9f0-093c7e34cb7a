import request from '@/utils/request'

// 获取用户头像

export function getAvatar(userName) {
  return request({
    url: '/vehicle-dispatch/vd/auth/user/avatar',
    params: {userName}
  })
}

export function getProjectStatus(projectId) {
  return request({
    url: '/vehicle-dispatch/vd/project/infoByPortal',
    params: { portalId: projectId },
  });
}

export function getDataBasePortalId() {
  return request({
    url: '/vehicle-dispatch/vd/system/info'
  })
}

// 获取当前用户门户列表
export function getCurrentUserPortalList() {
  return request({
    url: '/vehicle-dispatch/vd/user/portals',
    method: 'get'
  })
}

// 获取常用项目列表
export function getCommonlyUsedProjectList() {
  return request({
    url: '/vehicle-dispatch/vd/user/portals/commonly',
    method: 'get'
  })
}

// 记录项目切换
export function recordProjectSwitch(portalId) {
  return request({
    url: '/vehicle-dispatch/vd/user/portals/useRecord',
    method: 'post',
    params: { portalId }
  })
}
