<template>
  <fks-table
    :data="data"
    :header-cell-style="{
      background: 'transparent',
      color: '#333333aa !important',
      fontWeight: 'unset !important',
    }"
    :cell-style="{ color: '#333333 !important' }"
  >
    <fks-table-column
      header-align="left"
      align="left"
      prop="taskName"
      label="节点名称"
      width="150"
    />
    <fks-table-column header-align="left" align="left" prop="assigneeName" label="审批人" />
    <fks-table-column
      header-align="left"
      align="left"
      prop="approveStateName"
      label="审批结果"
      width="100"
    >
      <template slot-scope="{ row }">
        <card-tag
          style="line-height: 12px"
          :tag="{ text: processText(row.approveStateName), color: getTagColor(row) }"
        />
      </template>
    </fks-table-column>
    <fks-table-column header-align="left" align="left" prop="comment" label="审批意见">
      <template slot-scope="{ row }">
        <overflow-tooltip v-if="row.comment" :text="row.comment" />
      </template>
    </fks-table-column>
    <fks-table-column
      header-align="left"
      align="left"
      prop="approveDate"
      label="审批时间"
      width="180px"
    >
      <template slot-scope="{ row }">
        <span v-if="row.approveDate">{{ $dayjs(row.approveDate).format('YYYY-MM-DD HH:mm') }}</span>
      </template>
    </fks-table-column>
  </fks-table>
</template>

<script>
import CardTag from '@components/CardFlow/components/tag.vue'
import OverflowTooltip from '@components/OverflowTooltip/index.vue'

export default {
  name: 'FlowTable',
  components: { OverflowTooltip, CardTag },
  props: ['data'],
  methods: {
    getTagColor(row) {
      const text = row.approveStateName
      switch (text) {
        case '提交':
          return '#9BA0A3'
        case '通过':
          return '#03BE8A'
        case '待办':
          return '#3C83FF'
        case '退回':
          return '#FF3F4C'
        case '委托':
          return '#FFA01E'
        case '变更':
          return '#FFA01E'
        default:
          return '#3C83FF'
      }
    },
    processText(text) {
      if (text === '委托') {
        return '变更'
      }
      return text
    },
  },
}
</script>
