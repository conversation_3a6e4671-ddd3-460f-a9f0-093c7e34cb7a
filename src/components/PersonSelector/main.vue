<template>
  <fks-autocomplete
    popper-class="my-autocomplete"
    v-model="searchValue"
    :disabled="readOnly"
    :loading="loading"
    :popper-append-to-body="false"
    :fetch-suggestions="querySearch"
    :trigger-on-focus="false"
    placeholder="请输入姓名"
    class="full-width"
    @select="handleChange"
    @focus="handleFocus"
  >
    <template slot-scope="{ item }">
      <div class="option">
        <div class="user">
          <img :src="item.avatar.avatar_origin" />
          <span>{{ item.name }}</span>
        </div>
        <span class="dept">{{ item.deptName }}</span>
      </div>
      <div
        disabled
        v-if="finished && scrolled"
        style="display: flex; justify-content: center; align-items: center"
      >
        <span style="color: #8492a6; font-size: 12px; text-align: center">加载到底了</span>
      </div>
    </template>
  </fks-autocomplete>
</template>

<script>
// 滚动加载更多功能由于凤翎组件限制，不能实现
import { mapActions, mapMutations, mapState } from 'vuex'
import { APPLY_RESOURCE, OPEN_ID } from '@store/State/stateTypes'

export default {
  name: 'NewPersonSelector',
  props: {
    currentKey: {
      type: String,
      default: '',
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
    initialValue: {
      type: String,
    },
    appendToBody: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // searchValue: '',
      loading: false,
      list: [],
      pageToken: '',
      isNotFeishuUser: false,
      focusEmitTrigger: false,
      queryVal: '', // 用户输入的内容，在滚动加载时需要使用到
      finished: false, // 表示联系人列表是否搜索完，搜索完后不再进行接口搜索
      scrolled: false, // 监测用户是否滚动加载过
      selectId: '',
      isSelecting: false,
    }
  },
  computed: {
    ...mapState('CarApply', ['driverList', 'currUser']),
    ...mapState([OPEN_ID, APPLY_RESOURCE]),
    currentFeishuUser() {
      return this.list.find((item) => item.open_id === this.selectId)
    },
    openId() {
      const thirdPartyUser = this.$storage.getObject('thirdPartyUser')
      return thirdPartyUser['tpOpenId']
    },
    searchValue: {
      get: function () {
        return this.initialValue
      },
      set: function (val) {
        this.$emit('update:initialValue', val)
      },
    },
  },
  methods: {
    ...mapMutations('CarApply', ['SET_APPLY_CAR']),
    ...mapActions('CarApply', ['getApplyPerson']),
    ...mapActions('FeiShu', ['getFeiSuUserInfo']),
    searchMore() {
      this.scrolled = true
      this.getContacts(this.queryVal, false)
    },
    handleFocus() {
      this.$nextTick(() => {
        this.$emit('focus')
      })
    },
    handleChange(val) {
      this.isSelecting = true // 设置标记
      // this.searchValue = val.name;
      this.selectId = val.open_id
      this.$emit('update:initialValue', val.name)
      // 如果是飞书用户，那么搜索该用户的具体信息
      if (!this.isNotFeishuUser) {
        this.getFeiSuUserInfo({
          openId: this.openId,
          searchOpenId: this.currentFeishuUser.open_id,
        }).then((res) => {
          if (res.status) {
            this.$emit('select', res.data)
            const data = {
              id: this.currentFeishuUser.id,
              ucPersonFullName: res.data.user.name,
              ucPersonOpenId: res.data.user.openId,
              ucPersonUnionId: res.data.user.unionId,
              ucPersonPhone: res.data.user.mobile.split('+86')[1],
              ucPersonResource: 1,
              ucPersonUserName: res.data.user.en_name,
              currentKey: this.currentKey,
            }
            this.$emit('closePopup', data)
          }
        })
      }
    },
    async querySearch(val, cb) {
      // 默认把用户输入的内容作为最终结果，如果后续查询到飞书用户，则用后者去覆盖前者
      const payload = {
        ucPersonFullName: val,
        ucPersonResource: 3,
        currentKey: this.currentKey,
      }
      this.$emit('closePopup', payload)
      await this.getContacts(val)
      cb(this.list)
    },
    async getContacts(val, resetPageToken = true) {
      this.queryVal = val
      // 用户重新输入后，scrolled状态需要重置
      resetPageToken && (this.scrolled = false)
      // 用户在滚动加载时，如果数据加载完则停止搜索
      if (!resetPageToken && this.finished) {
        // 停止搜索
        return
      } else {
        this.finished = false
      }
      const params = {
        openId: this.openId, // 飞书openId
        pageSize: 15,
        pageToken: resetPageToken ? '' : this.pageToken, // 分页标识，获取首页不需要填写，获取下一页时传入上一页返回的分页标识值。请注意此字段的值并没有特殊含义，请使用每次请求所返回的标识值
        userName: this.currUser.userName, // 凤翎userName
        query: val, // 要执行搜索的字符串，一般为用户名
      }
      resetPageToken && (this.loading = true)
      const res = await this.getApplyPerson(params)
      resetPageToken && (this.loading = false)
      this.isNotFeishuUser = false
      // 滚动加载的数据需要去重再合并，搜索数据直接赋值即可
      if (resetPageToken) {
        this.list = res.data.users
      } else {
        this.list = Array.from(new Set(this.list.concat(res.data.users)))
      }
      this.pageToken = res.data.page_token
      if (res.data.has_more === false) {
        // 停止搜索
        this.finished = true
      }
      // 如果没有找到联系人，则将用户输入做为最终结果
      if (res.data.users.length === 0) {
        this.isNotFeishuUser = true
      }
    },
  },
  watch: {
    initialValue: {
      immediate: true,
      handler(newVal) {
        newVal && (this.searchValue = newVal)
      },
    },
  },
}
</script>

<style lang="less" scoped>
/deep/ .my-autocomplete {
  width: auto !important;
  min-width: 4.46rem;
}

.option {
  display: flex;
  align-items: center;
  justify-content: space-between;

  span {
    display: inline-block;
  }

  .user {
    display: flex;
    align-items: center;

    img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 10px;
    }
  }

  .dept {
    font-size: 24px;
    margin-left: 5px;
  }
}
</style>
