<template>
  <fks-select
    v-model="searchValue"
    v-load-more-select="searchMore"
    :loading="loading"
    :remote-method="remoteMethod"
    :placeholder="type === 'view' ? '' : placeholder"
    class="full-width"
    clearable
    filterable
    remote
    reserve-keyword
    @change="handleChange"
    @focus="handleFocus"
  >
    <fks-option
      v-for="item in options"
      :key="item[valueName]"
      :label="item[labelName]"
      :value="item[valueName]"
    />
    <div v-if="loading" class="flex col-center row-center">
      <i class="fks-icon-loading" />
    </div>
    <fks-option
      v-if="finished && scrolled"
      class="flex col-center row-center"
      disabled
      value=""
    >
      <span style="color: #8492a6; font-size: 12px; text-align: center">加载到底了</span>
    </fks-option>
  </fks-select>
</template>

<script>
export default {
  name: 'SearchSelector',
  props: {
    val: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'edit'
    },
    placeholder: {
      type: String
    },
    apiMethod: {
      type: Function,
      required: true
    },
    // propName 表示用于接口查询时，输入值所代表的属性名
    propName: {
      type: String,
      required: true
    },
    labelName: {
      type: String,
      required: true
    },
    valueName: {
      type: String,
      required: true
    },
    // params 表示接口调用的携带的额外参数
    params: {
      type: Object
    }
  },
  data() {
    return {
      currentItem: null,
      pageNo: 1,
      options: [],
      searchValue: '',
      queryValue: '', // 用户输入的值
      loading: false,
      finished: false, // 表示是否搜索完，搜索完后不再进行接口搜索
      scrolled: false // 监测用户是否滚动加载过
    };
  },
  methods: {
    handleFocus() {
      const val = this.currentItem ? this.currentItem[this.labelName] : ''
      this.remoteMethod(val, true);
    },
    searchMore() {
      this.scrolled = true;
      this.remoteMethod(this.queryValue, false);
    },
    handleChange(val) {
      this.$emit('update:val', val);
      const item = this.options.find(item => item[this.valueName] === val);
      this.currentItem = item;
      this.$emit('change', item);
    },
    // 远程搜索
    async remoteMethod(newValue, resetPageToken = true) {
      this.queryValue = newValue;
      // 用户重新输入后，scrolled状态需要重置
      resetPageToken && (this.scrolled = false);
      // 用户在滚动加载时，如果数据加载完则停止搜索
      if (!resetPageToken && this.finished) {
        // 停止搜索
        return;
      } else {
        this.finished = false;
      }
      resetPageToken && (this.loading = true);
      this.pageNo = resetPageToken ? 1 : ++this.pageNo;
      const res = await this.apiMethod({pageNo: this.pageNo, pageSize: 8, [this.propName]: newValue, ...this.params})
      resetPageToken && (this.loading = false);
      // 滚动加载的数据需要去重再合并，搜索数据直接赋值即可
      if (resetPageToken) {
        this.options = res.data.list;
      } else {
        this.options = this.uniqueFunc(Array.from(new Set(this.options.concat(res.data.list))), [this.valueName]);
      }
      if (res.data.isLastPage) {
        // 停止搜索
        this.finished = true;
      }
    },
    uniqueFunc(arr, uniId) {
      const res = new Map();
      return arr.filter(
        (item) => !res.has(item[uniId]) && res.set(item[uniId], 1)
      );
    }
  },
  created() {
    if (this.val && this.type === 'edit') {
      this.searchValue = this.val;
    }
  },
  watch: {
    val(newVal) {
      if (!newVal) {
        this.searchValue = '';
        this.currentItem = null;
      }
    }
  }
};
</script>

<style scoped lang="scss"></style>
