<template>
  <fks-select
      v-model="searchValue"
      v-loadmore="searchMore"
      :disabled="readOnly"
      :loading="loading"
      :remote-method="getContacts"
      :popper-append-to-body="appendToBody"
      filterable
      placeholder="请输入姓名"
      remote
      reserve-keyword
      class="full-width"
      @change="handleChange"
      @focus="$emit('focus')"
  >
    <fks-option
        v-for="item in list"
        :key="item.user_id"
        :value="item.user_id"
        :label="item.name"
    >
      <div class="option">
        <div class="user">
          <img :src="item.avatar.avatar_origin">
          <span>{{ item.name }}</span>
        </div>
        <span class="dept">{{ item.deptName }}</span>
      </div>
    </fks-option>
    <fks-option disabled v-if="finished && scrolled" style="display: flex;justify-content: center;align-items: center">
      <span style="color: #8492a6; font-size: 12px;text-align: center">加载到底了</span>
    </fks-option>
  </fks-select>
</template>

<script>
import {mapActions, mapMutations, mapState} from "vuex";
import {APPLY_RESOURCE, OPEN_ID} from "@store/State/stateTypes";
export default {
  name: 'PersonSelector',
  props: {
    readOnly: {
      type: Boolean,
      default: false
    },
    initialValue: {
      type: String
    },
    appendToBody: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchValue: '',
      loading: false,
      list: [],
      pageToken: '',
      isNotFeishuUser: false,
      queryVal: '', // 用户输入的内容，在滚动加载时需要使用到
      finished: false, // 表示联系人列表是否搜索完，搜索完后不再进行接口搜索
      scrolled: false // 监测用户是否滚动加载过
    }
  },
  computed: {
    ...mapState('CarApply', ['driverList', 'currUser']),
    ...mapState([OPEN_ID, APPLY_RESOURCE]),
    currentFeishuUser(){
      return this.list.find(item => item.user_id === this.searchValue)
    },
    openId(){
      const openId = this.currUser.vdThirdPartyUserList.find(item => item.tpUserType === 1)['tpOpenId'];
      return openId
    }
  },
  methods: {
    ...mapMutations('CarApply', ['SET_APPLY_CAR']),
    ...mapActions('CarApply', ['getApplyPerson']),
    ...mapActions('FeiShu', ['getFeiSuUserInfo']),
    searchMore(){
      this.scrolled = true
      this.getContacts(this.queryVal, false)
    },
    handleChange(val) {
      // 如果是飞书用户，那么搜索该用户的具体信息
      if (!this.isNotFeishuUser) {
        this.getFeiSuUserInfo({
          openId: this.openId,
          searchOpenId: this.currentFeishuUser.open_id
        }).then(res => {
          if (res.status) {
            const data = {
              id: this.currentFeishuUser.id,
              ucPersonFullName: res.data.user.name,
              ucPersonOpenId: res.data.user.open_id,
              ucPersonUnionId: res.data.user.union_id,
              ucPersonPhone: res.data.user.mobile.split('+86')[1],
              ucPersonResource: 1,
              ucPersonUserName: res.data.user.en_name
            }
            this.$emit('closePopup', data)
          }
        })
      }
    },
    async getContacts(val, resetPageToken = true) {
      this.queryVal = val;
      // 用户重新输入后，scrolled状态需要重置
      resetPageToken && (this.scrolled = false);
      // 用户在滚动加载时，如果数据加载完则停止搜索
      if (!resetPageToken && this.finished) {
        // 停止搜索
        return
      } else {
        this.finished = false
      }
      const params = {
        openId: this.openId, // 飞书openId
        pageSize: 10,
        pageToken: resetPageToken ? '' : this.pageToken, // 分页标识，获取首页不需要填写，获取下一页时传入上一页返回的分页标识值。请注意此字段的值并没有特殊含义，请使用每次请求所返回的标识值
        userName: this.currUser.userName, // 凤翎userName
        query: val // 要执行搜索的字符串，一般为用户名
      }
      resetPageToken && (this.loading = true);
      const res = await this.getApplyPerson(params);
      resetPageToken && (this.loading = false);
      this.isNotFeishuUser = false
      // 滚动加载的数据需要去重再合并，搜索数据直接赋值即可
      if (resetPageToken) {
        this.list = res.data.users
      } else {
        this.list = Array.from(new Set(this.list.concat(res.data.users)))
      }
      this.pageToken = res.data.page_token
      if (res.data.has_more === false) {
        // 停止搜索
        this.finished = true
      }
      // 如果没有找到联系人，则将用户输入做为最终结果
      if (res.data.users.length === 0) {
        this.isNotFeishuUser = true
        this.searchValue = val
        this.$emit('closePopup', {
          ucPersonFullName: val,
          ucPersonResource: 3,
        })
      }
    }
  },
  watch: {
    initialValue: {
      immediate: true,
      handler(newVal) {
        newVal && (this.searchValue = newVal)
      }
    },
  }
}
</script>
<style lang='less' scoped>

.option {
  display: flex;
  align-items: center;
  justify-content: space-between;

  span {
    display: inline-block;
  }

  .user {
    display: flex;
    align-items: center;

    img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 10px;
    }

  }

  .dept {
    font-size: 24px;
  }
}
</style>
