<template>
  <div class="no-result">
    <img fit="cover" :src="require('@/assets/img/car/no-data.png')" class="img0" />

    <div class="text0">暂无数据</div>
  </div>
</template>

<script>
export default {
  name: 'EmptyData'
}
</script>

<style lang='less' scoped>
.no-result {
// display: flex;
// flex-direction: column;
// flex-wrap: wrap;
// justify-content: center;
// align-items: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  .img0 {
    width: 232px;
    height: 160px;
  }
  .text0 {
    margin-top: 48px;
    font-size: 28px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 40px;
    text-align: center;
  }
}

</style>
