<template>
  <div class="owa_container">
    <div class="pic_frame" v-show="fileType === PIC_FILE" ref="pic_frame">
      <img ref="pic_img" :src="picURL" class="pic_img" :style="{'object-fit': 'contain','flex':'1'}" />
    </div>
    <div class="pdf_frame" v-show="fileType === PDF_FILE" style="height: 100%">
      <iframe v-if="isPC" id="pdf_frame" name="pdf_frame" :src="pdfBlobUrl"></iframe>
      <iframe v-if="isMobile" id="pdf_frame" name="pdf_frame" :src="pdfURL"></iframe>
    </div>
    <div class="else_frame" v-show="fileType === ELSE_FILE" style="height: 100%">
      <svg-icon :icon-class="showExe" class="else_icon"> </svg-icon>
      <section style="text-align: center; min-height: 40px">
        {{ fileName }}
      </section>
    </div>
  </div>
</template>

<script>
import { getFileAsPdf, getPic } from './api'
import {exportThumbnail} from "@/api/file";
import platform from '@/mixins/platform'
export default {
  name: 'PreView',
  mixins: [platform],
  props: {
    file: Object,
    showThumbnail: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    pdfURL() {
      return `./pdfjs/webs/viewer.html?file=${encodeURIComponent(this.pdfSrc)}`
    },
    picURL() {
      return this.picSrc
    },
    showExe() {
      return 'doc-exe'
    }
  },
  data() {
    return {
      pdfSrc: '',
      picSrc: '',
      fileName: '',
      fileType: -1,
      extTypePdf: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
      extTypePic: ['jpg', 'jpeg', 'png'],
      pdfBlobUrl: '',

      NO_FILE: -1,
      PIC_FILE: 0,
      PDF_FILE: 1,
      ELSE_FILE: 2
    }
  },
  beforeDestroy() {
    if (this.pdfBlobUrl) {
      URL.revokeObjectURL(this.pdfBlobUrl);
      this.pdfBlobUrl = null;
    }
  },
  methods: {
    setIframeWithBlob(blob) {
      const blobUrl = URL.createObjectURL(blob);
      const iframe = document.getElementById('pdf_frame');
      if (iframe) {
        iframe.src = blobUrl;
      }
      // 可选：保存起来后续销毁时 revoke
      this.pdfBlobUrl = blobUrl;
    },
    /**
     * @description: 判断后缀类型
     */
    getFileType() {
      let extName = ''
      //传入的文件可能没有extName,手动获取后缀
      if (!this.file.extName) {
        this.fileName = this.file.name
        let first = this.file.name.lastIndexOf('.')
        let namelength = this.file.name.length
        extName = this.file.name.substring(first + 1, namelength)
      } else {
        extName = this.file.extName
        this.fileName = this.file.fileName
      }
      if (this.extTypePdf.indexOf(extName) >= 0) {
        return this.PDF_FILE
      }
      if (this.extTypePic.indexOf(extName) >= 0) {
        return this.PIC_FILE
      }
      return this.ELSE_FILE
    },

    //
    /**
     * @description: 处理pdf/可转化为pdf的文件
     * @param {String}token  文件的token
     */
    getPdfFile(token) {
      getFileAsPdf(token).then((res) => {
        if (res.status) {
          let objecturl = window.URL.createObjectURL(res.data)
          this.pdfSrc = objecturl
          this.setIframeWithBlob(res.data);
        }
      })
    },
    /**
     * @description: 处理图片
     * @param {String}token  图片的token
     */
    getPicFile(token) {
      //获得图片的接口,更改picURL
      getPic(token).then((res) => {
        if (res.status) {
          let objecturl = window.URL.createObjectURL(res.data)
          this.picSrc = objecturl
        }
      })
    },
    getPicFileThumbnail(token) {
      //获得图片的接口,更改picURL
      exportThumbnail({
        f8s: token,
        width: 70,
        height: 70,
        thumbnail: true
      }).then((res) => {
        if (res.status) {
          let objecturl = window.URL.createObjectURL(res.data)
          this.picSrc = objecturl
        }
      })
    },

    initState() {
      this.fileType = this.NO_FILE
    },

    initData() {
      this.initState()
      if (this.file.fileToken) {
        var token = this.file.fileToken
        this.fileType = this.getFileType()
        if (this.fileType === this.PIC_FILE) {
          if (this.showThumbnail) {
            this.getPicFileThumbnail(token);
          } else {
            this.getPicFile(token);
          }
        }
        if (this.fileType === this.PDF_FILE) {
          this.getPdfFile(token)
        }
        if (this.fileType === this.ELSE_FILE) {
          //this.$message.warning('暂不支持该类型文件预览！')
        }
      }
    }
  },
  mounted() {},
  watch: {
    file: {
      deep: true,
      immediate: true,
      handler: function () {
        this.initData()
      }
    }
  }
}
</script>

<style scoped>
.owa_container {
  display: block;
  margin: 0;
  /* height: calc(100% - 50px); */
  height: 100%;
  border: none;
}
#pdf_frame {
  display: block;
  width: 100%;
  height: 100%;
  border: none;
  margin: 0;
}
.pic_frame {
  display: flex;
  border: none;
  margin: 0;
  text-align: center;
  overflow: hidden;
  align-content: flex-start;
  height: 100%;
}
.pic_img {
  max-width: 100%;
  height: 100%;
  margin: 0 auto;
}
.else_icon {
  width: 100%;
  height: 80%;
}
</style>
