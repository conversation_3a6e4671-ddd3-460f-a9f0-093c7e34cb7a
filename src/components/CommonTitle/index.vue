<template>
  <div v-if="isSlot" class="flex row-between">
    <div class="title-container">
      <div class="fks-title-icon"></div>
      <div class="second-title">{{title}}</div>
    </div>
    <slot name="titleOther"></slot>
  </div>
  <div v-else class="title-container">
<!--    <div class="fks-title-icon"></div>-->
    <div class="second-title">{{title}}</div>
  </div>
</template>
<script>

export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    isSlot: {
      type: Boolean,
      default: false
    }
  }
};
</script>
<style lang="less" scoped>
.title-container {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 20px;
  &:before {
    content: '';
    width: 4px;
    height: 28px;
    background: #3C83FF;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
  }
  div {
    display: inline-block;
  }
  h4 {
    font-size: 32px;
    font-weight: bold;
    margin: 18px 0;
  }
}
</style>
