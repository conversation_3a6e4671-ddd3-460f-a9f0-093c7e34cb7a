<template>
  <div class="border border-color-grey margin-bottom-10">
    <!-- 工具栏 -->
    <Toolbar
      class="border-bottom border-color-grey"
      :editor="editor"
      :default-config="toolbarConfig"
    />
    <!-- 编辑器 -->
    <Editor
      v-model="html"
      class="ofy-hidden height-40"
      style="font-size: 14px;"
      :style="{ height: `${height}px` }"
      :default-config="editorConfig"
      @onChange="onChange"
      @onCreated="onCreated"
    />
  </div>
</template>

<script>
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
  import { uploadFile } from '@/api/file';
  import { deleteFile } from '@/modules/FormCenter/api'
  import '@wangeditor/editor/dist/css/style.css';  // 引入 wangeditor 编辑器的样式文件
  import {uuid} from "@utils/util";
  import _ from "lodash";

  export default {
    name: 'MyEditor',
    components: { Editor, Toolbar },
    props: {
      content: {
        type: String,
        default: ''
      },
      disable: {
        type: Boolean,
        default: false
      },
      showImg: {
        type: Boolean,
        default: true
      },
      height: {
        type: Number,
        default: 300
      },
      faqFile: {
        type: String,
        default: ''
      },
      type: {
        type: String,
        default: 'view'
      }
    },
    data() {
      return {
        editor: null,
        html: null,
        toolbarConfig: {
          toolbarKeys: ['bold','through','italic','underline','bulletedList','numberedList','blockquote','insertLink','codeBlock','uploadImage'],  /* 显示哪些菜单，如何排序、分组 */
          // excludeKeys: ['group-video', 'insertImage','emotion'] /* 隐藏哪些菜单 */
        },
        editorConfig: {
          placeholder: '请输入内容...',
          MENU_CONF: {}
        },
        g9s: uuid(16, 32),
        tokenArr: [],
        lastImg: [],
        initValue: false
      };
    },
    mounted() {
      if(this.type !== 'edit') {
        this.initValue = true;
      }
      setTimeout(async() => {
        if (this.content) {
          this.html = await this.processContent(this.content);
          this.initValue = true;
          // this.editor.setHtml(this.html)
          // this.html = this.content
        }
        if (this.disable && this.editor?.disable) {
          this.editor.disable();
        }
      }, 500);
    },
    created() {
      this.uploadImg();
    },
    beforeDestroy() {
      const editor = this.editor;
      if (editor == null) return;
      editor.destroy(); // 组件销毁时，及时销毁 editor ，重要！！！
    },
    methods: {
      // url替换回显图片
      async processContent(content) {
        // 使用DOMParser来解析HTML字符串
        const parser = new DOMParser();
        const doc = parser.parseFromString(content, 'text/html');
        const pElement = doc.body.innerHTML;
        return pElement;
      },
      // 上传图片
      uploadImg() {
        const that = this;
        this.editorConfig.MENU_CONF['uploadImage'] = {
          customUpload(file, insertFn) {
            let formData = new FormData()
            if(that.faqFile) {
              formData.append('g9s', that.faqFile)
            }else {
              formData.append('g9s', that.g9s)
            }
            formData.append('file', file)
            uploadFile(formData).then((res) => {
              const url = `/xmb/api/sys-storage/download_image?f8s=${res.data.fileToken}`
              insertFn(url, res.data.fileToken, '');
              that.$emit('group-token', res.data.groupToken);
            });
          }
        };
      },
      deleteImage(token) {
        deleteFile({ f8s: token }).then((res) => {
        })
      },
      onCreated(editor) {
        this.editor = Object.seal(editor); // 【注意】一定要用 Object.seal() 否则会报错
      },
      onChange(editor) {
        if(this.initValue) {
          const content = editor.getHtml();
          const text = editor.getText();
          const img =  editor.getElemsByType('image') // 所有图片
          if(img.length < this.lastImg.length) {
            const deletedItems = _.differenceBy(this.lastImg, img, "alt");
            const deletedAlts = deletedItems.map(item => item.alt);
            if(deletedAlts.length > 0) {
              this.deleteImage(deletedAlts)
            }
          }
          this.lastImg = img;
          if (content === '<p><br></p>') return this.$emit('content-change', '','');
          this.$emit('content-change', content,text);
        }
      },
      insertTextHandler() {
        const editor = this.editor;
        if (editor == null) return;
      },
      printEditorHtml() {
        const editor = this.editor;
        if (editor == null) return;
      },
      disableHandler() {
        const editor = this.editor;
        if (editor == null) return;
      }
    }
  };
</script>

<!-- <style src="@wangeditor/editor/dist/css/style.css"></style> -->

<style>
.w-e-full-screen-container {
  z-index: 1000;
}
</style>
