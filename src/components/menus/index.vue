<template>
  <div class="parent">
    <div class="nav-menu">
      <div class="header">
        <img :src="require('@/assets/img/homepage/logo-frame.png')" height="32px" style="border-radius: 4px" width="32px"/>
        <span>派车管理</span>
      </div>
      <div class="infos">
        <div class="username" @click="visible = true">
          <span>
            {{ userFullName }}
<!--            <i class="fks-icon-edit"></i>-->
          </span>
        </div>
        <img :src="avatarUrl"/>
      </div>
    </div>
    <div class="container">
      <Menu :collapse="collapse" :data="tabRoutes" class="menu"/>
      <div :style="{left: collapse ? '63px' : '190px'}" class="indicator" @click="handleClick">
        <i :class="`fks-icon-arrow-${collapse ? 'right' : 'left'}`"/>
      </div>
      <div :style="{left: collapse ? '65px' : '192px'}" class="content">
        <slot/>
      </div>
    </div>
    <fks-dialog :visbile.sync="visible" :before-close="beforeClose" append-to-body>
      <template slot="title">
        <i class="fks-icon-edit"/>
        <span>编辑个人信息</span>
      </template>
      <fks-form ref="form" :model="form">
        <fks-form-item :rules="userNameRules" label="用户名" prop="userFullName">
          <fks-input v-model="form.userFullName" placeholder="请填写用户名"/>
        </fks-form-item>
      </fks-form>
      <template slot="footer">
        <fks-button @click="visible = false" icon="fks-icon-close">取 消</fks-button>
        <fks-button type="primary" @click="visible = false" icon="fks-icon-check">确 定</fks-button>
      </template>
    </fks-dialog>
  </div>

</template>
<script>
import MenuItem from './components/menu-item.vue'
import Menu from './components/Menu.vue'
import {mapMutations, mapState} from "vuex";
import * as StateTypes from '@/store/State/stateTypes'
import * as MutationTypes from '@/store/Mutation/mutationTypes'
import {traverse} from "@utils/util";
import platform from "@/mixins/platform";
import storage from "@utils/storage";

const routeList = [
  '/DispatchCar/carApply',
  '/DispatchCar/Apply',
  '/DispatchCar/Todo',
  '/DispatchCar/Read',
  '/statistics',
  '/department'
]
export default {
  name: 'Menus',
  components: {MenuItem, Menu},
  mixins: [platform],
  data() {
    return {
      visible: false,
      form: {
        userFullName: ''
      }
    }
  },
  computed: {
    ...mapState([StateTypes.ROUTES, StateTypes.USER_INFO, StateTypes.COLLAPSE]),
    ...mapState('CarApply', ['currUser']),
    tabRoutes() {
      let routes = [];
      traverse(this[StateTypes.ROUTES], (item) => {
        if (routeList.findIndex(route => route === item.path) > -1) {
          routes.push(item)
        }
      }, 'children')
      return routes.sort((a, b) => a.sort - b.sort)
    },
    avatarUrl() {
      return this?.currUser?.avatarOrigin || ''
    },
    userFullName() {
      return this?.currUser?.userFullName || ''
    },
    userNameRules() {
      return [
        {required: true, message: '请填写用户名'},
        {
          validator(rule, val, callback) {
            if (val.length >= 1 && val.length <= 10) {
              callback()
            } else {
              callback(new Error('字符长度不能超过10个'))
            }
          }
        },
        {
          validator(rule, val, callback) {
            if (/^[\u4e00-\u9fa5_a-zA-Z0-9]+$/.test(val)) {
              callback()
            } else {
              callback(new Error('不能包含空格和特殊字符'))
            }
          }
        },
      ]
    }
  },
  methods: {
    ...mapMutations([MutationTypes.SET_COLLAPSE]),
    handleClick() {
      this[MutationTypes.SET_COLLAPSE](!this[StateTypes.COLLAPSE])
      const val = (this[StateTypes.COLLAPSE]).toString()
      storage.set(StateTypes.COLLAPSE, val);
    },
    beforeClose() {
      this.$refs.form.resetFields();
      this.$refs.form.clearValidate();
    }
  }
}
</script>
<style lang="less" scoped>
.parent {
  display: flex;
  flex-direction: column;

  .nav-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;

    .header {
      display: flex;
      align-items: center;

      span {
        display: inline-block;
        margin-left: 20px;
        font-size: 36px;
        color: #333;
        font-weight: bold;
      }

      i {
        font-size: 64px;
        display: inline-block;
        cursor: pointer;
      }
    }

    .infos {
      display: flex;
      align-items: center;
      font-size: 32px;
      margin-right: 20px;

      .username {
        cursor: pointer;

        i {
          display: inline;
          margin-left: 10px;
          width: 36px;
          height: 36px;
        }
      }

      img {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        margin-left: 20px;
      }
    }
  }

  .container {
    background: white;
    position: relative;
    display: flex;
    //flex: 1;
    flex: 1 1 auto;

    .indicator {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      align-items: center;
      background: white;
      width: 38px;
      height: 80px;
      z-index: 99;
      font-size: 32px;
      justify-content: center;
      display: flex;
      border-top-right-radius: 20px;
      border-bottom-right-radius: 20px;
      cursor: pointer;
    }

    .menu {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
    }

    .content {
      position: absolute;
      top: 0px;
      bottom: 0;
      right: 40px;
      overflow-x: hidden;
      border: 40px solid rgba(243, 245, 249, 1)
    }
  }
}

</style>
