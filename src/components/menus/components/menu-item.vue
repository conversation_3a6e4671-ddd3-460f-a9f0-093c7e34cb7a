<template>
  <div
    v-if="!(collapse && isChildren)"
    :style="{width: collapse ? '75px' : '150px'}"
    class="menu-container"
  >
    <div v-if="!isChildren" class="switcher" @click="handleCollapseClick">{{collapse ? '>' : '<'}}</div>
    <div
      v-for="item in data"
      :key="item.id"
    >
      <div
        :class="{[collapse ? 'vertical' : 'horizontal']: true, 'is-active': activeId === item.id}"
        class="menu-item"
        @click="handleClick(item)"
        @mouseenter="handleMouseEnter(item)"
        @mouseleave="handleMouseLeave(item)"
      >
        <fm-icon class="icon" name="phone-o" size="16"/>
        <span class="menu-title">{{ item.title }}</span>
        <div v-if="collapse && showAppendMenu && item.children.length" class="append-menu">
          <div
            v-for="subItem in item.children"
            :key="subItem.id"
            :class="{'is-active': activeSubId === subItem.id}"
            class="sub-menu-item"
            @click.stop="handleSubItemClick(subItem)"
          >
            <span class="sub-menu-title">{{ subItem.title }}</span>
          </div>
        </div>
      </div>
      <menu-item v-if="item.children.length" :collapse="collapse" :data="item.children" is-children/>
    </div>
  </div>
</template>

<script>
import {traverse} from "@utils/util";

export default {
  name: 'MenuItem',
  props: {
    data: {
      type: Array
    },
    collapse: {
      type: Boolean,
      default: true
    },
    isChildren: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeId: '',
      activeSubId: '',
      showAppendMenu: false
    }
  },
  computed: {
    handleClick() {
      return this.isChildren ? this.handleSubItemClick : this.handleItemClick
    }
  },
  methods: {
    handleCollapseClick() {
      this.$emit('update:collapse', !this.collapse)
    },
    handleItemClick(item) {
      this.activeId = item.id
      // 清空子元素
      this.activeSubId = ''
      // 如果有children 那么默认选中children第一项
      if (item.children.length) {
        this.activeSubId = item.children[0].id
      }
    },
    handleSubItemClick(item) {
      // 子菜单点击后，父级菜单也要选中
      this.activeId = item.parentId
      this.activeSubId = item.id
    },
    handleMouseEnter(item) {
      item.children.length && (this.showAppendMenu = true)
    },
    handleMouseLeave(item) {
      this.showAppendMenu = false;
    },
  },
  mounted() {
    // 激活当前路由所在的菜单
    traverse(this.data, (item) => {
      if (item.path === this.$route.path) {
        this.activeId = item.id;
      }
    }, 'children')
  }
}
</script>
<style lang="less" scoped>
.menu-container {
  height: 100%;
  font-size: 32px;
  display: flex;
  flex-direction: column;
  padding: 20px;
  position: relative;
  .switcher {
    position: absolute;
    top: 50%;
    right: -60px;
    transform: translateY(-50%);
    box-shadow: 6px 0 12px 0 rgba(0, 0, 0, 0.1);
    border-left: none;
    width: 60px;
    height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    cursor: pointer;
    background-color: white;
  }

  .menu-item {
    padding: 20px;
    cursor: pointer;
    border-radius: 8px;
    margin: 10px 0;
    position: relative;

    &:hover {
      background-color: #e6eeff;
      color: #3C83FF;
    }

    &.vertical {
      display: flex;
      flex-direction: column;
      align-items: center;

      .menu-title {
        margin-top: 20px;
      }
    }

    &.horizontal {
      display: flex;
      align-items: center;
      .icon {
        margin-right: 20px;
      }
    }

    &.is-active {
      background-color: #e6eeff;
      color: #3C83FF;
    }

    .menu-title {
      display: inline-block;
    }

    .append-menu {
      display: flex;
      flex-direction: column;
      padding: 20px 10px;
      position: absolute;
      top: 0;
      left: 155px;
      white-space: nowrap;
      align-items: center;
      justify-content: center;
      color: initial;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      background-color: white;


      .sub-menu-item {
        padding: 20px;
        margin: 10px 0;
        width: 80%;
        text-align: center;

        &.is-active {
          background-color: #e6eeff;
          color: #3C83FF;
        }

        &:hover {
          background-color: #e6eeff;
        }
      }

      .sub-menu-title {
        display: inline-block;
      }
    }
  }

  .icon {
    width: 32px;
    height: 32px;
    font-size: 32px;
  }
}
</style>
