<template>
  <fks-menu
    :collapse="collapse"
    :default-active="defaultActive"
    :default-openeds="defaultOpens"
    class="fks-menu-vertical-demo"
    style="user-select: none;"
    :collapse-transition="false"
    @select="handleMenuSelect"
    router
  >
    <div
      v-for="item in data"
      :key="item.id"
    >
      <fks-submenu v-if="item.children.length" :key="item.id" :class="{disableRightIcon: collapse}"
                   :index="item.id">
        <template slot="title">
          <fks-icon :icon-class="item.icon" class="icon-text"/>
          <span v-if="!collapse" slot="title" class="spanLeft">{{ item.title }}</span>
        </template>
        <fks-menu-item
          v-for="item in item.children"
          :key="item.id"
          :index="item.id"
          :route="getRoute(item)"
        >
<!--          <fks-icon :icon-class="item.icon" class="icon-text" />-->
          <template slot="title">
            <span class="spanLeft">{{ item.title }}</span>
          </template>
        </fks-menu-item>
      </fks-submenu>
      <fks-menu-item
        v-else
        :index="item.id"
        :route="getRoute(item)"
      >
        <div class="position-relative flex col-center row-center">
          <sup v-if="dotTable[item.name] && collapse" class="small-dot">{{dotTable[item.name]}}</sup>
          <fks-icon :icon-class="item.icon" class="icon-text"/>
        </div>
        <template slot="title">
          <div v-if="!collapse" slot="title" class="flex col-center row-between m-l-20 flex-grow-1">
            <span v-if="!collapse">{{ item.title }}</span>
            <span v-if="dotTable[item.name]" class="m-r-20 dot">{{dotTable[item.name]}}</span>
          </div>
        </template>
      </fks-menu-item>
    </div>
  </fks-menu>
</template>

<script>

import menuRouteMixin from "@/mixins/menuRouteMixin";
import menuDotMixin from "@/mixins/menuDotMixin";
import {mapState} from "vuex";
import * as StateTypes from "@/store/State/stateTypes";

export default {
  name: 'Menu',
  mixins: [menuRouteMixin, menuDotMixin],
  props: {
    collapse: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array
    }
  },
  data() {
    return {
      defaultActive: '',
      defaultOpens: [],
    }
  },
  computed: {
    ...mapState([StateTypes.PERMISSION])
  },
  watch: {
    '$route.name': {
      handler(newVal){
        // const item = this.data.find(el => el.name === newVal);
        // this.defaultActive = item.id;
        this.defaultActive = this.findId(this.data,newVal)
      }
    },
    data: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length) {
          const item = newVal.find(el => el.name === this.$route.name);
          if (item) {
            this.defaultActive = item.id
            this.$router.push({name: item.name})
            this.getButtons(item)
          } else {
            this.defaultActive = newVal[0].id
            this.$router.push({name: newVal[0].name})
            this.getButtons(newVal[0])
          }
        }
      }
    }
  },
  methods: {
    handleMenuSelect(index) {
      if (!this[StateTypes.PERMISSION][index]) {
        this.getButtons({id: index})
      }
    },
    getButtons(item) {
      this.$store.dispatch('getPermissions', item.id);
    },
    findId(data, name) {
      for (const item of data) {
        if (item.name === name) {
          return item.id
        } else {
          if (item.children && item.children.length > 0) {
            return this.findId(item.children, name)
          }
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.dot {
  background-color: #ff4d4f;
  border-radius: 20px;
  color: #fff;
  display: inline-block;
  font-size: 24px;
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  text-align: center;
  white-space: nowrap;
  border: 2px solid #fff;
}
.small-dot {
  position: absolute;
  top: -30px;
  right: -25px;
  background-color: #ff4d4f;
  border-radius: 20px;
  color: #fff;
  display: inline-block;
  font-size: 24px;
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  text-align: center;
  white-space: nowrap;
  border: 2px solid #fff;
}
img {
  width: 36px;
  height: 36px;
  margin-right: 28px;
}

.fks-menu-vertical-demo {
  flex-grow: 1;
  overflow-y: auto;
  overflow-x: hidden;

  &.fks-menu {
    border-right: none !important;
  }

  span {
    display: inline-block;
  }


  &:not(.fks-menu--collapse) {
    width: 360px;
    min-height: 360px;
  }

  &.fks-menu--collapse {
    .fks-menu-item {
      padding-left: 32px !important;
    }
  }
}

.fks-menu--collapse {
  width: 96px;
}

/deep/ .fks-submenu.disableRightIcon {
  .fks-submenu__title {
    padding-left: 32px !important;
  }

  .fks-submenu__icon-arrow.fks-icon-arrow-right {
    opacity: 0;
  }
}

/deep/ .fks-submenu .fks-menu-item {
  min-width: 190px;
  border-radius: 12px;
}

/deep/ .fks-menu-item.is-active {
  background-color: #DCE4FA;
  color: #336EF3;
}

/deep/ .fks-menu-item {
  border-radius: 12px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 0;

  &:hover {
    background-color: #DCE4FA;
    color: #336EF3;
  }
}

/deep/ .fks-menu-item, .fks-submenu__title {
  height: 86px;

}

/deep/ .fks-submenu__title {
  height: 86px;
  line-height: 86px;
  border-radius: 12px;
  margin-bottom: 20px;

  &:hover {
    background-color: #DCE4FA;
    color: #336EF3;
  }
}

.spanLeft {
  display: inline-block;
  margin-left: 20px;
}
</style>
