<template>
  <fks-tooltip effect="light" :content="text" placement="top-start" popper-class="overflow-tooltip" :visible-arrow="false">
    <span class="text-ellipsis" :class="{[textClass]: true}" v-html="text" />
  </fks-tooltip>
</template>

<script>
export default {
  name: "OverflowTooltip",
  props: ['text', 'textClass']
}
</script>

<style lang="scss">
.overflow-tooltip {
  max-width: 200PX !important;
  border: none !important;
}
</style>
