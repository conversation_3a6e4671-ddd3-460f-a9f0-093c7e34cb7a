<template>
  <div :id="id" :style="{height}" class="trackMapContainer">
<!--    隐藏标签-->
<!--    <div v-if="distance && showTags && render" class="tags">-->
<!--      <fks-tag v-if="isPC" style="margin-right: 10px">{{ formattedDistance }}</fks-tag>-->
<!--      <fks-tag v-if="isPC">{{ formattedTime }}</fks-tag>-->
<!--      <fm-tag v-if="isMobile" size="large" style="margin-right: 10px" type="primary">{{ formattedDistance }}</fm-tag>-->
<!--      <fm-tag v-if="isMobile" size="large" type="primary">{{ formattedTime }}</fm-tag>-->
<!--    </div>-->
    <div v-if="showBack && render" class="tags">
      <fm-button v-if="isMobile" icon="arrow-left" @click="$router.go(-1)"></fm-button>
      <fks-button v-if="isPC" icon="fks-icon-reject" type="primary" @click="$router.go(-1)">返回</fks-button>
    </div>
    <div v-if="showBack && render" class="form">
      <fks-button type="primary" @click="$emit('detail')">
        查看申请表单
        <i class="fks-icon-right fks-icon--right"></i>
      </fks-button>
    </div>
  </div>
</template>

<script>
/*
* parameters strategy: 0
* start："longitude": 105.060817, "latitude": 29.588704
* end：longitude": 106.232117, "latitude": 29.559398,
* 轨迹图示例数据 {"name":"北京 -> 福州","path":[[116.405289,39.904987]，[116.406265,39.905014],[116.406441,39.905018]]}
* */
import platform from "@/mixins/platform";
import aMapMixin from "@/mixins/aMapMixin";
import {Tag} from 'fawkes-mobile-lib'

export default {
  name: 'Track',
  mixins: [platform, aMapMixin],
  components: {
    [Tag.name]: Tag
  },
  props: {
    id: {
      type: String,
      required: true
    },
    height: {
      type: String,
      default: '500px'
    },
    showBack: {
      type: Boolean,
      default: false
    },
    showTags: {
      type: Boolean,
      default: true
    },
    startPoint: {
      type: Object
    },
    endPoint: {
      type: Object
    },
    timeStr: {
      type: String,
      default: ""
    },
    distanceStr: {
      type: String,
      default: ""
    },
    wayPoints: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      distance: null,
      time: null,
      markers: [],
      map: null,
      render: false
    }
  },
  computed: {
    wayPointsStr() {
      return this.wayPoints.map(item => `${item.addressSmx},${item.addressSmy}`).join(';') || '';
    },
    formattedDistance() {
      return `${(this.distance / 1000).toFixed(2)}公里`
    },
    formattedTime() {
      let str = ''
      let append = ''
      if (this.time / 60 < 60) {
        append = `${(this.time / 60).toFixed(2)}分钟`
      } else {
        append = `${(this.time / 3600).toFixed(2)}小时`
      }
      return str + append
    }
  },
  mounted() {
    this.automate();
  },
  watch: {
    formattedTime(newVal){
      if (newVal) {
        this.$emit('updateTripInfo', {time: newVal, distance: this.formattedDistance})
      }
    },
    startPoint: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.x === oldVal.x) {
          this.renderMarker()
        } else {
          this.automate();
        }
      }
    },
    endPoint: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.x === oldVal.x) {
          this.renderMarker()
        } else {
          this.automate();
        }
      }
    },
    wayPointsStr() {
      this.automate();
    }
  },
  methods: {
    automate() {
      this.fetchPoints().then(points => {
        this.initMap(points)
      })
    },
    fetchPoints() {
      return new Promise((resolve) => {
        const obj = {
          origin: `${this.startPoint.x},${this.startPoint.y}`,
          destination: `${this.endPoint.x},${this.endPoint.y}`,
          waypoints: this.wayPointsStr,
          extensions: 'all',
          output: 'json',
          key: process.env.VUE_APP_AMAP_WEB_KEY
        }
        const params = Object.keys(obj).map(key => (`${key}=${obj[key]}`)).join('&')
        const url = `https://restapi.amap.com/v3/direction/driving?${params}`
        fetch(url).then(res => res.json()).then(res => {
          const path = res.route.paths[0]
          const steps = path?.steps || [];
          this.distance = Number(path.distance)
          this.time = Number(path.duration)
          const polylines = steps.map(item => item.polyline)
          let points = []
          for (let i = 0; i < polylines.length; i++) {
            points.push(...polylines[i].split(';'))
          }
          points = points.map(point => {
            return point.split(',').map(item => Number(item))
          })
          resolve(points)
        })
      })
    },

    renderMarker(points) {
      const configs = [
        {text: '起', prop: 'startPoint', color: 'green'},
        {text: '终', prop: 'endPoint', color: 'red'},
      ]
      window.AMapUI.load(['ui/overlay/SimpleMarker'], (SimpleMarker) => {
        configs.forEach(config => {
          // 起点，终点图标
          const marker = new SimpleMarker({
            iconLabel: {
              innerHTML: `
                <div style="text-align: center;">
                  <div style="color: white; font-size: 16px;">${config.text}</div>
                  <div style="background: #f7f7f7; font-size: 14px; line-height: 14px; padding: 0 8px; border-radius: 4px; margin-top: 4px; max-width: 120px; white-space: initial; word-wrap: break-word;">
                  </div>
                </div>
              `
            },
            iconTheme: 'default',
            iconStyle: config.color,
            map: this.map,
            position: [this[config.prop].x, this[config.prop].y]
          });
        });

        // 途经点图标
        this.wayPoints.forEach((point, index) => {
          new SimpleMarker({
            iconLabel: {
              innerHTML: `
                <div style="
                text-align: center;
                border: 1px solid #b8b8b8;
                font-size: 13px;
                background: #fff;
                border-radius: 6px 6px 6px 0;color: #777;
                padding: 3px;
                z-index: 2;
                white-space: nowrap;
                line-height: 130%;
                position: absolute;
                ">
                  途经点${index + 1}
                </div>
              `
            },
            map: this.map,
            position: [point.addressSmx, point.addressSmy]
          })
        })

        // 计算路线中间点并在该位置添加文本覆盖物显示时间和距离信息
        if (points && points.length) {
          const midIndex = Math.floor(points.length / 2);
          const midPoint = points[midIndex]; // [lng, lat]
          // 使用自定义 HTML 结构实现气泡形式的文本覆盖物
          const midInfo = new window.AMap.Text({
            text: `<div id="mid-info-custom" style="position: relative; display: inline-block;">
           <div style="background: rgba(255,255,255,1); padding: 4px 12px; font-size: 14px; line-height: 14px;
           font-weight: normal; color: #333333; display: inline-block;">
             ${this.timeStr}
             <span style="display: inline-block; vertical-align: middle; width: 1px; height: 10px; background: #E4E4E4; margin: 0 8px;"></span>
             ${this.distanceStr}
           </div>
           <div style="position: absolute; left: 50%; bottom: -8px; transform: translateX(-50%); width: 0; height: 0;
                       border-left: 8px solid transparent; border-right: 8px solid transparent;
                       border-top: 8px solid rgba(255,255,255,1);">
           </div>
         </div>`,
            anchor: 'center',
            // 向上偏移一些，保证整体位置合适
            offset: new window.AMap.Pixel(0, -25),
            position: midPoint
          });
          midInfo.setMap(this.map);

          // 延时获取顶层容器，修改其样式
          setTimeout(() => {
            const customEl = document.getElementById("mid-info-custom");
            if (customEl && customEl.parentNode) {
              const container = customEl.parentNode; // 顶层容器
              container.style.borderRadius = "32px";
              container.style.overflow = "visible";
              container.style.boxShadow = "0px 4px 10px 0px rgba(0, 0, 0, 0.1)";
              container.style.border = "unset";
              // 添加一个独特的类名，方便后续单独控制
              container.classList.add('custom-mid-info');
            }
          }, 0);
        }
      });
    },

    async initMap(points) {
      await this.initAMap();
      const map = new window.AMap.Map(this.id, {dragEnable: this.isPC});
      this.map = map;
      this.render = false;
      window.AMapUI.load(['ui/misc/PathSimplifier', 'lib/$'], function (PathSimplifier, $) {

        if (!PathSimplifier.supportCanvas) {
          alert('当前环境不支持 Canvas！');
          return;
        }

        //just some colors
        const colors = [
          "#3366cc", "#dc3912", "#ff9900", "#109618", "#990099", "#0099c6", "#dd4477", "#66aa00",
          "#b82e2e", "#316395", "#994499", "#22aa99", "#aaaa11", "#6633cc", "#e67300", "#8b0707",
          "#651067", "#329262", "#5574a6", "#3b3eac"
        ];

        const pathSimplifierIns = new PathSimplifier({
          zIndex: 100,
          map: map, //所属的地图实例

          getPath: function (pathData, pathIndex) {

            return pathData.path;
          },
          getHoverTitle: function (pathData, pathIndex, pointIndex) {
            return null
          },
          renderOptions: {
            pathLineStyle: {
              dirArrowStyle: true
            },
            getPathStyle: function (pathItem, zoom) {

              const color = colors[pathItem.pathIndex % colors.length],
                  lineWidth = Math.round(2 * Math.pow(1.1, zoom - 1));

              return {
                pathLineStyle: {
                  strokeStyle: color,
                  lineWidth: lineWidth
                },
                pathLineSelectedStyle: {
                  lineWidth: lineWidth + 2
                },
                pathNavigatorStyle: {
                  fillStyle: color
                }
              };
            }
          }
        });

        window.pathSimplifierIns = pathSimplifierIns;

        function onload() {
          pathSimplifierIns.renderLater();
        }

        function onerror(e) {
          alert('图片加载失败！');
        }

        setTimeout(() => {
          pathSimplifierIns.setData([{name: '路线', path: points}]);
          setTimeout(() => {
            map.setFitView()
          })
        })

      });
      this.renderMarker(points);
      map.on('complete', () => {
        this.$emit('complete', true)
        this.render = true
        // 去除logo
        const el = document.getElementById(this.id)
        const copyRightEl = el.querySelector('.amap-copyright')
        copyRightEl && copyRightEl.remove()
        const logoEle = el.querySelector('.amap-logo')
        logoEle && logoEle.remove();
      })
    }
  }
}
</script>
<style lang="less" scoped>
.trackMapContainer {
  width: 100%;
  position: relative;
  border-radius: 16px !important;

  .tags {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 9;
  }

  .form {
    position: absolute;
    bottom: 10px;
    right: 10px;
    z-index: 9;
  }
}
</style>
