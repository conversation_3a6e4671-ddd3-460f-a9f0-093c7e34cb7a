import request from '@/utils/request'
import storage from '@/utils/storage'

// 获取收藏地址列表
export function getSavedAddressList(addressType) {
  const userName = storage.getObject('user').userName;
  return request({
    url: '/vehicle-dispatch/vd/favorite/address/list',
    params: {
      userName,
      addressType: addressType
    }
  })
}

// 新增收藏地址
export function addSavedAddress(data) {
  return request({
    url: '/vehicle-dispatch/vd/favorite/address/add',
    method: 'POST',
    data
  })
}

// 删除地址
export function deleteSavedAddress(id) {
  return request({
    url: '/vehicle-dispatch/vd/favorite/address/delete',
    method: 'POST',
    params: {id}
  })
}
