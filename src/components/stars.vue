<template>
  <div class="flex col-center">
    <img v-for="star in stars" :key="star"
         :src="star > value ? require('@/assets/img/car/gray-star.svg') : require('@/assets/img/car/star.svg')"
         class="star-img" :height="size" :width="size"/>
  </div>
</template>

<script>
export default {
  name: 'Stars',
  props: {
    value: {
      type: Number,
      required: true
    },
    size: {
      type: String,
      default: '24px'
    }
  },
  data() {
    return {
      stars: [1, 2, 3, 4, 5]
    }
  }
}
</script>
