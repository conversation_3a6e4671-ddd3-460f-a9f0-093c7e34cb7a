<template>
  <div class="transfer-container">
    <div class="tree-container">
      <fks-tree
        ref="leftTreeRef"
        :data="leftTree"
        :default-expanded-keys="expandKeys"
        :disabled="type === 'view'"
        :props="treeProps"
        check-on-click-node
        node-key="id"
        show-checkbox
        @check-change="handleLeftCheck"
        @node-click="handleNodeClick"
        @node-expand="onExpand"
        @node-collapse="onCollapse"
      >
        <template slot-scope="{data, node}">
          {{getData(data, node)}}
        </template>
      </fks-tree>
    </div>
    <div class="buttons">
      <fks-button :disabled="!isTransferTo" @click="transferTo">
        <i class="fks-icon-arrow-right"/>
      </fks-button>
      <fks-button :disabled="!isTransferBack" style="margin-left: 0" @click="transferBack">
        <i class="fks-icon-arrow-left"/>
      </fks-button>
    </div>
    <div v-if="rightList.length" class="tree-container right">
      <div
        v-for="item in rightList"
        :key="item.id"
        class="flex box"
      >
        <fks-checkbox v-model="item.checked" :disabled="type === 'view'">
          <overflow-tooltip :text="item.name"/>
        </fks-checkbox>
      </div>
    </div>
    <fks-empty v-else description="请选择角色配置"></fks-empty>
  </div>
</template>

<script>
import {arrayToTree} from "@utils/array";
import OverflowTooltip from "@components/OverflowTooltip/index.vue";

export default {
  name: "TreeTransfer",
  components: {OverflowTooltip},
  props: {
    sourceData: {
      type: Array,
      required: true
    },
    type: {
      type: String,
      default: 'edit'
    },
    rightListFromParent: {
      required: true
    }
  },
  data() {
    return {
      isTransferTo: false,
      isTransferBack: false,
      leftList: [],
      rightList: [],
      treeProps: {
        label: 'name', children: 'children', disabled(data) {
          if (data.pid !== 0) {
            return false
          } else {
            return data.children.length === 0
          }
        }
      },
      expandKeys: [],
      deleteList: []
    }
  },
  computed: {
    leftTree() {
      return arrayToTree(this.leftList, 'pid', 0)
    }
  },
  methods: {
    getData(data, node) {
      if (data.type === 'node') {
        return data.name
      } else {
        return data.remark
      }
    },
    onExpand(item) {
      this.expandKeys.push(item.id)
    },
    onCollapse(item) {
      const index = this.expandKeys.findIndex(key => key === item.id)
      this.expandKeys.splice(index, 1)
    },
    handleNodeClick(item) {
      const index = this.expandKeys.findIndex(key => key === item.id)
      if (index > -1) {
        this.expandKeys.splice(index, 1)
      } else {
        this.expandKeys.push(item.id)
      }
    },
    handleLeftCheck() {
      const keys = this.$refs.leftTreeRef.getCheckedKeys();
      this.isTransferTo = keys.length > 0
    },
    transferTo() {
      // 将左侧选中的数据
      const keys = this.$refs.leftTreeRef.getCheckedKeys();
      if (keys.length) {
        this.leftList = this.leftList.filter(item => keys.findIndex(key => key === item.id) === -1)
        // 叶子节点
        const newRightList = JSON.parse(JSON.stringify(this.sourceData))
          .filter(item => {
            return keys.findIndex(key => key === item.id) > -1;
          })
          .filter(item => item.pid !== 0)
        this.rightList = this.rightList.concat(newRightList)
        // 传输完成后，禁用选择
        this.isTransferTo = false;
      }
    },
    transferBack() {
      const checkedNodes = this.rightList.filter(item => item.checked);
      this.rightList = this.rightList.filter(item => !item.checked);

      // 找出需要返回的节点的父节点
      let parentNodes = [];
      checkedNodes.forEach(node => {
        if (this.leftList.findIndex(item => item.id === node.pid) === -1) {
          if (parentNodes.findIndex(item => item.id === node.pid) === -1) {
            const parentNode = JSON.parse(JSON.stringify(this.sourceData)).find(el => el.id === node.pid)
            parentNodes.push(parentNode)
          }
        }
      })
      this.leftList.push(...[...parentNodes, ...checkedNodes])
      if (this.rightListFromParent && this.rightListFromParent.length) {
        // 撤销时需要调用批量删除角色接口
        this.deleteList.push(...checkedNodes);
      }
    },
  },
  watch: {
    sourceData: {
      immediate: true,
      handler(newVal) {
        if (newVal && !this.rightListFromParent) {
          this.leftList = JSON.parse(JSON.stringify(newVal))
        }
      }
    },
    rightList: {
      deep: true,
      handler(newVal) {
        this.isTransferBack = newVal.some(item => item.checked);
      }
    }
  },
  created() {
    if (this.rightListFromParent) {
      this.rightList = JSON.parse(JSON.stringify(this.sourceData))
        .filter(item => {
          const index = this.rightListFromParent.findIndex(rightItem => rightItem.id === item.id);
          return index > -1;
        });
      // 将不属于右侧列表的元素过滤出来
      this.leftList = JSON.parse(JSON.stringify(this.sourceData))
        .filter(item => {
          const index = this.rightList.findIndex(rightItem => rightItem.id === item.id);
          return index === -1
        })
    }
  }
}
</script>

<style lang="less" scoped>
.transfer-container {
  display: grid;
  grid-template-columns: 45% 10% 45%;
  height: 600px;

  .tree-container {
    overflow: auto;
    border: 2px solid #EBEEF5;
    border-radius: 8px;

    &.right {
      padding: 20px;
    }

    .box {
      line-height: 60px;
      cursor: pointer;
      padding-left: 20px;
      overflow: hidden;

      &:hover {
        background-color: #ecf5ff;
      }
    }
  }


  .buttons {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
  }
}
</style>
