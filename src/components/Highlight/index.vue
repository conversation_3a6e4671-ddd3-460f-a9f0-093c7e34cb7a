<template>
  <div :id="wrapperId" class="position-relative">
    <div :id="highlighterId" class="highlighter" />
    <slot />
  </div>
</template>

<script>
export default {
  name: 'Highlight',
  props: ['id'],
  computed: {
    wrapperId() {
      return this.id + '-wrapper'
    },
    highlighterId() {
      return this.id + '-highlighterId'
    }
  },
  methods: {

    clearHighlight(text) {
      this.$nextTick(() => {
        const wrapper = document.getElementById(this.wrapperId);
        const input = wrapper.querySelector('.fks-input__inner');
        const textarea = wrapper.querySelector('textarea');
        const highlighter = document.getElementById(this.highlighterId);

        if (!highlighter) return;

        // 同时更新高亮层和输入框的值
        highlighter.innerHTML = text;

        // 同步到实际输入框
        if (input) {
          input.value = text;
        } else if (textarea) {
          textarea.value = text;
        }
      });
    },

    handleHighlight(privatePart) {
      this.$nextTick(() => {
        // const wrapper = this.$refs.startAddressInputRef.$el;
        const wrapper = document.getElementById(this.wrapperId)
        const input = wrapper.querySelector('.fks-input__inner')
        const textarea = wrapper.querySelector('textarea')
        const highlighter = document.getElementById(this.highlighterId)
        let text;
        if (input) {
          text = input.value;
        } else if (textarea) {
          text = textarea.value;
        }

        let regexPattern = privatePart ? new RegExp(`(${privatePart})`, 'gi') : null;

        if (regexPattern) {
          highlighter.innerHTML = text.replace(regexPattern, '<span style="background: rgba(255, 0, 0, 0.1)">$1</span>');
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.highlighter {
  position: absolute;
  top: 0;
  left: 36px;
  z-index: 1;
  color: transparent;
  background: transparent;
  white-space: pre-wrap;
}
</style>
