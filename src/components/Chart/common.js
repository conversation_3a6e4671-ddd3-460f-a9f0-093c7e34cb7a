export const xAxisCommonConfig = {
  axisLine: {
    lineStyle: {
      color: '#cccccc'
    }
  },
  axisLabel: {
    textStyle: {
      color: 'rgba(0, 0, 0, 0.65)',
      fontSize: 12,
      fontFamily: 'Source Han Sans SC'
    }
  }
};

export const yAxisCommonConfig = {
  axisLabel: {
    textStyle: {
      color: 'rgba(0, 0, 0, 0.65)'
    },
    formatter(value) {
      return unitFormatter(value);
    }
  },
  nameTextStyle: {
    color: 'rgba(0, 0, 0, 0.65)'
  },
  axisLine: {
    lineStyle: {
      color: 'red'
    }
  },
  splitLine: {
    show: true,
    lineStyle: {
      color: ['#eeeeee']
    }
  }
};

export const getDataZoomCommonConfig = (length,isFullScreen) => {
  const show = isFullScreen && length > 15 || !isFullScreen && length > 4;
  const end = isFullScreen && length > 15 ? (15 / length) * 100 : !isFullScreen && length > 4 ? (4 / length) * 100 : 100 ;
  return {
    type: 'slider',
    show,
    start: 0,
    end,
    bottom: 0,
    height: 8,
    showDetail: false,
    fillerColor: 'rgba(0, 0, 0, 0.2)',
    brushSelect: false,
    borderRadius: 0,
    borderColor: '#f6f6f6',
    backgroundColor: '#f6f6f6',
    handleStyle: {
      opacity: 0
    },
    dataBackground: {
      lineStyle: {
        opacity: 0
      },
      areaStyle: {
        opacity: 0
      }
    },
    selectedDataBackground: {
      lineStyle: {
        opacity: 0
      },
      areaStyle: {
        opacity: 0,
        borderRadius: 4,
      }
    }
  }
}

export function getYMax(maxValue) {
  // 5 是echarts默认的y轴分割线数量
  const unitValue = maxValue / 5;
  const metric = parseInt(unitValue).toString().length;
  const partValue = 10 ** metric;
  return partValue * (5 + 1);
}

const fn = (value, fixed) => {
  if (fixed) {
    return Number(value).toFixed(1);
  }
  return Number(value);
};

export const unitFormatter = (val, fixed = true, appendix = true) => {
  const number = Math.abs(Number(val));
  const modifier = val >= 0 ? '' : '-';
  switch (true) {
    case number < 1e4:
      return modifier + number;
    case number >= 1e4 && number < 1e8:
      return modifier + fn(number / 1e4, fixed) + (appendix ? '万' : '');
    case number >= 1e8:
      return modifier + fn(number / 1e8, fixed) + (appendix ? '亿' : '');
  }
};
