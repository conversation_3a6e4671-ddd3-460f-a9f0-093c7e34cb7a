<script>
import * as echarts from 'echarts';
import EventBus from '@utils/eventBus'

export default {
  name: 'BasicEcharts',
  // inject: ['handleGroundClick'],
  props: {
    option: {
      type: Object,
      required: true
    },
    id: {
      type: String,
      required: true
    },
    height: {
      type: String,
      required: true
    },
    immediate: {
      type: Boolean,
      default: false
    },
    isEmpty: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null,
      observer: null,
      calcHeight: '150px'
    };
  },
  methods: {
    resizeChart() {
      this.chart.resize();
    },
    setHeight() {
      if (this.height === 'auto') {
        const el = document.getElementById(this.id);
        if (el) {
          this.calcHeight = el.parentElement.offsetHeight + 'px';
        }
      } else {
        this.calcHeight = this.height;
      }
    },
    initChart() {
      const dom = document.getElementById(this.id);
      if (dom) {
        const chart = echarts.getInstanceByDom(dom);
        chart && chart.dispose();
        this.chart = echarts.init(dom);
      }
    },
    renderChart(option) {
      if (this.chart && option) {
        this.setHeight();
        this.chart.setOption(option);
        this.observer = new ResizeObserver(this.chart.resize);
        this.observer.observe(document.getElementById(this.id));
      }
    }
  },
  mounted() {
    this.initChart();
    this.$nextTick(() => {
      this.immediate && this.renderChart(this.option);
    });
    this.$nextTick(() => {
      this.resizeChart();
      setTimeout(() => {
        this.resizeChart();
      }, 500)
    })
    window.addEventListener('resize', this.resizeChart); // 监听窗口大小变化
  },
  beforeDestroy() {
    this.observer && this.observer.disconnect();
    window.removeEventListener('resize', this.resizeChart); // 清理监听器
  },
  watch: {
    option: {
      deep: true,
      handler(newOption) {
        if (newOption) {
          this.initChart();
          this.$nextTick(() => {
            this.renderChart(newOption);
          });
          this.chart.on('click',(params) => {
            // if (this.handleGroundClick) {
            //   this.handleGroundClick(params)
            // }
            // EventBus.$emit('handleGroundClick', params);

          })

        }
      }
    }
  }
};
</script>

<template>
  <div v-if="!isEmpty" :id="id" :style="{height: calcHeight}" class="full-width" :ref="`chartRef-${id}`" />
</template>

<style scoped lang="scss">

</style>
