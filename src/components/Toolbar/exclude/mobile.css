.project-title-popover {
    left: 0 !important;
}

.portal-switch-popup {
    width: 100%;
    top: 48px;
    transform: translate3d(-50%, 0, 0);
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
    position: absolute;
    transition: all 0.2s ease-in-out;
    display: flex;
    flex-direction: column;
}

.portal-switch-popup .bar {
    width: 100%;
    padding: 10px;
}
.portal-switch-popup .bar .indicator {
    width: 32px;
    height: 4px;
    border-radius: 2px;
    background: rgba(0, 0, 0, 0.2);
    margin: 0 auto;
}

.portal-recent-container {
    margin-bottom: 16px;
    .title {
        font-size: 14px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: rgba(25, 25, 25, 0.4);
        margin-bottom: 12px;
        padding-left: 36px;
    }

    .recent-portal-list {
        border-bottom: 1px solid #F1F1F0;
    }
}

.portal-list-container .group-title {
    margin-bottom: 8px;
    padding: 2px 0 2px 16px;
    transition: all 0.2s ease-in-out;
}

.group-title.active {
    background: #DCE4FA;
}

.group-title-text {
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: normal;
    color: #000000;
}

.portal-item {
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #333333;
    white-space: normal;
    padding: 6px 0 6px 46px;
}

.portal-item:last-child {
    margin-bottom: 6px;
}

.portal-item .portal-item-text {
}

.portal-item.active {
    background: #DCE4FA;
}
