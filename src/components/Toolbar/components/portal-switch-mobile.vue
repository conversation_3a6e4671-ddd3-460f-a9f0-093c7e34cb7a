<template>
  <div class="flex col-center row-between" style="width: 90%">
    <fm-popover
      :visible.sync="showProjectTitlePopup"
      trigger="click"
      style="width: 80%"
      popper-class="project-title-popover"
    >
      <div style="font-size: 14px;color: #555555;padding: 10px">{{currentPortalName}}</div>
      <template slot="reference">
        <div class="text-ellipsis" style="font-size: 18px;color: #555555;font-weight: 500;">{{currentPortalName}}</div>
      </template>
    </fm-popover>
    <fks-icon
      icon-class="search"
      class="search-icon"
      style="height: 24px;width: 24px;flex-shrink: 0;"
      @click.native="handleSearchClick()"
    />
    <fks-icon
      icon-class="project-switch-icon"
      style="height: 24px;width: 24px;flex-shrink: 0"
      @click.native="showPopup = !showPopup"
    />
    <fm-popup
      :style="{height: expand ? '90%' : '60%'}"
      :visible.sync="showPopup"
      custom-class="portal-switch-popup"
    >
      <fm-search
        v-model="searchVal"
        clearable
        placeholder="请输入项目名称"
      />
      <main class="flex-grow-1 overflow-y-auto">
        <div class="portal-recent-container">
          <div class="title">收藏项目</div>
          <div class="recent-portal-list">
            <div
              v-for="(item, index) in favoriteProjects"
              :key="index"
              :class="{active: activeItem === item.id}"
              class="portal-item flex col-center row-between"
              @click="handleRecentClickMobile(item.id)"
            >
              <span class="portal-item-text">{{ item.name }}</span>
              <div class="portal-leaf-icon" @click.stop="handleCollectClick(item)" style="padding: 0 15px">
                <i v-if="!item.isCollect" class="fks-icon-star-off" style="color: #999999;font-size: 15px"></i>
                <i v-else class="fks-icon-star-on" style="color: #FEC73C;font-size: 16px"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="portal-recent-container">
          <div class="title">近6个月常用项目</div>
          <div class="recent-portal-list">
            <div
              v-for="(item, index) in recentlyUsedProjects"
              :key="index"
              :class="{active: activeItem === item.id}"
              class="portal-item"
              @click="handleRecentClickMobile(item.id)"
            >
              <span class="portal-item-text">{{ item.name }}</span>
            </div>
          </div>
        </div>
        <div class="portal-list-container">
          <div
            v-for="(group, index) in groupedThirdPortalTreeData"
            :key="index"
            class="portal-group-item"
          >
            <div
              :class="{active: activeItem === group.name}"
              class="group-title flex col-center"
              @click="handleTitleClick(group)"
            >
              <i
                :class="getIconClass(group.name)"
                style="margin-right: 4px;font-size: 16px;width: 16px;height: 16px"
              />
              <span class="group-title-text">{{ group.name }}</span>
            </div>
            <transition name="fold-transition">
              <div v-if="showFold(group)" class="portal-list">
                <div
                  v-for="(item, index) in group.children"
                  :key="index"
                  :class="{active: currentPortalId === item.id}"
                  class="portal-item flex col-center row-between"
                  @click.stop="handleNodeClick(item)"
                >
                  <span class="portal-item-text">{{ item.name }}</span>
                  <div class="portal-leaf-icon" @click.stop="handleCollectClick(item)" style="padding: 0 15px">
                    <i v-if="!item.isCollect" class="fks-icon-star-off" style="color: #999999;font-size: 15px"></i>
                    <i v-else class="fks-icon-star-on" style="color: #FEC73C;font-size: 16px"></i>
                  </div>
                </div>
              </div>
            </transition>
          </div>
        </div>
      </main>
      <div class="bar">
        <div class="indicator"/>
      </div>
    </fm-popup>
  </div>
</template>
<script>
import AlloyFinger from "alloyfinger";
import portalSwitchMixin from "@/mixins/portalSwitchMixin";
import * as ActionTypes from "@store/Action/actionTypes"
import EventBus from "@utils/eventBus";
export default {
  name: "PortalSwitchMobile",
  mixins: [portalSwitchMixin],
  data() {
    return {
      showProjectTitlePopup: false,
      showPopup: false,
      expand: false,
      activeItem: '',
      searchVal: '', // 搜索值，需要从其他组件传入进来
      expandKeys: []
    }
  },
  methods: {
    getIconClass(name) {
      const key = this.expandKeys.find(item => item === name);
      return key ? 'fks-icon-arrow-down' : 'fks-icon-arrow-right';
    },
    showFold(group) {
      return this.expandKeys.includes(group.name);
    },
    handleMoveUp() {
      if (this.expand) {
        this.expand = false
      }
    },
    handleMoveDown() {
      if (!this.expand) {
        this.expand = true
      }
    },
    handleRecentClickMobile(id) {
      // activeItem 用于点击项目后，需要有个点击选中的样式，选中后随即去掉选中样式
      this.activeItem = id;
      this.handleRecentClick(id);
      setTimeout(() => {
        this.activeItem = '';
      }, 300)
    },
    handleTitleClick(group) {
      // 在expandKeys中查找当前group的name
      const key = this.expandKeys.find(item => item === group.name);
      // 如果x存在，说明当前group已经展开，需要收起
      if (key) {
        this.expandKeys = this.expandKeys.filter(item => item !== group.name);
      } else {
        this.expandKeys.push(group.name);
      }
      this.activeItem = group.name;
      this.$forceUpdate();
      setTimeout(() => {
        this.activeItem = '';
      }, 200)
    },
    handleSearchClick() {
      EventBus.$emit('on-search-show', true);
    }
  },
  watch: {
    showPopup(val) {
      const that = this;
      if (val) {
        setTimeout(() => {
          const overlay = document.querySelector('.fm-overlay')
          if (overlay) {
            overlay.style.top = '49px';
          }
        })
        // 监听上滑/下滑事件
        this.$nextTick(() => {
          const el = document.querySelector('.bar')
          new AlloyFinger(el, {
            swipe(evt) {
              if (evt.direction === 'Down') {
                that.handleMoveDown()
              } else if (evt.direction === 'Up') {
                that.handleMoveUp()
              }
            }
          })
        })
      }
    }
  },
  mounted() {
    // 每次切换项目后需要更新常用项目列表
    this[ActionTypes.GET_RECENTLY_USED_PORTALS]();
  }
};
</script>

<style lang="less" scoped>
@import '../exclude/mobile.css';
/* 折叠过渡动画 */
.fold-transition-enter-active,
.fold-transition-leave-active {
  transition: all 0.3s cubic-bezier(0.3, 0.7, 0.5, 1);
}

.fold-transition-enter,
.fold-transition-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
.search-icon {
  margin-right: 10px;
}
</style>
