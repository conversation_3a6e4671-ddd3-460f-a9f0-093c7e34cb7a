<template>
  <div class="flex col-center row-between bg-white" style="padding: 10px 18px 10px 14px">
    <portal-switch-mobile class="flex-grow-1" />
    <fks-dropdown trigger="click" @command="handleCommand" style="flex-shrink: 0;margin-left: 12px">
      <fm-image :src="require('@/assets/svg/three-dot.png')" alt="#" height="24px" width="24px"/>
      <fks-dropdown-menu slot="dropdown">
        <fks-dropdown-item
          v-for="(item, index) in options"
          :key="index"
          :command="item.icon"
          :disabled="item.disabled"
        >
          <div class="flex col-center help-item" style="height: 32px">
            <fks-icon :icon-class="item.icon"/>
            <div class="dropdown-text" style="font-size: 14px;margin-left: 4px">{{ item.name }}
            </div>
            <div v-if="getNotifyStatus(item.name)" class="tool-dot">
            </div>
          </div>
        </fks-dropdown-item>
      </fks-dropdown-menu>
    </fks-dropdown>
  </div>
</template>

<script>
import PortalSwitchMobile from "@components/Toolbar/components/portal-switch-mobile.vue";
import { mapActions, mapState } from 'vuex'

export default {
  name: 'tool-bar',
  components: {PortalSwitchMobile},
  data() {
    return {
      options: [
        {name: '问题反馈', disabled: false, icon: 'feedback'},
        {name: '常见问题', disabled: false, icon: 'question'},
        // {name: '操作手册', disabled: true, icon: 'manual'},
        {name: '更新日志', disabled: false, icon: 'log'}
      ]
    }
  },
  computed: {
    ...mapState('HelpCenter', ['itemList']), // 注意这里用 state 的变量名 itemList，而 getter 用 mapGetters
  },
  created() {
    this.getUnReadMsg()
  },
  methods: {
    ...mapActions('HelpCenter', ['getUnReadMsg']),
    getNotifyStatus(name) {
      // 遍历 itemList 找到 name 相同的项
      const found = this.itemList.find(item => item.name === name);
      // 如果找到了就返回对应的 notify 值，否则返回 false 或其他默认值
      return found ? found.notify : false;
    },
    handleCommand(command) {
      if (command === 'feedback') {
        this.$router.push('/feedback')
      } else if (command === 'log') {
        this.$router.push('/updateLog')
      }else if (command === 'question') {
        this.$router.push('/question')
      }
    }
  }
}
</script>
<style lang="less" scoped>
.dropdown-text {
  font-weight: normal;
  line-height: normal;
  text-align: center;
  letter-spacing: normal;
  color: #555555;
}

.help-item {
  position: relative;
}

.tool-dot {
  position: absolute;
  top: 4px;
  right: -14px;
  width: 14px;
  height: 14px;
  background-color: red;
  border-radius: 50%;
}

</style>
