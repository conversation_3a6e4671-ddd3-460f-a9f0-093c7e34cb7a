<template>
	<div class="flex m-t-30 m-l-30 m-b-30">
		<div class="left-line m-r-20"></div>
    <div class="title-text">{{ text }}</div>
	</div>
</template>

<script>
	export default {
		name: "ItemText",
    props: {
      text: {
        type: String,
        default: '行程信息'
      }
    },
		data() {
			return {

			};
		}
	}
</script>

<style lang="scss" scoped>
.left-line {
  width: 8px;
  height: 32px;
  background: #1A90FE;
  border-radius: 4px;
}
.title-text {
  font-size: 32px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #272727;
  line-height: 32px;
}
</style>
