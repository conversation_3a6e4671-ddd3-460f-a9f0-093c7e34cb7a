<template>
  <fm-dialog
    :visible.sync="visible"
    title="提示"
    show-cancel-button
    @confirm="handleConfirm"
  >
    <div style="padding: 20px">
      <div class="m-b-24">{{ message }}</div>
      <fks-checkbox v-model="notificationChecked">我已知晓，不再提醒</fks-checkbox>
    </div>
  </fm-dialog>
</template>

<script>
import isConfirmXCMixin from '@/mixins/isConfirmXCMixin'

export default {
  name: "NotificationConfirmMobile",
  mixins: [isConfirmXCMixin]
}
</script>
