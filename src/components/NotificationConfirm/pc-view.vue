<template>
  <fks-dialog :visible.sync="visible" title="提示" width="450px">
    <div>
      <div class="m-b-24">{{ message }}</div>
      <fks-checkbox v-model="notificationChecked">我已知晓，不再提醒</fks-checkbox>
    </div>
    <span slot="footer" class="dialog-footer">
      <fks-button type="primary" @click="handleConfirm">确定</fks-button>
    </span>
  </fks-dialog>
</template>

<script>
import isConfirmXCMixin from '@/mixins/isConfirmXCMixin'
import { changeTips } from '@utils/constants'

export default {
  name: 'NotificationConfirm',
  mixins: [isConfirmXCMixin]
}
</script>
