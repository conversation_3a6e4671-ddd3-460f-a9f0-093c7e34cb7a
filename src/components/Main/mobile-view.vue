<template>
  <div>
    <router-view class="content" />
    <fm-tabbar
      v-if="!$route.path.includes('application') "
      route
      v-model="active"
      safe-area-inset-bottom
      @change="changeTab"
    >
      <fm-tabbar-item
        v-for="item in tabRoutes"
        :key="item.id"
        :name="item.path"
        :to="getRoute(item)"
        :badge="dotTable[item.name] || ''"
      >
        <template #icon="props">
          <fks-icon :icon-class="item.icon" class="svg" />
<!--            :icon-class="props.active ? `mobile-${item.icon}-active` : `mobile-${item.icon}`"-->
<!--          <fks-icon -->
<!--            v-else-->
<!--            :-->
<!--            class="svg"-->
<!--          />-->
        </template>
        {{item.title}}
      </fm-tabbar-item>
    </fm-tabbar>
  </div>
</template>
<script>
import mixins from './mixins';
import platform from "@/mixins/platform";
import menuRouteMixin from "@/mixins/menuRouteMixin";
import menuDotMixin from "@/mixins/menuDotMixin";
export default {
  name: 'MobileMain',
  provide() {
    return {
      reload: () => {}
    }
  },
  mixins: [mixins, platform, menuRouteMixin, menuDotMixin],
  components: {

  },
  data() {
    return {
      active: 0
    }
  },
  methods: {
    changeTab() {}
  }
}
</script>
<style lang="less" scoped>
@import "./mobile-view";
</style>
