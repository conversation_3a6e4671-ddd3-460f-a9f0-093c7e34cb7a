.main-container {
  position: relative;
  display: flex;

  .indicator {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    align-items: center;
    background: white;
    width: 38px;
    height: 80px;
    z-index: 99;
    font-size: 32px;
    justify-content: center;
    display: flex;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    cursor: pointer;
  }

  .menu {
    display: flex;
    flex-direction: column;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 90;
    background: #FFFFFD;
    box-shadow: 3px 0px 8px 0px rgba(0,0,0,0.03);
    .search-input {
      width: 360px;
      margin: 20px;
    }
  }

  .content {
    overflow: hidden;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    padding: 0;
    background: #F8F9FA;
    &.p-0 {
      padding: 0;
    }
    &:has(.cards) {
      padding: 0;
    }
    &:has(#projectIndexContainer) {
      padding: 0;
      border: none;
    }
  }
}
