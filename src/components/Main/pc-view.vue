<template>
  <div class="main-container" :class="{'bg-white': !['StatisticalCharts'].includes($route.name) }">
    <div class="menu">
      <fks-input
        v-if="!collapse"
        ref="searchInput"
        placeholder="搜索"
        class="search-input"
        v-model="searchVal"
        @keyup.enter.native="handleSearch"
        :clearable="true"
        @clear="handleClear"
      >
        <i slot="prefix" class="fks-input__icon fks-icon-search"></i>
      </fks-input>
      <div v-else style="height: 56px;" class="position-relative">
        <div class="position-absolute cursor-pointer bg-div" style="top: 8px; left: 9px; padding: 12px 16px" @click="handleIconClick">
          <i class="fks-icon-search" style="font-size: 14px;" />
        </div>
      </div>
      <Menu :collapse="collapse" :data="tabRoutes" style="margin: 0 10px 10px;"/>
      <div class="hamburger-container cursor-pointer full-width bg-white" style="box-sizing: border-box;height: 56px; border-top: 1px solid #DDDDDD; line-height: 56px; padding: 7px 10px">
        <div class="full-width full-height bg-div" style="vertical-align: middle; display: flex; align-items: center; user-select: none;" @click="handleClick">
          <img :src="require('@/assets/img/login/unpack.png')" alt="图标" width="18px" class="fks-icon-s-unfold" :class="{'is-active':collapse}" :style="!collapse ? 'margin-left: 22px;' : 'margin: 0 auto;'" />
          <span v-if="!collapse" style="font-size: 14px; color: #333; font-family: MicrosoftYaHei; margin-left: 5px;" class="">收起导航</span>
        </div>
      </div>
    </div>

    <div id="pcMain" :style="{left: collapse ? '65px' : '193px'}" class="content" :class="{'p-0': ['StatisticalCharts'].includes($route.name)}">
      <router-view v-if="isRouterAlive" key="isNotAlive" style="height: calc(100% - 14px);" />
      <div style="position: relative;bottom: 0;text-align: center;color: #999999;height: 14px;line-height: 14px;font-size: 10px">本系统由水电水利工程院研发，如有疑问和建议，可联系何展国、宋睿、周碧云。</div>
    </div>
  </div>
</template>

<script>
import Menu from '@components/menus/components/Menu.vue';
import mixins from './mixins';
import * as MutationTypes from "@store/Mutation/mutationTypes";
import * as StateTypes from "@store/State/stateTypes";
import * as ActionTypes from '@store/Action/actionTypes';
import storage from "@utils/storage";
import {mapMutations, mapActions} from "vuex";
import CompactedSearchBar from "@components/CompactedSearchBar/index.vue";
import { debounce, isEmpty } from '@utils/util'
import {searchConfigHashTable, GLOBAL_SEARCH} from "@utils/constants";
import EventBus from '@utils/eventBus'

export default {
  name: "PcMain",
  components: {Menu, CompactedSearchBar},
  mixins: [mixins],
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      searchVal: '',
      isRouterAlive: true,
      observer: {},
      highlightMarking: false,
      debouncedSearch: null,
    }
  },
  computed: {
    searchItem() {
      return searchConfigHashTable[this.$route.name];
    },
    searchConfigs() {
      return this.searchItem ? this.searchItem.configs : [];
    },
    searchProp() {
      return this.searchItem ? this.searchItem.defaultSearchProp : '';
    }
  },
  mounted() {
    setTimeout(() => {
      this.listenerDomChange();
    }, 3000)
  },
  beforeDestroy() {
    EventBus.$off('scroll');
    EventBus.$off(GLOBAL_SEARCH);
  },
  methods: {
    ...mapMutations([MutationTypes.SET_COLLAPSE, MutationTypes.SET_GLOBAL_QUERY_PARAMS, MutationTypes.SET_QUERY_TABLE_DATA]),
    ...mapActions([ActionTypes.QUERY_GLOBAL_TABLE_DATA]),
    reload() {
      console.log('reload 执行')
      this.isRouterAlive = false;
      this.$nextTick(()=>{
        this.isRouterAlive = true;
      })
    },
    handleIconClick() {
      this.handleClick();
      this.$nextTick(() => {
        this.$refs.searchInput && this.$refs.searchInput.focus();
      })
    },
    handleClick() {
      this[MutationTypes.SET_COLLAPSE](!this[StateTypes.COLLAPSE])
      const val = (this[StateTypes.COLLAPSE]).toString()
      storage.set(StateTypes.COLLAPSE, val);
    },
    handleSearch() {
      this[MutationTypes.SET_GLOBAL_QUERY_PARAMS]({searchValue: this.searchVal});
      this.$nextTick(() => {
        EventBus.$emit(GLOBAL_SEARCH);
      })
    },
    handleClear() {
      this[MutationTypes.SET_GLOBAL_QUERY_PARAMS]({});
      this.$nextTick(() => {
        EventBus.$emit(GLOBAL_SEARCH);
      })
    },
    listenerDomChange() {
      this.debouncedSearch = debounce(() => {
        this.highlightText(this.searchVal);
      }, 150);

      EventBus.$on('scroll', (val) => {
        this.canScroll = val;
      })

      // 创建一个 MutationObserver 实例，传入回调函数
      this.observer = new MutationObserver((mutationsList, observer) => {
        this.debouncedSearch();
      });

      // 选择要监听的 DOM 元素
      const targetNode = document.getElementById('pcMain');  // 选择具体的 DOM 节点

      // 配置监听选项
      const config = {
        childList: true,  // 监听子节点的添加或删除
        attributes: true, // 监听属性的变化
        subtree: true,    // 监听整个子树的变化
        characterData: true // 监听文本内容的变化
      };

      // 开始监听
      this.observer.observe(targetNode, config);

    },
    // 全局高亮函数
    highlightText(value) {
      if (this.highlightMarking) return;
      this.highlightMarking = true;

      const main = document.getElementById('pcMain');

      // 清除旧高亮标签
      try {
        const oldHighlights = main?.querySelectorAll('hl');
        oldHighlights?.forEach(el => {
          const parent = el.parentNode;
          if (parent) {
            const textNode = document.createTextNode(el.textContent);
            parent.replaceChild(textNode, el);
            parent.removeAttribute('data-highlighted');
          }
        });
      } catch (error) {
        console.error('Error clearing highlight tags:', error);
      }

      if (value && value.trim() !== "") {
        const keyword = value.toLowerCase();

        const recursiveHighlight = (node) => {
          if (!node) return;

          if (
            node.nodeType === Node.TEXT_NODE &&
            node.parentNode &&
            node.parentNode.nodeName !== 'HL' &&
            node.parentNode.getAttribute('data-highlighted') !== 'true'
          ) {
            const text = node.textContent;
            const lowerText = text.toLowerCase();
            const index = lowerText.indexOf(keyword);

            if (index !== -1) {
              const range = document.createRange();
              range.setStart(node, index);
              range.setEnd(node, index + keyword.length);

              const hl = document.createElement('hl');
              hl.style.backgroundColor = '#D9E3FA';
              hl.style.color = 'rgb(64, 158, 255)';
              hl.style.padding = '0 1px';
              hl.textContent = text.substring(index, index + keyword.length);

              range.deleteContents();
              range.insertNode(hl);

              if (hl.parentNode) {
                hl.parentNode.setAttribute('data-highlighted', 'true');
              }

              // 递归重新开始处理该父节点剩余子节点（因为结构发生变化）
              recursiveHighlight(hl.nextSibling);
            }
          } else if (node.nodeType === Node.ELEMENT_NODE && node.nodeName !== 'HL') {
            Array.from(node.childNodes).forEach(child => recursiveHighlight(child));
          }
        };

        recursiveHighlight(main);
      }

      this.$nextTick(() => {
        requestAnimationFrame(() => {
          this.highlightMarking = false;
        });
      });
    }
  },
  watch: {
    '$route.name': {
      handler(){
        this.searchVal = ''
        this[MutationTypes.SET_GLOBAL_QUERY_PARAMS]({});
      },
    }
  }
}
</script>

<style lang="less" scoped>
@import "./pc-view";
.fks-icon-s-unfold.is-active {
  transform: rotate(180deg);
  padding-left: 0px;
}
.hamburger-container {
  float: left;
  left: 0;
  bottom: 0;
  z-index: 9999;
  flex: 0 0 auto;
  transition: background 0.3s;
  -webkit-tap-highlight-color: transparent;

}
.bg-div:hover {
  background-color: #DCE4FA;
  color: #336EF3;
  border-radius: 12px;
}
</style>
