import * as MutationTypes from "@store/Mutation/mutationTypes";
import * as StateTypes from "@store/State/stateTypes";
import {mapMutations, mapState} from "vuex";
import {traverse} from "@utils/util";
import platform from "@/mixins/platform";
import {COMPANY_PORTAL} from "@utils/constants";
export default {
  mixins: [platform],
  computed: {
    ...mapState([StateTypes.COLLAPSE, StateTypes.PORTAL, StateTypes.ROUTES, StateTypes.SECOND_LEVEL_PORTAL, StateTypes.DATA_BASE_PORTAL]),
    // 是否是数据舱门户
    isDataBasePortal() {
      return this[StateTypes.PORTAL].id === this[StateTypes.DATA_BASE_PORTAL].companyPortalId
    },
    tabRoutes() {
      let routes = [];
      traverse(this[StateTypes.ROUTES], (item) => {
        if (this.isDataBasePortal) {
          if (item.title === COMPANY_PORTAL) {
            routes = item.children;
          }
        } else {
          if (item.title === this[StateTypes.SECOND_LEVEL_PORTAL]) {
            routes = item.children;
            return true;
          }
        }
      }, 'children')
      const routesInfo = routes
        .filter(route => {
          if (Array.isArray(route.meta.config) && route.meta.config.length) {
            return route.meta.config.findIndex(item => item === 'invisible') === -1
          }
          return true;
        })
        .filter(route => {
        if (this.isPC) {
          return route.remark !== 'mobile'
        } else if (this.isMobile) {
          return route.remark !== 'pc'
        }
        return true
      });
      if (this.isMobile) {
        routesInfo.forEach(route => {
          if (route.name === 'CarRecord') {
            route.sort = 1
          } else if (route.name === 'Todo') {
            route.sort = 3
          }
        })
      }
      return routesInfo.sort((a, b) => a.sort - b.sort);
    },
  }
}
