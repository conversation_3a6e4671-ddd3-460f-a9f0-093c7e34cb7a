<template>
  <transition name="fade">
    <div
      :class="type"
      class="custom-alert-container"
      style="padding: 4px 8px;border-radius: 50px;font-size: 14px"
    >
      <img :src="iconUrl" style="margin-right: 4px"/>
      <div>{{ title }}</div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'CustomAlert',
  props: {
    type: {
      type: String,
      default: 'success'
    }
  },
  computed: {
    iconUrl() {
      if (this.type === 'success') {
        return require('@/assets/img/application/success-1.svg')
      } else if (this.type === 'error') {
        return require('@/assets/img/application/error-1.svg')
      }
    },
    title() {
      if (this.type === 'success') {
        return '识别成功'
      } else if (this.type === 'error') {
        return '识别失败，您可选择重新上传或手动录入'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.custom-alert-container {
  position: fixed;
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;

  &.success {
    background: rgba(207, 238, 213, 1);
    color: #40BB5A;
  }

  &.error {
    background: rgba(255, 217, 217, 1);
    color: #FF4143;
  }
}
</style>
