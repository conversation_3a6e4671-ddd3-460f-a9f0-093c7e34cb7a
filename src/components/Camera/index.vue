<template>
  <div class="uploadFacePic">
    <!-- 1 原始相机 不兼容时的回退方案-->
    <input id="backup-camera" accept="image/*" capture="environment" style="display: none" type="file">
    <!-- 2 自定义的相机 -->
    <div v-if="cameraShow" class="camera-container">
      <i
        class="fks-icon-arrow-left back"
        style="font-size: 32px;top: 20px;left: 10px;"
        @click="handleBack"
      />
      <img
        :src="require('@/assets/img/application/capture.svg')"
        class="camera"
        @click="snapPhoto"
      />
      <div v-if="showFrame" class="frame-container">
        <div class="viewfinder">
          <div class="corner top-left"></div>
          <div class="corner top-right"></div>
          <div class="corner bottom-left"></div>
          <div class="corner bottom-right"></div>
        </div>
      </div>
      <div class="description">请将带有总里程数据的仪表界面置于框内，避免反光</div>
      <video playsinline></video>
      <canvas id="mycanvas"></canvas>
    </div>
  </div>
</template>

<script>
import audioSource from '@/assets/audio/audio.mp3';
import {recognizePicture, uploadFile} from "@components/Camera/api";

export default {
  name: 'CustomCamera',
  data() {
    return {
      disableSubmit: false,
      cameraShow: false,
      showFrame: false,
      isOriginal: true, // 是否使用原生相机
      status: 1, // 1 表示拍摄中 2 表示拍摄完成
      type: '',//上传类型 update|upload
      imageUrl: '',//自定义相机-抓拍url
      front: true,// 自定义相机-前置与后置转换（未验证）
      imageFile: '',//图片对象
      prop: ''
    }
  },
  methods: {
    handleBack() {
      this.cameraShow = false;
      this.showFrame = false;
      let video = document.querySelector('video');
      if (video && video.srcObject) {
        video.srcObject.getTracks().forEach(function (track) {
          track.stop();
        });
      }
    },
    openCamera(prop) {
      this.prop = prop;
      // 1. 先展示，因为要从这里获取video标签
      this.cameraShow = true
      // 2. constraints:指定请求的媒体类型和相对应的参数
      var constraints = {
        audio: false,
        video: {
          facingMode: 'environment',
          width: { ideal: 3024 / 2 },  // 添加分辨率约束
          height: { ideal: 4032 / 2 },
          frameRate: { ideal: 60 } // 添加帧率约束
        }
      }

      // 3. 兼容部分：
      // 老的浏览器可能根本没有实现 mediaDevices，所以我们可以先设置一个空的对象
      if (navigator.mediaDevices === undefined) {
        navigator.mediaDevices = {};
      }
      // 一些浏览器部分支持 mediaDevices。我们不能直接给对象设置 getUserMedia
      // 因为这样可能会覆盖已有的属性。这里我们只会在没有getUserMedia属性的时候添加它。
      if (navigator.mediaDevices.getUserMedia === undefined) {
        navigator.mediaDevices.getUserMedia = function (constraints) {
          // 首先，如果有getUserMedia的话，就获得它
          var getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia || navigator.oGetUserMedia;
          // 一些浏览器根本没实现它 - 那么就返回一个error到promise的reject来保持一个统一的接口
          if (!getUserMedia) {
            return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
          }
          // 否则，为老的navigator.getUserMedia方法包裹一个Promise
          return new Promise(function (resolve, reject) {
            getUserMedia.call(navigator, constraints, resolve, reject);
          });
        }
      }


      // 4. 获取视频流
      let that = this
      navigator.mediaDevices.getUserMedia(constraints)
        .then(function (stream) {
          // 进来这里表示能够兼容
          let video = document.querySelector('video');
          video.srcObject = stream;
          video.onloadedmetadata = function () {
            video.play();
            setTimeout(() => {
              that.showFrame = true;
            })
          };
          // 进入自定义拍摄模式
          that.status = 1
        })
        .catch(function (err) {
          // 进来这里表示不能兼容
          console.log('nonono', err)
          // 调用原始摄像头
          that.originCamera()
        });
    },
    playAudio() {
      const audio = new Audio(audioSource);
      audio.play();
    },
    snapPhoto() {
      if (this.disableSubmit) return;
      this.playAudio();
      var canvas = document.querySelector('#mycanvas');
      var video = document.querySelector('video');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);

      // 保存为文件，用于后续的上传至服务器（尚未实践）——>后续提交
      this.imageFile = this.canvasToFile(canvas)
      console.info('🚀🚀', 'this.imageFile -->', this.imageFile, `<-- index.vue/snapPhoto`)
      console.info('🚀🚀', 'this.imageFile.size -->', this.imageFile.size, `<-- index.vue/snapPhoto`)

      // blob转url：用于展示
      let p = new Promise((resolve) => {
        canvas.toBlob(blob => {
          let url = URL.createObjectURL(blob)
          resolve(url)
        });
      })
      let that = this
      p.then(value => {
        that.imageUrl = value
        this.uploadPicture();
        that.status = 2//表示拍摄完成
        URL.revokeObjectURL(value)
      })
    },
    canvasToFile(canvas) {
      var dataurl = canvas.toDataURL("image/png");
      var arr = dataurl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      var file = new File([u8arr], "phone.png", {type: mime});
      return file
    },
    originCamera() {
      console.log('调用原始摄像头！！')
      let that = this

      //关闭自定义相机
      that.cameraShow = false
      let promise = new Promise(function (resolve, reject) {
        let file = document.getElementById('backup-camera')
        file.click()
        file.onchange = function (event) {
          if (!event) {
            reject('empty')
          }
          //当选中或者拍摄后确定图片后，保存图片文件——>后续提交
          let file = event.target.files[0]
          resolve(file)
        }
      })
      promise.then(async (value) => {
        if (value.size >= 1024 * 1024 * 2) {
          const base64Str = await this.compressImage(value)
          this.imageFile = this.base64ToFile(base64Str, value.name);
        } else {
          this.imageFile = value;
        }
        await this.uploadPicture();
      })
    },
    fileToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = () => {
          // 去除 data URL 前缀（如："data:image/png;base64,"）
          const base64String = reader.result.split(',');
          resolve(base64String);
        };

        reader.onerror = error => reject(error);

        // 读取文件为 Data URL（自动包含 base64 编码）
        reader.readAsDataURL(file);
      });
    },
    base64ToFile(base64, fileName = '') {
      // 分割base64字符串，获取mime类型和实际的base64数据
      let arr = base64.split(",");
      let mime = arr[0].match(/:(.*?);/);
      // return new File([u8arr], fileName, { type: mime });
      // 移除Base64前缀（如果有）
      const base64Data = base64.replace(/^data:[^;]+;base64,/, '');

      // 将Base64解码为二进制字符串
      const byteString = atob(base64Data);

      // 创建一个Uint8Array来存储二进制数据
      const byteArray = new Uint8Array(byteString.length);
      for (let i = 0; i < byteString.length; i++) {
        byteArray[i] = byteString.charCodeAt(i);
      }

      // 创建Blob对象
      const blob = new Blob([byteArray], {type: mime});

      // 创建File对象
      return new File([blob], fileName, {type: mime});
    },
    compressImage(file) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        const reader = new FileReader();

        reader.onload = function (e) {
          img.src = e.target.result;
          img.onload = function () {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            // 设置目标宽度，例如缩小到800px宽度
            const maxWidth = 800;
            const scaleFactor = 1;
            const scale = maxWidth / img.width;
            canvas.width = maxWidth * scaleFactor;
            canvas.height = img.height * scale * scaleFactor;
            ctx.imageSmoothingEnabled = false;
            ctx.imageSmoothingQuality = 'high';
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            // 压缩质量，0.7表示70%质量
            const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.7);
            resolve(compressedDataUrl); // 成功时返回压缩后的图片
          };
          img.onerror = function () {
            reject(new Error('图片加载失败')); // 图片加载失败时拒绝
          };
        };

        reader.onerror = function () {
          reject(new Error('文件读取失败')); // 文件读取失败时拒绝
        };

        reader.readAsDataURL(file);
      });
    },
    async uploadPicture() {
      this.cameraShow && this.handleBack();
      this.$emit('loading', {key: this.prop, status: 1});
      const formData = new FormData();
      formData.append('file', this.imageFile);
      this.disableSubmit = true;
      const uploadRes = await uploadFile(formData);
      if (uploadRes.status) {
        const fileId = uploadRes.data.id;
        const res = await recognizePicture(fileId);
        if (res.status) {
          const mileage = res.data.mileage;
          if (Number.isNaN(Number(mileage))) {
            this.$emit('error', {key: this.prop, status: 2})
          } else {
            const payload = {
              key: this.prop,
              status: 0, value: Number(mileage)
            }
            if (uploadRes.data.fawkesFileInfo) {
              payload.groupToken = uploadRes.data.fawkesFileInfo.groupToken;
            }
            this.$emit('success', payload);
          }
        } else {
          this.$emit('error', {key: this.prop, status: 2})
        }
        this.disableSubmit = false;
      } else {
        this.$emit('error', {key: this.prop, status: 2})
      }
    }
  }
}
</script>
<style lang="less" scoped>
@import "exclude/index.less";
</style>
