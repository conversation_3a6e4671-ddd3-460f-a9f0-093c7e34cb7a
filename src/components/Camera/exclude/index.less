.uploadFacePic {
  .camera-container {
    .back {
      z-index: 9998;
      position: fixed;
      color: #fff;
    }

    .camera {
      z-index: 9999;
      position: fixed;
      left: 50%;
      transform: translateX(-50%);
      bottom: 10%;
    }

    .frame {
      z-index: 9991;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .description {
      z-index: 9991;
      position: fixed;
      bottom: 28%;
      left: 50%;
      transform: translateX(-50%);
      color: rgba(255, 255, 255, 0.8);
      font-size: 30px;
      white-space: nowrap;
    }

    .frame-container {
      z-index: 9991;
      overflow: hidden;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 500px;
      height: 500px;
      .viewfinder {
        &::before {
          content: '';
          position: absolute;
          top: -100%;
          left: 0;
          width: 100%;
          height: 2px;
          background: #4a9eff;
          box-shadow:
                  0 0 10px #4a9eff,
                  0 0 20px #4a9eff,
                  0 0 30px #4a9eff;
          animation: scan 3s linear infinite;
        }

        &::after {
          content: '';
          position: absolute;
          top: -100%;
          left: 0;
          width: 100%;
          height: 50px;
          background: linear-gradient(to bottom,
          rgba(60, 131, 255, 0) 0%,
          rgba(60, 131, 255, 0.2) 40%,
          rgba(60, 131, 255, 0.3) 50%,
          rgba(60, 131, 255, 0.2) 60%,
          rgba(60, 131, 255, 0) 100%
          );
          animation: scan 3s linear infinite;
          pointer-events: none;
        }

        @keyframes scan {
          from {
            top: -50px;
          }
          to {
            top: 100%;
          }
        }

        .corner {
          position: absolute;
          width: 80px;
          height: 80px;

          &::before,
          &::after {
            content: '';
            position: absolute;
            background-color: rgba(60, 131, 255, 1);
            box-shadow: 0 0 15px rgba(60, 131, 255, 0.8);
          }

          &::before {
            width: 10px;
            height: 80px;
          }

          &::after {
            width: 80px;
            height: 10px;
          }

        }
        .top-left {
          top: 0;
          left: 0;
        }

        .top-left::before {
          top: 0;
          left: 0;
        }

        .top-left::after {
          top: 0;
          left: 0;
        }

        .top-right {
          top: 0;
          right: 0;
        }

        .top-right::before {
          top: 0;
          right: 0;
        }

        .top-right::after {
          top: 0;
          right: 0;
        }

        .bottom-left {
          bottom: 0;
          left: 0;
        }

        .bottom-left::before {
          bottom: 0;
          left: 0;
        }

        .bottom-left::after {
          bottom: 0;
          left: 0;
        }

        .bottom-right {
          bottom: 0;
          right: 0;
        }

        .bottom-right::before {
          bottom: 0;
          right: 0;
        }

        .bottom-right::after {
          bottom: 0;
          right: 0;
        }
      }
    }

    video {
      z-index: 9990;
      position: fixed;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
