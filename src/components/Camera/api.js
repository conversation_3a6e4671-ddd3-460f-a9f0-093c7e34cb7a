import request from '@/utils/request'

export function uploadFile(data) {
  return request({
    url: '/vehicle-dispatch/vd/ai/files/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

export function recognizePicture(id) {
  return request({
    url: '/vehicle-dispatch/vd/ai/mileage/recognition',
    method: 'get',
    params: {uploadFileId: id}
  })
}
