import request from '@utils/request'
import storage from "@utils/storage";

export function commitFeedBack (data) {
  return request({
    url: '/vehicle-dispatch/vd/feedback/commit',
    method: 'post',
    data: data,
  })
}

// 提交反馈处理
export function modifyFeedBack (data) {
  return request({
    url: '/vehicle-dispatch/vd/feedback/alter',
    method: 'post',
    data: data,
  })
}

// 获得问题反馈搜索参数
export function getFeedBackParam(params) {
  return request({
    url: '/vehicle-dispatch/vd/feedback/page/getParam',
    params
  })
}

// 获得问题反馈用户分页数据
export function getFeedbackUserPage(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/feedback/user/page',
    method: 'post',
    data,
    params
  })
}


export function getFeedbackPage(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/feedback/page',
    method: 'post',
    data,
    params
  })
}

export function getFeedbackPageByUser(data, params) {
  return request({
    url: '/vehicle-dispatch/vd/feedback/user/page',
    method: 'post',
    data,
    params: {
      userName: storage.get('userName'),
      ...params
    }
  })
}

// 问题反馈详情
export function getFeedBackDetail (id) {
  return request({
    url: '/vehicle-dispatch/vd/feedback/detail',
    method: 'get',
    params: {
      id: id,
    },
  })
}


export function getCommentList (ids) {
  return request({
    url: '/vehicle-dispatch/vd/feedback/detail/common',
    method: 'get',
    params: {
      ids: ids,
    },
  })
}

// 问题反馈详情
export function getNotifyFeedbackList () {
  return request({
    url: '/vehicle-dispatch/vd/feedback/notify/list',
    method: 'post',
  })
}

// 读取消息
export function readNotify (id) {
  return request({
    url: '/vehicle-dispatch/vd/feedback/notify/read',
    method: 'post',
    params: {
      id
    }
  })
}



// 问题反馈详情
export function getUnReadTypeList () {
  return request({
    url: '/vehicle-dispatch/vd/msgRead/user/unRead/type',
    method: 'post',
  })
}


// 读取消息
export function doReadMsg (data) {
  return request({
    url: '/vehicle-dispatch/vd/msgRead/user/clear',
    method: 'post',
    data: data
  })
}
