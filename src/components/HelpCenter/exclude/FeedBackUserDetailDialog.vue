<template>
  <fks-dialog
    title="反馈详情"
    :visible.sync="visible"
    :close-on-click-modal="false"
    custom-class="feedback-detail-dialog"
    :before-close="handleBeforeClose"
  >
    <div class="feedback-dialog" v-loading="loading">
      <div class="feed-back-info">
        <div class="sub-title">反馈信息</div>

        <div class="info-item">
          <div class="item-label">所属项目</div>
          <div class="fb-item-content">
            {{ feedBackInfo.userProjectName }}
          </div>
        </div>

        <div class="info-item">
          <div class="item-label">问题/建议描述</div>
          <div class="fb-item-content">
            {{ feedBackInfo.feedbackContent }}
          </div>
        </div>

        <div class="info-item">
          <div class="item-label">附件图片</div>
          <div class="fb-item-content">
            <picture-preview v-if="feedBackInfo.feedbackFile" :g9s="feedBackInfo.feedbackFile" />
          </div>
        </div>

        <div class="info-item">
          <div class="item-label">反馈时间</div>
          <div class="fb-item-content">
            {{ dayjs(feedBackInfo.feedbackTime).format('YYYY-MM-DD HH:mm') }}
          </div>
        </div>
      </div>
      <div class="handle-info">
        <div class="sub-title">处理信息</div>
        <div class="info-item">
          <div class="item-label">问题分类</div>
          <div class="fb-item-content" v-if="feedBackInfo && feedBackInfo.feedbackType">
            {{ getTagText(feedBackInfo.feedbackType, 'feedbackType') }}
          </div>
        </div>
        <div class="info-item">
          <div class="item-label">处理状态</div>
          <div class="fb-item-content" v-if="feedBackInfo && feedBackInfo.feedbackStatus">
            <card-tag
              v-if="feedBackInfo.feedbackType"
              class="p-24 p-t-4 p-b-4"
              style="font-size: 11px; padding: 4px 8px"
              :tag="{
                color: getTagType(feedBackInfo.feedbackStatus, 'feedbackStatus'),
                text: getTagText(feedBackInfo.feedbackStatus, 'feedbackStatus'),
              }"
            />
          </div>
        </div>
        <div class="info-item">
          <div class="item-label">处理详情</div>
          <div class="fb-item-content comment-list">
            <div class="comment-item" v-for="(item, index) in commentList">
              <div class="comment-dot">
                <img src="@/assets/img/dialog/dot.svg" width="12" height="12" />
              </div>
              <div class="content">
                <div class="time">
                  {{
                    dayjs(item.createDate).format('YYYY-MM-DD HH:mm') +
                    ' ' +
                    item.commentUserFullName
                  }}
                </div>
                <div class="comment-content">
                  {{ item.commentContent }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </fks-dialog>
</template>

<script>
import { mapActions, mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import FormUpload from '@modules/FormCenter/components/FormUpload/index.vue'
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import {
  getFeedBackDetail,
  getFeedBackParam,
  getFeedbackUserPage,
} from '@components/HelpCenter/api'
import dayjs from 'dayjs'
import PicturePreview from '@components/PicturePreview/index.vue'

export default {
  name: 'FeedbackDialogUserDialog',
  components: {
    PicturePreview,
    CardTag,
    TempTable,
    FormUpload,
  },
  data() {
    return {
      // 是否显示 Dialog
      visible: false,
      loading: false,
      feedBackInfo: {},
      commentList: [],
      type: 'view',
    }
  },
  computed: {
    ...mapState([StateTypes.AUTH_PORTALS]),
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    feedbackStatusEnum() {
      return this.enums.VdFeedbackStatusEnums
    },
    feedbackTypeEnum() {
      return this.enums.VdFeedbackIssueTypeEnums
    },
    portal() {
      //当前门户
      return this.$store.state.portal
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter((item) => item.projectStatus === 100) // 100激活，200关闭
    },
  },
  created() {
  },
  methods: {
    dayjs,
    ...mapActions('HelpCenter', ['getUnReadMsg', 'readMsg']),
    getTagText(value, prop) {
      if (prop === 'feedbackType') {
        return this.feedbackTypeEnum.find((item) => item.key == value).value
      }
      if (prop === 'feedbackStatus') {
        return this.feedbackStatusEnum.find((item) => item.key == value).value
      }
      return value
    },
    getTagType(value, prop) {
      let status = []
      if (prop === 'feedbackStatus') {
        status = {
          100: '#FFA418',
          200: '#3C83FF',
          300: '#03BE8A',
          400: '#999999',
        }
      }
      return status[value]
    },
    async getData(id) {
      this.loading = true
      try {
        const { data } = await getFeedBackDetail(id)
        let userName = this.userInfo.userName
        this.feedBackInfo = data.vdFeedback
        this.commentList = data.vdFeedbackComments
        // 如果当前用户是反馈发起人，并且该反馈需要提醒，消除提醒
        if (userName == this.feedBackInfo.userName && this.feedBackInfo.needNotify) {
          await this.readMsg(id)
        }
        this.loading = false
      } catch (e) {
        console.log('异常', e)
        this.loading = false
      }
    },
    // 打开弹窗
    openDialog(data) {
      this.visible = true
      this.getData(data.id)
    },
    // 关闭弹窗
    closeDialog() {
      this.visible = false
    },
    handleBeforeClose(done) {
      // 重置表单
      // 允许关闭弹窗
      done()
    },
  },
}
</script>

<style>
.feedback-detail-dialog {
  display: flex;
  flex-direction: column;
  height: calc(75vh);
  max-height: unset;
  width: 1000px;
  max-width: 80%;

  .fks-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(75vh);
    max-height: unset;
  }
}
</style>
<style>
@import './detail.css';
</style>
