
.drop-container {
    position: relative;
    display: inline-block;
    font-size: 14px;
}

.main-btn {
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    user-select: none;
    padding: 8px;
}

.main-btn img {
    margin-right: 8px;
}

.item-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 10;
    width: 120px;
    border-radius: 4px;
    background: #FFFFFF;
    box-shadow: 5px 2px 30px 0px rgba(15, 38, 117, 0.15);
    padding: 8px 10px;
    box-sizing: border-box;
}

.item {
    position: relative;
    padding: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.item:hover {
    color: #027AFF;
    background-color: #DCE4FA;
}

.item svg {
    margin-right: 4px;
    padding-top: 2px;
    /* 根据项目中使用的图标库，可能需要额外的样式 */
}


/* 红色圆形小标识 */
.main-notify-dot {
    position: absolute;
    top: 8px;
    right: 5px;
    width: 6px;
    height: 6px;
    background-color: red;
    border-radius: 50%;
}

/* 红色圆形小标识 */
.notify-dot {
    position: absolute;
    top: 8px;
    right: 6px;
    width: 6px;
    height: 6px;
    background-color: red;
    border-radius: 50%;
}