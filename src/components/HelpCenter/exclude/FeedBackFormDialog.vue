<template>
  <fks-dialog
    title="问题反馈"
    :visible.sync="visible"
    :close-on-click-modal="false"
    custom-class="feedback-form-dialog"
    :before-close="handleBeforeClose"
  >

    <!-- 自定义 header 插槽 -->
    <template slot="title">
      <div class="dialog-header">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <!-- 标题居中显示 -->
          <span class="dialog-title" style="font-size: 16px">问题反馈</span>
          <!-- 业务标签放在关闭按钮左侧 -->
          <span class="business-label" @click="toFeedBackTable" >
            我的反馈记录
            <span v-if="feedbackNotify" class="feedback-notify"></span>
          </span>
        </div>
      </div>
    </template>
    <div class="feedback-dialog">
      <!-- 所属项目 -->
      <fks-form :model="form" ref="formRef" :rules="rules" label-width="120px">
        <fks-form-item label="所属项目">
          <fks-select v-model="form.userProjectId" placeholder="请选择所属项目" @change="handleProjectChange">
            <fks-option
              v-for="(item, index) in authPortalList"
              :key="item.id"
              :label="item.projectName"
              :value="item.portalId"
            />
          </fks-select>
        </fks-form-item>

        <!-- 问题反馈/描述 -->
        <fks-form-item label="问题/反馈描述" prop="feedbackContent">
          <fks-input
            type="textarea"
            v-model="form.feedbackContent"
            :maxlength="2000"
            show-word-limit
            placeholder="请输入问题/反馈描述..."
            rows="5"
          />
        </fks-form-item>

        <!-- 上传附件 -->
        <fks-form-item label="上传附件">
          <drag-upload v-if="!loading" v-model="form.feedbackFile"></drag-upload>
<!--          <fks-upload-->
<!--            class="upload-demo"-->
<!--            drag-->
<!--            multiple-->
<!--            action="https://jsonplaceholder.typicode.com/posts/"-->
<!--            :limit="9"-->
<!--            :on-exceed="handleExceed"-->
<!--            :on-success="handleSuccess"-->
<!--            :on-remove="handleRemove"-->
<!--            accept=".jpg,.jpeg,.png"-->
<!--            v-model="form.fileList"-->
<!--            :file-list="form.fileList"-->
<!--          >-->
<!--            <i class="fks-icon-upload"></i>-->
<!--            <div class="fks-upload__text">-->
<!--              拖动文件或图片到此区域或点击上传-->
<!--            </div>-->
<!--            <div class="fks-upload__tip" slot="tip">-->
<!--              按住Ctrl可多选多张，最多可上传9张图片，支持上传JPG/JPEG/PNG格式文件-->
<!--            </div>-->
<!--          </fks-upload>-->
        </fks-form-item>
      </fks-form>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <fks-button class="sub-btn" icon="fks-icon-close" danger-text @click="closeDialog"
      style="color: #FF4143 !important;">
        取消
      </fks-button>
      <fks-button :loading="loading" class="sub-btn" icon="fks-icon-check" text
                  @click="submitFeedback">
        提交
      </fks-button>
    </div>
  </fks-dialog>
</template>

<script>
import { mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import FormUpload from "@modules/FormCenter/components/FormUpload/index.vue";
import { commitFeedBack } from '@components/HelpCenter/api'
import DragUpload from '@modules/FormCenter/components/FormUpload/DragUpload.vue'

export default {
  name: "FeedbackDialog",
  components: {
    FormUpload, DragUpload
  },
  data() {
    return {
      // 是否显示 Dialog
      visible: false,
      // 所属项目列表
      projectList: [],
      form: {
        userName: "",
        userFullName: "",
        userProjectId: null, // 默认选中
        userProjectName: "", // 默认选中
        feedbackContent: "",
        feedbackFile: "",
        fileList: []
      },
      // 验证规则
      rules: {
        feedbackContent: [
          { required: true, message: "请输入问题描述", trigger: "blur" }
        ]
      },
      loading: false,
    };
  },
  computed: {
    ...mapState('HelpCenter', ['itemList']), // 注意这里用 state 的变量名 itemList，而 getter 用 mapGetters
    ...mapState([StateTypes.AUTH_PORTALS]),
    feedbackNotify() {
      const target = this.itemList.find(item => item.tag === 'feedback');
      return target ? target.notify : false;
    },
    portal() {
      //当前门户
      return this.$store.state.portal
    },
    userInfo() {
      return this.$storage.getObject('user')
    },
    authPortalList() {
      return this[StateTypes.AUTH_PORTALS].filter(item => item.projectStatus === 100); // 100激活，200关闭
    },
  },
  watch: {
    'form.userProjectId'(newVal) {
      const selectedItem = this.authPortalList.find(item => item.portalId === newVal);
      if (selectedItem) {
        this.form.userProjectName = selectedItem.projectName;
      }
    }
  },
  methods: {
    toFeedBackTable() {
      this.$emit("feedbackTable");
      this.closeDialog();
    },
    // 打开弹窗
    openDialog() {
      const isPortalAuthorized = this.authPortalList.some(item => item.portalId === this.portal.id);

      if (isPortalAuthorized) {
        // 如果找到了 portal.id，就设置表单值
        this.form.userProjectId = this.portal.id;
        this.form.userProjectName = this.portal.name;
      } else {
        // 如果没找到，就不设置或置空
        this.form.userProjectId = null;
        this.form.userProjectName = null;
      }
      this.form.userName = this.userInfo.userName;
      this.form.userFullName = this.userInfo.userFullName;

      this.visible = true;
    },
    // 关闭弹窗
    closeDialog() {
      this.form = {};
      this.visible = false;
    },
    handleBeforeClose(done) {
      // 重置表单
      this.form = {};
      // 允许关闭弹窗
      done();
    },
    handleProjectChange() {

    },
    // 提交表单
    submitFeedback() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.loading = true;
          let form = this.form;
          commitFeedBack(form).then((res) => {
            if(res.status) {
              this.$message.success("反馈提交成功！");
            }
            this.form = {};
            this.closeDialog();
            this.loading = false;
          })
        } else {
          // 验证失败时，可做相应提示或处理
          console.log("表单验证未通过");
          return false;
        }
      });
    }
  }
};
</script>

<style>
.feedback-form-dialog {
  width: 1000px;
  max-width: 80%;
  border-radius: 4px;
  .fks-dialog__body {
    padding-left: 26px;
  }
  .fks-dialog__footer {
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.0301);
    border-top: 1px solid #DFE0E2;
    padding: 12px 20px;
  }


  .sub-btn {
    padding: 5px 10px;
    color: #333 !important;
    border-color: #cccccc;
    transition: all 0.3s ease; /* 平滑过渡效果 */
  }

  .sub-btn:hover {
    color: #333 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
  }

  .sub-btn:active {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 点击时减小阴影 */
    transform: translateY(5px); /* 模拟点击下沉效果 */
  }
}

.business-label {
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #3C83FF;
  cursor: pointer;
  position: relative;
  padding-right: 50px
}

/* 红色圆形小标识 */
.feedback-notify {
  position: absolute;
  top: 1px;
  right: 45px;
  width: 6px;
  height: 6px;
  background-color: red;
  border-radius: 50%;
}


.dialog-footer {
  text-align: right;
  background: #FFFFFF;
}



</style>
