<template>
  <div>
    <div class="drop-container">
      <!-- 点击按钮切换下拉菜单显示状态 -->
      <div class="main-btn" @click="toggleItems">
        <img src="@/assets/img/help/help-center.svg" alt="帮助中心图标" />
        <span>帮助中心</span>
        <span v-if="hasNotify" class="main-notify-dot"></span>
      </div>
      <!-- 下拉菜单，根据 showItems 显示或隐藏 -->
      <div class="item-menu" v-if="showItems">
        <div
          class="item"
          v-for="(item, index) in itemList"
          :key="index"
          @click="handleItemClick(item)"
        >
          <fks-icon :icon-class="item.icon" class="svg" />
          <span>{{ item.name }}</span>
          <span v-if="item.notify" class="notify-dot"></span>
        </div>
      </div>
    </div>
    <FeedBackFormDialog ref="feedback" @feedbackTable="showFeedBackTable" />
    <FaqTableDialog ref="question"/>
    <FeedBackUserTableDialog ref="userTable" />
    <AnnouncementDialog ref="announcement" />
  </div>
</template>

<script>
import { getFeedBackLink, getOpManualLink } from '@/api/carApply'
import FeedBackFormDialog from './exclude/FeedBackFormDialog.vue'
import FeedBackUserTableDialog from '@components/HelpCenter/exclude/FeedBackUserTableDialog.vue'
import FaqTableDialog from '@components/HelpCenter/exclude/FaqTableDialog.vue'
import { mapActions, mapState } from 'vuex'
import PcLog from '@components/AnnouncementDialog/pc-log.vue'

export default {
  name: 'HelpCenterDropdown',
  components: {
    AnnouncementDialog: PcLog,
    FeedBackFormDialog,
    FeedBackUserTableDialog,
    FaqTableDialog,
  },
  data() {
    return {
      showItems: false,
    }
  },
  computed: {
    ...mapState('HelpCenter', ['itemList', 'unreadItems']), // 注意这里用 state 的变量名 itemList，而 getter 用 mapGetters
    // 如果任一菜单项的 notify 为 true，则返回 true
    hasNotify() {
      return this.itemList.some((item) => item.notify)
    },
  },
  created() {
    this.getUnReadMsg()
  },
  methods: {
    ...mapActions('HelpCenter', ['getUnReadMsg', 'readMsg']),
    showFeedBackTable() {
      this.$refs.userTable.openDialog()
    },
    toggleItems() {
      this.showItems = !this.showItems
    },
    // 点击下拉菜单项时的回调，根据需要添加逻辑
    handleItemClick(item) {
      this.showItems = false
      switch (item.icon) {
        case 'report':
          // 跳转到问题反馈
          this.$refs.feedback.openDialog()
          // this.jumpToFeedBack();
          break
        case 'option':
          // 跳转到操作手册
          this.jumpToOpManual()
          break
        case 'question':
          // 此处可以添加常见问题的逻辑
          this.$refs.question.openDialog()
          break
        case 'update-log':
          // 此处可以添加更新日志的逻辑
          this.$refs.announcement.openDialog()
          break
        default:
      }
    },

    // 跳转到操作手册
    jumpToOpManual() {
      getOpManualLink().then((res) => {
        window.open(res.data, '_blank') // _blank 表示在新窗口或标签页中打开
      })
    },
    jumpToFeedBack() {
      getFeedBackLink().then((res) => {
        window.open(res.data, '_blank') // _blank 表示在新窗口或标签页中打开
      })
    },
  },
}
</script>

<style scoped>
@import './exclude/HelpCenter.css';
</style>
