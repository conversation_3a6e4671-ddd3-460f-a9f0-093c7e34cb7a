<template>
  <div class="card-flow-container">
    <div class="full-height overflow-y-auto cards flex flex-column">
<!--      <fks-input-->
<!--        placeholder="搜索"-->
<!--        class="search-input"-->
<!--        v-model="searchVal"-->
<!--        style="margin-bottom: 8px"-->
<!--        @keyup.enter.native="handleSearch"-->
<!--        clearable-->
<!--      >-->
<!--        <i slot="prefix" class="fks-input__icon fks-icon-search"></i>-->
<!--      </fks-input>-->
      <slot name="card-header"></slot>
      <div
        v-if="cards.length > 0 || loading === true"
        class="flex-grow-1 overflow-y-auto scroll"
        v-loading="loading"
        element-loading-background="#F8F9FA"
        @scroll="handleScroll"
      >
        <div
          v-for="(card, index) in cards"
          class="card-item"
          :key="index"
          :ref="`cardRef-${index}`"
          :class="{'m-b-16': index < cards.length - 1, active: index === activeIndex}"
          @click="handleClick(index)"
        >
          <slot name="card" :card="card"/>
        </div>
        <i v-if="iconLoading" class="fks-icon-loading loading-icon"/>
      </div>
      <fks-empty
        :description="leftDescription"
        v-else-if="cards.length === 0 && loading === false"
        class="flex-grow-1 p-24"
      >
        <template slot="image">
          <div />
        </template>
      </fks-empty>
    </div>
    <fks-empty
      v-if="activeIndex === null"
      description="无内容"
      class="full-height panel"
      :image-size="200"
    />
    <div
      v-else
      class="panel full-height"
      v-loading="contentLoading"
      element-loading-background="white"
    >
      <slot name="panel"></slot>
    </div>
  </div>
</template>

<script>
import {mapState} from "vuex";
import * as StateTypes from '@/store/State/stateTypes'
import EventBus from "@utils/eventBus";
import { debounce } from '@utils/util'

export default {
  name: 'CardFlow',
  props: {
    cards: {
      type: Array,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    finished: {
      type: Boolean,
      default: false
    },
    leftDescription: {
      type: String,
      default: '暂无数据'
    }
  },
  data() {
    return {
      activeIndex: null,
      trigger: true,
      contentLoading: false,
      iconLoading: false,
      loadMore: false,
      canScroll: true,
      searchVal: '',
    }
  },
  computed: {
    ...mapState([StateTypes.TABLE_LOADING])
  },
  methods: {
    resetActiveIndex(index = 0) {
      this.activeIndex = index;
      this.$nextTick(() => {
        const el = document.querySelector('.card-item.active');
        if (el) {
          el.scrollIntoView({behavior: 'smooth'});
        }
        // 同步更新数据
        if (typeof index === 'number' && index >= 0) {
          this.$emit('indexChange', this.cards[index])
        }
      })
    },
    handleClick(index) {
      this.contentLoading = true;
      this.activeIndex = index;
      this.$emit('indexChange', this.cards[index])
    },
    handleScroll(e) {
      if (!this.canScroll) return;
      const el = e.target;
      const threshold = 50;
      const a = el.scrollTop + el.clientHeight;
      const b = el.scrollHeight - threshold

      if (el.scrollTop + el.clientHeight >= el.scrollHeight - threshold) {
        if (this.finished) return;
        if (!this.iconLoading) {
          this.loadMore = true;
          this.$emit('loadMore', this.searchVal)
        }
        this.iconLoading = true;
      }
    },
    disableContentLoading() {
      this.contentLoading = false;
    },
    handleSearch(){
      this.$emit('searchChange', this.searchVal);
    },
  },
  watch: {
    searchVal: {
      deep: true,
      handler(newValue) {
        if (!newValue) {
          this.handleSearch();
        }
      }
    },
    cards: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.iconLoading = false;
        if (newVal && newVal.length === 0) {
          this.resetActiveIndex(null);
        }
        if (newVal && newVal.length > 0 && this.trigger) {
          this.trigger = false;
          this.activeIndex = 0;
          this.contentLoading = true;
          this.$emit('indexChange', newVal[this.activeIndex]);
        }
        if (this.loadMore) {
          this.loadMore = false;
        } else {
          // if (!this.trigger) {
          //   const el = document.querySelector('.scroll');
          //   el && (el.scrollTop = 0);
          //   this.activeIndex = 0;
          //   this.contentLoading = true;
          //   this.$emit('indexChange', newVal[this.activeIndex]);
          // }
        }
      }
    }
  },
  beforeDestroy() {
  },
  mounted() {

  }
}
</script>

<style lang="less" scoped>
.loading-icon {
  font-size: 36px;
  color: #3c83ff;
  margin-left: 280px;
  margin-top: 20px;
}

.card-flow-container {
  display: grid;
  grid-template-columns: 640px auto;
  grid-column-gap: 16px;
  background: #f8f9fa;
  padding: 20px 20px 10px 20px;
  box-sizing: border-box;

  .scroll::-webkit-scrollbar {
    display: none;
  }

  .scroll {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .cards {
    background: #f8f9faff;

    .card-item {
      border: 4px solid #dfe0e2;
      border-radius: 16px;

      &.active {
        border: 4px solid #3c83ff;
      }
    }
  }

  .panel {
    overflow-y: hidden;
    overflow-x: hidden;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 12px;
    border: 2px solid #dfe0e2;
    padding: 60px 0 20px 60px;
  }
}
</style>
