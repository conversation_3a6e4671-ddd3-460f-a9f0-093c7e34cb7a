<template>
  <div
    class="tag"
    :style="{color: tag.color, backgroundColor: getBackgroundColor(tag.color)}"
  >
    {{ tag.text }}
  </div>
</template>

<script>
export default {
  name: 'card-tag',
  props: ['tag'],
  methods: {
    getBackgroundColor(color) {
      return `${color}33`
    }
  }
}
</script>

<style scoped lang="less">
.tag {
  //width: 120px;
  display: inline-block;
  text-align: center;
  border-radius: 22px;
  padding: 14px 20px;
  font-size: 22px;
}
</style>
