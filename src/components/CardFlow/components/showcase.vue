<template>
  <div class="container d-flex flex-column cursor-pointer">
    <header class="flex row-between col-center">
      <div class="title second-title">{{ title }}</div>
      <div class="tags flex col-center">
        <card-tag
          v-for="(tag, index) in tags"
          :key="index"
          :tag="tag"
          :class="{'m-l-10': index > 0}"
        />
      </div>
    </header>
    <main v-if="descriptions" class="flex-grow-1">
      <div
        v-for="({label, value}, index) in descriptions"
        class="description-item"
        :key="index"
      >
        <div class="label flex row-between col-center">
          <span v-for="(word, idx) in label" :key="idx">
            {{ word }}
          </span>
        </div>
        <div class="value">
          <overflow-tooltip text-class="tooltip" v-if="isOverflow(value)" :text="value" />
          <span v-else>{{ value || '-' }}</span>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import CardTag from "@components/CardFlow/components/tag.vue";
import OverflowTooltip from "@components/OverflowTooltip/index.vue";

export default {
  name: 'showcase',
  components: {OverflowTooltip, CardTag},
  props: ['title', 'tags', 'descriptions'],
  methods: {
    isOverflow(text) {
      if (typeof text === 'string') {
        const v = 14 * text.length
        return 14 * text.length > 230
      }
      return false
    }
  }
}
</script>
<style scoped lang="less">
.tooltip {
  width: 440px;
}
.container {
  border-radius: 16px;
  background: #FFFFFF;
  padding: 28px;

  header {
    .title {
      color: #333333;
      line-height: 44px;
      text-align: left;
      font-style: normal;
    }

  }

  main {
    margin-top: 16px;

    .description-item {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 28px;
      line-height: 40px;
      font-style: normal;
      margin-bottom: 20px;

      &:last-of-type {
        margin-bottom: 0;
      }

      .label {
        color: #33333366;
        width: 120px;
      }

      .value {
        flex-grow: 1;
        margin-left: 20px;
        color: #333333;
        overflow: hidden;
      }
    }
  }
}
</style>
