import Vue from 'vue'
import Vuex from 'vuex'
import state from './State/states'
import mutations from './Mutation/mutations'
import actions from './Action/actions'
import getters from './Getter/getters'
import CarApply from './modules/CarApply/index.js';
import WeChat from './modules/WeChat/index.js';
import HelpCenter from './modules/HelpCenter/index.js';
import FeiShu from './modules/FeiShu/index.js';
import Department from "./modules/Department";
import FormController from './modules/FormController'
Vue.use(Vuex)
const appStore = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
  modules: {
    CarApply,
    WeChat,
    FeiShu,
    Department,
    HelpCenter,
    FormController,
  },
}

const store = new Vuex.Store(appStore)
function getSubModules () {
  const reqs = require.context('@/modules', true, /store\/index\.js$/, 'lazy')
  for (let k = 0; k < reqs.keys().length; k++) {
    let path = reqs.keys()[k]
    reqs(path).then((req) => {
      req.default && store.registerModule(req.default.name, req.default)
    })
  }
}
getSubModules()

export default store
