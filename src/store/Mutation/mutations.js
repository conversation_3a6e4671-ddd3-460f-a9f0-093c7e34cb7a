/*
 * @Author: xie_sm
 * @Date: 2022-04-15 08:54:47
 * @LastEditors: ye_xf
 * @LastEditTime: 2022-08-04 09:26:24
 * @FilePath: \mobile-template\src\store\Mutation\mutations.js
 * @Description:
 *
 */
import * as types from './mutationTypes'
import * as stateTypes from '../State/stateTypes'
import { resetRouter } from '@/router'
import storage from '@/utils/storage'
import deepmerge from 'deepmerge'

const mutations = {
  [types.SET_QUICK_ACCESS_AREA]: (state, data) => {
    state[stateTypes.QUICK_ACCESS_AREA] = data
  },
  [types.SET_TEMP_USER]: (state, data) => {
    state[stateTypes.TEMP_USER] = data
  },
  [types.SET_ENUM]: (state, data) => {
    state[stateTypes.ENUM] = data
  },

  [types.SET_CURRENT_ROW]: (state, data) => {
    state[stateTypes.CURRENT_ROW] = data
  },
  [types.SET_REJECT_TASK_KEY]: (state, data) => {
    state[stateTypes.REJECT_TASK_KEY] = data
  },
  [types.SET_SCHEDULE_TABLE]: (state, data) => {
    state[stateTypes.SCHEDULE_TABLE] = data
  },
  [types.SET_STATUS_BAR_HEIGHT]: (state, data) => {
    state[stateTypes.STATUS_BAR_HEIGHT] = data
  },
  [types.SET_USER_INFO]: (state, data) => {
    state[stateTypes.USER_INFO] = data
  },
  [types.IS_LOGIN]: (state, data) => {
    state[stateTypes.IS_LOGIN] = data
  },
  [types.UPDATE_PERMISSION_QUEUE]: (state, data) => {
    state[stateTypes.PERMISSION_QUEUE].add(data)
  },
  //更新路由
  [types.SET_ROUTES]: (state, data) => {
    state[stateTypes.ROUTES] = data
  },
  //权限信息
  [types.SET_PERMISSION]: (state, data) => {
    state[stateTypes.PERMISSION] = deepmerge({ [data.key]: data.value }, state[stateTypes.PERMISSION])
  },
  [types.REMOVE_PERMISSION_QUEUE]: (state, code) => {
    state[stateTypes.PERMISSION_QUEUE].delete(code)
  },
  //获取门户列表
  [types.SET_PORTALS]: (state, data) => {
    state[stateTypes.PORTALS] = [...data]
  },
  [types.SET_AUTH_PORTAL]: (state, data) => {
    state[stateTypes.AUTH_PORTALS] = [...data]
  },
  [types.SET_FAVORITE_PORTALS]: (state, data) => {
    state[stateTypes.FAVORITE_PORTALS] = data;
  },
  //更新当前门户
  [types.UPDATE_PORTAL]: (state, data) => {
    state[stateTypes.PORTAL] = deepmerge({}, data)
    storage.set('portal', JSON.stringify(data))
  },
  [types.SET_SECOND_LEVEL_PORTAL]: (state, data) => {
    state[stateTypes.SECOND_LEVEL_PORTAL] = data;
  },
  [types.CLEAN_VIEWS]: state => {
    // not keep affix tags
    state[stateTypes.VISITED_VIEWS] = []
    state[stateTypes.CACHED_VIEWS] = []
  },
  [types.CLEAN_ROUTE]: (state) => {
    state[stateTypes.ROUTES] = []
    resetRouter()
  },
  [types.CLEAN_PERMISSION]: (state) => {
    state[stateTypes.PERMISSION] = {}
  },
  [types.CLEAN_PERMISSION_QUEUE]: (state, code) => {
    state[stateTypes.PERMISSION_QUEUE] = new Set()
  },
  [types.SET_CLIENT_CONFIG]: (state, data) => {
    state[stateTypes.ADVANCED_CONFIG] = { ...state[stateTypes.ADVANCED_CONFIG], ...(additionalInformation?.advancedConfig || {}) }
  },
  [types.SET_APPLY_RESOURCE]: (state, data) => {
    state[stateTypes.APPLY_RESOURCE] = data;
  },
  [types.SET_FEISHU_USER_INFO]: (state, data) => {
    state[stateTypes.FEISHU_USER_INFO] = data;
  },
  [types.SET_OPEN_ID]: (state, data) => {
    state[stateTypes.OPEN_ID] = data;
  },
  [types.SET_USER_LOCAL_INFO]: (state, res) => {
    storage.set('username', res.userName)
    storage.set('userFullname', res.userFullname)
    storage.set('access_token', res.access_token)
    storage.set('refresh_token', res.refresh_token)
    storage.set('userId', res.id)
  },
  [types.SET_PLATFORM]: (state, data) => {
    state[stateTypes.PLATFORM] = data;
  },
  SET_FIRST_LOAD(state, data){
    state.firstLoad = data;
  },
  [types.SET_FEISHU_LOADED]: (state, data) => {
    state[stateTypes.LOADED] = data
  },
  [types.SET_COLLAPSE]: (state, data) => {
    state[stateTypes.COLLAPSE] = data
  },
  [types.SET_APP_CREATED]: (state, data) => {
    state[stateTypes.IS_APP_CREATED] = data
  },
  [types.SET_FILTERS]: (state, data) => {
    const {id, filters, type} = data;
    if (type === 'set') {
      state[stateTypes.FILTERS][id] = filters
    } else if (type === 'remove') {
      delete state[stateTypes.FILTERS][id]
    } else if (type === 'clear') {
      state[stateTypes.FILTERS] = {}
    }
  },
  [types.SET_PROJECT_INFO]: (state, data) => {
    state[stateTypes.PROJECT_INFO] = data;
  },
  [types.SET_FEISHU_LINK]: (state, data) => {
    state[stateTypes.FEISHU_LINK] = data;
  },
  [types.SET_QUERY_TABLE_DATA]: (state, data) => {
    state[stateTypes.GLOBAL_TABLE_DATA] = data;
  },
  [types.SET_TABLE_LOADING]: (state, data) => {
    state[stateTypes.TABLE_LOADING] = data;
  },
  [types.SET_GLOBAL_QUERY_PARAMS]: (state, data) => {
    state[stateTypes.GLOBAL_QUERY_PARAMS] = data;
  },
  [types.SET_PROVINCES]: (state, data) => {
    state[stateTypes.PROVINCES] = data
  },
  [types.SET_PROJECT_CLOSED]: (state, data) => {
    state[stateTypes.IS_PROJECT_CLOSED] = data
  },
  [types.SET_DATA_BASE_PORTAL]: (state, data) => {
    state[stateTypes.DATA_BASE_PORTAL] = data
  },
  [types.SET_OLD_PROJECT_PORTAL]: (state, data) => {
    state[stateTypes.OLD_PROJECT_PORTAL] = data
  },
  [types.SET_VD_PROJECT_INFO]: (state, projectInfo) => {
    state[stateTypes.VD_PROJECT_INFO] = projectInfo
  },
  [types.SET_RECENTLY_USED_PORTALS]: (state, data) => {
    state[stateTypes.RECENTLY_USED_PORTALS] = data
  },
  [types.IS_PHONE_VERIFY]: (state, data) => {
    state[stateTypes.IS_PHONE_VERIFY] = data
  },
}

export default mutations
