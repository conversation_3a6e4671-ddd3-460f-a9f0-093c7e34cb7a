/*
 * @Author: xie_sm
 * @Date: 2022-04-15 08:54:47
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-08-30 14:13:48
 * @FilePath: \mobile-template\src\store\Getter\getters.js
 * @Description:
 *
 */
import * as types from './getterTypes'
import * as stateTypes from '../State/stateTypes'

import defaultSettings from '@/config'
import {COMPANY_PORTAL, PROJECT_PORTAL} from "@utils/constants";
import * as StateTypes from "@store/State/stateTypes";

const { multiPortal, timezone } = defaultSettings

const getters = {
  [types.THEME]: state => {
    return state[stateTypes.INTERFACE_CONFIG][types.THEME] || '#2F54EB'
  },
  [types.QUICK_ACCESS_AREA]: (state) => {
    return state[stateTypes.QUICK_ACCESS_AREA]
  },
  [types.TEMP_USER]: (state) => {
    return state[stateTypes.TEMP_USER]
  },
  [types.IS_LOGIN]: (state) => {
    return state[stateTypes.IS_LOGIN]
  },
  [types.PERMISSIONS]: state => {
    let arr = []
    for (let permission in state[stateTypes.PERMISSION]) {
      arr = [...arr, ...state[stateTypes.PERMISSION][permission]]
    }
    return arr
  },
  [types.TIMEZONE]: state => {
    return state[stateTypes.INTERFACE_CONFIG][types.TIMEZONE] || timezone
  },
  [types.MULTI_PORTAL]: state => {
    return multiPortal
  },
  [types.DEFAULT_ROUTE]: state => {
    // 是否是数据舱门户
    const isDataBasePortal = state[StateTypes.PORTAL].id === state[StateTypes.DATA_BASE_PORTAL].companyPortalId

    if (!isDataBasePortal) {
      return state[stateTypes.PLATFORM] === 'pc' ? defaultSettings.projectPortalDefaultRoute : defaultSettings.mobileProjectPortalDefaultRoute;
    } else if (isDataBasePortal) {
      return state[stateTypes.PLATFORM] === 'pc' ? defaultSettings.companyPortalDefaultRoute : defaultSettings.mobileCompanyPortalDefaultRoute;
    }
  },
  [types.BEHAVIOR_ANALYSIS]: state => {
    return state[stateTypes.ADVANCED_CONFIG][types.BEHAVIOR_ANALYSIS]
  },
  [types.IS_NOT_PC]: state => {
    return [1, 2].includes(state[stateTypes.APPLY_RESOURCE]);
  },
  [types.VD_PROJECT_INFO]: state => {
    return state[stateTypes.VD_PROJECT_INFO]
  },
  [types.GET_GLOBAL_STATE]: state => {
    let queryParams = { ...state[stateTypes.GLOBAL_QUERY_PARAMS] }; // 复制对象，避免修改 Vuex state
    return queryParams;
  },
  [types.IS_PHONE_VERIFY]: (state) => {
    return state[stateTypes.IS_PHONE_VERIFY]
  }
}
export default getters
