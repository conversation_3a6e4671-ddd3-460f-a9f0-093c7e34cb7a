/*
 * @Author: xie_sm
 * @Date: 2022-04-15 08:54:47
 * @LastEditors: ye_xf
 * @LastEditTime: 2022-08-01 14:30:17
 * @FilePath: \mobile-template\src\store\Getter\getterTypes.js
 * @Description:
 *
 */
export const THEME = 'theme'; // 主题色

export const QUICK_ACCESS_AREA = 'quickAccessArea'
export const TEMP_USER = 'tempUser'
export const IS_LOGIN = 'isLogin'
//权限
export const PERMISSIONS = 'permissions'
//时区
export const TIMEZONE = 'timezone'
//多门户模式
export const MULTI_PORTAL = 'multiPortal'
// 用户行为采集
export const BEHAVIOR_ANALYSIS = 'behaviorAnalysis'
// 非PC
export const IS_NOT_PC = 'isNotPC'
// 登录成功后或切换门户后跳转的默认地址
export const DEFAULT_ROUTE = 'defaultRoute'

export const VD_PROJECT_INFO = 'vdProjectInfo'

export const GET_GLOBAL_STATE = 'getGlobalState'

export const IS_PHONE_VERIFY = 'isPhoneVerify' // 是否进行了手机尾号验证
