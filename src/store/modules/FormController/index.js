import { getTaskFieldConfig } from '@/api/carApply'
import { FormConfigRegistry } from '@store/modules/FormController/formFieldConfig'

export default {
  namespaced: true,

  state: {
    formTaskKey: '',
    formBtnKey: '',
    fieldMap: {}     // { btnKey: { requiredFields: [], disabledFields: [] } }
  },

  mutations: {
    SET_FORM_TASK_KEY(state, key) {
      state.formTaskKey = key;
    },
    SET_FORM_BTN_KEY(state, key) {
      state.formBtnKey = key;
    },
    SET_FIELD_CONFIG(state, payload) {
      state.fieldMap = payload || {};
    },
    CLEAR_FIELD_CONFIG(state) {
      state.formTaskKey = '';
      state.formBtnKey = '';
      state.fieldMap = {};
    }
  },

  actions: {
    async loadFieldConfig({ commit }, formTaskKey) {
      const btnMap = FormConfigRegistry.getAllConfig()[formTaskKey] || {};
      commit('SET_FORM_TASK_KEY', formTaskKey);
      commit('SET_FIELD_CONFIG', btnMap);
    },
    setFormBtnKey({ commit }, formBtnKey) {
      commit('SET_FORM_BTN_KEY', formBtnKey);
    },
    cleanFieldConfig({ commit }) {
      commit('CLEAR_FIELD_CONFIG');
    }
  },

  getters: {
    currentFieldConfig: (state) => {
      return state.fieldMap[state.formBtnKey] || { required: [], disabled: [] };
    },
    isRequired: (state, getters) => (field) => {
      return getters.currentFieldConfig.required?.includes(field);
    },
    isReadonly: (state, getters) => (field) => {
      return getters.currentFieldConfig.disabled?.includes(field);
    },
    getFieldState: (state, getters) => (field) => {
      const { required = [], disabled = [] } = getters.currentFieldConfig;
      return {
        required: required.includes(field),
        readonly: disabled.includes(field)
      };
    },
  }
}
