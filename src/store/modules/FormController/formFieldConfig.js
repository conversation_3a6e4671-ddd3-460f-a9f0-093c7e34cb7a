// formFieldConfig.js

// ==================== 字段常量 ====================
export const FormFieldConstants = {
  // 基本信息字段
  FLAG_BB: 'flagBb',
  USE_CAR_TYPE: 'useCarNature',
  USE_CAR_REASON: 'useCarMatter',
  REASON_DESCRIPTION: 'remark1',
  FLIGHT_NUMBER: 'otherField2',
  FY_DEPT_NAME: 'fyDeptName',

  // 路线信息字段
  START_PLACE: 'startAddress',
  VIA_PLACE: 'vdAfWaypointList',
  DESTINATION: 'endAddress',

  // 时间相关字段
  DEPART_TIME: 'startTime',
  DEPART_TYPE: 'useCarTripType',
  EXPECT_RETURN_TIME: 'endTime',

  // 联系信息字段
  CONTACT_PERSON: 'contacts',
  CONTACT_PHONE: 'contactsPhone',

  // 乘车信息字段
  PASSENGER_NUM: 'useCarPersonNum',
  PASSENGER_LIST: 'vdAfUseCarPersonList',
  REMARK: 'remark2',

  // 车辆调度字段
  VEHICLE: 'carNum',             // 车牌号
  CAR_TYPE: 'carType',           // 车型
  CAR_AGE: 'carAge',             // 车龄
  DRIVER: 'driverFullName',      // 司机姓名
  DRIVER_PHONE: 'driverPhone',   // 司机电话
  DRIVER_AGE: 'driverAge',       // 驾龄
  DRIVER_LICENSE: 'driverLicenseNum', // 驾驶证号（可选扩展）

  // 审批字段
  APPROVAL_LEADER: 'task100UserName',
  DISPATCHER: 'task200UserName',

  // 实际行程与费用字段
  ACTUAL_START_TIME: 'startTime2',
  ACTUAL_END_TIME: 'endTime2',
  // 出车前后里程
  START_MILEAGE: 'km1',
  END_MILEAGE: 'km2',
  CURR_MILEAGE: 'km3',
  // 高速公路通行费
  HIGHWAY_FEE: 'cost1',
  HIGHWAY_FEE_TYPE: 'cost1Type',
  // 桥闸通行费
  BRIDGE_FEE: 'cost7',
  BRIDGE_FEE_TYPE: 'cost7Type',
  // 车辆燃油费
  FUEL_FEE: 'cost4',
  FUEL_FEE_TYPE: 'cost4Type',
  FUEL_TYPE: 'cost4Field1',
  // 停车费
  PARKING_FEE: 'cost9',
  PARKING_FEE_TYPE: 'cost9Type',
  FUEL_VOLUME: 'other2',
  // 车辆清洗费
  CLEAN_FEE: 'cost8',
  // 住宿费
  HOTEL_FEE: 'cost2',
  // 其他费用
  OTHER_FEE: 'cost5',
  FEE_REMARK: 'remark3',

  // 附件字段
  ATTACHMENT1: 'attachment1',
  ATTACHMENT2: 'attachment2',
  ATTACHMENT3: 'attachment3'
};

// 去重合并辅助函数
const merge = (...arrays) => [...new Set(arrays.flat())];

// ==================== 字段分组 ====================
export const BASE_INFO = [
  FormFieldConstants.FLAG_BB,
  FormFieldConstants.USE_CAR_TYPE,
  FormFieldConstants.USE_CAR_REASON,
  FormFieldConstants.REASON_DESCRIPTION,
  FormFieldConstants.FY_DEPT_NAME,
  FormFieldConstants.FLIGHT_NUMBER
];

export const BASE_INFO_NO_BB = BASE_INFO.filter(f => f !== FormFieldConstants.FLAG_BB);

export const ROUTE_INFO = [
  FormFieldConstants.START_PLACE,
  FormFieldConstants.VIA_PLACE,
  FormFieldConstants.DESTINATION
];

export const TIME_INFO = [
  FormFieldConstants.DEPART_TIME,
  FormFieldConstants.DEPART_TYPE,
  FormFieldConstants.EXPECT_RETURN_TIME
];

export const CONTACT_INFO = [
  FormFieldConstants.CONTACT_PERSON,
  FormFieldConstants.CONTACT_PHONE
];

export const PASSENGER_INFO = [
  FormFieldConstants.PASSENGER_NUM,
  FormFieldConstants.PASSENGER_LIST,
  FormFieldConstants.REMARK
];

export const DISPATCH_INFO = [
  FormFieldConstants.VEHICLE,
  FormFieldConstants.CAR_TYPE,
  FormFieldConstants.CAR_AGE,
  FormFieldConstants.DRIVER,
  FormFieldConstants.DRIVER_PHONE,
  FormFieldConstants.DRIVER_AGE,
  FormFieldConstants.DRIVER_LICENSE
];

export const APPROVAL_INFO = [
  FormFieldConstants.APPROVAL_LEADER,
  FormFieldConstants.DISPATCHER
];
// 费用填报相关字段
export const EXPENSE_INFO = [
  FormFieldConstants.ACTUAL_START_TIME,
  FormFieldConstants.ACTUAL_END_TIME,
  FormFieldConstants.START_MILEAGE,
  FormFieldConstants.END_MILEAGE,
  FormFieldConstants.CURR_MILEAGE,
  FormFieldConstants.HIGHWAY_FEE,
  FormFieldConstants.BRIDGE_FEE,
  FormFieldConstants.FUEL_FEE,
  FormFieldConstants.FUEL_TYPE,
  FormFieldConstants.FUEL_VOLUME,
  FormFieldConstants.CLEAN_FEE,
  FormFieldConstants.HOTEL_FEE,
  FormFieldConstants.PARKING_FEE,
  FormFieldConstants.OTHER_FEE,
  FormFieldConstants.FEE_REMARK,
  FormFieldConstants.ATTACHMENT1,
  FormFieldConstants.ATTACHMENT2,
  FormFieldConstants.ATTACHMENT3,
  FormFieldConstants.FUEL_FEE_TYPE,
  FormFieldConstants.HIGHWAY_FEE_TYPE,
  FormFieldConstants.PARKING_FEE_TYPE,
  FormFieldConstants.BRIDGE_FEE_TYPE,
];

// ==================== 场景配置 ====================
export const FormFieldGroups = {
  getSubmitConfig: () => ({ required: merge(
      BASE_INFO,
      ROUTE_INFO,
      TIME_INFO,
      CONTACT_INFO,
      PASSENGER_INFO,
      APPROVAL_INFO
    ), disabled: [] }),

  getModifyConfig: () => {
    const required = merge(
      BASE_INFO_NO_BB,
      ROUTE_INFO,
      TIME_INFO,
      CONTACT_INFO,
      PASSENGER_INFO
    );
    const disabled = [FormFieldConstants.FLAG_BB, ...APPROVAL_INFO];
    return { required, disabled };
  },

  getTripChangeConfig: () => {
    const required = merge(ROUTE_INFO, TIME_INFO);
    const disabled = merge(
      BASE_INFO,
      CONTACT_INFO,
      PASSENGER_INFO,
      APPROVAL_INFO,
    );
    return { required, disabled };
  },

  getVehicleDriverChangeConfig: () => {
    const required = [...DISPATCH_INFO];
    const disabled = merge(
      BASE_INFO,
      ROUTE_INFO,
      TIME_INFO,
      CONTACT_INFO,
      PASSENGER_INFO,
      APPROVAL_INFO,
    );
    return { required, disabled };
  }
};

// ==================== 任务与按钮 Key ====================
export const TaskKeys = {
  TASK0: 'UserTask_0',           // 用车申请
  TASK100: 'UserTask_100',       // 领导审批
  TASK200: 'UserTask_200',       // 调度员派车
  TASK300: 'UserTask_300',       // 司机确认
  TASK400: 'UserTask_400',       // 行车日志填报
  TASK500: 'UserTask_500',        // 费用审批
  VIEW: 'VIEW'
};

export const ButtonKeys = {
  HANDLE: 'HANDLE',
  RETURN: 'RETURN',
  FORM_MODIFY_ADMIN: 'FORM_MODIFY_ADMIN',
  XC_FORM_MODIFY: 'XC_FORM_MODIFY',
  XC_DRIVER_CAR_MODIFY: 'XC_DRIVER_CAR_MODIFY',
  VIEW: 'VIEW',
  FLAG_BB: 'FLAG_BB',
  FLAG_BB_REJECT: 'FLAG_BB_REJECT',
  HANDLE_REJECT: 'HANDLE_REJECT'
};

// ==================== FormConfigRegistry ====================
const CONFIG_MAP = {
  [TaskKeys.TASK0]: {
    [ButtonKeys.HANDLE]: FormFieldGroups.getSubmitConfig(),
    [ButtonKeys.HANDLE_REJECT]: {
      required: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        PASSENGER_INFO,
      ),
      disabled: [
        FormFieldConstants.APPROVAL_LEADER,
        FormFieldConstants.DISPATCHER
      ]
    },
    [ButtonKeys.FLAG_BB]: {
      required: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        PASSENGER_INFO,
        DISPATCH_INFO,
        FormFieldConstants.DISPATCHER
      ),
      disabled: []
    },
    [ButtonKeys.FLAG_BB_REJECT]: {
      required: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        PASSENGER_INFO,
        DISPATCH_INFO
      ),
      disabled: [
        FormFieldConstants.DISPATCHER
      ]
    }
  },

  [TaskKeys.TASK100]: {
    [ButtonKeys.HANDLE]: {
      required: [],
      disabled: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        PASSENGER_INFO,
        APPROVAL_INFO
      ),
    },
    [ButtonKeys.VIEW]: {
      required: [],
      disabled: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        PASSENGER_INFO,
        APPROVAL_INFO
      ),
    },
    [ButtonKeys.RETURN]: { required: ['approvalComment'], disabled: [] },
    [ButtonKeys.FORM_MODIFY_ADMIN]: FormFieldGroups.getModifyConfig(),
    [ButtonKeys.XC_FORM_MODIFY]: FormFieldGroups.getTripChangeConfig(),
  },

  [TaskKeys.TASK200]: {
    [ButtonKeys.HANDLE]: {
      required: DISPATCH_INFO,
      disabled: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        APPROVAL_INFO,
        PASSENGER_INFO,
      ),
    },
    [ButtonKeys.VIEW]: {
      required: [],
      disabled: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        APPROVAL_INFO,
        PASSENGER_INFO,
        DISPATCH_INFO
      ),
    },
    [ButtonKeys.RETURN]: { required: ['approvalComment'], disabled: [] },
    [ButtonKeys.FORM_MODIFY_ADMIN]: FormFieldGroups.getModifyConfig(),
    [ButtonKeys.XC_FORM_MODIFY]: FormFieldGroups.getTripChangeConfig(),
  },

  [TaskKeys.TASK300]: {
    [ButtonKeys.HANDLE]: {
      required: [
        FormFieldConstants.ACTUAL_START_TIME,
        FormFieldConstants.START_MILEAGE
      ],
      disabled: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        PASSENGER_INFO,
        DISPATCH_INFO,
        APPROVAL_INFO
      ),
    },
    [ButtonKeys.VIEW]: {
      required: [],
      disabled: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        PASSENGER_INFO,
        DISPATCH_INFO,
        APPROVAL_INFO
      ),
    },
    [ButtonKeys.RETURN]: { required: ['approvalComment'], disabled: [] },
    [ButtonKeys.FORM_MODIFY_ADMIN]: (() => {
      const modify = FormFieldGroups.getModifyConfig()
      modify.required.push(...DISPATCH_INFO)
      return modify
    })(),
    [ButtonKeys.XC_FORM_MODIFY]: FormFieldGroups.getTripChangeConfig(),
    [ButtonKeys.XC_DRIVER_CAR_MODIFY]: FormFieldGroups.getVehicleDriverChangeConfig(),
  },

  [TaskKeys.TASK400]: {
    [ButtonKeys.HANDLE]: {
      required: merge(ROUTE_INFO, EXPENSE_INFO),
      disabled: merge(BASE_INFO, TIME_INFO, CONTACT_INFO, PASSENGER_INFO, APPROVAL_INFO, DISPATCH_INFO),
    },
    [ButtonKeys.VIEW]: {
      required: [],
      disabled: merge(BASE_INFO, TIME_INFO, CONTACT_INFO, PASSENGER_INFO, APPROVAL_INFO, ROUTE_INFO, EXPENSE_INFO, DISPATCH_INFO),
    },
    [ButtonKeys.FORM_MODIFY_ADMIN]: (() => {
      const modify = FormFieldGroups.getModifyConfig()
      modify.required.push(...DISPATCH_INFO, ...EXPENSE_INFO)
      return modify
    })(),
    [ButtonKeys.XC_FORM_MODIFY]: (() => {
      const modify = FormFieldGroups.getTripChangeConfig()
      modify.disabled.push(...DISPATCH_INFO, ...EXPENSE_INFO)
      return modify
    })(),
    [ButtonKeys.XC_DRIVER_CAR_MODIFY]: (() => {
      const modify = FormFieldGroups.getVehicleDriverChangeConfig()
      modify.disabled.push(...EXPENSE_INFO)
      return modify
    })(),
  },

  [TaskKeys.TASK500]: {
    [ButtonKeys.HANDLE]: {
      required: [],
      disabled: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        PASSENGER_INFO,
        DISPATCH_INFO,
        APPROVAL_INFO,
        EXPENSE_INFO
      ),
    },
    [ButtonKeys.VIEW]: {
      required: [],
      disabled: merge(
        BASE_INFO,
        ROUTE_INFO,
        TIME_INFO,
        CONTACT_INFO,
        PASSENGER_INFO,
        DISPATCH_INFO,
        APPROVAL_INFO,
        EXPENSE_INFO
      ),
    },
    [ButtonKeys.FORM_MODIFY_ADMIN]: (() => {
      const modify = FormFieldGroups.getModifyConfig()
      modify.required.push(...DISPATCH_INFO, ...EXPENSE_INFO)
      return modify
    })(),
    [ButtonKeys.XC_FORM_MODIFY]: (() => {
      const modify = FormFieldGroups.getTripChangeConfig()
      modify.disabled.push(...DISPATCH_INFO, ...EXPENSE_INFO)
      return modify
    })(),
    [ButtonKeys.XC_DRIVER_CAR_MODIFY]: (() => {
      const modify = FormFieldGroups.getVehicleDriverChangeConfig()
      modify.disabled.push(...EXPENSE_INFO)
      return modify
    })(),
  },
  [TaskKeys.VIEW]: {
    [ButtonKeys.VIEW]: {
      required: [],
      disabled: Object.values(FormFieldConstants),
    },
    [ButtonKeys.FORM_MODIFY_ADMIN]: (() => {
      const modify = FormFieldGroups.getModifyConfig()
      modify.required.push(...DISPATCH_INFO, ...EXPENSE_INFO)
      return modify
    })(),
  },
}

export const FormConfigRegistry = {
  /** 获取单个 taskKey+btnKey 配置 */
  getConfig: (taskKey, btnKey) => {
    const btnMap = CONFIG_MAP[taskKey];
    return btnMap && btnMap[btnKey]
      ? btnMap[btnKey]
      : { required: [], disabled: [] };
  },

  /** 获取所有配置 */
  getAllConfig: () => {
    return CONFIG_MAP;
  }
};

// 用车记录，已办，行程库，行车日志查看时需要携带的参数
export const FORM_VIEW_PARAMS = {
  currentButtonKey: ButtonKeys.VIEW,
  taskKey: TaskKeys.VIEW
}
