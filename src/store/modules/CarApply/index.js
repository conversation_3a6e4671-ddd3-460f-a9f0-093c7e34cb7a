import {
  getEnums,
  currentUser,
  carDispatchUser,
  carCompany,
  carList,
  driverList,
  remindTitle,
  rejectCause,
  startAddress,
  carCompanyApprover,
  authRegister,
  authLogin,
  registerSms,
  applyPersonInfo,
  getFawkesSign,
  isSignup,
  getDriversAndCars,
  getFlowCar,
  getFlowButton,
  getFlowRevocation,
  getFlowChange,
  getStatsDriver, getRentCompanyStats, getUseCarStats, getThirdPartUser
} from '@/api/carApply.js';
import storage from "@utils/storage";

import {exportRecords, exportUseCarRecords} from "../../../api/carApply";
const state = () => ({
  applyCar: {},
  currUser: {
    "deptName": "",
    "phone": "",
    "roleId": "",
    "userId": "",
    "userName": ""
  }, // 当前登录人
  enums:  localStorage.getItem('enums') ? JSON.parse(localStorage.getItem('enums')) : {},
  carList: [],
  driverList: [],
  lastRoute: '',
  scrollTop: 0 // 记录页移动端的滚动高度
});

const getters = {
};

const mutations = {
  SET_SCROLL_TOP(state, data) {
    state.scrollTop = data;
  },
  SET_START_ADDRESS(state, data) {
    state.startAddress = data;
  },
  SET_END_ADDRESS(state, data) {
    state.endAddress = data;
  },
  SET_APPLY_CAR(state, data) {
    state.applyCar = data;
  },
  SET_ENUMS(state, data) {
    state.enums = data;
  },
  SET_USER(state, user) {
    state.currUser = user;
  },
  SET_CAR_LIST(state, carList) {
    state.carList = carList;
  },
  SET_DRIVER_LIST(state, driverList) {
    state.driverList = driverList;
  },
  SET_LAST_ROUTE(state, lastRoute) {
    state.lastRoute = lastRoute;
  }
}


const actions = {
  // 获取字典
  getEnum({ commit }) {
    return new Promise((resolve, reject) => {
      getEnums()
        .then((res) => {
          if (!(res.status && res.data)) {
            resolve(res)
          }
          let data = res.data;
          commit('SET_ENUMS', data);
          localStorage.setItem('enums', JSON.stringify(data));
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 获取当前用户
  getCurrentUser({ commit }, username) {
    return new Promise((resolve, reject) => {
      currentUser(username)
        .then((res) => {
          if (res.status && res.data) {
            commit('SET_USER', res.data);
            storage.set('user', JSON.stringify(res.data));
          }
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 获取用户第三方信息
  getThirdPartyUserInfo({commit}, username) {
    return new Promise((resolve, reject) => {
      getThirdPartUser(username)
        .then(res => {
          if (res.status) {
            resolve(res)
          }
        })
        .catch((err) => reject(err))

    })
  },
  // 申请时获取车辆调度员
  getCarDispatchUser({}, useCarType) {
    return new Promise((resolve, reject) => {
      carDispatchUser(useCarType)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 申请时获取租车公司
  getCarCompany({}) {
    return new Promise((resolve, reject) => {
      carCompany()
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 申请时获取车辆
  getCarList({commit},carCompanyInfoId) {
    return new Promise((resolve, reject) => {
      carList(carCompanyInfoId)
        .then((res) => {
          if (res.status && res.data) {
            commit('SET_CAR_LIST', res.data);
          }
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 申请时获取司机
  getDriverList({commit}, params) {
    return new Promise((resolve, reject) => {
     driverList(params)
        .then((res) => {
          if (res.status && res.data) {
            commit('SET_DRIVER_LIST', res.data);
          }
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 获取标题
  getRemindTitle({}) {
    return new Promise((resolve, reject) => {
      remindTitle()
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 退回原因
  getRejectCause({}) {
    return new Promise((resolve, reject) => {
      rejectCause()
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 申请时获取出发地
  getStartAddress({}) {
    return new Promise((resolve, reject) => {
      startAddress()
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 申请时获取租车公司审批人
  getCarCompanyApprover({}, carCompanyInfoId) {
    return new Promise((resolve, reject) => {
      carCompanyApprover(carCompanyInfoId)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 用户登录
  authLogin({}, params) {
    return new Promise((resolve, reject) => {
      authLogin(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 用户注册
  authRegister({}, params) {
    return new Promise((resolve, reject) => {
      authRegister(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 用户注册短信发送
  getRegisterSms({}, params) {
    return new Promise((resolve, reject) => {
      registerSms(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 申请时获取人员
  getApplyPerson({}, params) {
    return new Promise((resolve, reject) => {
      applyPersonInfo(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // fawkes签名生成
  getFawkesSign({}, params) {
    return new Promise((resolve, reject) => {
      getFawkesSign(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 用户是否注册
  getRegisterVerify({}, params) {
    console.log('stssss openId', params)
    return new Promise((resolve, reject) => {
      isSignup(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 申请时获取司机和车辆
  getDriverCar({}, params) {
    return new Promise((resolve, reject) => {
      getDriversAndCars(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 出车统计列表
  getFlowCar({}, params) {
    return new Promise((resolve, reject) => {
      getFlowCar(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 获取按钮
  getFlowButton({}, params) {
    return new Promise((resolve, reject) => {
      getFlowButton(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 撤销流程
  getFlowRevocation({}, params) {
    return new Promise((resolve, reject) => {
      getFlowRevocation(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 流程变更
  getFlowChange({}, params) {
    return new Promise((resolve, reject) => {
      getFlowChange(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 司机统计
  getStatsDriver({}, params) {
    return new Promise((resolve, reject) => {
      getStatsDriver(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 租车公司统计
  getRentCompanyStats({}, params) {
    return new Promise((resolve, reject) => {
      getRentCompanyStats(params)
        .then(res => {
          resolve(res)
        })
        .catch(err => reject(err))
    })
  },
  // 出车统计
  getUseCarStats({}, params) {
    return new Promise((resolve, reject) => {
      getUseCarStats(params)
        .then(res => {
          resolve(res)
        })
        .catch(err => reject(err))
    })
  },
  // 导出列表
  exportCarRecords({}, params) {
    return new Promise((resolve, reject) => {
      exportUseCarRecords(params)
        .then(res => {
          resolve(res)
        })
        .catch(err => reject(err))
    })
  },
  // 导出台账
  exportAllRecords({}, params) {
    return new Promise((resolve, reject) => {
      exportRecords(params)
        .then(res => {
          resolve(res)
        })
        .catch(err => reject(err))
    })
  }
}


export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
