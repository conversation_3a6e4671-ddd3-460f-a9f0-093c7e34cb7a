import {getDepartmentCarUseList} from '@/api/carApply.js';

const state = {}
const getters = {}
const mutations = {}
const actions = {
  getDepartmentCarUseList({commit}, params) {
    return new Promise((resolve, reject) => {
      getDepartmentCarUseList(params).then(res => {
        resolve(res)
      }).catch(err => reject(err))
    })
  }
}
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
