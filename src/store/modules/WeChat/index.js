import { authAccessToken, getWxSign } from '@/api/wx.js';
const state = () => ({
});

const getters = {
};

const mutations = {
}


const actions = {
  // 获取access_token
  getAuthAccessToken({}, code) {
    return new Promise((resolve, reject) => {
      authAccessToken(code)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
  // 微信公众号获取JS签名
  getWxSign({}, url) {
    return new Promise((resolve, reject) => {
      getWxSign(url)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  },
}


export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
