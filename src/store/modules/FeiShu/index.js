import { getFeiSuUserInfo } from '@/api/fs.js';
const state = () => ({
});

const getters = {
};

const mutations = {
}


const actions = {
  // 获取单个用户信息
  getFeiSuUserInfo({}, params) {
    return new Promise((resolve, reject) => {
      getFeiSuUserInfo(params)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => reject(err))
    });
  }
}


export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
