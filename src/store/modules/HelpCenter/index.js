// store/modules/message.js
import {
  doReadMsg,
  getNotifyFeedbackList,
  getUnReadTypeList,
  readNotify
} from '@components/HelpCenter/api'

const state = {
  itemList: [
    { name: "问题反馈", icon: "report", tag: "feedback", notify: false },
    { name: "常见问题", icon: "question", tag: "question", notify: false },
    { name: "操作手册", icon: "option", tag: "option", notify: false },
    { name: "更新日志", icon: "update-log", tag: "update-log", notify: false }
  ],
  msgList: [],
  /**
   *     // id
   *     private Long id;
   *     // 反馈状态
   *     private Integer feedbackStatus;
   *     // 新增的评论列表
   *     private List<Long> commentList;
   */
  notifyFeedbackList: [],
};

const getters = {
  getItemList: (state) => {
    return state.itemList
  },
  unreadItems: (state) => {
    return state.itemList.filter(item => item.notify);
  }
};

const mutations = {
  SET_MSG_LIST(state, msgList) {
    state.msgList = msgList;
  },
  SET_FEEDBACK_LIST(state, list) {
    state.notifyFeedbackList = list;
  },
  // 根据 id 删除 notifyFeedbackList 中对应的 item
  DELETE_NOTIFY_FEEDBACK_ITEM(state, id) {
    state.notifyFeedbackList = state.notifyFeedbackList.filter(item => item.id !== id);
  },
  UPDATE_NOTIFY_STATUS(state) {
    const feedbackItem = state.itemList.find(item => item.tag.toLowerCase() === "feedback");
    if (state.notifyFeedbackList && state.notifyFeedbackList.length > 0) {
      // 你可以对 feedbackItem 做一些特殊处理，或者返回固定对象
      feedbackItem.notify = true;
    } else {
      feedbackItem.notify = false;
    }
  },
};

const actions = {
  async getUnReadMsg({ commit }) {
    const res = await getNotifyFeedbackList();
    if (res.data) {
      commit('SET_FEEDBACK_LIST', res.data);
      commit('UPDATE_NOTIFY_STATUS');
    }
  },
  async readMsg({ commit, state, dispatch }, id) {
    const res = await readNotify(id);
    // 读完后，标记已读
    commit("DELETE_NOTIFY_FEEDBACK_ITEM", id)
    commit('UPDATE_NOTIFY_STATUS', state.notifyFeedbackList);
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
