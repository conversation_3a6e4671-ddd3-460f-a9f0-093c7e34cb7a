/*
 * @Author: xie_sm
 * @Date: 2022-04-15 08:54:47
 * @LastEditors: ye_xf
 * @LastEditTime: 2022-08-05 15:22:57
 * @FilePath: \mobile-template\src\store\State\stateTypes.js
 * @Description:
 *
 */
export const QUICK_ACCESS_AREA = 'quickAccessArea'
export const TEMP_USER = 'tempUser'
export const ENUM = 'enum' // 字典查询通用枚举
export const CURRENT_ROW = 'currentRow' // 任务待办点击每一项保存的值
export const REJECT_TASK_KEY = 'rejectTaskKey'
export const SCHEDULE_TABLE = 'scheduleTable'
export const STATUS_BAR_HEIGHT = 'StatusBarHeight' //状态栏高度
export const USER_INFO = 'userInfo' // 用户信息
export const IS_LOGIN = 'isLogin' // 用户是否登录
export const PERMISSION_QUEUE = 'permissionQueue'//鉴权队列
export const ROUTES = 'routes'
export const PERMISSION = 'permission'//用户权限
export const PORTAL = 'portal'//当前门户
export const PORTALS = 'portals'//门户
export const INTERFACE_CONFIG = 'interfaceConfig'
export const DATA_SAFE = 'dataSafe'
export const VISITED_VIEWS = 'visitedViews' //页面记录
export const CACHED_VIEWS = 'cachedViews'
export const LANG = 'language' //多语言配置文件存储
export const ADVANCED_CONFIG = 'advancedConfig' // 高级设置
export const APPLY_RESOURCE = 'applyResource' // 运行平台：1.飞书，2.微信，3.PC
export const ROLE_ID = 'roleId' // 用户角色： DEFAULT，APPLY，APPROVAL, DRIVER
export const FEISHU_USER_INFO = 'feishuUserInfo';// 飞书登录用户信息
export const OPEN_ID = 'openId';// 飞书，微信公众号的openId
export const PLATFORM = 'platform'
export const LOADED = 'loaded'
export const COLLAPSE = 'collapse'
export const IS_APP_CREATED = 'isAppCreated'
export const FILTERS = 'savedFilters'
export const SECOND_LEVEL_PORTAL = 'secondLevelPortal' // 二级门户
export const PROJECT_INFO = 'projectInfo' // 项目信息
export const FAVORITE_PORTALS = 'favoritePortals' // 收藏的门户
export const FEISHU_LINK = 'feishuLink'
export const GLOBAL_TABLE_DATA = 'globalTableData'
export const TABLE_LOADING = 'tableLoading'
export const GLOBAL_QUERY_PARAMS = 'globalQueryParams'
export const PROVINCES = 'provinces'
export const IS_PROJECT_CLOSED = 'isProjectClosed' // 项目是否关闭
export const AUTH_PORTALS = 'authPortals' // 获取已授权的项目列表
export const DATA_BASE_PORTAL = 'dataBasePortal' // 数据中台门户
export const OLD_PROJECT_PORTAL = 'oldProjectPortal' // 旧项目门户
export const VD_PROJECT_INFO = 'vdProjectInfo' // 旧项目门户
export const RECENTLY_USED_PORTALS = 'recentlyUsedPortals' // 最近使用的门户
export const IS_PHONE_VERIFY = 'isPhoneVerify' // 是否进行了手机尾号验证
