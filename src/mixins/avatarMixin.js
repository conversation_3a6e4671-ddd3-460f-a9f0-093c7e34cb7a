import {getAvatar} from "@components/HomeHeader/api";
import storage from "@utils/storage";
import {mapState} from "vuex";

export default {
  data() {
    return {
      avatarUrl: ''
    }
  },
  computed: {
    ...mapState('CarApply', ['currUser']),
    userName() {
      const user = storage.get("user");
      if (user) {
        return JSON.parse(user)?.userName || ''
      }
      return this?.currUser?.userName || ''
    },
    userFullName() {
      const user = storage.get("user");
      if (user) {
        return JSON.parse(user).userFullName || JSON.parse(user).userFullname || ''
      }
      return this?.currUser?.userFullName || ''
    },
  },
  filters: {
    getFamilyName(value) {
      return value?.substr(0, 1)
    },
  },
  watch: {
    userName: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          getAvatar(newVal).then(res => {
            if (res.status) {
              this.avatarUrl = res.data;
            }
          })
        }
      }
    }
  }
}
