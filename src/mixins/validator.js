import platform from './platform'
export default {
  mixins: [platform],
  methods: {
    // 大于0的正整数
    inputNumber(value) {
      return value.replace(/^0(0+|\d+)|[^\d]+/g,'');
    },
    // 两位小数的数字
    inputNumberLimit(value) {
      return value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1');
    },
    // 检测数字是否为正整数
    numberValidator(val, value, callback) {
      if (Number.isNaN(Number(value))) {
        callback(new Error('请输入数字'))
      } else {
        if (Number(value) < 0) {
          callback(new Error('请输入大于0的数字'))
        } else {
          callback()
        }
      }
    },
    phoneValidator(val, value, callback) {
      if (this.isMobile) {
        if (val) {
          return /^[1][23456789][0-9]{9}$/.test(val)
          // return /^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-9])|(18[0-9])|166|198|199|191|(147))\d{8}$/.test(val)
        }
        return true
      } else {
        if (/^[1][23456789][0-9]{9}$/.test(value)) {
          callback()
        } else {
          if (!value) {
            callback(new Error('请输入手机号'))
          } else {
            callback(new Error('请输入正确的手机格式'))
          }
        }
      }
    },
    carNumValidator(val, value, callback) {
      const xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/;
      const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
      // const regExp = new RegExp(reg);
      if (this.isMobile) {
        if (!val) {
          return false;
        } else if (val.length === 7 && !xreg.test(val)) {
          // 新能源车
          // callback(new Error('车牌号格式错误'))
          // callback();
          return true;
        } else if (val.length === 8 && !creg.test(val)) {
          // 普通汽车
          // callback(new Error('车牌号格式错误'))
          // callback();
          return true;
        } else if (![7, 8].includes(val.length)) {
          // callback(new Error('车牌号格式错误'));
          return false;
        } else {
          callback();
          return true;
        }
      } else {
        if (!value) {
          callback(new Error('请输入车牌号'));
        } else if (value.length === 7 && !xreg.test(value)) {
          // 新能源车
          // callback(new Error('车牌号格式错误'))
          callback();
        } else if (value.length === 8 && !creg.test(value)) {
          // 普通汽车
          // callback(new Error('车牌号格式错误'))
          callback();
        } else if (![7, 8].includes(value.length)) {
          callback(new Error('车牌号格式错误'));
        } else {
          callback();
        }
      }

    },
  }
}
