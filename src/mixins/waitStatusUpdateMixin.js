import EventBus from "@utils/eventBus";
import {sleep} from "@utils/util";

export default {
  methods: {
    startLoading() {
      this.loading = true;
      EventBus.$emit('buttonLoading', true)
    },
    endLoading() {
      this.loading = false;
      EventBus.$emit('buttonLoading', false)
    },
    async waitStatusUpdate() {
      this.startLoading()
      await sleep(1000 * 2)
      this.endLoading();
    },
  }
}
