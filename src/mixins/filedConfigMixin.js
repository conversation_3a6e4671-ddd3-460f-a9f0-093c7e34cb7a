import { mapActions, mapGetters, mapState } from 'vuex'

export default {
  data() {
    return {
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState(['applyResource', 'userInfo', 'openId', 'portal', 'vdProjectInfo']),
    ...mapState('FormController', ['formTaskKey', 'formBtnKey', 'fieldMap']),
    ...mapGetters('FormController', ['currentFieldConfig', 'isReadonly', 'isRequired', 'getFieldState']),

    vdUserInfo() {
      return this.$storage.getObject('user');
    }
  },
  methods: {
    ...mapActions('CarApply', ['getCurrentUser', 'getEnum']),
    ...mapActions('FormController', ['loadFieldConfig', 'setFormBtnKey', 'cleanFieldConfig']),

    async initFormFieldConfig(taskKey = this.formTaskKey, btnKey = 'HANDLE') {
      this.setFormBtnKey(btnKey);
      await this.loadFieldConfig(taskKey);
    },

    isFieldEditable(field) {
      const state = this.getFieldState(field);
      return !state.readonly;
    },
    getDisableFlag(field) {
      const state = this.isReadonly(field);
      return state;
    },
    getRequiredFlag(field) {
      const state = this.isRequired(field);
      return state;
    },
    getVisibleFlag(field) {
      const isReadOnly = this.isReadonly(field);
      const isRequired = this.isRequired(field);
      return isReadOnly || isRequired;
    },
    doCleanFieldInfo() {
      this.cleanFieldConfig()
    }

  },
  watch: {

  },
  async created() {
  },
  mounted() {
  }
}
