import {processColorTable, processStatusTable} from "@utils/constants";

export default {
  methods: {
    getTagType(row) {
      const status = row.applyFormUserState || row.processState
      return processStatusTable['pc'][status] || 'link'
    },
    getProperty(row, prop) {
      let status;
      if (prop) {
        status = prop
      } else {
        status = row.applyFormUserState ? 'applyFormUserState' : 'processState';
      }
      return status
    },
    getTagColor(row, prop) {
      const status = this.getProperty(row, prop);
      return processColorTable['pc'][row[status]] || '#9BA0A3'
    },
    getTagText(row, prop) {
      const enums = localStorage.getItem('enums');
      const status = this.getProperty(row, prop);
      const Enum = status === 'applyFormUserState' ? 'ApplyFormUserStateEnums' : 'ProcessStateEnums';
      const result = JSON.parse(enums)[Enum].find(item => item.key.toString() === row[status].toString())
      return result ? result.value : ''
    },
  }
}
