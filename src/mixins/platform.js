import {mapState} from "vuex";
import * as StateTypes from '@/store/State/stateTypes'

export default {
  computed: {
    ...mapState([StateTypes.PLATFORM]),
    ...mapState('CarApply', ['lastRoute']),
    isPC(){
      return this[StateTypes.PLATFORM] === 'pc'
    },
    isMobile(){
      return this[StateTypes.PLATFORM] === 'mobile'
    },
    noAuthPage() {
      // 用户从详情页进入或者从飞书卡片链接进入
      return this.$route.path.includes('/projectCar/details')
    },
  },
  methods: {
    goToLastRoute(){
      this.$router.push({
        name: this.lastRoute.name,
        params: this.lastRoute.params,
        query: this.lastRoute.query
      })
    },
    locateToErr() {
      setTimeout(() => {
        let errorDiv;
        if (this.isPC) {
          errorDiv = document.getElementsByClassName('is-error')[0]
        } else {
          errorDiv = document.getElementsByClassName('fm-field--error')[0]
        }
        if (errorDiv) {
          errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 0)
    },
  }
}
