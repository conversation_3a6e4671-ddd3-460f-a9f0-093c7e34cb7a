import {getExpireNum, getTaskNum} from "@/api/carApply";
import platform from "@/mixins/platform";
import EventBus from "@utils/eventBus";
import {sleep} from "@utils/util";

export default {
  mixins: [platform],
  data() {
    return {
      dotTable: {}
    }
  },
  computed: {
    source() {
      return this.isPC ? this.data : this.tabRoutes;
    }
  },
  methods: {
    getMethod(name) {
      switch (name) {
        case 'Reminder':
          return getExpireNum;
        case 'Todo':
          return getTaskNum;
        default:
          return null;
      }
    },
    async refresh(name = this.$route.name) {
      const fn = this.getMethod(name);
      if (fn) {
        await sleep(1000)
        fn().then(res => {
          this.$set(this.dotTable, name, res.data);
        })
      }
    }
  },
  async mounted() {
    EventBus.$on('refreshDot', this.refresh)
    const hasTodo = this.source.findIndex(item => item.name === 'Todo') > -1; // 是否有待办
    const hasReminder = this.source.findIndex(item => item.name === 'Reminder') > -1; // 是否有提醒

    if (hasReminder || hasTodo) {
      const [reminderRes, todoRes] = await Promise.all([
        hasReminder ? getExpireNum() : Promise.resolve(null),
        hasTodo ? getTaskNum() : Promise.resolve(null)
      ]);

      if (reminderRes) {
        this.$set(this.dotTable, 'Reminder', reminderRes.data);
      }

      if (todoRes) {
        this.$set(this.dotTable, 'Todo', todoRes.data);
      }
    }
  },
  beforeDestroy() {
    EventBus.$off('refreshDot', this.refresh)
  }
}
