/*
 * @Author: <EMAIL>
 * @Date: 2019-11-01 16:46:37
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-15 10:08:06
 * @Description: 各模块入口文件注册公用属性
 */
import { mapMutations } from 'vuex'
import { LANG } from '@/store/State/stateTypes'
import { SET_LOCAL_LANG } from '@/store/Mutation/mutationTypes'
import deepmerge from 'deepmerge'
export default {
  provide () {
    return {
      lan: () => this.LANG
    }
  },
  computed: {
    LANG () {
      return deepmerge(this.lan() || {}, this.$store.state[LANG][this.$options.name] || {}, { clone: true })  //注册模块的入口文件中，覆盖全局LANG,指向当前模块
    }
  },
  methods: {
    ...mapMutations({
      'SET_LOCAL_LANG': SET_LOCAL_LANG,
    }),
    //读取本地语言资源
  }
}
