import {PROJECT_PORTAL} from "@utils/constants";
import * as StateTypes from "@store/State/stateTypes";
import {mapState} from "vuex";

export default {
  ...mapState([StateTypes.PORTAL, StateTypes.PORTALS, StateTypes.SECOND_LEVEL_PORTAL, StateTypes.DATA_BASE_PORTAL, StateTypes.OLD_PROJECT_PORTAL]),
  computed: {
    thirdLevelPortals() {
      console.info('🚀🚀', 'this[StateTypes.PORTALS] -->', this[StateTypes.PORTALS], `<-- thirdLevelPortalMixin.js/thirdLevelPortals`)
      if(this[StateTypes.PORTALS]) {
        return this[StateTypes.PORTALS].filter(item => (item.parentName === PROJECT_PORTAL) && (item.id !== this[StateTypes.DATA_BASE_PORTAL].companyPortalId));
      }
      return []
    },
    mobileThirdLevelPortals() {
      return this.thirdLevelPortals.map(item => ({code: item.id, value: item.name, key: item.id}))
    }
  }
}
