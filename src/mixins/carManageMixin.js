import { mapGetters, mapState } from 'vuex'
import {getCarList, getDriverInfoByCar} from "@modules/ProjectCar/ProjectPortal/CarManage/api";
import * as StateTypes from "@store/State/stateTypes";
import {carStatusColorTable, PROJECT_PORTAL} from "@utils/constants";
import { getDriverList } from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import {sleep} from "@utils/util";
import * as GetterTypes from '@store/Getter/getterTypes'

export default {
  data() {
    return {
      loading: false,
      total: 0,
      tableData: [],
      pageNo: 1,
      pageSize: 10,
      type: '',
      queryParams: {
        projectId: ''
      },
      currentData: null,
      finished: false,
    }
  },
  computed: {
    ...mapState('CarApply', ['enums']),
    ...mapState(['portal']),
    ...mapState([StateTypes.PORTALS]),
    ...mapState([StateTypes.GLOBAL_TABLE_DATA, StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    cards() {
      return this.tableData.map(item => {
        return {
          ...item,
          title: item.carNum,
          tags: [{text: this.getCarStatusText(item), color: carStatusColorTable['pc'][item.carStatus]}],
          descriptions: [
            {label: '司机', value: item.driverFullName},
            {label: '联系电话', value: item.driverPhone},
            {label: '品牌型号', value: item.carName},
            {label: '座位数', value: item.carSeatNum},
          ]
        }
      })
    },
    carStatus() {
      return this.enums.CarStatusEnums;
    },
    carResource() {
      return this.enums.CarResourceEnums;
    },
    projects() {
      return this[StateTypes.PORTALS].filter(item => item.parentName === PROJECT_PORTAL)
    },
  },
  methods: {
    handleEdit(row, type) {
      this.$refs.add.open(row, type === '编辑' ? 'edit' : 'view');
    },
    handleAdd() {
      this.$refs.add.open({}, 'plus');
    },
    handleBatchAdd() {
      this.$refs.batchAdd.open({})
    },
    getCarStatusTag({carStatus}) {
      if (carStatus !== null) {
        const item = this.carStatus.find(item => item.key === carStatus)
        if (item) {
          switch (item.key) {
            case 100:
              return ''
            case 200:
              return 'warning'
            case 300:
              return 'info'
            case 400:
              return 'danger'
          }
        } else {
          return 'info'
        }
      }
      return 'info'
    },
    getCarStatusText({carStatus}) {
      if (carStatus !== null) {
        const item = this.carStatus.find(item => item.key === carStatus)
        return item ? item.value : '暂无'
      }
      return '暂无'
    },
    getResourceText({carResource}) {
      if (carResource) {
        const item = this.carResource.find(item => item.key === carResource)
        return item.value;
      }
    },
    async handleRefresh() {
      const activeIndex = this.$refs.cardFlowRef.activeIndex;
      await this.fetchData(activeIndex)
    },
    async tableRefresh(isDelete) {
      const activeIndex = this.$refs.cardFlowRef.activeIndex;
      // 删除当前数据
      if (isDelete) {
        this.tableData.splice(activeIndex, 1);
        const index = activeIndex <= this.cards.length - 1 ? activeIndex : 0;
        const item = this.cards[index];
        if (item) {
          this.$refs.cardFlowRef.resetActiveIndex(index);
        } else {
          this.currentData = null;
          this.$refs.cardFlowRef.resetActiveIndex(null);
        }
      }else {
        // 重新查询
        const params = {
          ...this.queryParams,
          pageNo: activeIndex + 1,
          pageSize: 1,
        };
        const res = await getCarList(params);
        const {list} = res.data || [];
        if (list.length) {
          const item = list[0];
          // 替换
          this.tableData.splice(activeIndex, 1, item)
        }
      }
    },
    handleIndexChange(item) {
      this.$refs.cardFlowRef.disableContentLoading();
      if (item) {
        this.currentData = JSON.parse(JSON.stringify(item));
      }
      this.type = 'view'
    },
    loadMore(value) {
      this.pageNo += 1;
      this.getData(this.pageNo, value)
    },
    searchData(value) {
      this.pageNo = 1;
      this.getData(1, value)
    },
    async fetchData(pageNo) {
      this.loading = true;
      await sleep(1000)
      this.loading = false;
      const res = await getCarList({
        ...this[GetterTypes.GET_GLOBAL_STATE],
        ...this.queryParams,
        pageNo: pageNo + 1,
        pageSize: 1,
      });
      const {list} = res.data || [];
      if (list.length) {
        const item = list[0];
        // 获取司机信息
        const {data: driverInfos} = await getDriverInfoByCar(item.id)
        const driverInfo = driverInfos[item.id] || {};
        // 替换
        this.tableData.splice(pageNo, 1, {
          ...item,
          driver: driverInfo.id,
          driverPhone: driverInfo.driverPhone,
          driverFullName: driverInfo.driverFullName
        })
      } else {
        this.tableData = [];
      }
    },
    async getData(pageNo, value) {
      try {
        if (!pageNo || pageNo < 2) {
          this.loading = true;
        }
        const params = {
          ...this.queryParams,
          pageNo: pageNo,
          pageSize: this.pageSize,
          carNum: value,
        };
        const res = await getCarList(params);
        if (!res.status) {
          return false;
        }
        this.total = res.data.total;
        this.finished = res.data.isLastPage;
        // 获取司机信息
        const carIds = res.data.list.map(item => item.id).join(',')
        if (carIds.length > 0) {
          const {data: driverInfos} = await getDriverInfoByCar(carIds)
          const newData = res.data.list.map(item => {
            const driverInfo = driverInfos[item.id] || {};
            return {...item, ...{driver: driverInfo.id, driverPhone: driverInfo.driverPhone, driverFullName: driverInfo.driverFullName}}
          });
          this.tableData = [...this.tableData, ...newData];
        } else {
          this.tableData = [...res.data.list]
        }
        this.loading = false;
      }catch (e) {
        this.loading = false;
      }
    },
    clear() {
      for (let i in this.queryParams) {
        this.queryParams[i] = '';
      }
      this.pageNo = 1
      this.tableData = [];
      this.queryParams.projectId = this.portal.id;
      this.getData(this.pageNo).then(() => {
        // 重置激活项为第一项
        this.$refs.cardFlowRef.resetActiveIndex(0);
      });
    }
  },
  watch: {
    [StateTypes.GLOBAL_TABLE_DATA]: {
      async handler(newVal) {
        this.pageNo = 1; // 手动重置index
        this.finished = newVal.isLastPage;
        this.tableData = newVal.list
        this.$refs.cardFlowRef.resetActiveIndex(null);
      }
    }
  },
}
