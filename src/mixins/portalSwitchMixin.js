import {mapActions, mapMutations, mapState} from "vuex";
import * as StateTypes from "@store/State/stateTypes";
import {PROJECT_PORTAL} from "@utils/constants";
import {getProjectStatus, recordProjectSwitch} from "@components/HomeHeader/api";
import * as mutationTypes from "@store/Mutation/mutationTypes";
import * as ActionTypes from "@store/Action/actionTypes";

export default {
  computed: {
    ...mapState([
      StateTypes.PORTAL,
      StateTypes.PORTALS,
      StateTypes.SECOND_LEVEL_PORTAL,
      StateTypes.DATA_BASE_PORTAL,
      StateTypes.OLD_PROJECT_PORTAL,
      StateTypes.RECENTLY_USED_PORTALS,
      StateTypes.FAVORITE_PORTALS
    ]),
    userInfo() {
      return this.$storage.getObject('user')
    },
    recentlyUsedProjects() {
      return this[StateTypes.RECENTLY_USED_PORTALS];
    },
    currentPortalId() {
      return this[StateTypes.PORTAL].id;
    },
    currentPortalName() {
      return this[StateTypes.PORTAL].name;
    },
    thirdLevelPortals() {
      return this[StateTypes.PORTALS].filter(item => (item.parentName === PROJECT_PORTAL) && (item.id !== this[StateTypes.DATA_BASE_PORTAL].companyPortalId));
    },
    filterThirdLevelPortals() {
      return this.thirdLevelPortals.filter(item => item.name.includes(this.searchVal));
    },
    groupedThirdPortalTreeData() {
      const grouped = {};
      this.filterThirdLevelPortals.forEach(item => {
        const deptName = item.projectManageDeptName;
        if (!grouped[deptName]) {
          grouped[deptName] = [];
        }
        if (this[StateTypes.FAVORITE_PORTALS].includes(item.id)) {
          item.isCollect = true;
        }
        grouped[deptName].push(item);
      });
      // 将grouped改为树形数据
      let treeData = [];
      for (const key in grouped) {
        const items = grouped[key];
        treeData.push({
          name: key,
          children: items
        });
      }
      let deptName = this.userInfo.userDepName;

      treeData.sort((a, b) => {
        if (a.name === deptName) return -1;
        if (b.name === deptName) return 1;
        return 0;
      });
      return treeData;
    },
    favoriteProjects() {
      const favorite = this[StateTypes.FAVORITE_PORTALS]
      let favoriteList = []
      if (favorite.length) {
        favoriteList = this[StateTypes.PORTALS].filter(item => favorite.includes(item.id))
        favoriteList.map(res => res.isCollect = true)
      }
      return favoriteList
    }
  },
  methods: {
    ...mapMutations([
      mutationTypes.SET_SECOND_LEVEL_PORTAL,
      mutationTypes.SET_PROJECT_CLOSED,
      mutationTypes.SET_OLD_PROJECT_PORTAL,
      mutationTypes.SET_VD_PROJECT_INFO,
      mutationTypes.SET_PORTALS,
      mutationTypes.SET_FAVORITE_PORTALS
    ]),
    ...mapActions([
      ActionTypes.CHANGE_PORTAL,
      ActionTypes.GET_RECENTLY_USED_PORTALS,
      ActionTypes.PORTALS_FAVORITE
    ]),
    handleSelectChange(val, shouldMark = true) {
      const portal = this[StateTypes.PORTALS].find(item => item.id === val);
      this[mutationTypes.SET_OLD_PROJECT_PORTAL](JSON.parse(JSON.stringify(portal)))
      this[ActionTypes.CHANGE_PORTAL](portal);
      // 标记项目被切换过
      shouldMark && recordProjectSwitch(val)

      getProjectStatus(val).then(res => {
        if (res.status) {
          // 100 正常，200 关闭
          const status = res.data ? (res.data.projectStatus === 200) : false; // 数据舱门户不设置为关闭
          this[mutationTypes.SET_PROJECT_CLOSED](status);
          let data = res.data;
          this[mutationTypes.SET_VD_PROJECT_INFO](data);
        }
      })

      this.$router.replace({path: "/distribute"});
    },
    handleRecentClick(id) {
      // 点击当前节点，不做任何操作
      if (id === this.currentPortalId) {
        return;
      }
      if (id) {
        // 已经被标记过最近使用，所以就不用调用标记接口了
        this.handleSelectChange(id, false);
      }
    },
    handleNodeClick(data) {
      // 点击当前节点，不做任何操作
      if (data.id === this.currentPortalId) {
        return;
      }
      if (data.id) {
        this.input = data.name;
        this.handleSelectChange(data.id);
      }
    },
    handleCollectClick(data) {
      const treeData = this[StateTypes.PORTALS]
      let collectData = this[StateTypes.FAVORITE_PORTALS]
      // 切换收藏图标
      const index = treeData.findIndex(item => item.id === data.id)
      treeData[index].isCollect = !data.isCollect
      this[mutationTypes.SET_PORTALS](treeData)
      // 更新收藏列表
      if (treeData[index].isCollect) {
        collectData.push(data.id)
        collectData = [...new Set(collectData)]
      } else {
        collectData = collectData.filter(item => item !== data.id)
      }
      this[mutationTypes.SET_FAVORITE_PORTALS](collectData)
      // 保存收藏状态
      const params = {
        favoriteFlag: treeData[index].isCollect,
        portalId: data.id
      }
      this[ActionTypes.PORTALS_FAVORITE](params)
    }
  },
  watch: {
    currentPortalId: {
      immediate: true,
      async handler(newVal) {
        const res = await getProjectStatus(newVal);
        if (res.status) {
          // 100 正常，200 关闭
          const status = res.data ? (res.data.projectStatus === 200) : false; // 数据舱门户不设置为关闭
          this[mutationTypes.SET_PROJECT_CLOSED](status);
          let data = res.data;
          this[mutationTypes.SET_VD_PROJECT_INFO](data);
        }
      }
    }
  },
}
