import {authLogin, authRegister, isSignup} from "@/api/carApply";
import {app_id, getFeishuUserToken, getFeishuCode} from "@utils/feishuapi";
import {mapActions, mapMutations} from "vuex";
import * as ActionTypes from "@store/Action/actionTypes";
import {Toast} from "fawkes-mobile-lib";
import * as MutationTypes from '@/store/Mutation/mutationTypes'
import * as mutationTypes from "@store/Mutation/mutationTypes";

export default {
  methods: {
    ...mapActions([ActionTypes.LOGIN_SUCCESS]),
    ...mapMutations([
      MutationTypes.SET_FEISHU_USER_INFO,
      MutationTypes.SET_OPEN_ID,
      MutationTypes.SET_USER_LOCAL_INFO,
      MutationTypes.SET_USER_INFO,
      MutationTypes.SET_FEISHU_LOADED
    ]),
    getFeishuCode,
    async handleLogin() {
      const code = await this.getFeishuCode();
      // 直接登录
      const loginData = await this.login({code, source: 1});
      const code_1 = await getFeishuCode();
      const r = await getFeishuUserToken(code_1);
      console.info('🚀🚀', '登录成功后获取token -->', r, `<-- feishuLogin.js/handleLogin`)
      this[MutationTypes.SET_USER_LOCAL_INFO](loginData);
      this[MutationTypes.SET_USER_INFO](loginData);
      const openId = loginData.sysThirdParty.uuid;
      this.SET_OPEN_ID(openId);
      // 验证是否注册
      console.info('🚀🚀', 'openId -->', openId, `<-- feishuLogin.js/handleLogin`)
      const isUserSignup = await isSignup({openId})
      console.info('🚀🚀', 'isUserSignup -->', isUserSignup, `<-- feishuLogin.js/handleLogin`)
      if (isUserSignup.data) {
        this[ActionTypes.LOGIN_SUCCESS](loginData)
      } else {
        // 先获取手机号
        const authCode = await this.getFeishuCode();
        // 获取用户信息
        console.info('🚀🚀', 'authCode -->', authCode, `<-- feishuLogin.js/handleLogin`)
        const getUserTokenRes = await getFeishuUserToken(authCode);
        console.info('🚀🚀', 'getUserTokenRes -->', getUserTokenRes, `<-- feishuLogin.js/handleLogin`)
        this.SET_FEISHU_USER_INFO(getUserTokenRes.data);
        const phoneNumber = Number(getUserTokenRes.data.mobile.toString().replace('+86', ''))
        const {
          status: authRegisterStatus,
          code: authRegisterCode,
          data,
          message
        } = await authRegister({
          openId,
          phone: phoneNumber,
          source: 1
        });
        console.info('🚀🚀', 'authRegisterStatus -->', authRegisterStatus, `<-- feishuLogin.js/handleLogin`)
        console.info('🚀🚀', 'authRegisterCode -->', authRegisterCode, `<-- feishuLogin.js/handleLogin`)
        if (authRegisterStatus) {
          this[ActionTypes.LOGIN_SUCCESS](data)
        } else {
          if (authRegisterCode === -8500010) {
            // 需要手机号认证
            await this.$router.push({
              path: '/sign',
              query: {openId}
            })
          } else {
            Toast.error(message)
            await this.$router.push({
              path: '/404',
              query: {errorMessage: message}
            })
          }
        }
      }
    },
    async login(params) {
      try {
        const res = await authLogin(params)
        return res.data;
      } catch (e) {
        console.info('🚀🚀', '登录失败： -->', e, `<-- feishuLogin.js/login`)
        this.$router.push({
          path: '/404',
          query: {errorMessage: '登录失败'}
        })
      }
    },
    async feishuInit() {
      try {
        this[MutationTypes.SET_FEISHU_LOADED](true)
        await this.handleLogin()
      } catch (e) {
        console.info('🚀🚀', '登录失败 -->', e, `<-- App.vue/feishuInit`)
        this.$router.push({
          path: '/404',
          query: {errorMessage: '登录失败'}
        })
      }
    },
  }
}
