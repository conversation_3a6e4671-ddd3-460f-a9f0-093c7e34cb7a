import platform from '@/mixins/platform'
import request from '@/utils/request'
import { changeTips } from '@utils/constants'

export default {
  mixins: [platform],
  data() {
    return {
      visible: false,
      notificationChecked: false,
      buttonKey: '',
    }
  },
  computed: {
    userInfo() {
      return this.$storage.getObject('user')
    },
    message() {
      return this.buttonKey ? changeTips[this.buttonKey] : ''
    },
  },
  methods: {
    open({buttonKey, checked}) {
      this.visible = true
      this.buttonKey = buttonKey
      this.notificationChecked = checked
    },
    handleConfirm() {
      this.visible = false;
      if (this.notificationChecked) {
        // 如果用户勾选了确认，向后端请求，保证以后不再弹出
        this.setNotification(this.buttonKey)
      }
    },
    getParams(buttonKey) {
      const userId = this.userInfo.userId
      const platform = this.isPC ? 'pc' : 'mobile'
      return {
        userId,
        platform,
        btnKey: buttonKey,
      }
    },
    getNotification(buttonKey) {
      const params = this.getParams(buttonKey)
      return request({
        url: '/vehicle-dispatch/vd/flow/notify/flag/get',
        method: 'post',
        params,
      }).then((res) => {
        if (res.status) {
          this.notificationChecked = res.data
          return res.data
        }
      })
    },
    setNotification(buttonKey) {
      const params = this.getParams(buttonKey)
      return request({
        url: '/vehicle-dispatch/vd/flow/notify/flag/set',
        method: 'post',
        params,
      })
    }
  },
}
