<svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="18"
     height="18" viewBox="0 0 18 18">
    <defs>
        <clipPath id="master_svg0_461_14892">
            <rect x="0" y="0" width="18" height="18" rx="0"/>
        </clipPath>
    </defs>
    <g clip-path="url(#master_svg0_461_14892)">
        <g>
            <rect x="0" y="0" width="18" height="18" rx="0" fill="currentColor" fill-opacity="0.009999999776482582"
                  style="mix-blend-mode:passthrough"/>
        </g>
        <g>
            <path d="M4.5,16L4.5,4Q4.5,3.9507543,4.509607,3.9024549Q4.519215,3.854155,4.53806,3.808658Q4.556906,3.763161,4.584265,3.722215Q4.611625,3.681269,4.646447,3.646447Q4.681269,3.611625,4.722215,3.584265Q4.763161,3.556906,4.808658,3.5380599999999998Q4.854155,3.519215,4.9024549,3.509607Q4.9507543,3.5,5,3.5L15,3.5Q15.0492,3.5,15.0975,3.509607Q15.1458,3.519215,15.1913,3.5380599999999998Q15.2368,3.556906,15.2778,3.584265Q15.3187,3.611625,15.3536,3.646447Q15.3884,3.681269,15.4157,3.722215Q15.4431,3.763161,15.4619,3.808658Q15.4808,3.854155,15.4904,3.9024549Q15.5,3.9507543,15.5,4L15.5,16Q15.5,16.0492,15.4904,16.0975Q15.4808,16.1458,15.4619,16.1913Q15.4431,16.236800000000002,15.4157,16.2778Q15.3884,16.3187,15.3536,16.3536Q15.3187,16.3884,15.2778,16.4157Q15.2368,16.4431,15.1913,16.4619Q15.1458,16.480800000000002,15.0975,16.4904Q15.0492,16.5,15,16.5L5,16.5Q4.9507543,16.5,4.9024549,16.4904Q4.854155,16.480800000000002,4.808658,16.4619Q4.763161,16.4431,4.722215,16.4157Q4.681269,16.3884,4.646447,16.3536Q4.611625,16.3187,4.584265,16.2778Q4.556906,16.236800000000002,4.53806,16.1913Q4.519215,16.1458,4.509607,16.0975Q4.5,16.0492,4.5,16ZM5.5,4.5L5.5,15.5L14.5,15.5L14.5,4.5L5.5,4.5Z"
                  fill="currentColor" fill-opacity="1" style="mix-blend-mode:passthrough"/>
        </g>
        <g>
            <path d="M3,14.5L5.142860000000001,14.5Q5.1921,14.5,5.2404,14.4904Q5.2887,14.4808,5.3342,14.4619Q5.3797,14.4431,5.420640000000001,14.4157Q5.46159,14.3884,5.49641,14.3536Q5.53123,14.3187,5.558590000000001,14.2778Q5.58595,14.2368,5.6048,14.1913Q5.62364,14.1458,5.63325,14.0975Q5.642860000000001,14.0492,5.642860000000001,14Q5.642860000000001,13.9508,5.63325,13.9025Q5.62364,13.8542,5.6048,13.8087Q5.58595,13.7632,5.558590000000001,13.7222Q5.53123,13.6813,5.49641,13.6464Q5.46159,13.6116,5.420640000000001,13.5843Q5.3797,13.5569,5.3342,13.5381Q5.2887,13.5192,5.2404,13.5096Q5.1921,13.5,5.142860000000001,13.5L5.1418800000000005,13.5L3.5,13.5L3.5,2.5L12.5,2.5L12.5,4.117649999999999Q12.5,4.16689,12.50961,4.21519Q12.51921,4.26349,12.53806,4.30899Q12.5569,4.35449,12.58426,4.39543Q12.61162,4.43638,12.64645,4.4712Q12.68127,4.5060199999999995,12.72221,4.53338Q12.76316,4.56074,12.80866,4.57959Q12.85415,4.5984300000000005,12.90245,4.60804Q12.95075,4.617649999999999,13,4.617649999999999Q13.0492,4.617649999999999,13.0975,4.60804Q13.1458,4.5984300000000005,13.1913,4.57959Q13.2368,4.56074,13.2778,4.53338Q13.3187,4.5060199999999995,13.3536,4.4712Q13.3884,4.43638,13.4157,4.39543Q13.4431,4.35449,13.4619,4.30899Q13.4808,4.26349,13.4904,4.21519Q13.5,4.16689,13.5,4.117649999999999L13.5,2Q13.5,1.9507543,13.4904,1.9024549Q13.4808,1.854155,13.4619,1.8086579999999999Q13.4431,1.763161,13.4157,1.722215Q13.3884,1.681269,13.3536,1.646447Q13.3187,1.611625,13.2778,1.584265Q13.2368,1.5569060000000001,13.1913,1.53806Q13.1458,1.519215,13.0975,1.509607Q13.0492,1.5,13,1.5L3.3571429999999998,1.5Q3.00383492,1.5,2.7531499999999998,1.747736Q2.5,1.99790743,2.5,2.352941L2.5,14Q2.5,14.0492,2.509607,14.0975Q2.519215,14.1458,2.5380599999999998,14.1913Q2.556906,14.2368,2.584265,14.2778Q2.611625,14.3187,2.646447,14.3536Q2.681269,14.3884,2.722215,14.4157Q2.763161,14.4431,2.808658,14.4619Q2.854155,14.4808,2.9024549,14.4904Q2.9507543,14.5,3,14.5Z"
                  fill-rule="evenodd" fill="currentColor" fill-opacity="1" style="mix-blend-mode:passthrough"/>
        </g>
        <g>
            <path d="M8,7.5L12,7.5Q12.04925,7.5,12.09755,7.509607Q12.14584,7.519215,12.19134,7.53806Q12.23684,7.556906,12.27778,7.584265Q12.31873,7.611625,12.35355,7.646447Q12.38837,7.681269,12.41573,7.722215Q12.44309,7.763161,12.46194,7.808658Q12.48078,7.854155,12.49039,7.9024549Q12.5,7.9507543,12.5,8Q12.5,8.0492457,12.49039,8.0975451Q12.48078,8.145845,12.46194,8.191342Q12.44309,8.236839,12.41573,8.277785Q12.38837,8.318731,12.35355,8.353553Q12.31873,8.388375,12.27778,8.415735Q12.23684,8.443094,12.19134,8.46194Q12.14584,8.480785000000001,12.09755,8.490393Q12.04925,8.5,12,8.5L8,8.5Q7.9507543,8.5,7.9024549,8.490393Q7.854155,8.480785000000001,7.808658,8.46194Q7.763161,8.443094,7.722215,8.415735Q7.681269,8.388375,7.646447,8.353553Q7.611625,8.318731,7.584265,8.277785Q7.556906,8.236839,7.53806,8.191342Q7.519215,8.145845,7.509607,8.0975451Q7.5,8.0492457,7.5,8Q7.5,7.9507543,7.509607,7.9024549Q7.519215,7.854155,7.53806,7.808658Q7.556906,7.763161,7.584265,7.722215Q7.611625,7.681269,7.646447,7.646447Q7.681269,7.611625,7.722215,7.584265Q7.763161,7.556906,7.808658,7.53806Q7.854155,7.519215,7.9024549,7.509607Q7.9507543,7.5,8,7.5Z"
                  fill-rule="evenodd" fill="currentColor" fill-opacity="1" style="mix-blend-mode:passthrough"/>
        </g>
        <g>
            <path d="M8,10.5L12,10.5Q12.04925,10.5,12.09755,10.509607Q12.14584,10.519214999999999,12.19134,10.53806Q12.23684,10.556906,12.27778,10.584265Q12.31873,10.611625,12.35355,10.646447Q12.38837,10.681269,12.41573,10.722215Q12.44309,10.763161,12.46194,10.808658Q12.48078,10.854155,12.49039,10.9024549Q12.5,10.9507543,12.5,11Q12.5,11.0492457,12.49039,11.0975451Q12.48078,11.145845,12.46194,11.191342Q12.44309,11.236839,12.41573,11.277785Q12.38837,11.318731,12.35355,11.353553Q12.31873,11.388375,12.27778,11.415735Q12.23684,11.443094,12.19134,11.46194Q12.14584,11.480785000000001,12.09755,11.490393Q12.04925,11.5,12,11.5L8,11.5Q7.9507543,11.5,7.9024549,11.490393Q7.854155,11.480785000000001,7.808658,11.46194Q7.763161,11.443094,7.722215,11.415735Q7.681269,11.388375,7.646447,11.353553Q7.611625,11.318731,7.584265,11.277785Q7.556906,11.236839,7.53806,11.191342Q7.519215,11.145845,7.509607,11.0975451Q7.5,11.0492457,7.5,11Q7.5,10.9507543,7.509607,10.9024549Q7.519215,10.854155,7.53806,10.808658Q7.556906,10.763161,7.584265,10.722215Q7.611625,10.681269,7.646447,10.646447Q7.681269,10.611625,7.722215,10.584265Q7.763161,10.556906,7.808658,10.53806Q7.854155,10.519214999999999,7.9024549,10.509607Q7.9507543,10.5,8,10.5Z"
                  fill-rule="evenodd" fill="currentColor" fill-opacity="1" style="mix-blend-mode:passthrough"/>
        </g>
    </g>
</svg>
