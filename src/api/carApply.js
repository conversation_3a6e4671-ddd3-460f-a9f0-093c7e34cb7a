import request from '@/utils/request'
import { getSign } from '@/utils/request/sign'
import Qs from 'qs'
import store from "@store";
// 查询字典接口
export function getEnums() {
  return request({
    url: '/vehicle-dispatch/vd/system/dict',
    method: 'GET',
    // sign: true,
  })
}

// 获取当前登录人
export function currentUser(userName) {
  return request({
    url: '/vehicle-dispatch/vd/system/currentUser',
    method: 'get',
    params: { userName }
    // sign: true,
  })
}

// 获取当前登录人第三方信息
export function getThirdPartUser(userName) {
  return request({
    url: '/vehicle-dispatch/vd/tp/user/list',
    params: {userName}
  })
}

// 申请时获取车辆调度员
export function carDispatchUser(useCarType) {
  return request({
    url: '/vehicle-dispatch/vd/af/carDispatchUser',
    method: 'GET',
    params: { useCarType }
    // sign: true,
  })
}


// 申请时获取租车公司
export function carCompany() {
  return request({
    url: '/vehicle-dispatch/vd/af/carCompany',
    method: 'GET'
    // sign: true,
  })
}

// 申请时获取车辆 租车公司ID:carCompanyInfoId
export function carList(carCompanyInfoId) {
  return request({
    url: '/vehicle-dispatch/vd/af/car',
    method: 'GET',
    params: { carCompanyInfoId }
  })
}

// 申请时获取车辆 租车公司ID:carCompanyInfoId
// searchValue	搜索条件（司机名称、用户名、手机号、驾龄）
export function driverList(params) {
  return request({
    url: '/vehicle-dispatch/vd/af/driver',
    method: 'GET',
    params
  })
}

// 提醒标题
export function remindTitle() {
  return request({
    url: '/vehicle-dispatch/vd/af/remindTitle',
    method: 'GET'
  })
}

// 问题反馈链接
export function getFeedBackLink() {
  return request({
    url: '/vehicle-dispatch/vd/feedBackLink',
    method: 'GET'
  })
}

// 操作手册链接
export function getOpManualLink() {
  return request({
    url: '/vehicle-dispatch/vd/opManualLink',
    method: 'GET'
  })
}

// 退回原因 类型 1 租车公司退回原因
export function rejectCause(type = 1) {
  return request({
    url: '/vehicle-dispatch/vd/af/reject/cause',
    method: 'GET',
    params: { type }
  })
}

// 申请时获取出发地
export function startAddress(type = 1) {
  return request({
    url: '/vehicle-dispatch/vd/af/startAddress',
    method: 'GET',
    params: { type }
  })
}

// 申请时获取租车公司审批人
export function carCompanyApprover(carCompanyInfoId) {
  return request({
    url: '/vehicle-dispatch/vd/af/carCompany/approver',
    method: 'GET',
    params: { carCompanyInfoId }
  })
}

// fawkes签名生成
// code
// grant_type
// source
export function getFawkesSign(params) {
  return request({
    url: '/vehicle-dispatch/fawkes/sign',
    method: 'GET',
    params
  })
}

// 用户注册
//  code: '' 第三方授权码 PC没有
//  phone: '' 手机号
//  source: '' 来源 1 飞书 2微信 3 PC
export function authRegister(data) {
  return request({
    url: '/vehicle-dispatch/vd/auth/register',
    method: 'post',
    data
  })
}

// 用户登录
// openId openId，PC没有
// source 来源 1 飞书 2微信 3 PC
export function authLogin(params) {
  return request({
    url: '/vehicle-dispatch/vd/auth/login',
    method: 'post',
    params
  })
}

// 用户注册短信发送
// phone 电话
export function registerSms(params) {
  return request({
    url: '/vehicle-dispatch/vd/auth/register/sms',
    method: 'post',
    params,
    sign: true
  })
}

// 申请时获取人员
export function applyPersonInfo(params) {
  return request({
    url: '/vehicle-dispatch/vd/af/person',
    method: 'get',
    params
  })
}

// 验证用户是否注册
export function isSignup(params){
  return request({
    url: '/vehicle-dispatch/vd/auth/register/verify',
    method: 'post',
    params,
    sign: true
  })
}

// 用户是否需要飞书认证
export function needFeishuAuth(phone) {
  return request({
    url: '/vehicle-dispatch/vd/auth/feishu/verify',
    method: 'post',
    params: {
      phone
    },
    sign: true
  })
}

// 申请时获取人员
/*
*carCompanyInfoId	租车公司ID
*carSearchValue	搜索条件（车辆名称、车牌号、车型、座位数）
* driverSearchValue	搜索条件（司机名称、用户名、手机号、驾龄）
* */
export function getDriversAndCars(params) {
  return request({
    url: '/vehicle-dispatch/vd/af/driverCar',
    method: 'get',
    params
  })
}

// 出车统计列表
export function getFlowCar(params) {
  return request({
    url: '/vehicle-dispatch/vd/statistics/flow/page',
    method: 'get',
    params
  })
}

// 获取操作按钮
export function getFlowButton(params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/button',
    method: 'get',
    params
  })
}
// 获取按钮
export function getFlowButton1(applyIds) {
  return request({
    url: '/vehicle-dispatch/vd/flow/uc/button',
    params: {applyIds}
  })
}

// 撤销流程
export function getFlowRevocation(data) {
  return request({
    url: '/vehicle-dispatch/vd/flow/revocation',
    method: 'post',
    data
  })
}

// 流程变更
export function getFlowChange(data) {
  return request({
    url: '/vehicle-dispatch/vd/flow/change',
    method: 'post',
    data
  })
}

// 司机统计
export function getStatsDriver(params) {
  return request({
    url: '/vehicle-dispatch/vd/stats/driver',
    method: 'get',
    params
  })
}

// 租车公司统计
export function getRentCompanyStats(params) {
  return request({
    url: '/vehicle-dispatch/vd/stats/carComp',
    params
  })
}

// 出车统计
export function getUseCarStats(params) {
  return request({
    url: '/vehicle-dispatch/vd/stats/flow/state/num',
    params
  })
}

// 部门用车列表
export function getDepartmentCarUseList(params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/dept/page',
    params
  })
}

// 导出出车记录
export function exportUseCarRecords(params) {
  return request({
    url: '/vehicle-dispatch/vd/statistics/export',
    method: 'post',
    params,
    responseType: 'blob'
  })
}

// 导出记录列表
export function exportRecords(params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/export',
    method: 'post',
    responseType: 'blob',
    params
  })
}

// 获取审批人列表
export function getApproveList(projectId, projectName) {
  return request({
    url: '/vehicle-dispatch/vd/af/sp/user/list',
    params: {projectId, projectName}
  })
}

// 获取乘车人列表
export function getPassengerList(applyIds) {
  return request({
    url: '/vehicle-dispatch/vd/flow/uc/person',
    params: {
      applyIds
    }
  })
}

// 途经点列表
export function getWayPoints(ids) {
  return request({
    url: '/vehicle-dispatch/vd/flow/uc/waypoint',
    params: {
      ids
    }
  })
}


// 获取车辆信息
export function getCarInfo(params) {
  const payload = typeof params === 'string' ? {ids: params} : params;
  return request({
    url: '/vehicle-dispatch/vd/flow/uc/car',
    params: payload
  })
}

// 获取司机信息
export function getDriverInfo(params) {
  const payload = typeof params === 'string' ? {ids: params} : params;
  return request({
    url: '/vehicle-dispatch/vd/flow/uc/driver',
    params: payload
  })
}

// 获取费用信息
export function getFeeInfo(applyIds) {
  return request({
    url: '/vehicle-dispatch/vd/flow/uc/fy',
    params: {applyIds}
  })
}

// 获取待办任务数量
export function getTaskNum() {
  return request({
    url: '/vehicle-dispatch/vd/flow/userTasks/count',
  })
}
// 获取到期提醒任务数量
export function getExpireNum() {
  return request({
    url: '/vehicle-dispatch/vd/expire/count',
    params: {
      projectId: store.state.portal.id
    }
  })
}

// 地址敏感词监测
export function sensitiveDetection(params) {
  return request({
    url: '/vehicle-dispatch/vd/ai/sensitive/key',
    params: {
      ...params
    }
  })
}

// 暂存行车日志填报
export function editForm(params) {
  return request({
    url: '/vehicle-dispatch/vd/flow/edit',
    method: 'post',
    data: params
  })
}

// 根据taskKey获取表单配置
export function getTaskFieldConfig(taskKey) {
  return request({
    url: '/vehicle-dispatch/vd/form/config/get',
    method: 'get',
    params: { taskKey }
  });
}

// 获得全部表单字段配置
export function getAllFieldConfig() {
  return request({
    url: '/vehicle-dispatch/vd/form/config/all',
    method: 'get',
  });
}

// 获取项目二级单位
export function getProjectUnit(params) {
  return request({
    url: '/vehicle-dispatch/vd/projectName/dept/level2',
    params
  })
}

// 获取全部二级单位
export function getAllProjectUnit() {
  return request({
    url: '/vehicle-dispatch/vd/dept/level2'
  })
}
