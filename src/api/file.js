/*
 * by: chen ming
 * time: 2019/12/2
 * desc: 文件相关
 * */
import request from '@/utils/request'
import axiosexport from "@utils/export";

//上传文件 form data格式
export function uploadFile (data) {
  return request({
    url: '/sys-storage/upload',
    method: 'post',
    data,
    timeout: 0
  })
}

// 获取缩略图
export function getThumbnail (params) {
  return request({
    url: '/sys-storage/down_thumbnail',
    method: 'get',
    responseType: 'blob',
    params,
  })
}

// 获取缩略图
export function exportThumbnail (params) {
  return axiosexport({
    url: '/sys-storage/down_thumbnail',
    method: 'get',
    responseType: 'blob',
    params,
  })
}

// 根据文件token和grouptoken获取文件信息,data中传至少传g9s数组和f8s数组中的一个
export function getFile (data) {
  return request({
    url: '/sys-storage/file',
    method: 'post',
    data,
  })
}

// 根据文件token和grouptoken删除文件，token删除单文件，grouptoken删除文件组
export function deleteFile (data) {
  return request({
    url: '/sys-storage/file',
    method: 'delete',
    data,
  })
}

//根据fileToken下载文件
export function downloadFile (f8s) {
  return request({
    url: '/sys-storage/download',
    method: 'GET',
    params: {
      f8s
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    timeout: 0,
    responseType: 'blob'
  })
}

//根据fileToken下载图片
export function downloadImage (f8s) {
  return request({
    method: 'get',
    url: '/sys-storage/download_image',
    responseType: 'blob',
    params: {
      f8s
    },
  })
}

//根据groupToken下载zip
export function downloadZip (groupToken) {
  return request({
    url: '/sys-storage/zip',
    method: 'get',
    params: { groupToken },
    responseType: 'blob'
  })
}

//修改已上传文件信息
export function changeFileG9s (groupToken, data) {
  return request({
    url: `/sys-storage/file/token?groupToken=${groupToken}`,
    method: 'put',
    data: data,
  })
}

//上传图片识别文字
export function orc (data) {
  return request({
    url: '/sys-storage/ocr',
    method: 'post',
    data,
    timeout: 0
  })
}
