import request from '@/utils/request'


// 通过code换取网页授权access_token
export function authAccessToken(code) {
  return request({
    url: '/vehicle-dispatch/wx/sns/oauth2/access_token',
    method: 'GET',
    params: { code }
  })
}

// 拉取用户信息(需scope为 snsapi_userinfo)
// accessToken	网页授权接口调用凭证,注意：此access_token与基础支持的access_token不同
// lang 返回国家地区语言版本，zh_CN 简体，zh_TW 繁体，en 英语,示例值(zh_CN)
// openid 用户的唯一标识
export function getUserInfo(params) {
  return request({
    url: '/vehicle-dispatch/wx/sns/userinfo',
    method: 'GET',
    params
  })
}

// 微信公众号获取JS签名
export function getWxSign(url) {
  return request({
    url: '/vehicle-dispatch/wx/get_wx_sign',
    method: 'GET',
    params: { url }
  })
}
