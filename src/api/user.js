/*
 * @Author: <EMAIL>
 * @Date: 2019-11-07 09:36:15
 * @LastEditors: ye_xf
 * @LastEditTime: 2022-08-05 15:28:31
 * @Description: 用户相关接口
 */
import request from '@/utils/request'
import store from '@/store'
/**查询用户菜单 */
export function getRouter(data) {
  return request({
    url: '/sys-user/user/menus',
    method: 'get',
    params: data,
    cMsg: true
  })
}

/**查询用户菜单下按钮 */
export function getPermissions(menuId) {
  return request({
    url: '/sys-user/user/buttons',
    method: 'get',
    params: {
      menuId,
      portalId: store.state.portal.id
    }
  })
}

/**获取所有门户 */
export function getPortals() {
  return request({
    // url: '/sys-user/user/portals',
    url: '/vehicle-dispatch/vd/user/portals',
    method: 'GET',
  })
}
/**获取收藏门户 */
export function getFavoritePortals() {
  return request({
    url: '/vehicle-dispatch/vd/user/portals/favoriteList',
    method: 'GET',
  })
}
// 收藏接口
export function portalsFavorite(data) {
  return request({
    url: '/vehicle-dispatch/vd/user/portals/favorite',
    method: 'post',
    params: data
  })
}
// 获取用户信息
export function getUser(data) {
  return request({
    url: 'sys-user/userInfo',
    method: 'get',
    params: data
  })
}

export function searchUser(params) {
  return request({
    url: '/sys-user/users/page',
    method: 'GET',
    params: params,
  })
}


export function getAuthPortalList() {
  return request({
    url: '/vehicle-dispatch/vd/user/projects',
    method: 'GET'
  })
}
