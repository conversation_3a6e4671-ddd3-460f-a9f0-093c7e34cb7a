module.exports = {
  // 测试环境
  testEnvironment: 'jsdom',

  // 模块文件扩展名
  moduleFileExtensions: [
    'js',
    'json',
    'vue'
  ],

  // 转换规则
  transform: {
    '^.+\\.vue$': 'vue-jest',
    '.+\\.(css|styl|less|sass|scss|svg|png|jpg|ttf|woff|woff2)$': 'jest-transform-stub',
    '^.+\\.jsx?$': 'babel-jest'
  },

  // 转换忽略的模块
  transformIgnorePatterns: [
    '/node_modules/(?!(fawkes-lib|fawkes-mobile-lib)/)'
  ],

  // 模块名映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@modules/(.*)$': '<rootDir>/src/modules/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@store/(.*)$': '<rootDir>/src/store/$1'
  },

  // 快照序列化器
  snapshotSerializers: ['jest-serializer-vue'],

  // 测试匹配模式
  testMatch: [
    '**/tests/unit/**/*.spec.(js|jsx|ts|tsx)|**/__tests__/*.(js|jsx|ts|tsx)'
  ],

  // 测试 URL
  testURL: 'http://localhost/',

  // 监听插件
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ],

  // 覆盖率配置
  collectCoverageFrom: [
    'src/**/*.{js,vue}',
    '!src/main.js',
    '!src/router/index.js',
    '!**/node_modules/**'
  ],

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50
    }
  },

  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/tests/unit/setup.js']
}
