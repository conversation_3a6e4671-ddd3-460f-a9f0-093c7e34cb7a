server {
    listen 80;
    server_name  localhost;
    # 开启|关闭 gzip。
    gzip on;

    # 文件大于指定 size 才压缩，以 kb 为单位。
    gzip_min_length 10;

    # 压缩级别，1-9，值越大压缩比越大，但更加占用 CPU，且压缩效率越来越低。
    gzip_comp_level 2;

    # 压缩的文件类型。
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/json;

    # 开启后如果能找到 .gz 文件，直接返回该文件，不会启用服务端压缩。
    gzip_static on;

    # 是否添加响应头 Vary: Accept-Encoding 建议开启。
    gzip_vary on;

    # 请求压缩的缓冲区数量和大小，以 4k 为单位，32 为倍数。
    gzip_buffers 32 4K;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    # disable any limits to avoid HTTP 413 for large image uploads
    client_max_body_size 0;

    # required to avoid HTTP 411: see Issue #1486 (https://github.com/docker/docker/issues/1486)
    chunked_transfer_encoding on;

    # proxy_hide_header Access-Control-Allow-Origin;
    proxy_hide_header Access-Control-Allow-Headers;
    proxy_hide_header Access-Control-Allow-Methods;
    proxy_hide_header Access-Control-Allow-Credentials;

    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Real-Port $remote_port;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    add_header Access-Control-Allow-Origin $http_origin;
    add_header Access-Control-Allow-Headers X-Requested-With;
    add_header Access-Control-Allow-Methods GET,POST,PUT,DELETE,OPTIONS;
    add_header Access-Control-Allow-Credentials true;

    # Upgrade headers
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    # 禁用PUT及DELETE方法时、通过nginx转发
    set $method $request_method;
    if ($http_x_HTTP_Method_Override ~* 'PUT|DELETE') {
        set $method $http_X_HTTP_Method_Override;
    }
    proxy_method $method;

    location /static/version.json {
        root   /usr/share/nginx/html;
        index  ${INDEX_HTML_NAME} index.htm;
        add_header Cache-Control "no-store";
    }

    location / {
        root   /usr/share/nginx/html;
        index  ${INDEX_HTML_NAME} index.htm;

        # 根路径不缓存
        if ($request_uri = "/") {
            expires off;
            add_header Cache-Control "no-store, no-cache, must-revalidate" always;
            add_header X-Debug "Matched root" always;
        }
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        # add_header Cache-Control "no-cache";
        # try_files $uri $uri/ /index.html; # history mode
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    location /api/feishu/ {
        rewrite  /api/feishu/(.*)  /$1  break;
        proxy_pass https://open.feishu.cn;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /xmb/api/ {
        proxy_pass ${HOST};
		proxy_buffering off;
		proxy_request_buffering off;
    }

    location /api/ {
        proxy_pass ${HOST};
		proxy_buffering off;
		proxy_request_buffering off;
    }

    location /filePreview/ {
        proxy_pass  ${FILE_PREVIEW};
		proxy_buffering off;
		proxy_request_buffering off;
    }

    #子应用代理
    location /sub_app_bpm/ {
       proxy_pass ${SUB_APP_BPM};
		proxy_buffering off;
		proxy_request_buffering off;
    }
    location /sub_app_wp/ {
       proxy_pass ${SUB_APP_WP};
		proxy_buffering off;
		proxy_request_buffering off;
    }
    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
